syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";

import "chilat/basis/model/cart_model.proto";
import "chilat/basis/model/goods_model.proto";
import "chilat/basis/model/sys_translate_model.proto";
import "common.proto";
import "common/business.proto";
import "mall/commodity/model/category_info_model.proto";
import "mall/commodity/model/goods_info_model.proto";


message GoodsDetailPageResp {
    common.Result result = 1;
    GoodsDetailPageModel data = 2;
}

message GoodsDetailPageModel {
    chilat.basis.ConfigModel globalConfig = 10; //商城全局配置（当前语言，翻译信息等）
    //passport.LoginUserModel loginUser = 20; //登录用户信息（空值表示未登录）
    common.SeoData seoData = 30; //SEO设置信息
    chilat.basis.MallCategoryTreeModel categoryTree = 40; //类目树
    repeated chilat.basis.MallCategoryPathItemModel categoryPath = 50; //商品分类路径
    chilat.basis.MidCartModel cartInfo = 70; //购物车数据
    commodity.GoodsInfoModel pageData = 100; //页面内主要数据内容
}

message GoodsDetailDataResp {
    common.Result result = 1;
    commodity.GoodsInfoModel data = 100; //页面内主要数据内容
}

