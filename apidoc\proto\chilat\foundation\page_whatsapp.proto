syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/foundation/model/page_whatsapp_model.proto";
import "chilat/foundation/param/page_whatsapp_param.proto";
import "common.proto";

// 页面中的WhatsApp管理
service PageWhatsApp {
  // 查询列表
  rpc listPage (PageWhatsAppPageQueryParam) returns (PageWhatsAppPageResp);
  // 查询详情
  rpc getDetail (common.IdParam) returns (PageWhatsAppDetailResp);
  // 保存信息（id不存在，则为新增）
  rpc saveInfo (PageWhatsAppSaveParam) returns (common.StringResult);
  // 查询日志
  rpc listLog (PageWhatsAppLogQueryParam) returns (PageWhatsAppLogResp);
}
