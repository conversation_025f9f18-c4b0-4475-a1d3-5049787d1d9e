<template>
  <div class="page-wrapper">
    <div class="bg-black min-w-[1280px]">
      <n-marquee class="!bg-black" auto-fill>
        <div
          class="h-[50px] leading-[50px] flex items-center gap-[10px] text-[14px] leading-[14px]"
        >
          <img
            src="@/assets/icons/home/<USER>"
            alt=""
            loading="lazy"
            class="w-[22px] h-[22px] ml-[20px]"
          />
          <span class="text-[#FFB700] font-medium">
            Mínimo desde US$ 2,000
          </span>
          <span class="text-[#7F7F7F]">hasta contenedor cerrado</span>
          <span class="text-[#D9D9D9]"
            >hace compras directamente del mercado mayorista chino</span
          >
        </div>
      </n-marquee>
    </div>

    <div class="header-wrapper min-w-[1280px] text-white relative">
      <div class="w-[1280px] mx-auto px-[38px] py-[20px]">
        <div class="header-content">
          <div class="flex items-center">
            <a
              href="/"
              data-spm-box="navigation-logo-icon"
              class="w-[218px] mr-auto"
            >
              <img
                src="@/assets/icons/common/logo-white.svg"
                alt="ChilatShop"
                loading="lazy"
                class="w-[218px]"
              />
            </a>

            <div class="country-select flex items-center mr-[60px]">
              <img
                loading="lazy"
                alt="address"
                class="w-[12px] mr-[8px]"
                src="@/assets/icons/common/address-white.svg"
              />
              <CountrySelect
                mode="popover"
                @save="onSaveCountry"
                spm="select_site_from_nav"
              />
            </div>
            <div class="flex gap-[20px]" data-spm-box="navigation-top-article">
              <a
                class="hover:underline underline-offset-[4px] cursor-pointer decoration-[#F2F2F2]"
                v-for="(news, index) in newsData"
                :key="index"
                :href="news.path"
                target="_blank"
              >
                {{ news.title }}
              </a>
            </div>
          </div>
        </div>
        <div
          class="text-[94px] leading-[94px] font-medium text-white mt-[72px]"
        >
          <span class="text-[#E50113]">Chilat</span>Shop
        </div>
        <div class="mt-[40px] flex gap-[72px]" id="auth-button">
          <div class="w-[446px] text-[20px] leading-[30px]">
            {{ authStore.i18n("cm_home_latamWholesale") }}
          </div>
          <div class="flex gap-[16px]">
            <div
              @click="onLoginClick('top', 1)"
              class="w-[222px] h-[60px] flex justify-center items-center border-1 border-[#fff] rounded-[500px] text-[18px] leading-[18px] cursor-pointer hover:bg-white hover:text-[#E50113] hover:font-medium transition-all duration-300"
            >
              {{ authStore.i18n("cm_common_login") }}
            </div>
            <div
              class="relative w-[222px] h-[60px] flex justify-center items-center bg-[#E50113] rounded-[500px] text-[18px] leading-[18px] cursor-pointer hover:bg-[#F20114] hover:font-medium transition-all duration-300"
              @click="onLoginClick('top', 0)"
            >
              {{ authStore.i18n("cm_common_register") }}
              <img
                loading="lazy"
                class="absolute z-10 w-[70px] right-[-20px] top-[-40px]"
                src="@/assets/icons/gift.webp"
                referrerpolicy="no-referrer"
              />
            </div>
          </div>
        </div>
        <div class="text-[16px] leading-[22px] flex mt-[82px] opacity-80">
          <div class="w-[358px]">
            {{ authStore.i18n("cm_home_factoryDirect") }}
          </div>
          <div class="w-[1px] h-[40px] ml-[32px] mr-[33px] bg-white"></div>
          <div class="w-[358px]">
            {{ authStore.i18n("cm_home_multiSupplier") }}
          </div>
          <div class="w-[1px] h-[40px] ml-[32px] mr-[33px] bg-white"></div>
          <div class="w-[358px]">
            {{ authStore.i18n("cm_home_importSimplified") }}
          </div>
        </div>
        <!-- 搜索功能区域 -->
        <div
          class="absolute top-[164px] right-[38px] flex flex-col gap-[22px] items-end"
        >
          <!-- 文字搜索容器 -->
          <div class="search-container">
            <!-- 点击搜索icon 搜索框向左展开 右侧固定  -->
            <div
              class="home-search"
              data-spm-box="navigation-keyword-search"
              :class="{ 'search-expanded': pageData.showSearchBox }"
            >
              <div class="home-search-inner" id="page-header-button">
                <div
                  class="search-bar-input-wrapper transition-opacity duration-300 ease-out"
                  :class="{
                    'opacity-100': pageData.showSearchBox,
                    'opacity-0': !pageData.showSearchBox,
                  }"
                >
                  <input
                    ref="searchInputRef"
                    class="search-bar-input"
                    type="text"
                    maxlength="50"
                    v-model.trim="pageData.keyword"
                    @keyup.enter="onKeywordClick($event)"
                    :placeholder="authStore.i18n('cm_home.searchPlaceholder')"
                  />
                </div>
                <div
                  class="flex items-center transition-all duration-300 delay-75 ease-out"
                  :class="{
                    'opacity-100 translate-x-0': pageData.showSearchBox,
                    'opacity-0 translate-x-2': !pageData.showSearchBox,
                  }"
                  v-show="pageData.showSearchBox"
                >
                  <button
                    class="search-bar-inner-button ml-2"
                    @click="onKeywordClick($event)"
                  >
                    <img
                      loading="lazy"
                      src="@/assets/icons/search.svg"
                      alt="search"
                      class="w-[24px] mr-[4px]"
                      referrerpolicy="no-referrer"
                    />
                    <span class="text-[18px] leading-[18px] font-medium">{{
                      authStore.i18n("cm_home.search")
                    }}</span>
                  </button>
                </div>

                <div class="popup common-panel association-popup"></div>
              </div>
            </div>
            <!-- 搜索icon -->
            <div
              class="search-icon-trigger"
              :class="{ 'icon-hidden': pageData.showSearchBox }"
              @click="toggleSearchBox"
            >
              <img
                src="@/assets/icons/common/search-primary.svg"
                alt="search"
                loading="lazy"
              />
            </div>
          </div>

          <!-- 以图搜图功能 -->
          <div class="flex justify-end">
            <image-search-multi placement="left-start">
              <div
                class="w-[44px] h-[44px] rounded-full bg-[#e50113] flex items-center justify-center cursor-pointer"
              >
                <img
                  src="@/assets/icons/common/image-white.svg"
                  alt="search"
                  loading="lazy"
                />
              </div>
            </image-search-multi>
          </div>
        </div>
        <!-- 固定头部 -->
        <div v-if="pageData.isHeaderFixed" class="fixed-header">
          <div
            class="w-[1280px] h-[74px] px-[38px] py-[14px] flex justify-between mx-auto"
          >
            <a href="/" data-spm-box="navigation-logo-icon">
              <img
                alt="ChilatShop"
                loading="lazy"
                class="w-[220px]"
                src="@/assets/icons/common/logo.svg"
              />
            </a>
            <FixedHeaderSearch class="flex-1 ml-[38px] mr-[30px]" />
            <div class="flex items-center">
              <div
                class="country-select text-[#333] flex items-center mr-[30px]"
              >
                <img
                  loading="lazy"
                  alt="address"
                  class="w-[12px] mr-[8px]"
                  src="@/assets/icons/common/address.svg"
                />
                <CountrySelect
                  mode="popover"
                  @save="onSaveCountry"
                  spm="select_site_from_nav"
                />
              </div>
              <!-- 登录注册按钮 -->
              <div class="flex gap-[10px]">
                <div
                  @click="onLoginClick('fixed', 1)"
                  class="w-[144px] h-[44px] flex justify-center items-center border-1 border-[#A6A6A6] rounded-[500px] text-[16px] leading-[16px] cursor-pointer hover:border-[#E50113] transition-all duration-300 text-[#333] hover:text-[#E50113]"
                >
                  {{ authStore.i18n("cm_common_login") }}
                </div>
                <div
                  class="w-[144px] h-[44px] flex justify-center items-center bg-[#E50113] rounded-[500px] text-[16px] leading-[16px] cursor-pointer hover:bg-[#F20114] transition-all duration-300 text-white relative"
                  @click="onLoginClick('fixed', 0)"
                >
                  {{ authStore.i18n("cm_common_register") }}
                  <img
                    loading="lazy"
                    class="absolute z-10 w-[54px] right-[-20px] top-[-26px]"
                    src="@/assets/icons/gift.webp"
                    referrerpolicy="no-referrer"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="w-[1280px] mx-auto">
      <!-- 福利明细 -->
      <div
        class="benefit-breakdown relative w-[1240px] h-[290px] mx-auto bg-[#ac2023] rounded-[12px] mt-[-58px]"
      >
        <img
          src="@/assets/icons/home/<USER>"
          alt=""
          loading="lazy"
          class="absolute"
        />
        <div
          class="pt-[36px] px-[44px] flex justify-between relative z-2 text-white"
        >
          <div
            v-for="(item, index) in benefitBreakdown"
            :key="index"
            class="flex justify-between gap-[19px]"
          >
            <div class="w-[200px]">
              <img
                :src="item.icon"
                :alt="item.title"
                loading="lazy"
                class="w-[32px] h-[32px]"
              />
              <div
                class="text-[20px] leading-[24px] mt-[20px] mb-[16px] h-[48px]"
              >
                {{ item.title }}
              </div>
              <div class="text-[16px] leading-[20px]">{{ item.desc }}</div>
            </div>
            <div
              class="w-[1px] h-[48px] bg-white mt-[48px]"
              v-show="index !== benefitBreakdown.length - 1"
            ></div>
          </div>
        </div>
      </div>

      <div class="w-full px-[64px] mt-[140px]">
        <div class="flex flex-col items-center gap-[40px]">
          <div
            class="relative w-[80px] h-[80px] flex items-center justify-center"
          >
            <img
              src="@/assets/icons/home/<USER>"
              :alt="authStore.i18n('cm_home_3stepPurchasing')"
              loading="lazy"
              class="w-[80px] h-[80px] absolute top-0"
            />
            <div
              class="text-[50px] leading-[50px] text-center text-[#e50113] relative z-2 ml-[5px]"
            >
              {{ buySteps.length }}
            </div>
          </div>
          <div class="text-[34px] leading-[34px] font-medium">
            {{ authStore.i18n("cm_home_3stepPurchasing") }}
          </div>
          <div class="w-[50px] h-[3px] bg-[#e50113]"></div>
        </div>

        <div class="flex mt-[78px]">
          <!-- 左侧主图区域 -->
          <div class="w-[508px] flex flex-col space-y-[64px]">
            <div
              v-for="(step, index) in buySteps"
              :key="index"
              class="relative w-full h-[200px]"
            >
              <img
                :src="step.image"
                :alt="step.title"
                loading="lazy"
                class="w-full h-[200px] object-cover rounded-[12px] transition-all duration-700 ease-in-out"
                :class="{
                  'brightness-110': index === currentStep,
                  'brightness-100': index !== currentStep,
                }"
              />
              <!-- 蒙层效果 -->
              <div
                class="absolute inset-0 bg-white bg-opacity-60 rounded-[12px] transition-all duration-700 ease-in-out"
                :class="{
                  'opacity-0 pointer-events-none': index === currentStep,
                  'opacity-100': index !== currentStep,
                }"
              ></div>
            </div>
          </div>

          <!-- 中间步骤线区域 -->
          <div
            class="w-[50px] relative flex flex-col items-center min-h-full ml-[58px] mr-[28px]"
          >
            <!-- 背景线 -->
            <div
              class="absolute left-1/2 transform -translate-x-1/2 w-[1px] bg-[#F2F2F2]"
              :style="{
                top: getStepPosition(0) + 25 + 'px',
                zIndex: 1,
                height: backgroundLineHeight + 'px',
              }"
            ></div>
            <!-- 进度线 -->
            <div
              class="absolute left-1/2 transform -translate-x-1/2 w-[1px] bg-[#E50113] transition-all duration-500 ease-out"
              :style="{
                top: getStepPosition(0) + 25 + 'px',
                height: progressLineHeight + 'px',
                zIndex: 2,
              }"
            ></div>

            <!-- 步骤点 -->
            <div
              v-for="(step, index) in buySteps"
              :key="index"
              :ref="(el) => (stepRefs[index] = el)"
              class="absolute"
              :style="{
                top: getStepPosition(index) + 'px',
                zIndex: 10,
              }"
            >
              <div
                class="relative w-[50px] h-[50px] z-20 bg-white rounded-full"
              >
                <!-- SVG 进度圆圈 -->
                <svg
                  class="absolute inset-0 w-[50px] h-[50px] transform -rotate-90"
                  viewBox="0 0 50 50"
                >
                  <!-- 背景圆圈 -->
                  <circle
                    cx="25"
                    cy="25"
                    r="24"
                    fill="none"
                    stroke="#e5e5e5"
                    stroke-width="1"
                  />
                  <!-- 进度圆圈 -->
                  <circle
                    cx="25"
                    cy="25"
                    r="24"
                    fill="none"
                    :stroke="index <= currentStep ? '#e50113' : '#e5e5e5'"
                    stroke-width="1"
                    stroke-dasharray="150.8"
                    :stroke-dashoffset="index <= currentStep ? 0 : 150.8"
                    class="transition-all duration-500 ease-out"
                    :class="{
                      'step-circle-animate':
                        index === currentStep && stepAnimations[index],
                    }"
                  />
                </svg>

                <!-- 步骤数字 -->
                <div
                  class="absolute inset-0 flex items-center justify-center text-[34px] leading-[34px] transition-colors duration-500"
                  :class="
                    index <= currentStep ? 'text-[#e50113]' : 'text-[#333]'
                  "
                >
                  {{ index + 1 }}
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧内容区域 -->
          <div class="flex-1 flex flex-col">
            <div
              v-for="(step, index) in buySteps"
              :key="index"
              :ref="(el) => (contentRefs[index] = el)"
              class="step-content mb-[64px]"
              :class="{ active: index === currentStep }"
            >
              <div
                class="text-[20px] leading-[20px] font-medium py-[24px] border-b-[1px] border-[#F2F2F2] mb-[34px]"
                :class="index > 0 ? 'border-t-[1px]' : ''"
              >
                {{ step.title }}
              </div>
              <div class="space-y-[30px]">
                <div
                  v-for="(item, itemIndex) in step.desc"
                  :key="itemIndex"
                  class="flex items-center gap-[10px]"
                >
                  <img
                    loading="lazy"
                    class="w-[14px]"
                    alt="check"
                    src="@/assets/icons/home/<USER>"
                  />

                  <span class="text-[16px] leading-[16px] text-[#7F7F7F]">{{
                    item
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 推荐分类 -->
      <div
        class="w-full px-[40px] mt-[140px]"
        data-spm-box="homepage-top-categories"
      >
        <div class="text-[34px] leading-[34px] font-medium text-center">
          {{ authStore.i18n("cm_home_startChinaSourcing") }}
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mt-[40px] mx-auto"></div>
        <div class="mt-[102px] flex gap-x-[13px] gap-y-[42px] flex-wrap">
          <a
            v-for="(category, index) in categoryData"
            :key="category.id"
            :data-spm-index="index + 1"
            :href="`/goods/list/${category.id}?cateName=${category.name}`"
            class="relative w-[290px] h-[140px] bg-[#FAFAFA] rounded-[12px]"
          >
            <img
              :alt="category.name"
              :src="category.categoryImage"
              loading="lazy"
              class="w-[166px] h-[110px] absolute top-[0px] left-[10px]"
            />
            <div
              class="w-[120px] text-[20px] leading-[24px] ml-[148px] mt-[22px] font-medium"
              :class="index === categoryData.length - 2 ? 'break-all' : ''"
            >
              {{ category.name }}
            </div>
            <img
              :alt="category.name"
              :src="category.categoryIcon"
              loading="lazy"
              class="w-[26px] h-[26px] absolute right-[10px] bottom-[10px]"
            />
          </a>
        </div>
        <a
          href="/goods/list/all"
          class="view-more relative w-[194px] h-[54px] mt-[94px] mx-auto flex justify-center items-center gap-[8px] border-[2px] border-transparent bg-[#FAFAFA] rounded-[500px] text-[20px] leading-[20px] font-medium cursor-pointer hover:bg-transparent hover:border-[#333] transition-all duration-300"
        >
          <span>Ver más</span>
          <img
            src="@/assets/icons/common/arrow-right-medium-black.svg"
            alt="arrow"
            loading="lazy"
            class="w-[10px]"
          />
        </a>
      </div>

      <div class="min-w-[1280px] pt-[80px] pb-[90px]">
        <!-- 热销货盘 -->
        <HotPallets
          class="w-[1200px] mx-auto"
          :showGoodsFindTitle="true"
        ></HotPallets>
      </div>

      <div class="px-[38px]">
        <div class="flex flex-col items-center gap-[40px]">
          <img src="@/assets/icons/home/<USER>" alt="point" loading="lazy" />
          <div class="flex items-center text-[34px] leading-[34px] font-medium">
            <span class="mr-[10px]">{{
              authStore.i18n("cm_home_whyGlobalBuyers")
            }}</span>
            <img
              src="@/assets/icons/common/logo.svg"
              alt="logo"
              loading="lazy"
              class="mr-[4px]"
            />
            <span>?</span>
          </div>
          <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto"></div>
        </div>
        <div class="flex justify-between mt-[78px]">
          <div
            v-for="(item, index) in whyUsData"
            :key="index"
            class="w-[286px] h-[496px] border-1 border-[#F2F2F2] rounded-[12px] px-[26px] py-[24px] flex flex-col gap-[18px]"
          >
            <img
              :src="item.icon"
              :alt="item.title"
              loading="lazy"
              class="w-[50px] h-[50px] mx-auto"
            />
            <div
              class="h-[78px] text-[20px] leading-[26px] font-medium flex items-center"
            >
              {{ item.title }}
            </div>
            <div v-if="index !== 0" class="flex flex-col gap-[12px]">
              <div v-for="desc in item.desc" :key="desc" class="flex gap-[8px]">
                <img
                  src="@/assets/icons/home/<USER>"
                  :alt="desc"
                  class="w-[14px] h-[14px]"
                />
                <span class="text-[16px] leading-[20px] text-[#7F7F7F]">{{
                  desc
                }}</span>
              </div>
            </div>
            <div v-else class="text-[16px] leading-[20px] text-[#7F7F7F]">
              <div>
                ¿Cansado de esperar respuestas manuales para cotizaciones?
              </div>
              <div class="flex gap-[8px] mt-[18px]">
                <img
                  loading="lazy"
                  class="w-[14px] h-[14px]"
                  src="@/assets/icons/home/<USER>"
                  alt="¿Cansado de esperar respuestas manuales para cotizaciones?"
                />
                Nuestra plataforma actualiza precios de fábricas chinas en
                tiempo real, puede consultar, comparar y ordenar en cualquier
                momento, sin depender de agentes.
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="w-full px-[38px] pt-[150px]">
        <div class="text-[34px] leading-[34px] font-medium text-center">
          {{ authStore.i18n("cm_home_whyChooseUs") }}
        </div>
        <div class="w-[50px] h-[3px] mx-auto mt-[40px] bg-[#e50113]"></div>
        <div class="custom-cursor">
          <ImageCarousel
            :images="carouselData"
            direction="left"
            :speed="100"
            :autoplay="true"
            :autoPlayWhenVisible="true"
            :gap="2"
            class="mt-[78px]"
            imageWidth="auto"
            imageHeight="220px"
          />
        </div>
        <div class="mt-[74px] flex justify-between">
          <div
            v-for="item in chooseData"
            :key="item.title"
            class="flex gap-[16px]"
          >
            <img
              :src="item.icon"
              :alt="item.title"
              loading="lazy"
              class="w-[40px] h-[40px]"
            />
            <div class="w-[300px]">
              <div class="text-[20px] leading-[22px] font-medium h-[44px]">
                {{ item.title }}
              </div>
              <div
                class="text-[16px] leading-[20px] mt-[12px] h-[77px] flex items-center"
              >
                {{ item.desc }}
              </div>
              <div class="w-[40px] h-[2px] bg-[#e50113] mt-[30px]"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 客户评价 -->
      <div class="w-full px-[4px] py-[150px]">
        <div class="text-[34px] leading-[34px] font-medium text-center">
          <span class="text-[40px] leading-[40px] mr-[12px]">5,000+</span>
          {{ authStore.i18n("cm_guestHome.userVideoTitle") }}
        </div>
        <div class="w-[50px] h-[3px] bg-[#e50113] mx-auto mt-[35px]"></div>
        <div class="flex justify-between mt-[78px]">
          <div
            class="relative cursor-pointer rounded-[6px] overflow-hidden"
            v-for="(video, index) in userVideoData"
            :key="video.id"
            @click="onOpenVideo(video, index)"
          >
            <img
              :src="video.videoBg"
              :img-props="{ referrerpolicy: 'no-referrer' }"
              loading="lazy"
            />
            <img
              loading="lazy"
              src="@/assets/icons/open/videoPlay.svg"
              alt="video"
              class="w-[50px] h-[48px] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <PageFooter></PageFooter>
  <VideoModal ref="videoModalRef"></VideoModal>
  <LoginRegisterModal ref="loginRegisterModalRef"></LoginRegisterModal>
</template>

<script setup lang="ts">
import LoginRegisterModal from "./LoginRegisterModal.vue";
import HotPallets from "./HotSelectors.vue";
import CountrySelect from "@/components/CountrySelect.vue";
import PageFooter from "@/components/PageFooter.vue";
import VideoModal from "@/components/videoModal.vue";
import ImageCarousel from "./ImageCarousel.vue";
import FixedHeaderSearch from "./FixedHeaderSearch.vue";
import { useAuthStore } from "@/stores/authStore";

import factoryDirect from "@/assets/icons/home/<USER>";
import quickQuote from "@/assets/icons/home/<USER>";
import cargoConsolidation from "@/assets/icons/home/<USER>";
import totalControl from "@/assets/icons/home/<USER>";
import hassleFreeShipping from "@/assets/icons/home/<USER>";
import selectionQuotation from "@/assets/icons/home/<USER>";
import confirmationPayment from "@/assets/icons/home/<USER>";
import qualityControlShipping from "@/assets/icons/home/<USER>";
import fashionAccessories from "@/assets/icons/home/<USER>";
import womenClothing from "@/assets/icons/home/<USER>";
import officeSupplies from "@/assets/icons/home/<USER>";
import beverages from "@/assets/icons/home/<USER>";
import luggageLeather from "@/assets/icons/home/<USER>";
import mensClothing from "@/assets/icons/home/<USER>";
import storageCleaning from "@/assets/icons/home/<USER>";
import toys from "@/assets/icons/home/<USER>";
import accessory from "@/assets/icons/home/<USER>";
import clothing from "@/assets/icons/home/<USER>";
import supply from "@/assets/icons/home/<USER>";
import beverage from "@/assets/icons/home/<USER>";
import leather from "@/assets/icons/home/<USER>";
import men from "@/assets/icons/home/<USER>";
import cleaning from "@/assets/icons/home/<USER>";
import toy from "@/assets/icons/home/<USER>";
import selfServiceOrders from "@/assets/icons/home/<USER>";
import spanishPlatform from "@/assets/icons/home/<USER>";
import flexiblePurchasing from "@/assets/icons/home/<USER>";
import remoteSelection from "@/assets/icons/home/<USER>";
import brand from "@/assets/icons/home/<USER>";
import team from "@/assets/icons/home/<USER>";
import resource from "@/assets/icons/home/<USER>";
import videoPoster1 from "@/assets/icons/home/<USER>";
import videoPoster2 from "@/assets/icons/home/<USER>";
import videoPoster3 from "@/assets/icons/home/<USER>";
import videoPoster4 from "@/assets/icons/home/<USER>";

const props = defineProps({});

const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");

const LAYOUT_CONFIG = {
  STEP_CIRCLE_OFFSET: 9, // 步骤圆圈偏移
  MAIN_IMAGE_OFFSET: 24, // 主图偏移
  CIRCLE_RADIUS: 25, // 圆圈半径
  MIN_STEP_GAP: 150, // 最小步骤间距
  DEFAULT_STEP_GAP: 400, // 默认步骤间距
  SCROLL_OFFSET: 80, // 滚动偏移量
};

const pageData = reactive(<any>{
  slidDis: 0,
  showNextArrow: true,
  showPrevArrow: false,
  categoryData: <any>[],
  categoryInRow: 7,
  categoryAllWidth: 0, // 所有分类的宽度
  categoryItemWidth: 175, // 分类子项目的宽度
  categoryContainerWidth: 0, // 分类容器的宽度
  keyword: "",
  showSearchBox: false, // 控制搜索框显示/隐藏
  isHeaderFixed: false,
});

const videoModalRef = ref<any>(null);
const searchInputRef = ref<HTMLInputElement | null>(null);
const currentStep = ref(0);
const progressLineHeight = ref(0);
const backgroundLineHeight = ref(0);
const stepRefs = ref<any[]>([]);
const contentRefs = ref<any[]>([]);
const stepAnimations = ref(new Array(3).fill(false)); // buySteps.length = 3
const stepGaps = ref<number[]>([]);

const newsData = [
  {
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/article/about-us",
  },
  {
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/article/quick-guide",
  },
  {
    title: authStore.i18n("cm_news.helpCenter"),
    path: "/article/help-center",
  },
  {
    title: authStore.i18n("cm_news.commission"),
    path: "/article/commission",
  },
  {
    title: authStore.i18n("cm_news.chilatshopTutorials"),
    path: "/article/tutorials",
  },
];

const benefitBreakdown = [
  {
    icon: factoryDirect,
    title: "Directo desde fábrica",
    desc: "Productos a precios competitivos sin intermediarios.",
  },
  {
    icon: quickQuote,
    title: "Cotización rápida",
    desc: "Generas tu lista de pedido online y te damos precios inmediatos.",
  },
  {
    icon: cargoConsolidation,
    title: "Consolidación de carga",
    desc: "Compras a varios proveedores y enviamos todo en un mismo contenedor.",
  },
  {
    icon: totalControl,
    title: "Control total",
    desc: "Verificamos calidad y gestionamos el pago por ti.",
  },
  {
    icon: hassleFreeShipping,
    title: "Envío sin complicaciones",
    desc: "Asistimos en la logística internacional hasta tu país.",
  },
];

const buySteps = [
  {
    image: selectionQuotation,
    title: "Selección & Cotización",
    desc: [
      "Explore millones de productos, agregue al carrito",
      "Envíe su solicitud (sin pago inmediato)",
    ],
  },
  {
    image: confirmationPayment,
    title: "Confirmación & Pago",
    desc: [
      "Nuestro equipo calcula costos finales (incluye envío e impuestos)",
      "Pago seguro tras confirmación",
    ],
  },
  {
    image: qualityControlShipping,
    title: "Control de Calidad & Envío",
    desc: [
      "Inspección manual + máquina en nuestro almacén",
      "Opciones de transporte aéreo/marítimo, seguimiento en tiempo real",
      "Entrega directa en su dirección",
    ],
  },
];

const categoryData = [
  {
    categoryIcon: accessory,
    categoryImage: fashionAccessories,
    id: "1930926390579204114",
    name: authStore.i18n("cm_home.fashionAccessories"),
  },
  {
    categoryIcon: clothing,
    categoryImage: womenClothing,
    id: "1930926390579204139",
    name: authStore.i18n("cm_home.womenClothing"),
  },
  {
    categoryIcon: supply,
    categoryImage: officeSupplies,
    id: "1930926390579204122",
    name: authStore.i18n("cm_home.officeSupplies"),
  },
  {
    categoryIcon: beverage,
    categoryImage: beverages,
    id: "1930926390579204153",
    name: authStore.i18n("cm_home.beverages"),
  },
  {
    categoryIcon: leather,
    categoryImage: luggageLeather,
    id: "1930926390579204142",
    name: authStore.i18n("cm_home.luggageLeather"),
  },
  {
    categoryIcon: men,
    categoryImage: mensClothing,
    id: "1930926390579204138",
    name: authStore.i18n("cm_home.mensClothing"),
  },
  {
    categoryIcon: cleaning,
    categoryImage: storageCleaning,
    id: "1930926390579204154",
    name: authStore.i18n("cm_home.storageCleaning"),
  },
  {
    categoryIcon: toy,
    categoryImage: toys,
    id: "1930926390579204135",
    name: authStore.i18n("cm_home.toys"),
  },
];

const whyUsData = [
  {
    icon: selfServiceOrders,
    title: "Pedidos autogestionados 24/7, sin límites por diferencia horaria",
  },
  {
    icon: spanishPlatform,
    title: "Plataforma 100% en español, sin barreras",
    desc: [
      "Somos el único sitio mayorista chino completamente en español, con descripciones, especificaciones y soporte al cliente en su idioma.",
    ],
  },
  {
    icon: flexiblePurchasing,
    title: "Flexibilidad para todo tipo de compras",
    desc: [
      'Pequeños pedidos: "Caja mínima", más de 50,000 proveedores directos, mezcla de artículos permitida.',
      "Grandes volúmenes: Sistema de comparación inteligente, revise precios de 30 proveedores en segundos.",
      "Contenedores completos: Mezcla de categorías para ahorrar en logística, con informes de control de calidad.",
    ],
  },
  {
    icon: remoteSelection,
    title: "Selección remota, ahorro de tiempo y costos",
    desc: [
      "No necesita viajar a China, todo el proceso se gestiona en línea.",
      'Si desea visitar, organizamos itinerarios "selección + tour de fábricas + logística" para mayor eficiencia.',
    ],
  },
];

const chooseData = [
  {
    icon: brand,
    title: authStore.i18n("cm_guestHome.brandTitle"),
    desc: authStore.i18n("cm_guestHome.brandDesc"),
  },
  {
    icon: team,
    title: authStore.i18n("cm_guestHome.teamTitle"),
    desc: authStore.i18n("cm_guestHome.teamDesc"),
  },
  {
    icon: resource,
    title: authStore.i18n("cm_guestHome.resourceTitle"),
    desc: authStore.i18n("cm_guestHome.resourceDesc"),
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    videoBg: videoPoster1,
  },
  {
    id: "Tj0nrnhxgXw",
    videoBg: videoPoster2,
  },
  {
    id: "_omi5a-pHkA",
    videoBg: videoPoster3,
  },
  {
    id: "4FVIz0PvEcE",
    videoBg: videoPoster4,
  },
];

const carouselData = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
];

function onOpenVideo(video: any, index: any) {
  if (videoModalRef.value) {
    window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
    videoModalRef.value.onOpenVideo(video);
  }
}

const onLoginClick = async (position: any, type?: any) => {
  // 埋点
  if (position === "top" && type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_register",
      "点击顶部导航注册，打开注册窗口"
    );
  } else if (position === "top" && type === 1) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_login",
      "点击顶部导航登录，打开登录窗口"
    );
  } else if (!position && type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_home_body_register",
      "点击首页正文区注册"
    );
  }
  loginRegister?.openLogin("", type);
};

const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};

function onKeywordClick(event: any) {
  navigateToPage(
    `/goods/list/all`,
    {
      keyword: pageData.keyword?.trim(),
    },
    true,
    event
  );
}

// 获取步骤圆圈的位置
const getStepPosition = (index: any) => {
  if (index === 0) return LAYOUT_CONFIG.STEP_CIRCLE_OFFSET;

  let position = LAYOUT_CONFIG.STEP_CIRCLE_OFFSET;
  for (let i = 0; i < index; i++) {
    position += stepGaps.value[i] || LAYOUT_CONFIG.DEFAULT_STEP_GAP;
  }
  return position;
};

// 计算步骤间距 - 基于右侧内容的实际位置动态计算
const calculateStepGaps = () => {
  nextTick(() => {
    if (contentRefs.value.length === 0) return;

    const gaps = [];
    for (let i = 0; i < buySteps.length - 1; i++) {
      const gap = calculateSingleStepGap(i);
      gaps.push(gap);
    }
    stepGaps.value = gaps;
  });
};

// 计算单个步骤间距
const calculateSingleStepGap = (index: number) => {
  const currentContent = contentRefs.value[index];
  const nextContent = contentRefs.value[index + 1];

  if (!currentContent || !nextContent) {
    return LAYOUT_CONFIG.DEFAULT_STEP_GAP;
  }

  const currentRect = currentContent.getBoundingClientRect();
  const nextRect = nextContent.getBoundingClientRect();

  // 计算实际距离并确保最小间距
  const actualDistance =
    nextRect.top - currentRect.top + LAYOUT_CONFIG.MAIN_IMAGE_OFFSET;
  return Math.max(actualDistance, LAYOUT_CONFIG.MIN_STEP_GAP);
};

// 计算当前激活的步骤
const calculateActiveStep = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  const windowHeight = window.innerHeight;
  let activeStep = 0;

  for (let i = 0; i < contentRefs.value.length; i++) {
    const element = contentRefs.value[i];
    if (!element) continue;

    const rect = element.getBoundingClientRect();
    const elementTop = rect.top + scrollTop;
    const elementMiddle =
      elementTop + rect.height / 2 - LAYOUT_CONFIG.SCROLL_OFFSET;

    if (scrollTop + windowHeight / 2 >= elementMiddle) {
      activeStep = i;
    }
  }
  return activeStep;
};

// 触发步骤动画
const triggerStepAnimation = (activeStep: number, previousStep: number) => {
  if (activeStep !== previousStep && activeStep < stepAnimations.value.length) {
    stepAnimations.value[activeStep] = false;
    nextTick(() => {
      stepAnimations.value[activeStep] = true;
    });
  }
};

// 更新进度线和背景线
const updateProgressLines = (activeStep: number) => {
  // 计算进度线高度
  if (activeStep === 0) {
    progressLineHeight.value = 0;
  } else {
    const targetStepPosition = getStepPosition(activeStep);
    const firstStepPosition = getStepPosition(0);
    progressLineHeight.value = Math.max(
      0,
      targetStepPosition - firstStepPosition
    );
  }

  // 计算背景线高度
  const lastStepPosition = getStepPosition(buySteps.length - 1);
  const firstStepPosition = getStepPosition(0);
  backgroundLineHeight.value = Math.max(
    0,
    lastStepPosition - firstStepPosition
  );
};

// 滚动监听逻辑
const handleScroll = () => {
  // 处理固定头部逻辑
  const scrollTop =
    window.pageYOffset ||
    document.documentElement.scrollTop ||
    document.body.scrollTop;

  // 获取注册登录按钮的位置 - 使用更精确的选择器
  const authButtons = document.querySelector("#auth-button"); // 注册登录按钮容器
  if (authButtons) {
    const buttonRect = authButtons.getBoundingClientRect();
    const buttonTop = buttonRect.top + scrollTop;

    // 当滚动超过注册登录按钮位置时显示固定头部
    pageData.isHeaderFixed = scrollTop > buttonTop;
  }

  // 原有的步骤动画逻辑
  if (!contentRefs.value.length || !stepRefs.value.length) return;

  const activeStep = calculateActiveStep();
  const previousStep = currentStep.value;
  currentStep.value = activeStep;

  triggerStepAnimation(activeStep, previousStep);

  updateProgressLines(activeStep);
};

// 窗口大小变化处理函数
const handleResize = () => {
  calculateStepGaps();
};

// 初始化函数
const initializeLayout = () => {
  try {
    calculateStepGaps();
    handleScroll();
  } catch (error) {
    console.warn("Layout initialization failed:", error);
  }
};

// 切换搜索框显示/隐藏
const toggleSearchBox = () => {
  pageData.showSearchBox = !pageData.showSearchBox;

  if (pageData.showSearchBox) {
    // 展开搜索框后自动聚焦输入框
    setTimeout(() => {
      searchInputRef.value?.focus();
    }, 200);
  }
};

// 处理点击外部关闭搜索框
const handleClickOutside = (event: Event) => {
  const searchContainer = document.querySelector(".search-container");
  if (
    pageData.showSearchBox &&
    searchContainer &&
    !searchContainer.contains(event.target as Node)
  ) {
    pageData.showSearchBox = false;
  }
};

// 处理ESC键关闭搜索框
const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === "Escape" && pageData.showSearchBox) {
    pageData.showSearchBox = false;
  }
};

onMounted(() => {
  initializeLayout();
  window.addEventListener("scroll", handleScroll, { passive: true });
  window.addEventListener("resize", handleResize, { passive: true });
  // 添加搜索框相关事件监听
  document.addEventListener("click", handleClickOutside);
  document.addEventListener("keydown", handleEscapeKey);

  // 初始检查滚动位置（解决SSR水合问题）
  nextTick(() => {
    handleScroll();
  });
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  window.removeEventListener("resize", handleResize);
  // 移除搜索框相关事件监听
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("keydown", handleEscapeKey);
});
</script>
<style lang="scss" scoped>
:deep(.n-scrollbar) {
  min-height: 380px !important;
}

:deep(.n-marquee) {
  background-color: #000;
}

:deep(.country-select) {
  .country-delivery {
    font-size: 16px;
    margin-right: 6px;
  }
  .country-code {
    font-size: 16px;
    line-height: 14px;
  }
}

.page-wrapper {
  max-width: 1980px;
  min-width: 1280px;
  margin: 0 auto;
}

.header-wrapper {
  width: 100%;
  height: 624px;
  position: relative;
  object-fit: cover;
  background-size: cover;
  background-image: url("@/assets/icons/home/<USER>");
  background-repeat: no-repeat;
}

// 固定头部样式
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  animation: slideDown 0.3s ease-out;
}

.fixed-header-content {
  width: 1280px;
  max-width: 1280px;
  margin: 0 auto;
  padding: 12px 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  box-sizing: border-box;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.custom-cursor {
  cursor: url("@/assets/icons/common/cursor.ico"), auto !important;
}

// 图片切换动画
.image-fade-enter-active {
  transition: opacity 0.3s ease-in-out;
}

.image-fade-leave-active {
  transition: none;
}

.image-fade-enter-from {
  opacity: 0;
}

.image-fade-leave-to {
  opacity: 0;
}

.image-fade-enter-to,
.image-fade-leave-from {
  opacity: 1;
}

// 步骤内容样式
.step-content {
  transition: opacity 0.3s ease-in-out;
}

// 步骤圆圈动画
.step-circle-animate {
  animation: circleProgress 0.6s ease-out;
}

@keyframes circleProgress {
  0% {
    stroke-dashoffset: 150.8;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

// 搜索容器样式
.search-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 674px;
  height: 50px;
}

.home-search {
  position: absolute;
  right: 0;
  width: 0;
  overflow: hidden;
  justify-content: flex-start;
  z-index: 5;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 60px;
  border-radius: 50px;
  will-change: width;
  transform: translateZ(0);
  backface-visibility: hidden;
  &.search-expanded {
    width: 674px;
  }

  .home-search-inner {
    position: relative;
    display: flex;
    width: 100%;
    height: 100%;
    background-color: #fff;
    justify-content: center;
    align-items: center;
  }
  .search-bar-input-wrapper {
    position: relative;
    display: block;
    width: 100%;
    margin: 0 24px;
    box-sizing: border-box;
  }
  .search-bar-input {
    font-size: 16px;
    width: 100%;
    color: #222;
    background-color: #fff;
    line-height: 36px;
    margin: 0;
    padding: 0;
    outline: none;
    border: none;
    background-image: none;
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
  .search-bar-inner-button {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132px;
    height: 52px;
    background: #e50113;
    color: #fff;
    border: 0 solid transparent;
    cursor: pointer;
    border-radius: 50px;
    margin-right: 4px;
  }
  .bottom-recommend-wrap {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    box-sizing: border-box;
    padding: 0 22px;
    width: 100%;
    height: 26px;
    overflow: hidden;
    font-weight: 400;
    font-size: 12px;
    line-height: 17px;
    height: 36px;
    margin-top: 32px;
    padding: 0;
    .keyword {
      background-color: rgba(0, 0, 0, 0.2);
      border: 1px solid #fff;
      border-radius: 22px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      font-size: 14px;
      height: 36px;
      line-height: 36px;
      margin: 0 10px;
      padding: 0 24px;
      text-decoration: none;
      &:hover {
        background-color: rgba(0, 0, 0, 0.5);
        text-decoration: none;
      }
    }
  }
}

// 搜索图标样式
.search-icon-trigger {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  // 性能优化：启用硬件加速
  will-change: transform, opacity;
  backface-visibility: hidden;

  &:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.icon-hidden {
    opacity: 0;
    pointer-events: none;
    transform: translateY(-50%) scale(0.8);
    transition-delay: 0s;
  }

  &:not(.icon-hidden) {
    transition-delay: 0.2s; // 延迟0.2s显示，让搜索框先收起一半
  }
}
</style>
