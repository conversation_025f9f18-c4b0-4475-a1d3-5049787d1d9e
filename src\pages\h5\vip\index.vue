<template>
  <div class="page-container">
    <div class="w-full px-[0.4rem] pt-[0.44rem] pb-[1.48rem]">
      <div class="w-[2.44rem] h-[0.56rem] mx-auto">
        <img
          loading="lazy"
          :src="configStore.pageTheme.chilatLogo"
          alt="logo"
          class="w-[2.44rem]"
        />
      </div>
      <div
        class="w-[6.7rem] h-[3.76rem] rounded-[0.08rem] overflow-hidden mt-[1.56rem]"
      >
        <video-you-tube
          :width="6.7 * resetFontSize"
          :height="3.76 * resetFontSize"
          :poster="vipCover"
          youtubeId="C09WtgdUp3M"
          titleCh="你的VIP订制找货助理"
        ></video-you-tube>
      </div>
      <div
        class="w-full text-[0.56rem] leading-[0.72rem] font-medium mt-[1.48rem]"
        v-html="authStore.i18n('cm_vip.personalAssistant')"
      ></div>
      <div
        class="w-full text-[0.32rem] leading-[0.48rem] text-[#7F7F7F] mt-[0.2rem]"
        v-html="authStore.i18n('cm_vip.findSuppliers')"
      ></div>
    </div>
    <div class="w-full px-[0.4rem] pb-[1.08rem]">
      <div class="flex justify-between flex-wrap">
        <div
          v-for="(item, index) in vipIntroData"
          :key="index"
          class="relative w-[7.84rem] bg-white border border-[#333] rounded-[0.4rem] pt-[4.44rem] px-[0.28rem] pb-[0.52rem] text-center mb-[0.52rem]"
        >
          <img
            loading="lazy"
            :src="item.imgUrl"
            alt="img"
            class="absolute top-0 left-0 w-full"
          />
          <div
            class="w-[1.08rem] h-[1.08rem] bg-[#e50113] text-[0.6rem] leading-[1.08rem] text-[#fff] absolute left-[50%] translate-x-[-50%] top-[3.16rem] rounded-full border-1 border-[#fff]"
          >
            {{ index + 1 }}
          </div>
          <div class="text-[0.4rem] leading-[0.48rem]">
            {{ item.title }}
          </div>
          <div
            class="text-[0.28rem] leading-[0.32rem] text-[#7F7F7F] mt-[0.2rem]"
          >
            {{ item.desc }}
          </div>
        </div>
      </div>
    </div>
    <div class="w-full px-[0.4rem] pb-[2rem]">
      <div class="text-[0.68rem] leading-[0.68rem] font-medium text-center">
        {{ authStore.i18n("cm_vip.fourSteps") }}
      </div>
      <div class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.48rem]"></div>
      <div class="mt-[1rem]">
        <div
          v-for="(step, index) in vipProcData"
          :key="index"
          class="w-full flex flex-col items-center text-center mb-[0.8rem] px-[0.2rem]"
        >
          <div
            class="w-[1.92rem] h-[1.92rem] relative flex items-center justify-center"
          >
            <img
              loading="lazy"
              :src="step.imgUrl"
              alt=""
              class="w-full h-full absolute top-0 left-0"
            />
            <div
              class="text-[0.36rem] leading-[0.36rem] text-[#e50113] italic font-semibold relative"
            >
              {{ step.imgText }}
            </div>
          </div>
          <div class="text-[0.36rem] leading-[0.36rem] mt-[0.4rem]">
            {{ step.title }}
          </div>
          <div
            class="text-[0.28rem] leading-[0.36rem] text-[#7F7F7F] mt-[0.2rem]"
          >
            {{ step.desc }}
          </div>
        </div>
      </div>
    </div>
    <div class="w-full px-[0.4rem]">
      <div
        class="text-[0.56rem] leading-[0.68rem] text-center font-medium"
        v-html="authStore.i18n('cm_vip.clientsTrustMobile')"
      ></div>
      <div class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.48rem]"></div>
      <div
        class="text-[0.32rem] leading-[0.4rem] text-center mt-[0.48rem] text-[#7F7F7F]"
      >
        {{ authStore.i18n("cm_vip.clientsExperience") }}
      </div>
      <div class="w-full flex my-[1.2rem] pb-[3.1rem]">
        <n-carousel
          :space-between="10"
          :loop="false"
          draggable
          slides-per-view="auto"
          centered-slides
          show-arrow
          :on-update:current-index="onUpdateCurrentIndex"
        >
          <n-carousel-item
            class="!w-[5.76rem] !h-[3.24rem] rounded-[0.4rem] relative overflow-hidden flex-shrink-0"
            v-for="(video, index) in userVideoData"
            :key="video.id"
            @click="onOpenVideo(video)"
          >
            <div class="w-[5.76rem] h-[3.24rem]">
              <n-image
                lazy
                preview-disabled
                :src="video.poster"
                class="img"
                object-fit="cover"
              />
              <img
                loading="lazy"
                :src="videoPlay"
                alt="video"
                class="w-[1rem] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
              />
            </div>
          </n-carousel-item>
          <template #arrow="{ prev, next }">
            <div class="custom-arrow">
              <icon-card
                @click="prev"
                name="fe:arrow-left"
                size="26"
                :color="
                  pageData.currentCarouselIndex === 0 ? '#D9D9D9' : '#e50113'
                "
                class="mr-[0.6rem]"
              >
              </icon-card>
              <icon-card
                @click="next"
                name="fe:arrow-right"
                size="26"
                :color="
                  pageData.currentCarouselIndex === userVideoData.length - 1
                    ? '#D9D9D9'
                    : '#e50113'
                "
              >
              </icon-card>
            </div>
          </template>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li
                v-for="index of total"
                :key="index"
                :class="{ ['is-active']: index - 1 <= currentIndex }"
              ></li>
            </ul>
          </template>
        </n-carousel>
      </div>
    </div>
    <div class="whatsapp-wrapper">
      <div
        class="w-[6rem] text-[0.44rem] leading-[0.76rem] font-semibold text-[#333] pt-[0.32rem] ml-[0.4rem] relative"
      >
        <span>Haga clic en</span>
        <span class="relative">
          <span class="text-[#25D366]"> WhatsApp</span>
          <img
            loading="lazy"
            src="@/assets/icons/open/greenLine.svg"
            alt="line"
            class="w-[3.2rem] absolute top-[0.51rem] left-[0.22rem]"
          />
        </span>
        <br />
        <span
          >para contactarnos y<br />
          obtener más información</span
        >
      </div>
      <img
        loading="lazy"
        alt="click"
        class="icon"
        @click="onHandleWhatsAppClick('bottom')"
        src="@/assets/icons/open/mobileWhatsappClick.png"
      />
      <n-button
        class="button"
        color="#E50113"
        @click="onHandleWhatsAppClick('bottom')"
        data-spm-box="potential_user_note_submit"
      >
        <div
          class="flex items-center justify-center text-[0.36rem] leading-[0.36rem] font-semibold"
          v-html="authStore.i18n('cm_nota.submitWhatsappMobile')"
        ></div>
      </n-button>
    </div>
    <div class="side-affix">
      <icon-card
        name="logos:whatsapp-icon"
        size="0.44rem"
        title="W/app"
        :border="true"
        :multiple="true"
        @click="onHandleWhatsAppClick()"
      ></icon-card>
    </div>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import vipStep1 from "@/assets/icons/open/vipStep1.svg";
import vipStep2 from "@/assets/icons/open/vipStep2.svg";
import vipStep3 from "@/assets/icons/open/vipStep3.svg";
import vipStep4 from "@/assets/icons/open/vipStep4.svg";
import videoPlay from "@/assets/icons/open/videoPlay.svg";
import vipService from "@/assets/icons/open/vipService.png";
import cargoConsolidation from "@/assets/icons/open/cargoConsolidation.png";
import worryFree from "@/assets/icons/open/worryFree.png";
import vipCover from "@/assets/icons/open/vipCover.jpg";
import userVideoPoster1 from "@/assets/icons/open/userVideoPoster1.png";
import userVideoPoster2 from "@/assets/icons/open/userVideoPoster2.png";
import userVideoPoster3 from "@/assets/icons/open/userVideoPoster3.png";
import userVideoPoster4 from "@/assets/icons/open/userVideoPoster4.png";
import userVideoPoster5 from "@/assets/icons/open/userVideoPoster5.png";
import userVideoPoster6 from "@/assets/icons/open/userVideoPoster6.png";

const authStore = useAuthStore();
const configStore = useConfigStore();
const videoModalRef = ref<any>(null);

const vipIntroData = [
  {
    imgUrl: vipService,
    title: authStore.i18n("cm_vip.vipService"),
    desc: authStore.i18n("cm_vip.importGuidance"),
  },
  {
    imgUrl: cargoConsolidation,
    title: authStore.i18n("cm_vip.cargoConsolidation"),
    desc: authStore.i18n("cm_vip.minimumPurchase"),
  },
  {
    imgUrl: worryFree,
    title: authStore.i18n("cm_vip.worryFree"),
    desc: authStore.i18n("cm_vip.secureControl"),
  },
];

const vipProcData = [
  {
    imgUrl: vipStep1,
    imgText: authStore.i18n("cm_vip.step1"),
    title: authStore.i18n("cm_vip.productSourcing"),
    desc: authStore.i18n("cm_vip.productExpert"),
  },
  {
    imgUrl: vipStep2,
    imgText: authStore.i18n("cm_vip.step2"),
    title: authStore.i18n("cm_vip.sampleConfirmation"),
    desc: authStore.i18n("cm_vip.provideQuoteAndSamples"),
  },
  {
    imgUrl: vipStep3,
    imgText: authStore.i18n("cm_vip.step3"),
    title: authStore.i18n("cm_vip.productionTracking"),
    desc: authStore.i18n("cm_vip.orderTracking"),
  },
  {
    imgUrl: vipStep4,
    imgText: authStore.i18n("cm_vip.step4"),
    title: authStore.i18n("cm_vip.goodsReceptionShipping"),
    desc: authStore.i18n("cm_vip.receiveInspectShip"),
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    poster: userVideoPoster1,
    titleCh: "1.我们的客户对我们的看法7",
  },
  {
    id: "Tj0nrnhxgXw",
    poster: userVideoPoster2,
    titleCh: "2.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: userVideoPoster3,
    titleCh: "3.我们的客户对我们的看法6",
  },
  {
    id: "4FVIz0PvEcE",
    poster: userVideoPoster4,
    titleCh: "4.我们的客户对我们的看法5",
  },
  {
    id: "f6Zzt70urW8",
    poster: userVideoPoster5,
    titleCh: "5.我们的客户对我们的看法1",
  },
  {
    id: "wOX-XcU4AYY",
    poster: userVideoPoster6,
    titleCh: "6.我们的客户对我们的看法9",
  },
];

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

const pageData = reactive(<any>{
  currentCarouselIndex: 0,
});

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}

function onUpdateCurrentIndex(index: any) {
  pageData.currentCarouselIndex = index;
}

async function onHandleWhatsAppClick(val?: any) {
  let clickSource = "vip页面悬浮按钮点击WhatsApp";
  if (val) {
    clickSource = "vip页面底部按钮点击WhatsApp";
  }
  window?.MyStat?.addPageEvent(
    "potential_user_click_whatsapp",
    clickSource,
    true
  );
  let paramsObj = {
    clickSource,
  };
  const res: any = await useClickWhatsapp(paramsObj);
  if (res?.result?.code === 200) {
    onWhatsAppClick();
  } else {
    showToast(res?.result?.message);
  }
}
</script>

<style scoped lang="scss">
.page-container {
  height: auto;
  min-height: 100vh;
  color: #333;
}

.scrollable-container {
  overflow-y: auto;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.n-carousel {
  overflow: visible;
}
.custom-arrow {
  display: flex;
  position: absolute;
  bottom: -1.6rem;
  right: 0.2rem;
  z-index: 10;
}

.custom-dots {
  display: flex;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: -1.4rem;
  left: 0.4rem;
  border-radius: 0.08rem;
  background-color: #d9d9d9;
}

.custom-dots li {
  display: inline-block;
  width: 0.66rem;
  max-width: none;
  height: 0.04rem;
  background-color: #d9d9d9;
  border-radius: 0;
  cursor: pointer;
  margin: 0;
}

.custom-dots li.is-active {
  height: 0.06rem;
  background: #e50113;
}
.vjs-poster img {
  object-fit: none;
  border-radius: 0.24rem !important;
}

.side-affix {
  position: fixed;
  right: 0.12rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}
.whatsapp-wrapper {
  position: relative;
  width: 100%;
  height: 8.68rem;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/open/mobileWhatsappBg.png");
  .icon {
    position: absolute;
    top: 2.8rem;
    width: 2.8rem;
    right: 0.32rem;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 4.64rem;
    right: 0.4rem;
    height: fit-content;
    display: inline-flex;
    padding: 0.32rem 0.68rem;
    align-items: center;
    gap: 0.08rem;
    border-radius: 12.96rem;
    border: 0.06rem solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0 0.02rem 0.04rem 0 rgba(0, 0, 0, 0.5);
  }
}
</style>
