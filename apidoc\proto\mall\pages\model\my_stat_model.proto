syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";

import "common.proto";
import "mall/passport/model/auth_model.proto";


message PageClickIncomingResp {
    common.Result result = 1;
    PageClickIncomingModel data = 2;
}

message PageClickIncomingModel {
    string visitCode = 100; //页面访问代码（PageClickKey解密字符串）
    string headFirstScript = 110; //若存在，插入到 head 标签开始后的位置
    string headLastScript = 120; //若存在，插入到 head 标签结束前的位置
    repeated common.NameValueModel responseHeaders = 130; //若存在，在 response 响应头部输出: res.setHeader(name, vale)
    string abtestMode = 140; //AB测试方案之选择的页面模式（仅当abtestPage非空时返回；前端默认取A；若只有新旧两个版本对照，则通常A表示旧版，B表示新版）
    string defaultCountryCode = 150; //默认国家代码（两位ISO国家代码；若不能解析IP地理信息，则为null）
    passport.LoginUserModel loginUser = 160; //登录用户信息（空值表示未登录）
    int32 siteId = 170; //当前站点ID（即配送国家ID；若站点ID为空，则弹窗由用户选择）
    repeated SiteListModel siteList = 180; //站点选择列表
}

//站点选择列表
message SiteListModel {
    int32 id = 10; //站点ID（与国家ID相同）
    string code = 20; //站点代码（两位配送国家代码；大写英文字母）
    string name = 30; //站点名称（与国家名称相同）
    string logo = 40; //站点LOGO图片（国家标识图片URL，46x32像素）
}

