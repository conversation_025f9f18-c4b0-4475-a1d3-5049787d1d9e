syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.model";

import "chilat/user/model/role_model.proto";
import "chilat/user/user_common.proto";
import "common.proto";

message UserPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated UserModel data = 3;
}

message UserListResp {
  common.Result result = 1;
  repeated UserModel data = 2;
}

message UserResp {
  common.Result result = 1;
  UserModel data = 2;
}

message UserPermissionResp {
  common.Result result = 1;
  repeated UserPermissionModel data = 2;
}

// 用户信息
message UserModel {
  string id = 1; // 用户ID
  string username = 2; // 用户名
  string initPassword = 3; // 初始密码
  string phone = 4; // 手机号
  string realName = 5; // 真实姓名
  string email = 6; // 邮箱
  repeated RoleModel roles = 7; // 角色
  repeated JobDuty duties = 8; // 岗位职责
  int32 inquiryCount = 9; // 询盘客户数
  int32 memberCount = 10; // 关联客户数
  UserInquiryDataModel inquiryData = 11; // 询盘数据
  bool enabled = 12; // 是否启用
  string coperator = 13; // 创建人
  int64 cdate = 14; // 创建时间
  bool isSuperUser = 15;
  bool isManageUser = 16;
  string departmentName = 17; // 所属部门
  string departmentId = 18; //  所属部门ID
}

// 业务员数据
message UserInquiryDataModel {
  int32 processing = 1; // 处理中
  int32 todayAssigned = 2; // 今日已分配
  int32 todayRemained = 3; // 今日剩余
  int32 yesterdayDone = 4; // 今日已完成
}

// 用户权限
message UserPermissionModel {
  string id = 1; // 权限ID
  bool editable = 2; // 是否可编辑
}

message LogResultResp {
  common.Result result = 1;
  repeated UserLogModel data = 2;
}

message UserLogModel {
  int64 id = 1;
  string action = 2;
  string remark = 3;
  repeated string images = 4;
  string createBy = 5;
  int64 createTime = 6;
  string batchId = 7;
}

message DepartmentListResp {
  common.Result result = 1;
  repeated DepartmentModel data = 2;
}

message DepartmentModel {
  string departmentId = 1;
  string departmentName = 2;
}

