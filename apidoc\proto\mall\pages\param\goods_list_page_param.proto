syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.param";

import "common.proto";
import "common/business.proto";
import "mall/commodity/commodity_common.proto";

message GoodsListQueryParam {
    string goodsId = 5; //商品ID
    string categoryId = 10; //类目ID（必填）
    string childCategoryId = 15; //选择商品分类过滤项（可将末级分类转为：上级分类ID查询，末级分类过滤）
    string brandIds = 20; //品牌ID，多个用下划线（_）分隔
    string keyword = 30; //关键字，支持以图搜图
    double minPrice = 45; //最低价限制，支持以图搜图
    double maxPrice = 60; //最高价限制，支持以图搜图
    int32 leMinBuyQuantity = 61; //小于等于“最小购买数量”（起订量）
    int32 geMinBuyQuantity = 62; //大于等于“最小购买数量”（起订量）
    string marketingRuleId = 63; //营销规则ID
    string marketingRuleCode = 64; //营销规则代码
    int32 sortField = 70; //排序字段（11:人气升序；12:人气倒序；21:销量升序；22:销量倒序；31:价格升序；32:价格倒序；42:加购数倒序），支持以图搜图
    int32 pageNo = 80; //页号（从1开始，默认1）
    int32 pageSize = 90; //每页显示条数（后端自动匹配到最接近的页面条数）
    repeated string goodsIds = 100; //商品ID
    string imageId = 110; // 以图搜图之图片ID
    repeated string tagIds = 120; //标签id
    int32 siteId = 130; //站点ID
    string padc = 88; //促销活动动态代码
}
