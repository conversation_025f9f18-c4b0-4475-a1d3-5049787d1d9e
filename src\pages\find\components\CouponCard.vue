<template>
  <div class="validation-coupon pt-[24px]">
    <n-grid
      x-gap="20"
      :cols="3"
      :class="{
        '!flex !justify-center': props.couponList.length < 3,
      }"
    >
      <n-gi v-for="coupon in props.couponList" :key="coupon?.id">
        <div
          class="flex items-center text-[#e50113] mb-[8px]"
          :class="{
            'comm-coupon': coupon?.couponType === 'COUPON_TYPE_COMMISSION',
          }"
        >
          <div class="coupon-card mr-[6px] text-[#fff]">
            <div
              class="w-full h-[20px] mb-[6px] flex items-center justify-center"
            >
              <template v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
                <div>
                  <span class="text-[20px] leading-[20px] font-medium">
                    {{ discountToPercentage(coupon?.discount) }}
                  </span>
                  <span class="coupon-discount">
                    {{ authStore.i18n("cm_coupon.discount") }}
                  </span>
                </div>
              </template>
              <template v-else>
                <span class="coupon-unit">
                  <span>{{ currencyUnit }}</span>
                </span>
                <span class="text-[20px] leading-[20px] font-medium"
                  >$
                  {{ setNewUnit(coupon?.preferentialAmount, "noUnit") }}</span
                >
              </template>
            </div>
            <div class="coupon-type">
              <span v-if="coupon?.couponType === 'COUPON_TYPE_PRODUCT'">
                {{ authStore.i18n("cm_coupon.productCoupon") }}
              </span>
              <span v-if="coupon?.couponType === 'COUPON_TYPE_COMMISSION'">
                {{ authStore.i18n("cm_coupon.commissionCoupon") }}
              </span>
            </div>
          </div>
          <div class="flex w-[30px] text-[20px] tracking-[-1px">
            <span class="mr-[2px]">x</span>
            {{ coupon?.count }}
          </div>
        </div></n-gi
      >
    </n-grid>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const props = defineProps({
  couponList: {
    type: Array,
    default: () => [],
  },
});
</script>
<style scoped lang="scss">
.coupon-card {
  width: 120px;
  height: 58px;
  background: url("@/assets/icons/marketing/productCoupon.png");
  background-size: 100%100%;
  padding: 14px 4px 4px 36px;
}
.comm-coupon {
  color: #dd4f12;
  .coupon-card {
    background: url("@/assets/icons/marketing/commissionCoupon.png");
    background-size: 100%100%;
  }
}
.coupon-type {
  width: 100%;
  text-transform: uppercase;
  letter-spacing: -1px;
  text-align: center;
  text-wrap: nowrap;

  span {
    width: 100%;
    display: inline-block;
    font-size: 12px;
    line-height: 10px;
    transform: scale(0.74);
    transform-origin: left top;
  }
}
.coupon-unit {
  display: inline-block;
  transform: rotate(-90deg);
  font-weight: 500;
  span {
    display: inline-block;
    font-size: 12px;
  }
}
.coupon-discount {
  display: inline-block;
  font-size: 12px;
  margin-left: 2px;
  font-weight: 500;
}
</style>
