syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing";

import "chilat/marketing/model/promotion_activity_model.proto";
import "chilat/marketing/param/promotion_activity_param.proto";
import "common.proto";

// 促销活动管理接口
service PromotionActivity {
  // 查询列表
  rpc pageList (PromotionPageListParam) returns (PromotionPageListResp);
  // 保存活动信息
  rpc savePromotionActivity (SavePromotionParam) returns (common.ApiResult);
  // 复制保存活动信息
  rpc copySavePromotionActivity (CopySavePromotionParam) returns (common.ApiResult);
  // 查询活动详情
  rpc getDetail (common.IdParam) returns (GetPromotionDetailResp);
  // 保存活动商品信息
  rpc saveGoodsSelector (SavePromotionGoodsSelectorParam) returns (common.ApiResult);
  // 保存专属客户信息
  rpc saveExclusiveCustomer (SavePromotionExclusiveCustomerParam) returns (common.ApiResult);
  // 查询日志
  rpc listLog (PromotionOperationLogQueryParam) returns (PromotionOperationLogQueryResp);
  // 获取商品上架数
  rpc multiGetOnlineCount (common.IdsParam) returns (MultiGetOnlineCountResp);
  // 生成商城短链链
  rpc getMallShortLink (common.IdParam) returns (PromotionMallShortLinkResp);
}
