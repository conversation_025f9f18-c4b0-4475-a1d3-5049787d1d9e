syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "common.proto";
import "chilat/support/model/article_category_model.proto";
import "mall/pages/param/word_press_page_param.proto";
import "mall/pages/model/word_press_page_model.proto";

service WordPressPage {
  // 查询栏目
  rpc listWordPressCategory(common.EmptyParam) returns (ListWordPressCategoryResp);
  // 查询文章详情
  rpc wordPressDetail(WordPressDetailParam) returns (WordPressDetailResp);
  //  根据筛选条件查询文章列表
  rpc searchWordPressList(SearchWordPressListParam) returns (SearchWordPressListResp);
}