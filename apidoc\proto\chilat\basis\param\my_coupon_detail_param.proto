syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param.coupon";

import "chilat/coupon/coupon_common.proto";
import "chilat/coupon/coupon_detail_common.proto";
import "common.proto";

// 我的优惠券列表 未使用/已使用/已失效
message MyCouponDetailParam {
    common.PageParam page = 1;//分页参数
    coupon.TicketStatus ticketStatus = 2;//优惠券状态
    string userId = 3;//用户id (选填)
    coupon.CouponTypeStatus couponType = 4; //产品券：couponTypeProduct ,佣金券：couponTypeCommission
}




