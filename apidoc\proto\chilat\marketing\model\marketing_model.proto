syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing.model";

import "common.proto";
import "chilat/marketing/marketing_common.proto";

message MarketingRulePageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated MarketingRuleModel data = 3;
}

message MarketingRuleModel {
  string id = 1;
  string ruleName = 2; // 规则名称
  repeated string deliveryTimeIds = 3; //交期
  repeated string categoryIds = 4; //分类
  repeated string brandIds = 5; //品牌
  int32 minimumSales = 6; //最低销量
  int32 quantity = 7; //展示数量
  GoodsOnlineDays goodsOnlineDays = 8; //商品上架时间
  string logo = 9; //logo
  repeated string pageNames = 10; //已关联页面
  StockLocation stockLocation = 11; // 库存位置
  int32 goodsCount = 12; //商品数量
  repeated string goodsTagIds = 13; //标签id
}

message MarketingRuleListResp {
  common.Result result = 1;
  repeated MarketingRuleModel data = 3;
}

message MarketingCategoryDetailResp {
  common.Result result = 1;
  MarketingCategoryModel data = 2;
}

// 营销分类信息
message MarketingCategoryModel {
  string id = 1; // 营销分类id
  string parentId = 2; // 营销分类父id
  int32 cateLevel = 3; // 营销分类级别,1:一级类目,2:二级类目,3:三级类目
  int32 idx = 4; // 排序
  string cateName = 5; // 营销分类名称
  string cateAlias = 6; // 营销分类-别名
  string cateLogo = 7; // 营销分类logo
  bool enabled = 8; //是否启用
  int32 goodsCount = 9; //商品数量（通过营销分类搜索得到）
  repeated MarketingCategoryModel children = 10; // 下级营销分类
  CategoryMappingType mappingType = 60; //营销分类的商品映射类型（“营销规则ID”与“商品列表ID取其一”）
  repeated common.IdNameModel goodsCategories = 70; //商品后台分类信息（当 mappingType == CATEGORY_MAPPING_TYPE_GOODS_CATEGORY 时返回）
  repeated MarketingRuleModel rules = 80; //营销规则信息（当 mappingType == CATEGORY_MAPPING_TYPE_MARKETING_RULE 时返回）
}

message MarketingCategoryTreeResp {
  common.Result result = 1;
  repeated MarketingCategoryModel data = 2;
}

message MarketingRuleResp {
  common.Result result = 1;
  MarketingRuleModel data = 2;
}