syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";
import "chilat/marketing/marketing_common.proto";




message UpdateStockForOrderParam {
  string tradeCode = 10; //订单号
  repeated UpdateStockForOrderSkuQuantity skuList = 20; //SKU列表
}

message UpdateStockForOrderSkuQuantity {
  string skuId = 10; //SKU ID
  int32 quantity = 20; //商品数量
}

message UpdateStockForCancelParam {
  string tradeCode = 10; //订单号
}