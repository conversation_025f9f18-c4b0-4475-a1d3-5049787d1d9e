<template>
  <div class="w-full bg-[#F2F2F2] min-h-[100vh] py-[1rem] h-full bg-white">
    <div
      class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] bg-white items-center justify-center fixed top-0 z-50"
    >
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        data-spm-box="navigation-back-icon"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_coupon.myCoupon") }}
      </div>
    </div>
    <n-tabs
      v-if="!pageData.isNeedMailVerify"
      animated
      v-model:value="pageData.ticketStatus"
      :on-update:value="onUpdateTab"
      class="w-full px-[0.16rem] py-[0.2rem] h-full"
    >
      <n-tab-pane
        v-for="tab in couponTabData"
        :key="tab.value"
        :tab="tab.label"
        :name="tab.value"
      >
        <template #tab>
          <span
            class="text-[0.3rem] leading-[0.3rem] text-[#333]"
            :class="{ 'font-medium': pageData.ticketStatus === tab.value }"
          >
            {{ tab.label }}
          </span>
        </template>
        <div class="flex flex-col mt-[0.36rem]">
          <!-- 未使用的优惠券展示tab -->
          <n-tabs
            class="custom-tabs"
            justify-content="space-around"
            v-model:value="pageData.couponType"
            :on-update:value="onUpdateTypeTab"
            v-if="pageData.ticketStatus === 'TICKET_NOT_USE'"
          >
            <n-tab-pane
              v-for="tab in couponTypeData"
              :key="tab.value"
              :tab="tab.label"
              :name="tab.value"
              ><template #tab>
                <span
                  class="coupon-type-tab"
                  :class="{
                    '!font-medium !text-[#333] !border-[0.04rem] !border-[#333]':
                      pageData.couponType === tab.value,
                  }"
                >
                  {{ tab.label }}
                </span>
              </template>
            </n-tab-pane>
          </n-tabs>
          <div
            v-if="pageData.couponList.length && pageData.isMailVerified"
            class="flex-1 flex flex-col"
          >
            <coupon-card
              v-for="coupon in pageData.couponList"
              :key="coupon.id"
              :coupon="coupon"
              :pageSource="'modal'"
              class="mb-[0.2rem]"
            ></coupon-card>
            <!-- 已使用的优惠券 -->
            <template v-if="pageData.ticketStatus === 'TICKET_USE'">
              <div class="coupon-desc">
                <icon-card
                  name="mingcute:warning-fill"
                  size="16"
                  color="#999"
                ></icon-card>
                {{ authStore.i18n("cm_coupon.usedCouponDesc") }}
              </div>
            </template>
            <!-- 已过期的优惠券 -->
            <template v-if="pageData.ticketStatus === 'TICKET_LOSE_EFFICACY'">
              <div class="coupon-desc">
                <icon-card
                  name="mingcute:warning-fill"
                  size="16"
                  color="#999"
                ></icon-card>
                {{ authStore.i18n("cm_coupon.invalidCouponDesc") }}
              </div>
            </template>
          </div>
          <!-- 没有数据提示 -->
          <div class="mt-[0.6rem] px-[0.4rem] text-center" v-else>
            <img
              loading="lazy"
              alt="noCoupon"
              class="w-[2rem] h-[2rem] mx-auto"
              src="@/assets/icons/marketing/noCoupon.svg"
              referrerpolicy="no-referrer"
            />
            <!-- 未使用的优惠券 -->
            <div
              v-if="pageData.ticketStatus === 'TICKET_NOT_USE'"
              class="no-coupon-data"
            >
              {{ authStore.i18n("cm_coupon.noCouponData") }}
            </div>
            <!-- 已使用的优惠券 -->
            <div v-if="pageData.ticketStatus === 'TICKET_USE'">
              <div class="no-coupon-data">
                {{ authStore.i18n("cm_coupon.noUsedCouponData") }}
              </div>
              <div class="no-coupon-desc">
                <icon-card
                  name="mingcute:warning-fill"
                  size="18"
                  color="#999"
                ></icon-card>
                {{ authStore.i18n("cm_coupon.usedCouponDesc") }}
              </div>
            </div>
            <!-- 已过期的优惠券 -->
            <div v-if="pageData.ticketStatus === 'TICKET_LOSE_EFFICACY'">
              <div class="no-coupon-data">
                {{ authStore.i18n("cm_coupon.noInvalidCouponData") }}
              </div>
              <div class="no-coupon-desc">
                <icon-card
                  name="mingcute:warning-fill"
                  size="18"
                  color="#999"
                ></icon-card>
                {{ authStore.i18n("cm_coupon.invalidCouponDesc") }}
              </div>
            </div>
          </div>
        </div>
      </n-tab-pane>
    </n-tabs>
    <email-validation
      v-else
      :couponList="pageData.pendingCouponList"
    ></email-validation>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import EmailValidation from "@/pages/h5/user/components/EmailValidation.vue";
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive(<any>{
  ticketStatus: route?.query?.ticketStatus || "TICKET_NOT_USE", // 优惠券状态
  couponType: route?.query?.couponType || "COUPON_TYPE_ALL", // 优惠券类型
  couponList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 10,
    total: 0,
  },
  isLoading: false,
  noData: false,
  isNeedMailVerify: null, //是否需要展示邮箱校验 邮箱校验状态为false且该用户激活后会得到优惠券则展示邮箱校验
  pendingCouponList: <any>[], // 用户激活后的会获取的优惠券列表
});
const couponTabData = reactive<any>([
  {
    // 未使用的优惠券
    value: "TICKET_NOT_USE",
    label: authStore.i18n("cm_coupon.unusedCoupon"),
  },
  {
    // 已使用的优惠券
    value: "TICKET_USE",
    label: authStore.i18n("cm_coupon.usedCoupon"),
  },
  {
    // 过期优惠券
    value: "TICKET_LOSE_EFFICACY",
    label: authStore.i18n("cm_coupon.expiredCoupon"),
  },
]);

const couponTypeData = reactive<any>([
  {
    // 产品+佣金
    value: "COUPON_TYPE_ALL",
    label: authStore.i18n("cm_coupon.allCoupon"),
  },
  {
    // 产品券
    value: "COUPON_TYPE_PRODUCT",
    label: authStore.i18n("cm_coupon.productCoupon"),
  },
  {
    // 佣金券
    value: "COUPON_TYPE_COMMISSION",
    label: authStore.i18n("cm_coupon.commissionCoupon"),
  },
]);

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onMounted(() => {
  window.addEventListener("scroll", onScrollBottom);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollBottom);
});

onQueryVerifyMailResult();

// 查询邮箱是否已验证, 以及验证后可以得到的优惠券
async function onQueryVerifyMailResult() {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo?.value?.username,
    isNeedCoupon: true,
    verifyMailScene: "MY_COUPON_LIST",
  });
  if (res?.result?.code === 200) {
    pageData.isMailVerified = res?.data.isMailVerified;
    if (pageData.isMailVerified) {
      return onGetCouponList();
    }
    pageData.pendingCouponList = res?.data?.couponList || [];
    if (!pageData.isMailVerified && pageData.pendingCouponList?.length) {
      pageData.isNeedMailVerify = true;
    } else {
      pageData.isNeedMailVerify = false;
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

async function onGetCouponList(scroll?: any) {
  const params = <any>{
    page: pageData.pageInfo,
    ticketStatus: pageData.ticketStatus,
  };
  if (
    pageData.ticketStatus === "TICKET_NOT_USE" &&
    pageData.couponType !== "COUPON_TYPE_ALL"
  ) {
    params.couponType = pageData.couponType;
  }
  const res: any = await useGetMyCouponDetailList(params);
  pageData.isLoading = false;
  if (res?.result?.code === 200) {
    if (scroll && !res?.data?.length) {
      pageData.noData = true;
      pageData.isLoading = false;
      return;
    }
    if (scroll) {
      pageData.couponList = pageData.couponList.concat(res?.data);
    } else {
      pageData.couponList = res?.data;
      pageData.pageInfo = res?.page;
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    --pageData.pageInfo.current;
    showToast(res.result?.message);
  }
}

function onUpdateTab(val: any) {
  pageData.pageInfo.current = 1;
  const url = new URL(window.location.href);
  url.searchParams.set("ticketStatus", val);
  window.history.replaceState(null, "", url.toString());
  pageData.ticketStatus = val;
  onGetCouponList();
  window?.MyStat?.addPageEvent(
    "coupon_list_select_status",
    `选择优惠券状态：${val}`
  ); // 埋点
}

function onUpdateTypeTab(val: any) {
  pageData.pageInfo.current = 1;
  const url = new URL(window.location.href);
  url.searchParams.set("couponType", val);
  window.history.replaceState(null, "", url.toString());
  pageData.couponType = val;
  onGetCouponList();
  window?.MyStat?.addPageEvent(
    "coupon_list_select_type",
    `选择优惠券类型：${val}`
  ); // 埋点
}

// 加载下一页
async function onScrollBottom() {
  if (pageData.isLoading || pageData.noData) return;
  if (window.innerHeight + window.scrollY < document.body.scrollHeight) return; // 判断是否滚动到底部
  pageData.isLoading = true;
  pageData.pageInfo.current++;
  onGetCouponList("scroll");
}

// 返回
function onBackClick() {
  router.go(-1);
}
</script>
<style scoped lang="scss">
:deep(.n-tabs .n-tab-pane) {
  padding: 0;
}

.coupon-type-tab {
  display: flex;
  padding: 0.16rem 0.2rem;
  justify-content: center;
  align-items: center;
  border-radius: 2rem;
  border-width: 0.02rem;
  border-style: solid;
  border-color: #4d4d4d;
  color: #4d4d4d;
  font-size: 0.26rem;
  font-style: normal;
  font-weight: 400;
  line-height: 0.26rem;
}
.custom-tabs {
  margin-bottom: 0.36rem;
  :deep(.n-tabs-bar) {
    display: none;
  }
  :deep(.n-tabs-tab) {
    padding: 0;
  }
}
.no-coupon-data {
  font-size: 0.36rem;
  font-weight: 400;
  line-height: 0.46rem;
  color: #333;
  margin: 0.08rem 0 0.28rem;
}
.no-coupon-desc {
  font-size: 0.32rem;
  line-height: 0.4rem;
  color: #999;
}
.coupon-desc {
  margin-top: 0.4rem;
  font-size: 0.28rem;
  line-height: 0.28rem;
  color: #999;
}
:deep(.n-tabs .n-tabs-bar) {
  background-color: #333;
}
</style>
