// uno.config.ts
import { defineConfig } from "unocss";

export default defineConfig({
  rules: [
    ["cwidth", { width: "1280px" }],
    ["min-cwidth", { "min-width": "1280px" }],
    ["theme", { color: "#E50113" }],
    ["primary", { color: "#E50113" }],
    ["bgColor", { color: "rgb(125, 159, 172)" }],
    [
      "absolute-center",
      {
        position: "absolute",
        left: "50%",
        top: "50%",
        transform: "translate(-50%, -50%)",
      },
    ],
    ["font-lg", { "font-size": "16px" }],
    ["font-2lg", { "font-size": "20px" }],
    ["font-xl", { "font-size": "16px" }],
    ["font-2xl", { "font-size": "24px" }],
    ["font-3xl", { "font-size": "32px" }],
    ["font-4xl", { "font-size": "40px" }],
  ],
  shortcuts: [
    {
      "flex-center": "flex justify-center items-center",
    },
  ],
});
