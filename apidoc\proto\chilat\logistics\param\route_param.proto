syntax = "proto3";
package chilat.logistics;

option java_package = "com.chilat.rpc.logistics.param";

import "common.proto";

message RouteSaveParam {
  string id = 1;
  string routeName = 2; //线路名称
  string routeCode = 3; //线路代码
  string routeAlias = 4; //线路别名
  string remark = 5; //备注
  repeated string transportMethodCodes = 6; //运输方式
  repeated RouteTransportDayParam transportDays = 7; //运输时效
  bool supportGoodsFreight = 20; //是否支持商品运费配置（true为支持，默认不支持）
  bool enabled = 8; //是否启用
}

message RouteTransportDayParam {
  string countryId = 10; //国家
  int32 minDays = 20; //最快时效
  int32 maxDays = 30; //最慢时效
  double m3FreightUsd = 50; //1立方米预估运费，单位：美元
  double kgFreightUsd = 60; //1公斤（KG）预估运费，单位：美元
}

message RoutePageListParam {
  common.PageParam page = 1;
  RouteListQueryParam param = 2;
}

message RouteListQueryParam {
  string routeName = 1; //线路名称
  string routeCode = 2; //线路代码
  string countryId = 3; //国家
  string transportMethodCode = 4; //运输方式
  bool enabled = 5; //是否启用
}

message RouteFindByCountryParam {
  string countryId = 1; //国家
}

