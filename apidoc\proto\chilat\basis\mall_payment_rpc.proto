syntax = "proto3";
package chilat.basis;


option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/mall_payment_model.proto";
import "chilat/basis/param/mall_payment_param.proto";
import "common.proto";

service MallPaymentRpc {

  //未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
  rpc openCashDesk (OpenCashDeskParam) returns (OpenCashDeskResp);

  //查询收银台信息，包括待支付金额、支付方式列表、是否已支付
  rpc getCashDeskInfo (GetCashDeskInfoParam) returns (GetCashDeskInfoResp);

  //提交支付到第三方支付平台
  rpc submitPayment (SubmitPaymentParam) returns (SubmitPaymentResp);

  //查询支付结果
  rpc queryPayResult (QueryPayResultParam) returns (QueryPayResultResp);

  //查询取消订单理由配置
  rpc queryCancelReasonConfig (QueryCancelReasonConfigParam) returns (QueryCancelReasonConfigResp);

  //pagsmile的支付结果回调
  rpc pagSmileNotifyProcess (PagSmileNotifyParam) returns (common.ApiResult);

  //查询处于支付中状态的在线支付记录
  rpc getPayingTradeList (GetPayingTradeListParam) returns (GetOnlineTradeListResp);

  rpc getOnlineTradeListByOrderNo (GetOnlineTradeListParam) returns (GetOnlineTradeListResp);

  //查询pagsmile侧的支付详情
  rpc queryPagSmilePayDetail (QueryPagSmilePayDetailParam) returns (QueryPagSmilePayDetailResp);

  //获取支持在线支付的国家列表
  rpc queryOnlineCountry (common.EmptyParam) returns (QueryOnlineCountryResp);

  //admin填写线下支付信息
  rpc submitOfflinePay (SubmitOfflinePayParam) returns (SubmitOfflinePayResp);
}