syntax = "proto3";
package chilat.inquiry;

import "chilat/inquiry/model/inquiry_detail_model.proto";
import "chilat/inquiry/model/inquiry_info_model.proto";
import "chilat/inquiry/param/inquiry_cbm_edit_param.proto";
import "chilat/inquiry/param/inquiry_info_param.proto";
import "common.proto";

option java_package = "com.chilat.rpc.inquiry";

// 询价管理
service InquiryInfoService {
  // 询价列表查询
  rpc getInquiryInfoList (InquiryInfoParam) returns (InquiryInfoPageResp);

  // 询价详情查询
  rpc getInquiryInfoDetail (InquiryDetailParam) returns (InquiryDetailModelResp);

  // 询价详情CBM编辑
  rpc editCbmInquiry (InquiryCbmEditParam) returns (common.ApiResult);

  // 询价开始询价
  rpc startInquiry (InquiryDetailParam) returns (common.ApiResult);
  // 询价完成
  rpc finshInquiry(InquiryDetailParam) returns (common.ApiResult);

  // 导出询价信息
  rpc exportInquiryInfoInfo(InquiryDetailParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };

  // 导出SKU装箱信息（模板）
  rpc exportCbmBoxInfo (InquiryDetailParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };

  // 导入SKU装箱信息
  rpc importCbmBoxInfo (InquiryDetailParam) returns (common.ImportResult) {
    option (common.webapi).upload = true;
  };

}