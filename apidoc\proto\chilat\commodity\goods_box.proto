syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/model/goods_box_model.proto";
import "chilat/commodity/param/goods_box_param.proto";
import "chilat/commodity/param/goods_info_param.proto";
import "common.proto";

// 商品装箱信息
service GoodsBox {
  // 导入批次分页查询
  rpc listBatchPage (ImportBoxBatchPageParam) returns (GoodsBoxBatchPageResp);

  // 导入批次的商品分页查询
  rpc listBatchGoodsPage (ImportBoxBatchGoodsPageParam) returns (GoodsBoxBatchGoodsPageResp);

  // 下载商品装箱信息模板（可选参数：GOODS_IMPORT_TYPE_BOX shop商品维度；GOODS_IMPORT_TYPE_BOX_WITH_1688 1688商品维度）
  rpc downloadImportTemplate (GoodsImportParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };

  // 批量导入shop商品维度装箱信息（shop商品维度导入1/2）
  rpc importShopBoxInfo (common.EmptyParam) returns (ImportShopBoxInfoResp) {
    option (common.webapi).upload = true;
  };

  // 确认shop商品维度装箱信息（shop商品维度导入2/2）
  rpc confirmShopBoxInfo (ConfirmShopBoxInfoParam) returns (SubmitImportBoxInfoResp);

  // 批量导入1688商品维度装箱信息
  rpc import1688BoxInfo (common.EmptyParam) returns (SubmitImportBoxInfoResp) {
    option (common.webapi).upload = true;
  };

}
