<template>
  <div class="mobile-container">
    <div class="w-full relative">
      <div class="w-full flex flex-col">
        <img loading="lazy"
          alt="chilat"
          class="w-[2.6rem] ml-[0.2rem] mt-[1rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/14/06d542a7-955c-41af-b5ba-457be4e1b40d.png"
        />
        <div
          class="text-[0.48rem] leading-[0.66rem] font-semibold text-[#fff] relative z-10 px-[0.36rem] mt-[0.84rem]"
        >
          {{ authStore.i18n("cm_survey.customerFeedback") }}
        </div>
        <img loading="lazy"
          alt="chilat"
          class="w-[5.4rem] absolute right-[0.16rem] top-[0.4rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/03/18/a3638b45-d098-4070-a68b-2698dd25736e.png"
        />
      </div>
      <div class="mt-[0.48rem] px-[0.16rem] relative z-10">
        <!-- 提交成功后显示感谢语 -->
        <div
          v-if="pageData.submitted"
          class="w-full px-[0.5rem] pt-[0.5rem] bg-white rounded-[0.08rem] text-center h-[60vh]"
        >
          <img loading="lazy"
            alt="paySuccess"
            src="@/assets/icons/order/paySuccess.svg"
            class="w-[1.04rem] h-[1.04rem] mx-auto mb-[0.48rem]"
          />
          <span class="font-semibold text-[0.52rem] leading-[0.52rem] text-[#000]">
            {{ authStore.i18n("cm_nota.submitSuccess") }}
          </span>
          <div
            class="w-full text-[#939393] text-[0.28rem] leading-[0.42rem] mt-[0.32rem]"
          >
            {{ authStore.i18n("cm_survey.feedbackThanks") }}
          </div>
        </div>

        <!-- 表单部分 -->
        <template v-else>
          <div
            class="w-full py-[0.32rem] px-[0.4rem] bg-white rounded-[0.08rem] text-[0.28rem] leading-[0.4rem]"
          >
            {{ authStore.i18n("cm_survey.feedbackIntro") }}
          </div>
          <n-form
            class="my-[0.16rem]"
            :rules="userRules"
            :model="userForm"
            ref="userFormRef"
            label-align="left"
            require-mark-placement="left"
          >
            <div
              class="w-full bg-white rounded-[0.08rem] px-[0.2rem] pt-[0.2rem] pb-[0.4rem]"
            >
              <!-- 销售方式 -->
              <n-form-item
                path="userType"
                class="top-form-item"
                :label="authStore.i18n('cm_survey.userType')"
              >
                <div class="flex items-center">
                  <n-radio-group
                    class="relative"
                    v-model:value="userForm.userType"
                    :on-update:value="
                      (value) =>
                        onSelectEvent(
                          value,
                          'userType',
                          authStore.i18n('cm_survey.userType')
                        )
                    "
                  >
                    <n-space vertical>
                      <n-radio
                        v-for="item in userTypeList"
                        :value="item.value"
                        :key="item.value"
                      >
                        {{ item.label }}
                      </n-radio>
                    </n-space>
                  </n-radio-group>
                  <n-form-item
                    label=""
                    path="userTypeRemark"
                    class="inner-form-item"
                    v-if="userForm.userType === 'other'"
                  >
                    <n-input
                      v-trim
                      clearable
                      @keydown.enter.prevent
                      v-model:value="userForm.userTypeRemark"
                      :placeholder="
                        authStore.i18n('cm_survey.inputPlaceholder')
                      "
                      @blur="
                        onBlurEvent(
                          userForm.userTypeRemark,
                          authStore.i18n('cm_survey.userType')
                        )
                      "
                    />
                  </n-form-item>
                </div>
              </n-form-item>

              <!-- 职位 -->
              <n-form-item
                path="positionType"
                class="top-form-item"
                :label="authStore.i18n('cm_survey.positionType')"
              >
                <div class="flex items-center">
                  <n-radio-group
                    class="relative"
                    v-model:value="userForm.positionType"
                    :on-update:value="
                      (value) =>
                        onSelectEvent(
                          value,
                          'positionType',
                          authStore.i18n('cm_survey.positionType')
                        )
                    "
                  >
                    <n-space vertical>
                      <n-radio
                        v-for="item in positionTypeList"
                        :value="item.value"
                        :key="item.value"
                      >
                        {{ item.label }}
                      </n-radio>
                    </n-space>
                  </n-radio-group>
                  <n-form-item
                    label=""
                    path="positionTypeRemark"
                    class="inner-form-item"
                    v-if="userForm.positionType === 'other'"
                  >
                    <n-input
                      v-trim
                      clearable
                      @keydown.enter.prevent
                      v-model:value="userForm.positionTypeRemark"
                      :placeholder="
                        authStore.i18n('cm_survey.inputPlaceholder')
                      "
                      @blur="
                        onBlurEvent(
                          userForm.positionTypeRemark,
                          authStore.i18n('cm_survey.positionType')
                        )
                      "
                    />
                  </n-form-item>
                </div>
              </n-form-item>

              <!-- 了解途径 -->
              <n-form-item
                path="foundType"
                class="top-form-item"
                :label="authStore.i18n('cm_survey.foundType')"
              >
                <div class="flex items-center">
                  <n-radio-group
                    class="relative"
                    v-model:value="userForm.foundType"
                    :on-update:value="
                      (value) =>
                        onSelectEvent(
                          value,
                          'foundType',
                          authStore.i18n('cm_survey.foundType')
                        )
                    "
                  >
                    <n-space vertical>
                      <n-radio
                        v-for="item in foundTypeList"
                        :value="item.value"
                        :key="item.value"
                      >
                        {{ item.label }}
                      </n-radio>
                    </n-space>
                  </n-radio-group>
                  <n-form-item
                    label=""
                    path="foundTypeRemark"
                    class="inner-form-item"
                    v-if="userForm.foundType === 'other'"
                  >
                    <n-input
                      v-trim
                      clearable
                      @keydown.enter.prevent
                      v-model:value="userForm.foundTypeRemark"
                      :placeholder="
                        authStore.i18n('cm_survey.inputPlaceholder')
                      "
                      @blur="
                        onBlurEvent(
                          userForm.foundTypeRemark,
                          authStore.i18n('cm_survey.foundType')
                        )
                      "
                    />
                  </n-form-item>
                </div>
              </n-form-item>

              <!-- 是否购买过 -->
              <n-form-item
                path="havePurchased"
                class="top-form-item"
                :label="authStore.i18n('cm_survey.havePurchased')"
              >
                <n-radio-group
                  v-model:value="userForm.havePurchased"
                  :on-update:value="
                    (value) =>
                      onSelectEvent(
                        value,
                        'havePurchased',
                        authStore.i18n('cm_survey.havePurchased')
                      )
                  "
                >
                  <n-space>
                    <n-radio
                      v-for="item in havePurchasedList"
                      :value="item.value"
                      :key="item.value"
                      >{{ item.label }}</n-radio
                    >
                  </n-space>
                </n-radio-group>
              </n-form-item>
            </div>

            <!-- 根据是否购买过显示不同的问卷内容 -->
            <div
              v-if="userForm.havePurchased"
              class="w-full bg-white rounded-[0.08rem] px-[0.2rem] mt-[0.16rem] overflow-auto"
            >
              <template v-if="userForm.havePurchased === 'yes'">
                <!-- 您选择在我们平台下单的主要原因有哪些？（可多选） -->
                <n-form-item
                  path="purchaseReasons"
                  class="top-form-item"
                  :label="authStore.i18n('cm_survey.purchaseReasons')"
                >
                  <div class="flex items-center">
                    <n-checkbox-group
                      v-model:value="userForm.purchaseReasons"
                      :on-update:value="
                        (value) =>
                          onSelectEvent(
                            value,
                            'purchaseReasons',
                            authStore.i18n('cm_survey.purchaseReasons')
                          )
                      "
                    >
                      <n-space vertical>
                        <n-checkbox
                          v-for="item in purchaseReasonList"
                          :value="item.value"
                          :key="item.value"
                        >
                          {{ item.label }}
                        </n-checkbox>
                      </n-space>
                    </n-checkbox-group>
                    <n-form-item
                      label=""
                      path="purchaseReasonsRemark"
                      class="inner-form-item"
                      v-if="userForm.purchaseReasons.includes('other')"
                    >
                      <n-input
                        v-trim
                        clearable
                        @keydown.enter.prevent
                        v-model:value="userForm.purchaseReasonsRemark"
                        :placeholder="
                          authStore.i18n('cm_survey.inputPlaceholder')
                        "
                        @blur="
                          onBlurEvent(
                            userForm.purchaseReasonsRemark,
                            authStore.i18n('cm_survey.purchaseReasons')
                          )
                        "
                      />
                    </n-form-item>
                  </div>
                </n-form-item>

                <!-- 在您选择采购平台时，下列哪些因素最为关键？（请选择最重要的3项） -->
                <n-form-item
                  path="importantFactors"
                  class="top-form-item !mb-[0.6rem]"
                  :label="authStore.i18n('cm_survey.importantFactors')"
                >
                  <div class="flex items-center">
                    <n-checkbox-group
                      :max="3"
                      v-model:value="userForm.importantFactors"
                      :on-update:value="
                        (value) =>
                          onSelectEvent(
                            value,
                            'importantFactors',
                            authStore.i18n('cm_survey.importantFactors')
                          )
                      "
                    >
                      <n-space vertical>
                        <n-checkbox
                          v-for="item in factorList"
                          :value="item.value"
                          :key="item.value"
                        >
                          {{ item.label }}
                        </n-checkbox>
                      </n-space>
                    </n-checkbox-group>
                    <n-form-item
                      label=""
                      path="importantFactorsRemark"
                      class="inner-form-item"
                      v-if="userForm.importantFactors.includes('other')"
                    >
                      <n-input
                        v-trim
                        clearable
                        maxlength="200"
                        @keydown.enter.prevent
                        v-model:value="userForm.importantFactorsRemark"
                        :placeholder="
                          authStore.i18n('cm_survey.inputPlaceholder')
                        "
                        @blur="
                          onBlurEvent(
                            userForm.importantFactorsRemark,
                            authStore.i18n('cm_survey.importantFactors')
                          )
                        "
                      />
                    </n-form-item>
                  </div>
                </n-form-item>
              </template>

              <template v-else-if="userForm.havePurchased === 'no'">
                <!-- 未下单的原因主要是？（可多选） -->
                <n-form-item
                  path="noOrderReasons"
                  class="top-form-item"
                  :label="authStore.i18n('cm_survey.noOrderReasons')"
                >
                  <div class="flex items-center">
                    <n-checkbox-group
                      v-model:value="userForm.noOrderReasons"
                      :on-update:value="
                        (value) =>
                          onSelectEvent(
                            value,
                            'noOrderReasons',
                            authStore.i18n('cm_survey.noOrderReasons')
                          )
                      "
                    >
                      <n-space vertical>
                        <n-checkbox
                          v-for="item in noOrderReasonList"
                          :value="item.value"
                          :key="item.value"
                        >
                          {{ item.label }}
                        </n-checkbox>
                      </n-space>
                    </n-checkbox-group>
                    <n-form-item
                      label=""
                      path="noOrderReasonsRemark"
                      class="inner-form-item"
                      v-if="userForm.noOrderReasons.includes('other')"
                    >
                      <n-input
                        v-trim
                        clearable
                        @keydown.enter.prevent
                        v-model:value="userForm.noOrderReasonsRemark"
                        :placeholder="
                          authStore.i18n('cm_survey.inputPlaceholder')
                        "
                        @blur="
                          onBlurEvent(
                            userForm.noOrderReasonsRemark,
                            authStore.i18n('cm_survey.noOrderReasons')
                          )
                        "
                      />
                    </n-form-item>
                  </div>
                </n-form-item>
                <!-- 您认为我们平台目前在哪些方面存在不足或需要改进？ -->
                <n-form-item
                  path="improvement"
                  class="mt-[0.4rem]"
                  :label="authStore.i18n('cm_survey.improvement')"
                >
                  <n-input
                    type="textarea"
                    v-trim
                    clearable
                    :autosize="{
                      minRows: 3,
                      maxRows: 5,
                    }"
                    v-model:value="userForm.improvement"
                    :placeholder="authStore.i18n('cm_survey.inputPlaceholder')"
                    @blur="
                      onBlurEvent(
                        userForm.improvement,
                        authStore.i18n('cm_survey.improvement')
                      )
                    "
                  />
                </n-form-item>
              </template>
            </div>

            <!-- 活动偏好和其他建议 -->
            <div
              class="w-full bg-white rounded-[0.08rem] px-[0.2rem] mt-[0.16rem]"
            >
              <!-- 哪些类型的活动最能吸引您？（可多选） -->
              <n-form-item
                path="promotionTypes"
                class="top-form-item"
                :label="authStore.i18n('cm_survey.promotionPreference')"
              >
                <div class="flex items-center">
                  <n-checkbox-group
                    v-model:value="userForm.promotionTypes"
                    :on-update:value="
                      (value) =>
                        onSelectEvent(
                          value,
                          'promotionTypes',
                          authStore.i18n('cm_survey.promotionPreference')
                        )
                    "
                  >
                    <n-space vertical>
                      <n-checkbox
                        v-for="item in promotionTypeList"
                        :value="item.value"
                        :key="item.value"
                      >
                        {{ item.label }}
                      </n-checkbox>
                    </n-space>
                  </n-checkbox-group>
                  <n-form-item
                    label=""
                    path="promotionTypesRemark"
                    class="inner-form-item"
                    v-if="userForm.promotionTypes.includes('other')"
                  >
                    <n-input
                      v-trim
                      clearable
                      @keydown.enter.prevent
                      v-model:value="userForm.promotionTypesRemark"
                      :placeholder="
                        authStore.i18n('cm_survey.inputPlaceholder')
                      "
                      @blur="
                        onBlurEvent(
                          userForm.promotionTypesRemark,
                          authStore.i18n('cm_survey.promotionPreference')
                        )
                      "
                    />
                  </n-form-item>
                </div>
              </n-form-item>
              <!-- 请您提出其他建议或意见 -->
              <n-form-item
                class="mt-[0.4rem]"
                path="otherSuggestions"
                :label="authStore.i18n('cm_survey.otherSuggestions')"
              >
                <n-input
                  type="textarea"
                  v-trim
                  clearable
                  :autosize="{
                    minRows: 3,
                    maxRows: 5,
                  }"
                  v-model:value="userForm.otherSuggestions"
                  :placeholder="authStore.i18n('cm_survey.inputPlaceholder')"
                  @blur="
                    onBlurEvent(
                      userForm.otherSuggestions,
                      authStore.i18n('cm_survey.otherSuggestions')
                    )
                  "
                />
              </n-form-item>
            </div>
          </n-form>
        </template>
      </div>
      <!-- 提交按钮 -->
      <div
        v-if="!pageData.submitted"
        class="bg-white pt-[0.16rem] pb-[0.6rem] mt-[0.16rem] rounded-[0.08rem] px-[0.16rem]"
      >
        <n-button
          class="w-full"
          type="primary"
          size="large"
          :loading="pageData.submitLoading"
          @click="onSubmit"
        >
          {{ authStore.i18n("cm_survey.submit") }}
        </n-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { type FormInst, type FormRules } from "naive-ui";

// 问卷代码定义
const SURVEY_CODE = "v2503-CustomerExperienceOpinion";

const authStore = useAuthStore();
const userForm = reactive<any>({
  userType: null, // 销售方式
  userTypeRemark: null, // 销售方式备注
  positionType: null, // 职位
  positionTypeRemark: null, // 职位备注
  foundType: null, // 了解途径
  foundTypeRemark: null, // 了解途径备注
  havePurchased: null, // 是否购买过
  purchaseReasons: [], // 选择平台的原因
  purchaseReasonsRemark: null, // 选择平台的原因备注
  importantFactors: [], // 最重要的因素
  importantFactorsRemark: null, // 最重要的因素备注
  noOrderReasons: [], // 未下单的原因
  noOrderReasonsRemark: null, // 未下单的原因备注
  improvement: null, // 需要改进的地方
  promotionTypes: [], // 活动偏好
  promotionTypesRemark: null, // 活动偏好备注
  otherSuggestions: null, // 其他建议
});
const pageData = reactive<any>({
  submitted: false,
  submitLoading: false,
});

// 选项列表值与后端对应
// userType options:
// wholesale - 批发
// retail - 零售
// online - 网店
// other - 其他（请注明）
const userTypeList = [
  { value: "wholesale", label: authStore.i18n("cm_survey.wholesale") },
  { value: "retail", label: authStore.i18n("cm_survey.retail") },
  { value: "online", label: authStore.i18n("cm_survey.onlineShop") },
  { value: "other", label: authStore.i18n("cm_survey.other") },
];

// positionType options:
// owner - 老板
// partner - 合伙人
// purchasing - 采购主管/专员
// other - 其他（请注明）
const positionTypeList = [
  { value: "owner", label: authStore.i18n("cm_survey.businessOwner") },
  { value: "partner", label: authStore.i18n("cm_survey.businessPartner") },
  { value: "purchasing", label: authStore.i18n("cm_survey.procurementLead") },
  { value: "other", label: authStore.i18n("cm_survey.other") },
];

// foundType options:
// friend - 朋友推荐
// ad - 网络广告
// search - 搜索引擎
// expo - 展会
// other - 其他（请注明）
const foundTypeList = [
  { value: "friend", label: authStore.i18n("cm_survey.friendReferral") },
  { value: "ad", label: authStore.i18n("cm_survey.onlineAd") },
  { value: "search", label: authStore.i18n("cm_survey.searchEngine") },
  { value: "expo", label: authStore.i18n("cm_survey.tradeShow") },
  { value: "other", label: authStore.i18n("cm_survey.other") },
];

// havePurchased options:
// yes - 是
// no - 否
const havePurchasedList = [
  { value: "yes", label: authStore.i18n("cm_survey.yesPurchased") },
  { value: "no", label: authStore.i18n("cm_survey.noPurchased") },
];

// purchaseReason options:
// price - 价格优势
// quality - 产品质量及品种繁多
// brand - chilat品牌实力
// promotions - 定期优惠活动吸引
// other - 其他（请注明）
const purchaseReasonList = [
  { value: "price", label: authStore.i18n("cm_survey.priceAdvantage") },
  { value: "quality", label: authStore.i18n("cm_survey.productQuality") },
  { value: "brand", label: authStore.i18n("cm_survey.brandStrength") },
  {
    value: "promotions",
    label: authStore.i18n("cm_survey.promotionAttraction"),
  },
  { value: "other", label: authStore.i18n("cm_survey.other") },
];

// factors options:
// language - 语言优势
// price - 价格竞争力
// logistics - 运费及物流效率
// quality - 产品质量
// trust - 平台信任度及信誉
// service - 客户服务
// promotions - 优惠活动及促销力度
// consolidation - 是否可以散货拼柜
// other - 其他（请注明）
const factorList = [
  { value: "language", label: authStore.i18n("cm_survey.languageAdvantage") },
  { value: "price", label: authStore.i18n("cm_survey.priceCompetitiveness") },
  { value: "logistics", label: authStore.i18n("cm_survey.shippingEfficiency") },
  { value: "quality", label: authStore.i18n("cm_survey.productQuality") },
  { value: "trust", label: authStore.i18n("cm_survey.platformTrust") },
  { value: "service", label: authStore.i18n("cm_survey.customerService") },
  { value: "promotions", label: authStore.i18n("cm_survey.promotionStrength") },
  { value: "consolidation", label: authStore.i18n("cm_survey.lclShipping") },
  { value: "other", label: authStore.i18n("cm_survey.other") },
];

// noOrderReason options:
// price - 价格没有优势
// experience - 平台操作体验不好
// trust - 不信任
// other - 其他（请注明）
const noOrderReasonList = [
  { value: "price", label: authStore.i18n("cm_survey.noPriceAdvantage") },
  { value: "experience", label: authStore.i18n("cm_survey.badExperience") },
  { value: "trust", label: authStore.i18n("cm_survey.noTrust") },
  { value: "other", label: authStore.i18n("cm_survey.other") },
];

// promotionType options:
// flash_sale - 限时折扣
// discount - 满额减免
// shipping - 运费优惠
// loyalty - 积分或会员奖励
// other - 其他（请注明）
const promotionTypeList = [
  { value: "flash_sale", label: authStore.i18n("cm_survey.flashSale") },
  { value: "discount", label: authStore.i18n("cm_survey.thresholdDiscount") },
  { value: "shipping", label: authStore.i18n("cm_survey.shippingDiscount") },
  { value: "loyalty", label: authStore.i18n("cm_survey.memberRewards") },
  { value: "other", label: authStore.i18n("cm_survey.other") },
];

const userFormRef = ref<FormInst | null>(null);
const userRules: FormRules = {
  userType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_survey.selectPlaceholder"), // 请选择销售方式
  },
  userTypeRemark: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (userForm.userType === "other" && !value) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
  positionType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_survey.selectPlaceholder"),
  },
  positionTypeRemark: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (userForm.positionType === "other" && !value) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
  foundType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_survey.selectPlaceholder"),
  },
  foundTypeRemark: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (userForm.foundType === "other" && !value) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
  havePurchased: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_survey.selectPlaceholder"),
  },
  purchaseReasons: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (userForm.havePurchased === "yes" && (!value || value.length === 0)) {
        return new Error(authStore.i18n("cm_survey.selectPlaceholder"));
      }
      return true;
    },
  },
  purchaseReasonsRemark: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (
        userForm.havePurchased === "yes" &&
        userForm.purchaseReasons.includes("other") &&
        !value
      ) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
  importantFactors: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (userForm.havePurchased === "yes" && (!value || value.length === 0)) {
        return new Error(authStore.i18n("cm_survey.selectPlaceholder"));
      }
      return true;
    },
  },
  importantFactorsRemark: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (
        userForm.havePurchased === "yes" &&
        userForm.importantFactors.includes("other") &&
        !value
      ) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
  noOrderReasons: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (userForm.havePurchased === "no" && (!value || value.length === 0)) {
        return new Error(authStore.i18n("cm_survey.selectPlaceholder"));
      }
      return true;
    },
  },
  noOrderReasonsRemark: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (
        userForm.havePurchased === "no" &&
        userForm.noOrderReasons.includes("other") &&
        !value
      ) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
  promotionTypes: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_survey.selectPlaceholder"),
    validator: (rule, value) => {
      if (!value || value.length === 0) {
        return new Error(authStore.i18n("cm_survey.selectPlaceholder"));
      }
      if (
        userForm.promotionTypes.includes("other") &&
        userForm.promotionTypesRemark === ""
      ) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
  promotionTypesRemark: {
    required: true,
    trigger: "blur",
    validator: (rule, value) => {
      if (userForm.promotionTypes.includes("other") && !value) {
        return new Error(authStore.i18n("cm_survey.inputPlaceholder"));
      }
      return true;
    },
  },
};

// 问题配置
const QUESTION_CONFIG = {
  // 单选题配置
  RADIO_QUESTIONS: [
    {
      code: "Q110-SalesMethod",
      field: "userType",
    },
    {
      code: "Q120-Job",
      field: "positionType",
    },
    {
      code: "Q130-HowFindOur",
      field: "foundType",
    },
    {
      code: "Q140-PurchaseHistory",
      field: "havePurchased",
    },
  ],

  // 多选题配置
  CHECKBOX_QUESTIONS: [
    {
      code: "Q150-ChoiceUsReason",
      field: "purchaseReasons",
      condition: () => userForm.havePurchased === "yes",
    },
    {
      code: "Q160-ChoiceMostImportant",
      field: "importantFactors",
      condition: () => userForm.havePurchased === "yes",
    },
    {
      code: "Q170-NotChoiceReason",
      field: "noOrderReasons",
      condition: () => userForm.havePurchased === "no",
    },
    {
      code: "Q190-InterestActivity",
      field: "promotionTypes",
    },
  ],

  // 文本题配置
  TEXT_QUESTIONS: [
    {
      code: "Q180-Shortcomings",
      field: "improvement",
      condition: () => userForm.havePurchased === "no",
    },
    {
      code: "Q200-OtherOpinion",
      field: "otherSuggestions",
    },
  ],
};

// 格式化答案
function onFormatAnswer(question: any) {
  const value = userForm[question.field];
  const remark = userForm[`${question.field}Remark`];

  // 基础答案结构
  const answer = {
    questionCode: question.code,
    optionValues: Array.isArray(value)
      ? value.map((val) => ({
          value: val,
          otherText: val === "other" ? remark : undefined,
        }))
      : [
          {
            value: value,
            otherText: value === "other" ? remark : undefined,
          },
        ],
    textAnswer: undefined,
  };

  // 纯文本题不需要选项值
  if (
    question.code.includes("Q180-Shortcomings") ||
    question.code.includes("Q200-OtherOpinion")
  ) {
    answer.optionValues = [];
    answer.textAnswer = value;
  }

  return answer;
}

// 重构提交方法
async function onSubmit() {
  try {
    const isValid = await userFormRef.value?.validate();
    if (isValid) {
      pageData.submitLoading = true;
      try {
        // 收集所有问题的答案
        const surveyAnswers = [
          // 1. 直接处理单选题(必答题无需过滤)
          ...QUESTION_CONFIG.RADIO_QUESTIONS.map(onFormatAnswer),

          // 2. 处理条件题(多选题和文本题)
          ...[
            ...QUESTION_CONFIG.CHECKBOX_QUESTIONS,
            ...QUESTION_CONFIG.TEXT_QUESTIONS,
          ]
            .filter((q) => !q.condition || q.condition())
            .map(onFormatAnswer),
        ];

        // 调用提交接口
        const res: any = await useSaveUserSurvey({
          surveyCode: SURVEY_CODE,
          answers: surveyAnswers,
        });
        if (res?.result?.code === 200) {
          pageData.submitted = true; // 设置提交成功状态
          window?.MyStat?.addPageEvent(
            "survey_form_submit_success",
            `保存问卷调查成功,顺序号:${res?.data?.seqNo}`
          );
        } else {
          showToast(
            res?.result?.message || authStore.i18n("cm_find.errorMessage")
          );
          window?.MyStat?.addPageEvent(
            "survey_form_submit_error",
            `问卷调查表单错误：${res?.result?.message}`
          );
        }
      } catch (error) {
        showToast(authStore.i18n("cm_find.errorMessage"));
        window?.MyStat?.addPageEvent(
          "survey_form_submit_error",
          `问卷调查表单错误：${error}`
        );
      } finally {
        pageData.submitLoading = false;
      }
    }
  } catch (error) {
    const remark = `${error?.[0]?.[0]?.message} [${error?.[0]?.[0]?.field}]`;
    window?.MyStat?.addPageEvent(
      "survey_form_submit_error",
      `问卷调查表单错误：${remark}`
    );
  }
}

// 下拉选择埋点事件
function onSelectEvent(value: any, attr: any, label: any) {
  userForm[attr] = value;
  let list: any[] = [];

  // 根据不同选项获取对应的列表
  switch (attr) {
    case "userType":
      list = userTypeList;
      break;
    case "positionType":
      list = positionTypeList;
      break;
    case "foundType":
      list = foundTypeList;
      break;
    case "havePurchased":
      list = havePurchasedList;
      break;
    case "purchaseReasons":
      list = purchaseReasonList;
      break;
    case "importantFactors":
      list = factorList;
      break;
    case "noOrderReasons":
      list = noOrderReasonList;
      break;
    case "promotionTypes":
      list = promotionTypeList;
      break;
    default:
      return;
  }

  // 处理多选框的情况
  if (Array.isArray(value)) {
    const selectedLabels = value
      .map((val) => {
        const match = list.find((item) => item.value === val);
        return match ? match.label : null;
      })
      .filter((label) => label !== null)
      .join(", ");

    if (selectedLabels) {
      // 埋点事件
      window?.MyStat?.addPageEvent(
        "survey_form_select",
        `${label} 选择：${selectedLabels}`
      );
    }
  } else {
    const match = list.find((item) => item.value === value);
    if (!value || !match) return;

    // 埋点事件
    window?.MyStat?.addPageEvent(
      "survey_form_select",
      `${label} 选择：${match.label}`
    );
  }
}

// 输入框埋点事件
function onBlurEvent(value: string, label: any) {
  if (!value) return;

  // 埋点事件
  window?.MyStat?.addPageEvent("survey_form_input", `${label} 输入：${value}`);
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: auto;
  min-height: 100vh;
  background: linear-gradient(180deg, #db1121 0%, #f87e7e 100%), #f2f2f2;
  background-size: 100% 5.1rem, 100% 100%;
  background-repeat: no-repeat;
  color: #333;
}
:deep(.n-form-item-label__text) {
  font-weight: 500;
  font-size: 0.28rem;
  line-height: 0.36rem;
}

:deep(.n-radio__label) {
  color: #333;
}

.top-form-item {
  padding-top: 0.4rem;
  padding-bottom: 0.16rem;
  margin-bottom: 0.1rem;
  position: relative;
  border-bottom: 0.02rem solid #e6e6e6;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -0.6rem;
    left: 0;
    color: #e50113;
  }
}

.inner-form-item {
  width: 3.9rem;
  margin-left: 0.16rem;
  position: absolute;
  bottom: 0rem;
  left: 2.7rem;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -0.6rem;
    left: -2.9rem;
    color: #e50113;
  }
}
</style>
