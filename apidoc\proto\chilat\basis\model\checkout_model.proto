syntax = "proto3";
package chilat.basis;

import "chilat/basis/model/sales_order_model.proto";
import "common.proto";

option java_package = "com.chilat.rpc.basis.model";




message ParseBuySkuTextResp {
    common.Result result = 1;
    ParseBuySkuTextModel data = 2;
}

message ParseBuySkuTextModel {
    repeated SalesOrderLineModel skuList = 20;
    repeated string parsedLines = 30; //解析后的行数据（第一行为字段名）
}

message PreviewBuySkuListResp {
    common.Result result = 1;
    PreviewBuySkuListModel data = 2;
}

message PreviewBuySkuListModel {
    repeated SalesOrderLineModel skuList = 20;
}


//计算预估运费的返回结果
message MidEstimateFreightResp {
    common.Result result = 1;
    MidEstimateFreightModel data = 2;
}

//预估运费接口返回对象
message MidEstimateFreightModel {
    double totalEstimateFreight = 10; //预估运费总计（全部SKU可预估运费时，才返回）
    double partEstimateFreight = 20; //预估运费部分（只要有一个SKU可预估运费时，则返回）
    int32 freightGoodsQty = 30; //参与预估运费的商品数量
    map<string, double> skuFreightMap = 40; //sku维度的预估运费Map（KEY: skuId, VALUE: 预估运费）
    map<string, double> goodsFreightMap = 50; //goods维度的预估运费Map（KEY: skuId, VALUE: 预估运费）
    string selectedRouteId = 70; //选中的线路ID（以此更新页面中选中的线路ID）
    repeated MidRouteFreightModel routeList = 80; //线路运费列表
}

//线路运费对象
message MidRouteFreightModel {
    string routeId = 10; //线路ID
    string routeName = 20; //线路名称
    string deliverTimeName = 30; //交期名称（将交期格式化后的字符串）
    double totalEstimateFreight = 110; //预估运费总计（全部SKU可预估运费时，才返回）
    double partEstimateFreight = 120; //预估运费部分（只要有一个SKU可预估运费时，则返回）
    int32 freightGoodsQty = 130; //参与预估运费的商品数量
    bool selected = 140; //是否选中
}