<template>
  <n-modal
    preset="dialog"
    class="holiday-modal"
    :closable="false"
    :show-icon="false"
    :auto-focus="false"
    :block-scroll="true"
    v-model:show="pageData.showModal"
    :on-close="onCloseModal"
    :on-esc="onCloseModal"
    :on-mask-click="onCloseModal"
    :style="
      isMobile
        ? { width: '100%', padding: '0' }
        : { width: '690px', padding: '0' }
    "
  >
    <img loading="lazy"
      v-if="isMobile"
      @click="onCloseModal"
      class="w-[100%] block object-fill"
      src="@/assets/icons/mobileHolidayNoticeBg.png"
    />
    <img loading="lazy"
      v-else
      @click="onCloseModal"
      class="w-[100%] block object-fill"
      src="@/assets/icons/holidayNoticeBg.png"
    />
  </n-modal>
</template>

<script setup lang="ts" name="HolidayNoticeModal">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const pageData = reactive({
  showModal: false,
});

onMounted(() => {
  if (!window?.MyStat.getSessionValue("isHolidayNoticeShown")) {
    onOpenModal();
  }
});

function onOpenModal() {
  pageData.showModal = true;
  window?.MyStat?.setSessionValue("isHolidayNoticeShown", "true");
}
function onCloseModal() {
  pageData.showModal = false;
}

defineExpose({
  onOpenModal,
});
</script>
<style scoped lang="scss"></style>
<style>
.holiday-modal.n-dialog {
  background: none !important;
}
.holiday-modal.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0px !important;
}
</style>
