<template>
  <div class="w-full bg-[#F2F2F2]">
    <div
      class="flex flex-col gap-[30px] items-center px-[24px] pt-[18vh] max-w-[768px] mx-auto min-h-screen bg-[#FEFEFE] relative"
    >
      <img
        src="@/assets/icons/find-goods/bg2.png"
        alt="bg"
        class="absolute right-0 top-0 w-[167px] h-[95px]"
      />
      <img
        src="@/assets/icons/find-goods/bg1.png"
        alt="bg"
        class="absolute left-0 bottom-0 w-[114px] h-[81px]"
      />
      <img
        src="@/assets/icons/common/chilat.png"
        alt="chilat"
        class="w-[134px] mb-[10px]"
      />
      <div class="text-[26px] leading-[26px] font-medium text-center">
        Por favor, introduzca el nombre del grupo
      </div>

      <n-input
        size="large"
        v-model:value="pageData.keyword"
        placeholder=""
        @keydown.enter="onStartClick"
        class="h-[48px] rounded-[8px]"
        maxlength="64"
      />

      <n-button
        block
        type="primary"
        class="h-[44px] rounded-[200px]"
        @click="onStartClick"
      >
        <span class="text-[18px] leading-[18px]">Empezar</span>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter();

const STORAGE_KEY = "comprar_grupo_keyword";

const pageData = reactive({
  keyword: "",
});

onMounted(() => {
  const savedKeyword = localStorage.getItem(STORAGE_KEY);
  if (savedKeyword) {
    pageData.keyword = savedKeyword;
  }
});

async function onStartClick() {
  if (!pageData.keyword.trim()) {
    return showToast("Por favor, introduzca el nombre del grupo");
  }

  const trimmedKeyword = pageData.keyword.trim();
  localStorage.setItem(STORAGE_KEY, trimmedKeyword);
  navigateToPage("/h5/comprar/goods", { keyword: trimmedKeyword }, false);
}
</script>

<style scoped lang="scss">
:deep(.n-input .n-input__input-el) {
  height: 48px;
}
</style>
