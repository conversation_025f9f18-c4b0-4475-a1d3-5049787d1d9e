syntax = "proto3";
package chilat.logistics;

option java_package = "com.chilat.rpc.logistics";

import "chilat/logistics/param/fee_item_param.proto";
import "chilat/logistics/model/fee_item_model.proto";
import "common.proto";

// 费用管理
service FeeItem {
  // 保存费用
  rpc saveFeeItem(SaveFeeItemParam) returns (common.ApiResult);
  // 查询费用
  rpc getFeeItem(common.EmptyParam) returns (FeeItemResp);
  // 查询日志
  rpc listLog (common.IdParam) returns (FeeItemLogListResp);
  // 开启关闭
  rpc enabled (common.EnabledParam) returns (common.ApiResult);
  // 查询费用
  rpc getFeeItemByType(FeeItemByTypeParam) returns (FeeItemResp);
}