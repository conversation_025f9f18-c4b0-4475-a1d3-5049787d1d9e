<template>
  <div class="mobile-container">
    <!-- 头部信息 -->
    <mobile-query-bar :showCart="true"></mobile-query-bar>

    <div
      id="category-container"
      class="h-full overflow-hidden flex text-[#333333] text-[0.28rem] leading-[0.4rem]"
    >
      <!-- 左侧一级分类 -->
      <div
        class="w-[32%] scroll-height flex flex-col bg-[#F2F2F2] flex-shrink-0 top-[2rem] left-0"
      >
        <div
          v-for="(item, index) in pageData.category"
          :key="item.id"
          @click="showSecondaryCates(item, index)"
          class="pl-[0.16rem] py-[0.16rem] flex items-center cursor-pointer"
          :class="pageData.primaryCate.id === item?.id ? 'active-cate' : ''"
        >
          <n-ellipsis
            :line-clamp="6"
            :tooltip="false"
            class="break-all px-[0.06rem] text-[0.26rem] leading-[0.32rem]"
          >
            {{ item.cateName }}
          </n-ellipsis>
        </div>
      </div>

      <!-- 右侧二级和三级分类 -->
      <div
        class="w-[68%] scroll-height pl-[0.16rem] pr-[0.08rem] top-[2.1rem] right-0"
      >
        <!-- 二级分类列表 -->
        <div v-if="pageData.secondaryCates?.length">
          <div
            v-for="(item, iIndex) in pageData.secondaryCates"
            :key="item.id"
            class="mb-[0.4rem]"
          >
            <!-- 二级分类标题 -->
            <a
              data-spm-box="category-home-subdir"
              :href="`/h5/search/list?categoryId=${item.id}&cateName=${item.cateName}`"
              class="block px-[0.16rem] py-[0.12rem] font-medium flex"
              @click="onCategoryClick(item)"
              :data-spm-index="iIndex + 1"
            >
              <div class="flex-1 break-all">{{ item.cateName }}</div>
              <icon-card
                color="#333"
                size="0.36rem"
                name="solar:alt-arrow-right-outline"
              ></icon-card>
            </a>

            <!-- 三级分类列表 -->
            <div v-if="item.children?.length" class="ml-[0.2rem]">
              <a
                v-for="(child, cIndex) in item.children"
                :key="child.id"
                data-spm-box="category-home-subdir"
                :href="`/h5/search/list?categoryId=${child.id}&cateName=${child.cateName}`"
                class="block py-[0.1rem] px-[0.16rem] text-[0.26rem] text-[#666]"
                @click="onCategoryClick(child)"
                :data-spm-index="iIndex * 100 + cIndex + 1"
              >
                {{ child.cateName }}
              </a>
            </div>
          </div>
        </div>

        <n-empty
          v-else
          class="mt-[1.6rem]"
          :description="authStore.i18n('cm_home.noData')"
        >
        </n-empty>
      </div>
    </div>
    <!-- 底部信息 -->
    <mobile-tab-bar :naiveBar="1" />
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import MobileQueryBar from "@/pages/h5/components/MobileQueryBar.vue";
const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const pageData = reactive(<any>{
  categoryId: route?.query?.cate_id || "0",
  category: <any>[],
  logoCates: <any>[],
  isLoading: false,
  noGoodsData: false, //最新商品
  isShowHeaderBtn: false,
  primaryCate: <any>{},
  secondaryCates: [],
  visible: false,
});

await onGetCategoryTree();
async function onGetCategoryTree() {
  const res: any = await useCategoryTree({});
  if (res?.result?.code === 200) {
    pageData.category = res.data;
    let targetIndex = pageData.category?.findIndex(
      (item: any) => item.id == pageData.categoryId
    );
    let target;
    if (targetIndex === -1 || targetIndex === undefined) {
      target = pageData.category[0];
      targetIndex = 0;
    } else {
      target = pageData.category[targetIndex];
    }
    authStore.getCartList();
    onUpdateSecondary(target, targetIndex);
  }
}

async function onCategoryClick(cate: any) {
  window?.MyStat?.addPageEvent(`search_category`, `分类名称：${cate.cateName}`); //埋点
}

async function showSecondaryCates(cate: any, index: any) {
  const url = new URL(window.location.href);
  url.searchParams.set("cate_id", cate.id);
  window.history.replaceState(null, "", url.toString());

  window?.MyStat?.addPageEvent(`click_category`, `分类名称：${cate.cateName}`); //埋点
  onUpdateSecondary(cate, index);
}

function onUpdateSecondary(cate: any, index: any) {
  pageData.primaryCate = cate;
  pageData.primaryCateIndex = index;
  let target = pageData.category?.find((item: any) => item.id === cate.id);
  if (!target) {
    target = pageData.category[0];
  }
  pageData.secondaryCates = target?.children || [];
}

function setViewportHeight() {
  const viewportHeight = window.innerHeight;
  const container = document.getElementById("category-container");
  if (container) {
    container.style.height = `${viewportHeight}px`;
  }
}

onMounted(() => {
  setViewportHeight();
  window.addEventListener("resize", setViewportHeight);
  document.documentElement.style.overflow = "hidden";
});

onUnmounted(() => {
  document.documentElement.style.overflow = "";
});
</script>

<style scoped lang="scss">
.mobile-container {
  height: 100vh;
  overflow: hidden;
}
.active-cate {
  position: relative;
  background-color: #fff;
  color: #e50113;
}

.active-cate:before {
  content: "";
  background-color: #e50113;
  width: 0.06rem;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  -webkit-border-radius: 0.06rem;
  -moz-border-radius: 0.06rem;
  border-radius: 0.06rem;
}

.scroll-height {
  height: calc(100% - 3.4rem);
  overflow-y: auto;
  position: fixed;
}
</style>
