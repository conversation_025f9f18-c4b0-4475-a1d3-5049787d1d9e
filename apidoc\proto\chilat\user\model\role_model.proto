syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.model";

import "common.proto";

message RolePageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated RoleModel data = 3;
}

message RoleListResp {
  common.Result result = 1;
  repeated RoleModel data = 2;
}

message RoleResp {
  common.Result result = 1;
  RoleModel data = 2;
}

// 角色信息
message RoleModel {
  string id = 1; // 角色ID
  string name = 2; // 角色名称
  string remark = 3; // 备注
  string coperator = 4; // 创建人
  int64 cdate = 5; // 创建时间
}
