<template>
  <n-modal
    preset="dialog"
    :block-scroll="true"
    :show-icon="false"
    :on-close="onCancel"
    :on-esc="onCancel"
    :on-mask-click="onCancel"
    v-model:show="pageData.showCancelOrder"
    :title="authStore.i18n('cm_order.orderCancelTitle')"
  >
    <n-form ref="cancelFormRef" :model="cancelForm" :rules="rules" class="mt-4">
      <n-form-item
        path="cancelReasonId"
        :label="authStore.i18n('cm_order.orderCancelReason')"
      >
        <n-select
          clearable
          filterable
          value-field="id"
          label-field="reason"
          :options="pageData.reasonList"
          v-model:value="cancelForm.cancelReasonId"
          :placeholder="authStore.i18n('cm_order.chooseReason')"
          :on-focus="onFocusReasonSelect"
          :on-update:value="onUpdateReason"
        />
      </n-form-item>
      <n-form-item
        path="cancelRemark"
        :label="authStore.i18n('cm_order.orderCancelRemark')"
      >
        <n-input
          v-trim
          clearable
          v-model:value="cancelForm.cancelRemark"
          type="textarea"
          :autosize="{
            minRows: 3,
            maxRows: 5,
          }"
          @keydown.enter.prevent
          :placeholder="authStore.i18n('cm_order.inputRemark')"
        />
      </n-form-item>
      <div class="w-full flex justify-end">
        <n-button @click="onCancel" class="p-4 mx-4">
          {{ authStore.i18n("cm_order.orderCancelNo") }}
        </n-button>
        <n-button color="#E50113" @click="onConfirm" class="p-4">
          {{ authStore.i18n("cm_order.orderCancelYes") }}
        </n-button>
      </div>
    </n-form>
  </n-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormRules } from "naive-ui";

const route = useRoute();
const authStore = useAuthStore();
const emit = defineEmits(["updateOrderState"]);
const cancelFormRef = ref<FormInst | null>(null);
const pageData = reactive(<any>{
  showCancelOrder: false,
  reasonList: <any>[],
});

const cancelForm = reactive({
  orderNo: "",
  cancelReasonId: null,
  cancelRemark: null,
});

const rules: FormRules = {
  cancelReasonId: {
    required: true,
    trigger: "change",
    message: authStore.i18n("cm_order.chooseReason"),
  },
  cancelRemark: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_order.inputRemark"),
  },
};
onBeforeMount(() => {
  onGetReason();
});
async function onGetReason() {
  const res: any = await useGetCancelOrderReasonConfig({});
  if (res?.result?.code === 200) {
    pageData.reasonList = res?.data.reasonList;
  }
}

function onOpenDialog(data: any) {
  cancelForm.orderNo = data;
  cancelForm.cancelReasonId = null;
  cancelForm.cancelRemark = null;
  pageData.showCancelOrder = true;
  window?.MyStat?.addPageEvent(
    "order_open_cancel_dialog",
    `打开订单（${cancelForm.orderNo}）取消对话框`
  ); // 埋点
}

function onCancel() {
  window?.MyStat?.addPageEvent(
    "order_close_cancel_dialog",
    `关闭订单（${cancelForm.orderNo}）取消对话框`
  ); // 埋点
  pageData.showCancelOrder = false;
}

function onFocusReasonSelect() {
  window?.MyStat?.addPageEvent(
    "order_click_cancel_reason_list",
    `点击订单取消原因列表`
  ); // 埋点
}

function onUpdateReason(val: any) {
  cancelForm.cancelReasonId = val;
  const cancelReason = pageData.reasonList.find(
    (item: any) => item.id === val
  )?.reason;
  window?.MyStat?.addPageEvent(
    "order_choice_cancel_reason",
    `选择订单取消原因：${cancelReason}`
  ); // 埋点
}

// 订单取消确认
async function onConfirm() {
  await cancelFormRef.value?.validate();
  const reason = pageData.reasonList.find(
    (item: any) => item.id === cancelForm.cancelReasonId
  );
  const res: any = await useCancelOrder({
    ...cancelForm,
    cancelReason: reason.reason,
  });
  if (res?.result?.code === 200) {
    emit("updateOrderState");
    pageData.showCancelOrder = false;
    window?.MyStat?.addPageEvent(
      "order_submit_cancel_ok",
      `提交订单取消成功：${cancelForm.orderNo}`
    ); // 埋点
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "order_submit_cancel_fail",
      `提交订单取消失败：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message);
  }
}

defineExpose({
  onOpenDialog,
});
</script>
<style scoped lang="scss"></style>
