<template>
  <div class="w-full bg-[#F2F2F2]">
    <div class="mobile-container max-w-[768px] mx-auto">
      <div
        class="w-full bg-white pt-[20px] pb-[12px] flex items-center justify-center"
      >
        <img
          src="@/assets/icons/common/chilat.png"
          alt="chilat"
          class="w-[108px]"
        />
      </div>
      <div
        class="w-full bg-white flex flex-col gap-[10px] items-center pt-[12px] pb-[14px]"
      >
        <div class="text-[22px] leading-[22px] font-medium">
          Tarjeta de Compra
        </div>
        <div v-if="pageData.taskInfo?.groupName" class="flex px-[20px]">
          <div class="flex-shrink-0 mr-[2px]">Nombre del grupo:</div>
          <span class="font-medium">{{ pageData.taskInfo?.groupName }}</span>
        </div>
      </div>
      <div v-if="isHeaderSticky" class="w-full h-[80px]"></div>
      <div class="w-full py-[8px]">
        <div :class="isHeaderSticky ? 'px-[0px]' : 'px-[8px]'">
          <div
            ref="stickyHeaderRef"
            class="w-full bg-white rounded-[8px] pt-[20px] pb-[30px] px-[12px] shadow-sm transition-all duration-200 px-[8px]"
            :class="{
              'fixed top-0 left-0 right-0 z-50 shadow-md px-[0px] !py-[14px]':
                isHeaderSticky,
              relative: !isHeaderSticky,
            }"
          >
            <div v-if="!isHeaderSticky" class="mb-[32px]">
              <div class="text-[18px] leading-[22px] font-medium text-center">
                Imagen de referencia
              </div>
              <div class="text-[18px] leading-[22px] text-center">
                (Hacer clic para cambiar)
              </div>
            </div>
            <div
              v-else
              class="text-[18px] leading-[22px] text-center mb-[20px]"
            >
              Hacer clic para cambiar
            </div>

            <div class="flex gap-[20px] justify-center flex-wrap">
              <div
                v-for="(item, index) in pageData.taskInfo?.questionAnswers"
                :key="index"
                class="relative cursor-pointer"
                @click="switchToQuestion(index)"
              >
                <!-- 商品图片容器 -->
                <div
                  class="relative w-[98px] h-[98px] rounded-[8px] overflow-hidden"
                  :class="{
                    'ring-2 ring-[#e50113]':
                      pageData.currentQuestionIndex === index,
                    'ring-2 ring-transparent':
                      pageData.currentQuestionIndex !== index,
                  }"
                >
                  <img
                    :src="item.questionImageUrl"
                    alt=""
                    referrerpolicy="no-referrer"
                    class="w-full h-full object-contain"
                  />

                  <!-- 完成状态遮罩 -->
                  <div
                    v-if="isQuestionCompleted(index)"
                    class="w-full h-[24px] absolute bottom-0 bg-black bg-opacity-65 flex items-center justify-center"
                  >
                    <div class="text-white text-[16px] leading-[16px]">
                      Completar
                    </div>
                  </div>
                </div>

                <!-- 完成状态图标 -->
                <img
                  v-if="isQuestionCompleted(index)"
                  src="@/assets/icons/find-goods/complete.svg"
                  alt="complete"
                  referrerpolicy="no-referrer"
                  class="absolute top-[-12px] right-[-12px] w-[24px] h-[24px]"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="px-[8px]">
          <div
            class="bg-white rounded-[8px] py-[20px] px-[12px] flex gap-[12px] mt-[2px] mb-[8px]"
          >
            <n-image
              lazy
              class="w-[168px] h-[168px] flex-shrink-0 rounded-[8px]"
              :src="pageData.currentQuestion?.questionImageUrl"
              :img-props="{ referrerpolicy: 'no-referrer' }"
            />
            <div v-if="pageData.currentQuestion?.questionSpecDesc">
              <div class="text-[18px] leading-[18px] font-medium mb-[18px]">
                Parámetro
              </div>
              <div class="text-[16px] leading-[20px] whitespace-pre-wrap">
                {{ pageData.currentQuestion?.questionSpecDesc }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单区域 -->
      <n-form
        ref="lookingFormRef"
        :model="pageData"
        class="pb-[100px] px-[8px]"
      >
        <!-- 商品信息部分 - 循环渲染多个商品 -->
        <div
          v-for="(item, index) in pageData.foundGoodsList"
          :key="index"
          class="mb-[16px] bg-white rounded-[8px] p-[16px] relative"
          v-show="!pageData.isLoading"
        >
          <!-- 商品图片 -->
          <n-form-item>
            <template #label>
              <span class="text-[18px] leading-[18px] font-medium"
                >Modelo parecido {{ index + 1 }}</span
              >
              <span class="text-[#e50113] ml-[2px] align-top">*</span>
            </template>
            <div>
              <div class="text-[16px] text-[#7F7F7F] mb-[6px]">
                (Subir 2 imágenes)
              </div>
              <MobileImageUpload
                :ref="(el) => setGoodsImageUploadRef(el, index)"
                :key="`goods-images-${pageData.currentQuestionIndex}-${index}`"
                :max="2"
                :multiple="true"
                @upload-event="(val) => onFileUpload(val, index)"
                :files="item.goodsImages"
              />
            </div>
          </n-form-item>

          <!-- Precio(US$) -->
          <n-form-item>
            <template #label>
              <span class="text-[16px] leading-[16px]">Precio(US$)</span>
              <span class="text-[#e50113] ml-[2px]">*</span>
            </template>
            <n-input-number
              v-model:value="item.goodsPrice"
              placeholder=""
              class="w-full"
              :min="0"
              :max="999999.99"
              :precision="2"
              @update:value="tempStoreData()"
            />
          </n-form-item>

          <!-- Cantidad por bulto (UND) -->
          <n-form-item>
            <template #label>
              <span class="text-[16px] leading-[16px]"
                >Cantidad por bulto (UND)</span
              >
              <span class="text-[#e50113] ml-[2px]">*</span>
            </template>
            <n-input-number
              v-model:value="item.packingCount"
              placeholder=""
              class="w-full"
              :min="1"
              :max="999999"
              :precision="0"
              @update:value="tempStoreData()"
            />
          </n-form-item>

          <!-- Volumen por bulto(m³) -->
          <n-form-item>
            <template #label>
              <span class="text-[16px] leading-[16px]"
                >Volumen por bulto(m³)</span
              >
              <span class="text-[#e50113] ml-[2px]">*</span>
            </template>
            <n-input-number
              v-model:value="item.goodsVolume"
              placeholder=""
              class="w-full"
              :min="0"
              :max="999999.999"
              :precision="3"
              @update:value="tempStoreData()"
            />
          </n-form-item>

          <!-- La mínima cantidad (UND) -->
          <n-form-item>
            <template #label>
              <span class="text-[16px] leading-[16px]"
                >La mínima cantidad (UND)</span
              >
              <span class="text-[#e50113] ml-[2px]">*</span>
            </template>
            <n-input-number
              v-model:value="item.minBuyQuantity"
              placeholder=""
              class="w-full"
              :min="0"
              :max="999999"
              :precision="0"
              @update:value="tempStoreData()"
            />
          </n-form-item>

          <!-- Motivo de recomendación -->
          <n-form-item>
            <template #label>
              <span class="text-[16px] leading-[16px]"
                >Motivo de recomendación</span
              >
              <span class="text-[#e50113] ml-[2px]">*</span>
            </template>
            <n-input
              v-model:value="item.recommendReason"
              type="textarea"
              placeholder=""
              :autosize="{ minRows: 1, maxRows: 4 }"
              @change="tempStoreData()"
            />
          </n-form-item>

          <!-- Origen de compra -->
          <n-form-item>
            <template #label>
              <span class="text-[16px] leading-[16px]">Origen de compra</span>
              <span class="text-[#e50113] ml-[2px]">*</span>
            </template>
            <n-select
              v-model:value="item.findSource"
              placeholder=""
              @update:value="tempStoreData()"
              :options="findSourceOptions"
            >
            </n-select>
          </n-form-item>

          <!-- Imagen de evidencia-->
          <n-form-item>
            <template #label>
              <span class="text-[16px] leading-[16px]"
                >Imagen de evidencia</span
              >
              <span class="text-[#e50113] ml-[2px]">*</span>
            </template>
            <div>
              <div class="text-[16px] leading-[20px] text-[#7F7F7F] mb-[8px]">
                (hace foto con la tienda/captura de chinagoods /captura de
                chilatshop)
              </div>
              <MobileImageUpload
                :ref="(el) => setProofImageUploadRef(el, index)"
                :key="`proof-images-${pageData.currentQuestionIndex}-${index}`"
                :max="5"
                :multiple="true"
                @upload-event="(val) => onProofImagesUpload(val, index)"
                :files="item.proofImages || []"
                :spm-code="`goods_find_upload_proof${index + 1}`"
              />
            </div>
          </n-form-item>
        </div>
      </n-form>

      <div
        class="fixed left-0 right-0 bottom-0 bg-white p-[8px] border-t border-gray-200 max-w-[768px] mx-auto z-2"
      >
        <n-button
          class="submit-btn"
          type="primary"
          :loading="pageData.isSubmitLoading"
          @click="onSubmitGoodsLooking"
        >
          <span class="text-[16px] leading-[16px]">{{
            getSubmitButtonText()
          }}</span>
        </n-button>
      </div>
    </div>

    <!-- 上传等待弹窗 -->
    <n-modal
      v-model:show="pageData.showUploadWaitingModal"
      :mask-closable="false"
      :closable="false"
      :close-on-esc="false"
    >
      <n-card style="width: 6rem" :bordered="false">
        <div class="flex items-center justify-center mb-[16px] mt-[12px]">
          <n-spin size="medium" />
        </div>
        <div class="text-center text-[16px]">La imagen se está subiendo...</div>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts" name="GoodsLooking">
import { useAuthStore } from "@/stores/authStore";
import MobileImageUpload from "@/components/MobileImageUpload.vue";

const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo;

// 创建默认商品项的函数
function createDefaultFoundGoods(answerSeq: number) {
  return {
    answerSeq: answerSeq, // 回答次序号（第1个商品则为1，第2个商品则为2）
    goodsImages: [], // 商品图片
    goodsPrice: null, // 商品价格
    packingCount: null, // 装箱数
    goodsVolume: null, // 商品体积（立方米）
    minBuyQuantity: null, // 最小购买数量（起订量）
    recommendReason: "", // 推荐理由
    findSource: null, // 采购来源
    proofImages: [], // 证明图片数组
  };
}

// 吸顶效果相关
const stickyHeaderRef = ref<HTMLElement>();
const isHeaderSticky = ref(false);
const stickyThreshold = ref(0);

const pageData = reactive<any>({
  // 基本信息
  keyword: route.query.keyword || "",
  currentGoodsIndex: 0,
  isSubmitLoading: false,
  isLoading: true,

  // 任务信息
  taskInfo: null,
  groupId: "",
  groupCode: "",
  groupName: "",

  // 当前问题信息
  currentQuestion: null,
  currentQuestionIndex: 0,
  questionId: "",
  questionImageUrl: "",
  questionSpecDesc: "",
  answerVersion: 0,

  // 找到的商品列表（最多2个）
  foundGoodsList: [createDefaultFoundGoods(1), createDefaultFoundGoods(2)],

  showUploadWaitingModal: false,
});

const findSourceOptions = [
  {
    value: "FIND_GOODS_TASK_SOURCE_MERCADO",
    label: "mercado",
  },
  {
    value: "FIND_GOODS_TASK_SOURCE_ALIBABA",
    label: "chinagoods",
  },
  {
    value: "FIND_GOODS_TASK_SOURCE_CHILATSHOP",
    label: "chilatshop",
  },
];

const lookingFormRef = ref<any>(null);

// 组件引用管理
const goodsImageUploadRefs = ref<any[]>([]);
const proofImageUploadRefs = ref<any[]>([]);

// 设置商品图片上传组件引用
const setGoodsImageUploadRef = (el: any, index: number) => {
  if (el) {
    goodsImageUploadRefs.value[index] = el;
  }
};

// 设置证明图片上传组件引用
const setProofImageUploadRef = (el: any, index: number) => {
  if (el) {
    proofImageUploadRefs.value[index] = el;
  }
};

// 检查是否有图片正在上传
const hasAnyUploadingImages = () => {
  // 检查商品图片上传组件
  const hasUploadingGoods = goodsImageUploadRefs.value.some((ref) => {
    return (
      ref &&
      typeof ref.hasUploadingFiles === "function" &&
      ref.hasUploadingFiles()
    );
  });

  // 检查证明图片上传组件
  const hasUploadingProof = proofImageUploadRefs.value.some((ref) => {
    return (
      ref &&
      typeof ref.hasUploadingFiles === "function" &&
      ref.hasUploadingFiles()
    );
  });

  return hasUploadingGoods || hasUploadingProof;
};

// 等待所有图片上传完成
const waitForAllUploadsComplete = async (): Promise<void> => {
  return new Promise((resolve) => {
    const checkInterval = setInterval(() => {
      if (!hasAnyUploadingImages()) {
        clearInterval(checkInterval);
        resolve();
      }
    }, 500);
  });
};

// 显示上传等待弹窗
const showUploadWaitingModal = () => {
  pageData.showUploadWaitingModal = true;
};

// 隐藏上传等待弹窗
const hideUploadWaitingModal = () => {
  pageData.showUploadWaitingModal = false;
};

// 通用的上传状态检查和等待函数
const checkAndWaitForUploads = async (
  callback: () => Promise<void> | void
): Promise<void> => {
  // 检查是否有图片正在上传
  if (hasAnyUploadingImages()) {
    // 显示上传等待弹窗
    showUploadWaitingModal();

    try {
      // 等待所有上传完成
      await waitForAllUploadsComplete();

      // 隐藏弹窗
      hideUploadWaitingModal();

      // 上传完成后执行回调
      await callback();
    } catch (error) {
      // 隐藏弹窗
      hideUploadWaitingModal();
      throw error;
    }
  } else {
    // 没有上传中的图片，直接执行回调
    await callback();
  }
};

onBeforeMount(() => {
  document.title = authStore.i18n("cm_common.documentTitle");
});

onBeforeMount(async () => {
  // 获取找货任务信息
  await loadFindGoodsInfo();
});

// 获取找货任务信息
async function loadFindGoodsInfo() {
  if (!pageData.keyword) {
    return navigateTo({
      path: "/h5/comprar",
    });
  }

  pageData.isLoading = true;
  try {
    const res: any = await useGetFindGoodsInfo({
      keyword: pageData.keyword,
    });

    if (res?.result?.code === 200 && res?.data) {
      const taskInfo = res.data;
      pageData.taskInfo = taskInfo;
      pageData.groupId = taskInfo.groupId;
      pageData.groupCode = taskInfo.groupCode;
      pageData.groupName = taskInfo.groupName;

      if (taskInfo.questionAnswers && taskInfo.questionAnswers.length > 0) {
        loadQuestionData(0);
      }
    } else {
      showToast(res?.result?.message);
    }
  } catch (error) {
    console.error("获取找货任务信息失败:", error);
  } finally {
    pageData.isLoading = false;
  }
}

// 加载问题数据
function loadQuestionData(questionIndex: number) {
  if (!pageData.taskInfo?.questionAnswers?.[questionIndex]) {
    return;
  }

  const questionData = pageData.taskInfo.questionAnswers[questionIndex];
  pageData.currentQuestion = questionData;
  pageData.currentQuestionIndex = questionIndex;
  pageData.questionId = questionData.questionId;
  pageData.questionImageUrl = questionData.questionImageUrl;
  pageData.questionSpecDesc = questionData.questionSpecDesc;
  pageData.answerVersion = questionData.answerVersion || null;

  // 重置商品列表为默认值（清空上一个问题的数据）
  pageData.foundGoodsList = [
    createDefaultFoundGoods(1),
    createDefaultFoundGoods(2),
  ];

  // 加载已有的回答数据
  if (questionData.foundGoodsList && questionData.foundGoodsList.length > 0) {
    questionData.foundGoodsList.forEach((foundGoods: any, index: number) => {
      // 确保 foundGoodsList 有足够的长度
      while (index >= pageData.foundGoodsList.length) {
        pageData.foundGoodsList.push(
          createDefaultFoundGoods(pageData.foundGoodsList.length + 1)
        );
      }

      pageData.foundGoodsList[index] = {
        answerSeq: foundGoods.answerSeq || index + 1,
        goodsImages: foundGoods.goodsImages || [],
        goodsPrice: foundGoods.goodsPrice || null,
        packingCount: foundGoods.packingCount || null,
        goodsVolume: foundGoods.goodsVolume || null,
        minBuyQuantity: foundGoods.minBuyQuantity || null,
        recommendReason: foundGoods.recommendReason || "",
        findSource: foundGoods.findSource || null,
        proofImages: foundGoods.proofImages || [],
      };
    });
  }
}

// 切换问题
function switchNextQuestion(questionIndex: number) {
  loadQuestionData(questionIndex);
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}

// 切换到指定问题
async function switchToQuestion(questionIndex: number) {
  if (questionIndex === pageData.currentQuestionIndex) {
    return; // 已经是当前问题，无需切换
  }

  // 使用通用的上传状态检查和等待函数
  await checkAndWaitForUploads(async () => {
    // 先保存当前问题的数据到后端
    if (pageData.currentQuestionIndex >= 0) {
      await saveCurrentQuestionWithoutNavigation();
    }

    // 在切换前获取最新任务信息，确保切换到的问题数据是最新的
    await refreshTaskInfo();

    // 切换到新问题
    switchNextQuestion(questionIndex);
  });
}

// 保存当前问题的数据到taskInfo中
function saveCurrentQuestionData() {
  if (!pageData.taskInfo?.questionAnswers?.[pageData.currentQuestionIndex]) {
    return;
  }

  const currentQuestion =
    pageData.taskInfo.questionAnswers[pageData.currentQuestionIndex];
  currentQuestion.foundGoodsList = pageData.foundGoodsList.map(
    (item: any, index: number) => ({
      answerSeq: index + 1,
      goodsImages: item.goodsImages || [],
      goodsPrice: item.goodsPrice || null,
      packingCount: item.packingCount || null,
      goodsVolume: item.goodsVolume || null,
      minBuyQuantity: item.minBuyQuantity || null,
      recommendReason: item.recommendReason || "",
      findSource: item.findSource || null,
      proofImages: item.proofImages || [],
    })
  );
}

// 判断当前问题是否有任何有效数据（用户是否填写了任何字段）
function hasValidData(): boolean {
  if (!pageData.foundGoodsList || pageData.foundGoodsList.length === 0) {
    return false;
  }

  // 检查是否有任何商品包含有效数据
  return pageData.foundGoodsList.some((goods: any) => {
    return (
      goods.goodsImages?.length > 0 ||
      (goods.goodsPrice && goods.goodsPrice > 0) ||
      (goods.packingCount && goods.packingCount > 0) ||
      (goods.goodsVolume && goods.goodsVolume > 0) ||
      (goods.minBuyQuantity && goods.minBuyQuantity > 0) ||
      (goods.recommendReason && goods.recommendReason.trim()) ||
      goods.findSource ||
      goods.proofImages?.length > 0
    );
  });
}

// 判断问题是否完成
function isQuestionCompleted(questionIndex: number): boolean {
  if (!pageData.taskInfo?.questionAnswers?.[questionIndex]) {
    return false;
  }

  const question = pageData.taskInfo.questionAnswers[questionIndex];
  const foundGoodsList = question.foundGoodsList || [];

  // 如果没有商品，则未完成
  if (foundGoodsList.length === 0) {
    return false;
  }

  // 所有商品都必须完整填写
  return foundGoodsList.every((goods: any) => {
    return (
      goods.goodsImages?.length > 0 &&
      goods.goodsPrice > 0 &&
      goods.packingCount > 0 &&
      goods.goodsVolume > 0 &&
      goods.minBuyQuantity > 0 &&
      goods.recommendReason?.trim() &&
      goods.findSource &&
      goods.proofImages?.length > 0
    );
  });
}

// 检查所有问题是否都完成
function areAllQuestionsCompleted(): boolean {
  if (!pageData.taskInfo?.questionAnswers) {
    return false;
  }

  // 检查所有问题是否都完成
  for (let i = 0; i < pageData.taskInfo.questionAnswers.length; i++) {
    if (!isQuestionCompleted(i)) {
      return false;
    }
  }
  return true;
}

// 获取下一个未完成的问题索引
function getNextIncompleteQuestionIndex(): number {
  if (!pageData.taskInfo?.questionAnswers) {
    return -1;
  }

  const totalQuestions = pageData.taskInfo.questionAnswers.length;

  // 首先从当前问题的下一个开始查找
  for (let i = pageData.currentQuestionIndex + 1; i < totalQuestions; i++) {
    if (!isQuestionCompleted(i)) {
      return i;
    }
  }

  // 如果后面没有未完成的问题，从头开始查找（但跳过当前问题）
  for (let i = 0; i < pageData.currentQuestionIndex; i++) {
    if (!isQuestionCompleted(i)) {
      return i;
    }
  }

  return -1;
}

// 获取提交按钮文字
function getSubmitButtonText(): string {
  // 只有当所有问题都完成时才显示"Guardar y completar todo"
  if (areAllQuestionsCompleted()) {
    return "Guardar y completar todo";
  } else {
    // 否则显示"Guardar y completar el siguiente"
    return "Guardar y completar el siguiente";
  }
}

// 文件上传处理方法
function onFileUpload(val: any, index: number) {
  pageData.foundGoodsList[index].goodsImages = val
    .map((file: any) => (typeof file === "string" ? file : file.fileUrl))
    .filter((url: string) => url);

  // 保存到当前问题数据中
  saveCurrentQuestionData();
  // 自动暂存数据
  tempStoreData();
}

// 证明图片上传处理方法
function onProofImagesUpload(val: any, index: number) {
  pageData.foundGoodsList[index].proofImages = val
    .map((file: any) => (typeof file === "string" ? file : file.fileUrl))
    .filter((url: string) => url);

  // 保存到当前问题数据中
  saveCurrentQuestionData();
  // 自动暂存数据
  tempStoreData();
}

// 暂存数据
async function tempStoreData() {
  if (!pageData.groupId || !pageData.questionId) {
    return;
  }

  // 先保存到当前问题数据中
  saveCurrentQuestionData();

  try {
    const foundGoodsList = pageData.foundGoodsList.map(
      (item: any, index: number) => ({
        answerSeq: index + 1, // 回答次序号（第1个商品则为1，第2个商品则为2）
        goodsImages: item.goodsImages || [],
        goodsPrice: item.goodsPrice || null,
        packingCount: item.packingCount || null,
        goodsVolume: item.goodsVolume || null,
        minBuyQuantity: item.minBuyQuantity || null,
        recommendReason: item.recommendReason || "",
        findSource: item.findSource,
        proofImages: item.proofImages || [],
      })
    );

    await useTempStoreFindGoods({
      groupId: pageData.groupId,
      questionId: pageData.questionId,
      answerVersion: pageData.answerVersion,
      foundGoodsList: foundGoodsList,
    });
  } catch (error) {}
}

// 获取最新任务信息
async function refreshTaskInfo() {
  try {
    const res: any = await useGetDetailByGroupId({
      id: pageData.groupId,
    });

    if (res?.result?.code === 200 && res?.data) {
      // 更新任务信息
      pageData.taskInfo = res.data;

      // 重新加载当前问题的数据（保持当前问题索引不变）
      if (pageData.currentQuestionIndex >= 0) {
        loadQuestionData(pageData.currentQuestionIndex);
      }
      return true; // 返回成功标识
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

// 保存当前问题但不跳转（用于图片切换时）
async function saveCurrentQuestionWithoutNavigation() {
  try {
    // 先保存到本地数据结构中
    saveCurrentQuestionData();

    // 检查是否有有效数据，没有有效数据时不调用后端API
    if (!hasValidData()) {
      // 即使没有数据也要获取最新任务信息
      await refreshTaskInfo();
      return;
    }

    // 构建当前问题的数据
    const currentQuestion =
      pageData.taskInfo.questionAnswers[pageData.currentQuestionIndex];
    const requestData = {
      groupId: String(pageData.groupId),
      questionId: String(currentQuestion.questionId),
      answerVersion: pageData.answerVersion || null,
      foundGoodsList: pageData.foundGoodsList.map(
        (goods: any, index: number) => ({
          answerSeq: goods.answerSeq || index + 1,
          goodsImages: goods.goodsImages || [],
          goodsPrice: goods.goodsPrice || null,
          packingCount: goods.packingCount || null,
          goodsVolume: goods.goodsVolume || null,
          minBuyQuantity: goods.minBuyQuantity || null,
          recommendReason: goods.recommendReason || "",
          findSource: goods.findSource,
          proofImages: goods.proofImages || [],
        })
      ),
    };

    const res: any = await useSaveFindGoodsOnce(requestData);

    // 保存成功后，更新任务信息
    if (res?.result?.code === 200 && res?.data) {
      pageData.taskInfo = res.data;

      // 重新加载当前问题的数据
      if (pageData.currentQuestionIndex >= 0) {
        loadQuestionData(pageData.currentQuestionIndex);
      }
    }
  } catch (error) {
    console.warn("图片切换时保存出错:", error);
  }
}

async function onSubmitGoodsLooking() {
  if (pageData.isSubmitLoading) return;

  // 使用通用的上传状态检查和等待函数
  await checkAndWaitForUploads(async () => {
    saveCurrentQuestionData();
    await saveCurrentQuestionOnce();
  });
}

// 单次保存当前问题并跳转到下一个问题
async function saveCurrentQuestionOnce() {
  try {
    pageData.isSubmitLoading = true;

    // 检查是否有有效数据
    const hasData = hasValidData();
    let latestTaskInfo = null;

    if (hasData) {
      // 有有效数据时才调用后端保存接口
      const currentQuestion =
        pageData.taskInfo.questionAnswers[pageData.currentQuestionIndex];
      const requestData = {
        groupId: String(pageData.groupId),
        questionId: String(currentQuestion.questionId),
        answerVersion: pageData.answerVersion || null,
        foundGoodsList: pageData.foundGoodsList.map(
          (goods: any, index: number) => ({
            answerSeq: goods.answerSeq || index + 1,
            goodsImages: goods.goodsImages || [],
            goodsPrice: goods.goodsPrice || null,
            packingCount: goods.packingCount || null,
            goodsVolume: goods.goodsVolume || null,
            minBuyQuantity: goods.minBuyQuantity || null,
            recommendReason: goods.recommendReason || "",
            findSource: goods.findSource,
            proofImages: goods.proofImages || [],
          })
        ),
      };

      // 调用单次保存接口
      const res: any = await useSaveFindGoodsOnce(requestData);

      if (res?.result?.code !== 200) {
        showToast(res?.result?.message);
        return;
      }

      // 保存成功后，获取最新的任务信息
      if (res?.data) {
        latestTaskInfo = res.data;
        pageData.taskInfo = latestTaskInfo;
      }
    } else {
      // 没有数据时，也要获取最新任务信息
      await refreshTaskInfo();
      latestTaskInfo = pageData.taskInfo;
    }

    // 无论是否有数据，都执行跳转逻辑
    // 检查是否所有问题都完成了
    if (areAllQuestionsCompleted()) {
      await navigateTo("/h5/comprar/success");
    } else {
      // 查找下一个未完成的问题
      const nextIncompleteIndex = getNextIncompleteQuestionIndex();
      if (nextIncompleteIndex !== -1) {
        // 跳转到下一个未完成的问题
        switchNextQuestion(nextIncompleteIndex);
      }
    }
  } catch (error) {
    showToast(error);
  } finally {
    pageData.isSubmitLoading = false;
  }
}

// 滚动监听函数
function handleScroll() {
  if (!stickyHeaderRef.value) return;

  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  isHeaderSticky.value = scrollTop > stickyThreshold.value;
}

// 初始化吸顶效果
function initStickyHeader() {
  if (!stickyHeaderRef.value) return;

  // 获取元素初始位置
  const rect = stickyHeaderRef.value.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  stickyThreshold.value = rect.top + scrollTop;

  // 添加滚动监听
  window.addEventListener("scroll", handleScroll, { passive: true });
}

// 组件挂载时初始化
onMounted(() => {
  nextTick(() => {
    initStickyHeader();
  });
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<style scoped lang="scss">
.mobile-container {
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  font-size: 0.28rem;
  line-height: 0.4rem;
  min-height: 100vh;
}

.submit-btn {
  width: 100%;
  height: 50px;
  text-align: center;
  font-size: 0.32rem;
  line-height: 50px;
  font-weight: 500;
  border-radius: 6px;
}
</style>
