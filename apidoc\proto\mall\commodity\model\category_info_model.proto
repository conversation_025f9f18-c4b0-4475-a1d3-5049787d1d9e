syntax = "proto3";
package mall.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "common/business.proto";
import "mall/commodity/commodity_common.proto";

message GetCategoryInfoResp {
    common.Result result = 1;
    CategoryInfoModel data = 2;
}

message CategoryInfoModel {
    string id = 10; //分类ID
    string categoryName = 20; //分类名称
    bool enabled = 30;
}