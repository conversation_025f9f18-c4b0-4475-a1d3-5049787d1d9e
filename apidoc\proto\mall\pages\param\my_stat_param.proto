syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.param";

import "common.proto";
import "common/business.proto";

message PageClickIncomingParam {
    string method = 10; //请求方法（GET/POST）
    string scheme = 20; //协议（http 或 https）
    string host = 30; //主机
    string requestUri = 40; //请求URI（对应Nuxt中的fullPath）
    string abtestPage = 50; //AB测试的页面代码（仅当abtestPage非空时返回前端页面选择字段：abtestMode；页面代码的编码参考为“页面类型+年月”
}

message PageClickDataParam {
    string clickData = 10; //点击数据字符串
    string event = 20; //事件
    int64 jsTime = 30; //JS时间戳
    int32 jsZone = 35; //JS时区（单位：小时）
    int32 docLoad = 40; //DocLoad触发时间
    int32 jsLoad = 45; //JsLoad触发时间
    int32 winLoad = 50; //WinLoad触发时间
    int32 activeTime = 60; //有效在线时间
    int32 totalTime = 70; //总在线时间
    int32 activeFixTime = 75; //有效在线补差时间（起止时间-定时器）
    int32 zoom = 80; //屏幕缩放百分比
    string screen = 90; //屏幕大小
    string window = 100; //窗口大小
    string document = 105; //文档大小
    string pageEvents = 110; //页面内事件埋点
}

