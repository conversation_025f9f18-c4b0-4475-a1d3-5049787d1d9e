syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";
import "chilat/basis/model/goods_model.proto";
import "chilat/basis/param/goods_param.proto";
import "chilat/basis/param/milvus_param.proto";

// 商品服务
service GoodsRpc {
    // 查询商品详情（参数：goodsId）
    rpc getGoodsInfo (MidGoodsInfoQueryParam) returns (MidGoodsInfoResp);
    // 查询商城中的商品详情（参数：goodsId）
    rpc getGoodsInfoForMall (MidGoodsInfoQueryParam) returns (MidGoodsInfoResp);
    // 根据 SKU ID 查询商品详情（参数：skuId）
    rpc getGoodsBySkuId (MidGoodsInfoQueryParam) returns (MidGoodsInfoResp);
    // 根据 商品编号（goodsNo）或SKU编号（skuNo） 查询商品详情（参数：skuId）
    rpc getGoodsByGoodsOrSkuNo (MidGoodsInfoQueryParam) returns (MidGoodsInfoResp);
    // 查询商品分类（默认为 ROOT 节点）
    rpc getCategoryTree (common.IdParam) returns (MidCategoryTreeResp);
    // 查询商品分类权重分（参数为商品分类ID，默认返回0）
    rpc getCategoryRank(common.IdParam) returns (common.IntResult);
    // 查询本级及下级商品分类ID（类目ID空值，返回null，ROOT分类返回null）
    rpc getOwnerCategoryIds (common.IdParam) returns (common.IdsResult);
    // 根据商品ID，获取多个库存信息
    rpc getMultiStockByGoods (common.IdsParam) returns (MidGoodsStockSummaryResp);
    // 根据SKU ID，获取多个库存信息
    rpc getMultiStockBySku (common.IdsParam) returns (MidGoodsStockSummaryResp);
//    // 根据营销规则查询商品信息
//    rpc pageListGoodsByRule(MidGoodsPageQueryParamByRule) returns (MidGoodsPageResp);
    // 商品搜索接口
    rpc searchGoods(MidSearchGoodsParam) returns (MidSearchResultResp);
    // 重建商品的ES索引
    rpc recreateESIndex(common.EmptyParam) returns (RecreateESIndexResp);
    // 更新全部商品的ES索引（删除未被更新的ES商品）
    rpc updateAllGoodsESIndex(common.EmptyParam) returns (UpdateAllGoodsESIndexResp);
    // 更新多个商品的ES索引
    rpc updateGoodsESIndex(common.IdsParam) returns (common.IntResult);
    // 计算阶梯价（返回值NotNull，已失效的skuId不返回）
    rpc calcStepPrice(MidCalcStepPriceParam) returns (MidCalcStepPriceResp);
    // 添加商品展示事件
    rpc addGoodsImpress(MidAddGoodsImpressParam) returns (common.ApiResult);
    // 设置商品展示的转化
    rpc setImpressConversion(MidSetImpressConversionParam) returns (common.ApiResult);
    // 推荐商品
    rpc recommendGoods(common.EmptyParam) returns (MidRecommendGoodsResp);
    // 首页商品
    rpc homePageGoods(common.EmptyParam) returns (HomePageGoodsResp);
    // 人民币转美元
    rpc convertCNY2USD(common.DoubleParam) returns (common.DoubleResult);
    // 美元转人民币
    rpc convertUSD2CNY(common.DoubleParam) returns (common.DoubleResult);
    // pc首页商品
    rpc pcHomePageGoods(common.EmptyParam) returns (PcHomePageGoodsResp);
    // pc首页分类
    rpc homePageCategory(common.EmptyParam) returns (HomePageCategoryResp);
    // 推荐商品v2,包含义乌热销和美客多热销
    rpc recommendGoodsV2(MidRecommendGoodsParam) returns (MidRecommendGoodsV2Resp);
    // 专属用户的推荐商品
    rpc recommendCustomerGoods(MidRecommendCustomerGoodsParam) returns (MidRecommendCustomerGoodsResp);

    rpc searchGoodsBy1688(MidSearchGoodsParam) returns (MidSearchResultResp);

    rpc searchGoodsByEmbedding(MidSearchGoodsParam) returns (MidSearchResultResp);

    //更新全部商品的向量
    rpc updateAllGoodsVector(UpdateAllGoodsVectorParam) returns (basis.UpdateAllGoodsVectorResp);

    //更新指定商品的向量
    rpc updateGoodsVector(UpdateGoodsVectorParam) returns (common.LongResult);

    rpc goodsVectorStat(GoodsVectorStatParam) returns (GoodsVectorStatResp);
}