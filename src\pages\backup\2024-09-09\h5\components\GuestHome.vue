<template>
  <div class="page-container">
    <div>
      <img loading="lazy" :src="headerBg" width="100%" />
      <div class="news-wrapper" data-spm-box="homepage-welcome-bar">
        <div
          v-for="(news, index) in newsData"
          :key="index"
          class="news-item"
          :style="`background-color: ${cateColorArr[index].cateColor}`"
        >
          <a class="flex justify-center items-center" :href="news.path">
            <div>{{ news.title }}</div>
            <icon-card :name="news.icon" size="30" color="#FFF"></icon-card
          ></a>
        </div>
      </div>
    </div>

    <div class="nav-wrapper">
      <div
        class="nav-item"
        v-for="(nav, index) in navData"
        :key="index"
        @click="scrollToNav(nav)"
        :click-title="`${index + 1}. ${nav.title} `"
      >
        <div class="icon-container">
          <icon-card :name="nav.icon" size="0.44rem"></icon-card>
        </div>
        <div class="nav-title">{{ nav.title }}</div>
        <div class="content-container">
          {{ nav.content }}
        </div>
      </div>
    </div>

    <div
      class="category-wrapper"
      id="NavItem1"
      data-spm-box="homepage-top-categories"
    >
      <div class="w-full">
        <div class="category-title">
          {{ authStore.i18n("cm_guestHome.categoryTitle") }}
        </div>
        <div class="category-number">
          <div
            class="number-item"
            v-for="(item, index) in categoryIntroData"
            :key="index"
          >
            <span>{{ item.number }}</span>
            <p>
              {{ item.title }}
            </p>
          </div>
        </div>
        <div class="category-list">
          <div
            class="category-row"
            v-for="(cate, index) in pageData.categoryData"
            :key="index"
          >
            <a
              v-for="cateItem in cate"
              :key="cateItem.id"
              v-bind:data-spm-index="index + 1"
              :href="`/h5/search/list?categoryId=${cateItem.id}&cateName=${cateItem.name}`"
              ><div class="category-item">
                <n-image
                  lazy
                  preview-disabled
                  :src="cateItem.cateLogo"
                  class="img"
                />
                <div class="category-name">
                  <span>{{ cateItem.name }}</span>
                </div>
              </div></a
            >
          </div>
        </div>
      </div>
    </div>

    <div class="simplify-purchase" id="NavItem2">
      <img loading="lazy" :src="purchaseImg" class="w-[3rem]" />
      <div class="purchase-title">
        Ahórrese los tediosos y variados pasos,mejore la eficacia de sus
        compras.
      </div>
    </div>

    <div
      class="security-floor"
      id="NavItem3"
      :style="`background-image: url(${securityBg})`"
    >
      <div class="security-floor-title">
        {{ authStore.i18n("cm_guestHome.securityTitle") }}
      </div>
      <div>
        <div
          class="security-floor-item"
          v-for="(item, index) in securityData"
          :key="index"
        >
          <div class="flex items-center">
            <img loading="lazy" class="floor-item-icon" :src="item.icon" />
            <div class="floor-item-title">
              {{ item.title }}
            </div>
          </div>
          <div class="floor-item-desc">
            {{ item.desc }}
          </div>
        </div>
      </div>
    </div>

    <div id="NavItem4" class="full-link">
      <div class="full-link_title">
        {{ authStore.i18n("cm_guestHome.fullLinkTitle") }}
      </div>
      <ul class="full-link_item_wrapper">
        <li
          class="full-link_item"
          v-for="(link, index) in fullLinkData"
          :key="index"
          @mouseenter="onLinkHover(index)"
          :class="{
            'full-link_item_enter': pageData.activatedLinkIndex == index,
          }"
          :click-title="`${index + 1}. ${link.title} `"
        >
          <div class="full-link_icon_wrapper">
            <n-image
              lazy
              preview-disabled
              class="full-link_icon"
              :src="
                pageData.activatedLinkIndex == index
                  ? link.iconActive
                  : link.icon
              "
            />
          </div>
          <div class="full-link_content">
            <div class="full-link_item_title" title="Search for matches">
              {{ link.title }}
            </div>
            <div class="full-link_item_desc">
              {{ link.desc }}
            </div>
          </div>
        </li>

        <div class="full-link_item_tail"></div>
      </ul>
    </div>

    <!-- 美客多和义乌推荐 -->
    <div
      v-if="props.mercadoHotSaleGoods?.goodsList?.length > 0"
      class="mt-[0.48rem]"
      data-spm-box="homepage-hot-mercado"
    >
      <n-image
        lazy
        preview-disabled
        class="carousel-img pb-1"
        :src="props.mercadoHotSaleGoods?.banner"
        @click="onMercadoHotViewMore(props.mercadoHotSaleGoods, $event)"
      />
      <category-card
        :moreButton="true"
        :cateInfo="props.mercadoHotSaleGoods"
        data-spm-box="homepage-hot-mercado"
      ></category-card>
    </div>
    <div
      v-if="props.yiwuHotSaleGoods?.goodsList?.length > 0"
      data-spm-box="homepage-hot-yiwu"
    >
      <n-image
        lazy
        preview-disabled
        class="carousel-img pb-1"
        :src="props.yiwuHotSaleGoods.banner"
        @click="onYiWuHotViewMore(props.yiwuHotSaleGoods, $event)"
      />
      <category-card
        :moreButton="true"
        :cateInfo="props.yiwuHotSaleGoods"
        data-spm-box="homepage-hot-yiwu"
      ></category-card>
    </div>

    <div class="login-guide" :style="`background-image: url(${loginBg})`">
      <div class="login-title">¿Listo para empezar?</div>
      <div class="login-desc">
        ¡Explore millones de productos de proveedores confiables hoy!
      </div>
      <a
        href="/h5/user/login?pageSource=/h5"
        data-spm-box="homepage-body-login"
      >
        <n-button color="#db2221" class="section_banner-button">
          Iniciar sesión
        </n-button>
      </a>
    </div>
    <div class="user-video">
      <div class="video-title">CONOZCA LAS PERSONAS QUE ELIGIERON CHILAT</div>
      <div class="video-wrapper">
        <div
          class="video-item"
          v-for="(video, index) in userVideoData"
          :key="video.id"
          @click="onOpenVideo(video.id, index)"
        >
          <n-image lazy preview-disabled :src="video.poster" class="img" />
          <div class="video-icon">
            <icon-card
              name="mingcute:play-fill"
              size="20"
              color="#322623"
            ></icon-card>
          </div>
        </div>
      </div>
    </div>
    <video-modal ref="videoModalRef"></video-modal>
  </div>
</template>

<script setup lang="ts">
import CategoryCard from "./CategoryCard.vue";
import { useAuthStore } from "@/stores/authStore";
import headerBg from "@/assets/icons/guestMHome.jpg";
import searchIcon from "@/assets/icons/search.png";
import searchActIcon from "@/assets/icons/search-active.png";
import cartIcon from "@/assets/icons/cart.png";
import cartActIcon from "@/assets/icons/cart-active.png";
import moneyIcon from "@/assets/icons/money.png";
import moneyActIcon from "@/assets/icons/money-active.png";
import boxIcon from "@/assets/icons/box.png";
import boxActIcon from "@/assets/icons/box-active.png";
import userIcon from "@/assets/icons/user.png";
import userActIcon from "@/assets/icons/user-active.png";

const props = defineProps({
  yiwuHotSaleGoods: {
    type: Array,
    default: () => <any>[],
  },
  mercadoHotSaleGoods: {
    type: Array,
    default: () => <any>[],
  },
});

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const videoModalRef = ref<any>(null);
const pageData = reactive(<any>{
  categoryData: <any>[],
  activatedLinkIndex: 0,
});

const purchaseImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/4cf7e50f-43d1-4daf-bd90-a589aca10a7c.png";
const securityBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/b9cb4cb8-7189-4a3e-8241-10e39bc0fafa.png";
const loginBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/31/4d30911f-d27e-4b7d-a0ab-2e9281569117.png";

const newsData = [
  {
    icon: "mdi:information-variant-circle",
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/h5/article/about-us",
  },
  {
    icon: "mdi:compass",
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/h5/article/quick-guide",
  },
  {
    icon: "material-symbols:help",
    title: authStore.i18n("cm_news.askedQuestions"),
    path: `/h5/article/frequently-questions`,
  },
  // {
  //   icon: "material-symbols:security",
  //   title: authStore.i18n("cm_news.warrantyService"),
  //   path: "/h5/article?code=10002",
  // },
  {
    icon: "f7:money-dollar-circle-fill",
    title: authStore.i18n("cm_news.commission"),
    path: "/h5/article/commission",
  },
];

const cateColorArr = reactive(<any>[
  {
    textColor: "#FFF",
    cateColor: "#ff3242",
  },
  {
    textColor: "#FFF",
    cateColor: "#ff5e6a",
  },
  {
    textColor: "#FFF",
    cateColor: "#ff7659",
  },
  {
    textColor: "#FFF",
    cateColor: "#ffae42",
  },
  {
    textColor: "#FFF",
    cateColor: "#ffd700",
  },
  {
    textColor: "#FFF",
    cateColor: "#7a7687",
  },
  {
    textColor: "#FFF",
    cateColor: "#9ec8c7",
  },
]);

const navData = [
  {
    navId: "NavItem1",
    icon: "iconamoon:category",
    title: authStore.i18n("cm_guestHome.navTitle1"),
    content: authStore.i18n("cm_guestHome.navContent1"),
  },
  {
    navId: "NavItem2",
    icon: "bx:dollar",
    title: authStore.i18n("cm_guestHome.navTitle2"),
    content: authStore.i18n("cm_guestHome.navContent2"),
  },
  {
    navId: "NavItem3",
    icon: "clarity:shield-check-line",
    title: authStore.i18n("cm_guestHome.navTitle3"),
    content: authStore.i18n("cm_guestHome.navContent3"),
  },
  {
    navId: "NavItem4",
    icon: "iconamoon:like-light",
    title: authStore.i18n("cm_guestHome.navTitle4"),
    content: authStore.i18n("cm_guestHome.navContent4"),
  },
];

const categoryIntroData = [
  {
    number: authStore.i18n("cm_guestHome.number1"),
    title: authStore.i18n("cm_guestHome.numberDesc1"),
  },
  {
    number: authStore.i18n("cm_guestHome.number2"),
    title: authStore.i18n("cm_guestHome.numberDesc2"),
  },
];

const securityData = [
  {
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/3447a23e-9bb7-4824-a978-97ee506721b8.png",
    title: authStore.i18n("cm_guestHome.securityQual"),
    desc: authStore.i18n("cm_guestHome.securityQualDesc"),
  },
  {
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/001a3c9b-c5b1-42be-8eb9-ca6eb718d918.png",
    title: authStore.i18n("cm_guestHome.securityPay"),
    desc: authStore.i18n("cm_guestHome.securityPayDesc"),
  },
];

const fullLinkData = [
  {
    icon: searchIcon,
    iconActive: searchActIcon,
    title: authStore.i18n("cm_guestHome.linkProducts"),
    desc: authStore.i18n("cm_guestHome.linkProductsDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/c11a9189-8e3a-4d34-92a3-834f072acf0f.png",
  },
  {
    icon: cartIcon,
    iconActive: cartActIcon,
    title: authStore.i18n("cm_guestHome.linkOrder"),
    desc: authStore.i18n("cm_guestHome.linkOrderDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/0ce0487b-35a4-4bf2-9ae9-a06c36c9571a.png",
  },
  {
    icon: moneyIcon,
    iconActive: moneyActIcon,
    title: authStore.i18n("cm_guestHome.linkPay"),
    desc: authStore.i18n("cm_guestHome.linkPayDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/3e8d485d-88f6-43a9-8985-0610e628ead9.png",
  },
  {
    icon: boxIcon,
    iconActive: boxActIcon,
    title: authStore.i18n("cm_guestHome.linkTrans"),
    desc: authStore.i18n("cm_guestHome.linkTransDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/ceb67233-155d-4d1d-94c2-5ccbba29c8ad.png",
  },
  {
    icon: userIcon,
    iconActive: userActIcon,
    title: authStore.i18n("cm_guestHome.linkOrgan"),
    desc: authStore.i18n("cm_guestHome.linkOrganDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/24e1c475-75f9-44ef-9025-87046d08e648.png",
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
  },
  {
    id: "Tj0nrnhxgXw",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
  },
  {
    id: "_omi5a-pHkA",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
  },
  {
    id: "4FVIz0PvEcE",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
  },
];

onPageCategoryData();
onMounted(() => {
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
});

function scrollToNav(nav: any) {
  if (!nav.navId) return;
  const element = document.getElementById(nav.navId);
  if (!element) return;
  const additionalOffset = 2.4 * 50; //固定头部搜索栏的高度
  const elementRect = element.getBoundingClientRect();
  const offsetPosition = elementRect.top + window.scrollY - additionalOffset;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth",
  });
}

async function onPageCategoryData() {
  const res: any = await useHomePageCategory({});
  if (res?.result?.code === 200) {
    pageData.categoryData = organizeDataByColumns(res?.data);
  }
}

function organizeDataByColumns(data: any[]) {
  const numRows = 2;
  const organizedData = [];
  for (let i = 0; i < data.length; i += numRows) {
    const column = data.slice(i, i + numRows);
    organizedData.push(column);
  }
  return organizedData;
}

const onLinkHover = (index: any) => {
  pageData.activatedLinkIndex = index;
};

function onMercadoHotViewMore(item: any, event: any) {
  navigateToPage(
    "/h5/search/list",
    { tagId: item.tagId, tag: "mercado" },
    false,
    event
  );
}

function onYiWuHotViewMore(item: any, event: any) {
  navigateToPage(
    "/h5/search/list",
    { tagId: item.tagId, tag: "yiwu" },
    false,
    event
  );
}

function onOpenVideo(youtubeId: any, index: any, title?: any) {
  // if (videoModalRef.value) {
  //   window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
  //   videoModalRef.value.onOpenVideo(youtubeId, title);
  // }
}
</script>
<style scoped lang="scss">
.page-container {
  height: 100%;
  overflow-y: auto;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-wrapper {
  display: flex;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  padding-left: 0.26667rem;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  margin-top: 0.48rem;
  &::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }
  .news-item {
    display: inline-block;
    height: 1.28rem;
    border-radius: 0.08rem;
    margin-right: 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0.2rem;
    div {
      width: 1.62rem;
      height: 0.64rem;
      font-size: 0.26rem;
      font-weight: 500;
      color: #fff;
      line-height: 0.4rem;
      white-space: normal;
    }
  }
}
.nav-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin: 0.48rem auto;
  padding-left: 0.26667rem;
  color: #fff;
  overflow-x: scroll;
  overflow-y: hidden;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .nav-item {
    background-color: #3a190b;
    border-radius: 0.16rem;
    display: block;
    text-decoration: none;
    padding: 0.32rem;
    margin-right: 0.2rem;
    &:hover {
      cursor: pointer;
    }
    .icon-container {
      background-color: hsla(0, 0%, 100%, 0.08);
      border-radius: 0.6rem;
      height: 0.72rem;
      padding: 0.04rem;
      width: 0.72rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .nav-title {
      color: #fff;
      font-size: 0.36rem;
      letter-spacing: 0;
      line-height: 0.52rem;
      margin: 0.2rem 0;
      width: 4.76rem;
      font-weight: 500;
      min-height: 1rem;
    }
    .content-container {
      color: #fff;
      font-size: 0.26rem;
      height: auto;
      letter-spacing: 0;
      line-height: 0.52rem;
      min-height: 0.8rem;
    }
  }
}

.category-wrapper {
  width: 100%;
  padding: 0.6rem 0.32rem;
  background-color: #fff;
  .category-title {
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #222;
  }
  .category-number {
    display: grid;
    row-gap: 0.2rem;
    column-gap: 0.2rem;
    grid-template-columns: 1fr 1fr;
    height: fit-content;
    .number-item {
      padding-left: 0.32rem;
      position: relative;
      margin: 0.32rem 0;
      &::before {
        background-color: #ddd;
        border-radius: 0.04rem;
        content: "";
        display: inline-block;
        height: 100%;
        left: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 0.06rem;
      }
      span {
        color: #db2221;
        font-size: 0.32rem;
        font-weight: 500;
        letter-spacing: -0.73px;
        line-height: 0.6rem;
      }
      p {
        font-size: 0.24rem;
        line-height: 0.56rem;
      }
    }
  }
  .category-list {
    margin-top: 0.32rem;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }

    .category-row {
      height: 100%;
      a {
        color: inherit;
        text-decoration: inherit;
      }
      .category-item {
        align-items: center;
        border: 0.04rem solid #eaeaea;
        border-radius: 1.4rem;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        height: 1.84rem;
        justify-content: center;
        margin-bottom: 0.2rem;
        margin-right: 0.16rem;
        width: 1.84rem;
        &:hover {
          border: 0.04rem solid #db2221;
        }
        .img {
          display: initial;
          height: 0.4rem;
          vertical-align: middle;
          width: 0.4rem;
          padding: 0.04rem;
          box-sizing: content-box;
          margin-bottom: 0.04rem;
        }
        .category-name {
          height: 0.64rem;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            color: #222;
            display: -webkit-box;
            font-size: 0.24rem;
            font-weight: 400;
            line-height: 0.32rem;
            overflow: hidden;
            padding: 0 0.08rem;
            text-align: center;
            width: 1.4rem;
          }
        }
      }
    }
  }
}

.simplify-purchase {
  width: 100%;
  padding: 0.6rem 0.32rem;
  background-color: #f4f4f4;
  display: flex;
  align-items: center;
  .purchase-title {
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #222;
    margin-left: 0.24rem;
  }
}

.security-floor {
  width: 100%;
  background-color: #442b20;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 180% 100%;
  color: #fff;
  margin: auto;
  overflow: hidden;
  width: 100%;
  padding: 0.6rem 0.32rem;
  .security-floor-title {
    width: 100%;
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.4rem;
  }
  .security-floor-item {
    background: hsla(0, 0%, 100%, 0.11);
    border-radius: 0.16rem;
    font-size: 0.36rem;
    line-height: 0.48rem;
    overflow: hidden;
    padding: 0.32rem 0.16rem;
    position: relative;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    width: 100%;
    margin-bottom: 0.2rem;
    .floor-item-icon {
      display: block;
      height: 0.48rem;
      margin-right: 0.08rem;
    }
    .floor-item-title {
      font-size: 0.32rem;
      font-weight: 500;
      line-height: 0.48rem;
      margin-bottom: 0.12rem;
    }
    .floor-item-desc {
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 0.24rem;
      line-height: 0.36rem;
    }
  }
}

.full-link {
  width: 100%;
  color: #222;
  background-color: #fff;
  padding: 0.6rem 0.32rem;

  .full-link_title {
    -webkit-box-orient: vertical;
    color: #222;
    display: -webkit-box;
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
  }
  .full-link_item_wrapper {
    -webkit-flex-direction: column;
    flex-direction: column;
    margin-top: 0.6rem;
    position: relative;
    width: 100%;
    display: flex;
    .full-link_item {
      cursor: pointer;
      z-index: 1;
      display: flex;
    }
    .full-link_item_enter:not(:last-of-type) {
      margin-bottom: 0.08rem;
    }
    .full-link_item:not(:last-of-type) {
      margin-bottom: 0.32rem;
    }
    .full-link_item:not(:first-child) {
      margin-top: 0.32rem;
    }

    .full-link_icon_wrapper {
      align-items: center;
      background-color: #fff;
      border-radius: 0.96rem;
      -webkit-box-shadow: 0 0 0 0.16rem #fff;
      box-shadow: 0 0 0 0.16rem #fff;
      display: flex;
      justify-content: center;
      margin-inline-end: 0.28rem;
      position: relative;
      height: 0.72rem;

      width: 0.72rem;
      flex-shrink: 0;
    }
    .full-link_item_title {
      font-size: 0.28rem;
      line-height: 0.48rem;
    }
    .full-link_item_desc {
      color: #767676;
      display: none;
      font-size: 0.24rem;
      height: fit-content;
      line-height: 0.48rem;
      margin-top: 0.12rem;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .full-link_item_enter .full-link_icon_wrapper {
      border-width: 0;
      -webkit-box-shadow: 0 0 0 0.12rem #fff;
      box-shadow: 0 0 0 0.12rem #fff;
      transform: scale(1.2);
    }
    .full-link_item_enter .full-link_item_title {
      color: #51200b;
      font-size: 0.32rem;
      font-weight: 500;
      line-height: 0.48rem;
      margin-top: -0.1rem;
    }

    .full-link_item_enter .full-link_item_desc {
      display: -webkit-box;
    }
    .full-link_item_tail {
      background-color: #ddd;
      height: 100%;
      left: 0.4rem;
      position: absolute;
      width: 0.04rem;
    }
  }
  .full-link_item_enter:last-of-type ~ .full-link_item_tail {
    height: calc(100% - 1.32rem);
  }
}

.login-guide {
  width: 100%;
  align-items: center;
  background-color: #473229;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-align: center;
  width: 100%;
  color: #fff;
  padding: 0.6rem 0.32rem;

  .login-title {
    font-size: 0.4rem;
    font-weight: 500;
    line-height: 0.6rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .login-desc {
    font-size: 0.28rem;
    line-height: 0.4rem;
    margin-top: 0.2rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .section_banner-button {
    font-size: 0.32rem;
    line-height: 0.7rem;
    padding: 0.2rem 0.34rem;
    border-radius: 0.36rem;
    margin-top: 0.48rem;
    box-shadow: 0.06rem 0.06rem 0.1rem rgba(0, 0, 0, 0.3);
  }
}

.user-video {
  width: 100%;
  padding: 0.6rem 0.32rem;

  .video-title {
    font-size: 0.26rem;
    font-weight: 500;
    line-height: 0.6rem;
    text-align: center;
  }
  .video-wrapper {
    width: 100%;
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }
  .video-item {
    flex: 0 0 2.8rem;
    width: 2.8rem;
    margin: 0.2rem 0.2rem 0.2rem 0;
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      border-radius: 0.24rem;
    }
    .video-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #fff;
      width: 0.6rem;
      height: 0.6rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
