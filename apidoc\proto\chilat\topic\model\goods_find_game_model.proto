syntax = "proto3";
package chilat.topic;

option java_package = "com.chilat.rpc.topic.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";
import "common/topic.proto";

message GoodsFindGameListResp {
  common.Result result = 1;
  repeated GoodsFindGameModel data = 2;
}

message GoodsFindGameModel {
  string id = 10;
  repeated string imageUrls = 20; // 参考图片
  string url = 30; // 寻货链接
}

message GoodsFindGameDetailResp {
  common.Result result = 1;
  repeated GoodsFineGameQuestionModel data = 2;
}

message GoodsFineGameQuestionModel {
  string id = 10;
  string imageUrl = 20; // 图片url
  string specDesc = 30; // 参数-规格描述
}

message GoodsFindGameGroupListResp {
  common.Result result = 1;
  repeated GoodsFindGameGroupModel data = 2;
}

message GoodsFindGameGroupModel {
  string id = 10;
  string groupCode = 20; // 组号
  string startTime = 30; // 寻货开始时间
  string endTime = 40; // 寻货结束时间
  repeated GoodsFindGameAnswerModel answerList = 50;
}

message GoodsFindGameAnswerModel {
  string id = 10;
  string questionId = 20; // 问题ID
  string groupId = 30; // 组Id
  string groupCode = 40; // 组号
  repeated string goodsImages = 50; // 类似图片列表
  common.FindGoodsPurchaseSource findSource = 60; // 采购来源
}
