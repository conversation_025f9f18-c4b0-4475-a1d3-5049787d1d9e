<template>
  <div class="error-wrapper">
    <img
      alt="chilat"
      loading="lazy"
      class="error-image"
      src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/04/10/698a0418-31ed-49e9-b8fb-3ef4f14a0c5b.svg"
    />
    <div class="error-message">
      El sistema está en mantenimiento. Por favor, inténtelo de nuevo en cinco
      minutos.
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  error: Object,
});

const route = useRoute();
const { isMobile } = useDevice();

// 处理页面不存在错误
const onHandleNotFound = () => {
  if (isMobile) {
    navigateTo(`/h5/?notfound=${encodeURIComponent(route.fullPath)}`);
  } else {
    navigateTo(`/?notfound=${encodeURIComponent(route.fullPath)}`);
  }
};

// 根据错误类型进行不同处理
if (props.error?.statusCode === 404) {
  // 404错误 - 页面不存在
  onHandleNotFound();
}

onMounted(() => {
  const url = new URL(window.location.href);
  const reloadTimes = Number(url.searchParams.get("reload_times") || 0) + 1;
  url.searchParams.set("reload_times", String(reloadTimes));
  setTimeout(() => {
    window.location.href = url.toString();
  }, 60000);
});
</script>

<style scoped lang="scss">
.error-wrapper {
  min-height: 100vh;
  padding: 20vh 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-image {
  width: 100%;
  max-width: 580px;
  height: auto;
}

.error-message {
  font-size: 24px;
  line-height: 1.6;
  margin-top: 40px;
  color: #333;
}

@media (max-width: 1280px) {
  .error-image {
    max-width: 435px;
  }

  .error-message {
    font-size: 18px;
    line-height: 30px;
  }
}

@media (max-width: 750px) {
  .error-wrapper {
    max-width: 435px;
    margin: 0 auto;
    justify-content: center;
    padding-top: 0;
  }

  .error-message {
    font-size: 18px;
    line-height: 32px;
  }
}
</style>
