syntax = "proto3";
package mall.trade;

import "chilat/basis/param/sales_order_param.proto";

option java_package = "com.chilat.rpc.trade.param";



message GetFindGoodsInfoParam {
  string keyword = 10; //小组查找关键字（groupCode查找预处理：去除空白字符，西语字母转英语，英文数字大于6个字符时去除标点符号；若不存在，则自动创建；自动创建时，原输入转为groupName）
}

//快速下单参数
message FastCreateOrderParam {
  string siteId = 10; //站点ID（必填，当前购物车页面中的国家ID）
  chilat.basis.OrderAddressParam orderAddress = 20; //配送信息（必填）
  repeated CheckoutSkuParam skuList = 100; //结算商品列表（必填）

//  string countryId = 30; //支付国家ID（订单归属国家；若不传值，默认取收货地址中的国家）
//  repeated SalesOrderRouteFeeParam routeFeeList = 40; //线路总运费（仅当 isRouteFeeInBox=false 才可能需要传值）
//  repeated string goodsLookingNoList = 50; //关联询盘单号
//  string buyerRemark = 60; //客户备注（最大1000字）
//  string sellerRemark = 70; //卖家备注（最大1000字）
//  common.QuotationMode priceModel = 80; //报价模式
//  common.PayMode paymentModel = 90; //支付模式:0.一次性支付 100.首次支付只支付国内费用，二次支付待仓库收货之后再支付
//  OrderAddressParam orderAddress = 100; //配送信息
//  repeated SalesOrderBoxParam salesOrderBoxList= 110; //装箱信息
//  repeated SalesOrderSundryFeeParam salesOrderFeeList = 120; //费用信息
//  repeated SalesOrderLineParam soLineList = 130; //订单行信息
//  double commission = 140; //佣金
//  double goodsDiscountAmount = 150; //商品优惠金额（默认0，大于0表示享受了商品优惠）
//  bool isRouteFeeInBox = 160; //装箱信息中，是否包含线路运费（必填）
//  string salesManUserId = 170; //业务员Id

}

//结算商品信息
message CheckoutSkuParam {
  string skuId = 10; //SKU ID（必填）
  int32 quantity = 20; //购买数量（必填）
  string routeId = 30; //线路ID（必填，暂不支持多线路同时下单）
  string padc = 88; //促销活动动态代码
}

