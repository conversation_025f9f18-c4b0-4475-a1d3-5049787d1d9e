<template>
  <div class="page-container">
    <div class="w-[806px] mx-auto relative">
      <div class="w-full pt-[106px]">
        <img loading="lazy"
          alt="chilat"
          class="w-[160px] absolute left-0 top-[24px]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/18/ef9d46aa-1b6c-45c9-841c-e41089c3ee6d.png"
        />
        <div
          class="text-[32px] leading-[32px] font-semibold text-center text-[#fff] relative z-10"
        >
          {{ authStore.i18n("cm_nota.oneOnOneService") }}
        </div>
        <img loading="lazy"
          alt="chilat"
          class="w-[340px] absolute right-[-20px] top-0"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/15/8edbcaa4-7b80-434a-9fdd-384940a4a00e.png"
        />
      </div>
      <div
        class="mt-[30px] mx-[0.16rem] relative z-10 pt-[78px] bg-white h-[476px] rounded-[4px] flex flex-col items-center h-[60vh]"
      >
        <img loading="lazy"
          alt="paySuccess"
          src="@/assets/icons/order/paySuccess.svg"
          class="w-[52px] h-[52px] mx-auto mb-[24px]"
        />
        <span class="font-semibold text-[26px] leading-[26px] text-[#000] mb-[16px]"
          >{{ authStore.i18n("cm_nota.submitSuccess") }}
        </span>
        <div
          class="w-[540px] text-[#939393] text-[16px] leading-[24px] px-[12px] text-center"
        >
          <span
            v-if="pageData.serviceType === 'POTENTIAL_SERVICE_TYPE_VISIT_CHINA'"
          >
            {{ authStore.i18n("cm_nota.hello") }} {{ pageData.contactName }},
            {{ authStore.i18n("cm_nota.visitChinaTip1") }}
            ({{ pageData.whatsapp }})
            {{ authStore.i18n("cm_nota.visitChinaTip2") }}
          </span>
          <span
            v-if="
              pageData.serviceType ===
              'POTENTIAL_SERVICE_TYPE_CUSTOMIZE_PRODUCT'
            "
          >
            {{ authStore.i18n("cm_nota.hello") }} {{ pageData.contactName }},
            {{ authStore.i18n("cm_nota.customizeProductTip1") }}
            ({{ pageData.whatsapp }})
            {{ authStore.i18n("cm_nota.customizeProductTip2") }}
          </span>
          <template
            v-if="pageData.serviceType === 'POTENTIAL_SERVICE_TYPE_ONLINE_BUY'"
          >
            <div class="px-[16px]">
              {{ pageData.contactName }}
              {{ authStore.i18n("cm_nota.hello") }}
              ({{ pageData.whatsapp }}),
              <span
                v-html="authStore.i18n('cm_nota.onlineSelectionHtml')"
              ></span>
            </div>
            <div class="w-full border-t-1 border-[#BBB] mt-[26px] py-[28px]">
              <div v-if="pageData.timeLeft">
                <span
                  class="text-[22px] mr-[0.12rem] text-[#e50113] w-[14px] inline-block"
                  >{{ pageData.timeLeft }}</span
                >
                <span class="text-[16px] text-[#333]">{{
                  authStore.i18n("cm_goods.timeLeftTip")
                }}</span>
              </div>
              <a
                href="/"
                data-spm-box="potential_user_note_success"
                ref="homeButtonRef"
              >
                <n-button
                  color="#E50113"
                  class="w-full h-[42px] py-[10px] rounded-[200px] text-[21px] leading-[22px] font-semibold mt-[10px]"
                >
                  {{ authStore.i18n("cm_nota.goShopping") }}
                </n-button>
              </a>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const route = useRoute();
import { useAuthStore } from "@/stores/authStore";

const homeButtonRef = ref(<any>{});
const authStore = useAuthStore();
const pageData = reactive<any>({
  serviceType: route?.query?.serviceType || "",
  contactName: route?.query?.contactName || "",
  whatsapp: route?.query?.whatsapp || "",
  timeLeft: 0,
});

onMounted(() => {
  if (pageData.serviceType === "POTENTIAL_SERVICE_TYPE_ONLINE_BUY") {
    pageData.timeLeft = 3;
    const countdown = setInterval(() => {
      if (pageData.timeLeft === 1) {
        homeButtonRef.value?.click();
        clearInterval(countdown);
      } else {
        pageData.timeLeft--;
      }
    }, 1000);
  }
});
</script>
<style scoped lang="scss">
.page-container {
  height: auto;
  min-height: 100vh;
  background: linear-gradient(180deg, #db1121 0%, #f87e7e 100%), #f2f2f2;
  background-size: 100% 236px, 100% 100%;
  background-repeat: no-repeat;
  color: #333;
}
</style>
