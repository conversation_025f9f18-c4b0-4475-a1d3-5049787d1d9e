<template>
  <div class="guide-video-model">
    <n-modal
      preset="dialog"
      :block-scroll="true"
      :show-icon="false"
      v-model:show="pageData.showGuideVideo"
      :on-close="onCloseGuide"
      :on-esc="onCloseGuide"
      :on-mask-click="onCloseGuide"
      class="guide-video-dialog"
      :class="{ 'pc-video': !isMobile }"
      :style="
        isMobile
          ? { width: '90vw', padding: 0 }
          : {
              width: '1200px',
              padding: 0,
            }
      "
    >
      <video-card
        :width="pageData.windowWidth"
        :height="pageData.windowWidth * 0.5625"
        :src="pageTheme.guideVideo"
        :autoplay="true"
      ></video-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts" name="GuideVideo">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
const route = useRoute();
const authStore = useAuthStore();

const pageData = reactive({
  windowWidth: 400,
  showGuideVideo: false,
});
const pageTheme = computed(() => useConfigStore().getPageTheme);
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

onMounted(() => {
  if (authStore.getShowedGuideStatus() && route.query.guide) {
    pageData.showGuideVideo = false;
  } else {
    pageData.showGuideVideo = true;
  }
  authStore.setShowedGuideStatus(true);
  updateWindowSize();
});

const updateWindowSize = () => {
  if (isMobile.value) {
    pageData.windowWidth = window.innerWidth * 0.9;
  } else {
    pageData.windowWidth = 1200;
  }
};

function onCloseGuide() {
  pageData.showGuideVideo = false;
}
</script>
<style scoped lang="scss">
.close-icon {
  position: absolute;
  right: 0.8rem;
  top: 0.8rem;
  z-index: 1000;
}
</style>
<style>
.guide-video-dialog.n-dialog .n-dialog__close {
  color: #fff;
  background-color: #ecd9d94b;
}
.pc-video.n-dialog .n-dialog__close {
  width: 2rem;
  height: 2rem;

  color: #fff;
  background-color: #ecd9d94b;
}
.pc-video.n-dialog .n-dialog__close .n-base-icon {
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
}
.pc-video.n-dialog .n-dialog__close .n-base-icon svg {
  width: 2rem;
  height: 2rem;
}
.guide-video-dialog.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0px !important;
}
.n-modal-mask {
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
