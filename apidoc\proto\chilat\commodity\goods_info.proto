syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_info_param.proto";
import "chilat/commodity/model/goods_info_model.proto";
import "chilat/commodity/commodity_common.proto";
import "chilat/basis/param/goods_param.proto";
import "chilat/basis/model/goods_model.proto";
import "common.proto";

// 商品信息管理
service GoodsInfo {
  // 查询列表（库存调整接口：GoodsStock.saveStock，暂不包括“同步X，货源信息API”）
  rpc listPage (GoodsInfoPageQueryParam) returns (GoodsInfoPageResp);
  // 查询详情
  rpc getDetail (common.IdParam) returns (GoodsInfoDetailResp);
  // 保存商品信息（id不存在，则为新增）
  rpc saveGoods (GoodsInfoSaveParam) returns (common.StringResult);
  // 更新价格系数
  rpc updatePriceFactor (UpdatePriceFactorParam) returns (UpdatePriceFactorResp);
  // 获取商品发布页的选项（传参：商品分类ID）
  rpc getPageOptions (common.IdParam) returns (GoodsInfoOptionResp);
  // 商品上下架（SKU上下架接口：GoodsSku.changeSkuOnline）
  rpc changeGoodsOnline(ChangeGoodsOnlineStateParam) returns (ChangeGoodsOnlineStateResp);
  // 查询日志
  rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
  // 导出商品任务
  rpc exportGoods (GoodsInfoQueryParam) returns (common.StringResult);
//  // 商品导入模板下载
//  rpc downloadGoodsImportTemplate (GoodsImportParam) returns (common.DownloadResult) {
//    option (common.webapi).download = true;
//  };
//  // 导入商品
//  rpc importGoods (GoodsImportParam) returns (GoodsImportResp) {
//    option (common.webapi).upload = true;
//  };
  // 翻译商品
  rpc translateGoods (GoodsInfoTranslateParam) returns (common.MapResult);
//  // 根据营销规则查询商品信息
//  rpc pageListGoodsByRule(MarketingRuleSearchGoodsParam) returns (basis.MidGoodsPageResp);
  // 搜索商城商品（营销规则预览商品）
  rpc searchMallGoods (MallGoodsListQueryParam) returns (MallGoodsListDataResp);
  // 商品回收
  rpc recycleGoods(common.IdsParam) returns (common.ApiResult);
  // 重建商品的ES索引
  rpc recreateESIndex(common.EmptyParam) returns (basis.RecreateESIndexResp);
  // 更新全部商品的ES索引（删除未被更新的ES商品）
  rpc updateAllGoodsESIndex(common.EmptyParam) returns (basis.UpdateAllGoodsESIndexResp);
  // 更新多个商品的ES索引
  rpc updateGoodsESIndex(common.IdsParam) returns (common.IntResult);
  // 同步商品数据到X系统
  rpc syncToX(SyncToXParam) returns (SyncToXResp);
  // 重新更新
  rpc reSync (common.IdParam) returns (common.ApiResult);
  // 根据商品ID查询1688商品
  rpc get1688ByGoodsId (common.IdParam) returns (GoodsInfoDetailResp);
  // 根据SKU ID查询1688商品
  rpc get1688BySkuId (common.IdParam) returns (GoodsInfoDetailResp);
  // 商品恢复
  rpc recoverGoods(common.IdsParam) returns (common.ApiResult);
  // 商品打标
  rpc tagGoods(TagGoodsParam) returns (common.ApiResult);

  // 下载装箱信息模板
  rpc downloadTemplate (GoodsImportParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };

  // 批量导入商品装箱信息
  rpc importGoodsBoxInfo (common.EmptyParam) returns (common.ImportResult) {
    option (common.webapi).upload = true;
  };

  rpc updateAllGoodsVector(basis.UpdateAllGoodsVectorParam) returns (basis.UpdateAllGoodsVectorResp);

  //更新指定商品的向量
  rpc updateGoodsVector(basis.UpdateGoodsVectorParam) returns (common.LongResult);

  rpc goodsVectorStat(basis.GoodsVectorStatParam) returns (basis.GoodsVectorStatResp);

  // 商品标签管理查询商品列表
  rpc goodsListForTag (GoodsListForTagPageQueryParam) returns (GoodsInfoPageResp);

  // 移除商品标签
  rpc removeGoodsTag (RemoveGoodsTagParam) returns (common.ApiResult);

  // 批量发布商品并上架
  rpc batchPublishAndPutOnGoods (PublishAndPutOnGoodsParam) returns (common.ApiResult);
}
