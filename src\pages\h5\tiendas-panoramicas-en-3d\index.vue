<template>
  <div class="w-full bg-white">
    <mobile-search-bar></mobile-search-bar>
    <div class="page-header text-[#fff] overflow-auto px-[0.4rem] py-[0.6rem]">
      <div class="text-[1.04rem] leading-[1.16rem] font-medium mt-[0.6rem]">
        Tiendas panorámicas en 3D
      </div>
      <div class="text-[0.32rem] leading-[0.48rem] mt-[0.48rem]">
        Miles de productos presentados online.
      </div>
    </div>
    <div class="px-[0.16rem] pt-[0.88rem] pb-[1rem]">
      <div v-if="pageData.categoryList.length > 0">
        <n-dropdown
          trigger="click"
          :options="dropdownOptions"
          @select="onCategoryClick"
          placement="bottom-start"
          style="
            max-height: 5.8rem;
            border-radius: 0.24rem;
            overflow-y: auto;
            padding: 0.16rem 0.16rem;
          "
          :render-option="renderOption"
          :show="pageData.showDropdown"
        >
          <div
            class="w-[fit-content] flex items-center ml-auto border border-[#F2F2F2] rounded-[10rem] px-[0.24rem] py-[0.2rem] bg-[#F2F2F2]"
            :class="{ 'bg-[#fff]': pageData.showDropdown }"
            @click="toggleDropdown"
          >
            <span class="text-[0.28rem] leading-[0.28rem]">{{
              pageData.selectedCategory === "Todos" ||
              !pageData.selectedCategory
                ? "Categorías"
                : pageData.selectedCategory
            }}</span>
            <img
              loading="lazy"
              alt="arrow"
              src="@/assets/icons/tiendas-panoramicas-en-3d/arrow-down.svg"
              class="transition-all duration-300 ml-[0.16rem]"
              :class="{ 'rotate-180': pageData.showDropdown }"
              referrerpolicy="no-referrer"
            />
          </div>
        </n-dropdown>
      </div>

      <div>
        <div
          class="flex flex-wrap justify-between gap-y-[0.24rem] mt-[0.72rem]"
        >
          <div v-for="item in pageData.storeList" :key="item.id">
            <a
              :href="`/h5/tienda/${item.title}`"
              class="block group w-[3.52rem]"
            >
              <div
                class="relative w-[3.52rem] h-[4.94rem] rounded-[0.16rem] overflow-hidden"
              >
                <img
                  loading="lazy"
                  :src="item.cover"
                  :alt="item.title"
                  referrerpolicy="no-referrer"
                  class="w-full h-full object-cover object-center"
                />
              </div>
              <div
                class="w-[3.52rem] text-[0.28rem] leading-[0.28rem] mt-[0.16rem] break-all line-clamp-1"
              >
                {{ item.title }}
              </div>
            </a>
          </div>
        </div>

        <!-- 加载更多区域 -->
        <div
          v-if="
            pageData.loading ||
            (!pageData.hasMore && pageData.storeList.length > 0)
          "
          class="flex justify-center items-center py-[0.6rem] relative z-10 bg-white"
        >
          <n-spin :size="20" v-if="pageData.loading" />
          <span
            class="text-[0.28rem] leading-[0.28rem] text-[#666] ml-[0.2rem]"
            v-if="pageData.loading"
            >Cargando...</span
          >
          <span class="text-[0.28rem] text-[#999]" v-else-if="!pageData.hasMore"
            >- No hay más tiendas -</span
          >
        </div>
      </div>
    </div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>

<script setup lang="ts">
import homeIcon from "@/assets/icons/tiendas-panoramicas-en-3d/home.svg";
import Breadcrumb from "@/pages/tiendas-panoramicas-en-3d/components/Breadcrumb.vue";
import { useRoute } from "vue-router";
import type { DropdownOption } from "naive-ui";

const route = useRoute();
useHead({
  title: "Tiendas panorámicas en 3D - Chilat",
});

const breadcrumbItems = [
  { link: "/", icon: homeIcon, alt: "home" },
  { link: "/tiendas-panorámicas-en-3d", text: "Tiendas panorámicas en 3D" },
];

// 定义商店项目的接口类型
interface StoreItem {
  id: string | number;
  title: string;
  cover: string;
  product: string;
  // 其他可能存在的属性
}

const pageData = reactive({
  categoryList: [],
  selectedCategory: "",
  showDropdown: false, // 控制下拉菜单显示/隐藏
  storeList: [] as StoreItem[],
  pageCount: 1,
  pageItems: 1,
  pageNum: 1,
  pageSize: 20,
  loading: false, // 加载状态
  hasMore: true, // 是否还有更多数据
});

// 准备下拉菜单选项
const dropdownOptions = computed(() => {
  return pageData.categoryList.map((category) => ({
    label: category,
    key: category,
  }));
});

const renderOption = (info: DropdownOption) => {
  const { option } = info;
  const selected = option.key === pageData.selectedCategory;

  return h(
    "div",
    {
      style: {
        borderBottom: selected ? "none" : "0.02rem solid #F2F2F2",
      },
      onClick: () => {
        onCategoryClick(option.key as string);
      },
    },
    [
      h(
        "div",
        {
          style: {
            height: "0.76rem",
            display: "flex",
            alignItems: "center",
            padding: "0.24rem 0.16rem",
            fontSize: "0.28rem",
            lineHeight: "0.28rem",
            backgroundColor: selected ? "#F2F2F2" : "transparent",
            borderRadius: "0.16rem",
            transition: "background-color 0.2s ease",
          },
        },
        typeof option.label === "string" ? option.label : (option.key as string)
      ),
    ]
  );
};

// 切换下拉菜单显示/隐藏状态
function toggleDropdown() {
  pageData.showDropdown = !pageData.showDropdown;
}

await onListWordPressCategory();
onSearchWordPressList();

async function onListWordPressCategory() {
  const res: any = await useListWordPressCategory({});
  if (res?.result?.code === 200) {
    res.data.categoryList.unshift("Todos");
    // 转换接口返回的数据格式
    pageData.categoryList = res.data.categoryList;

    // 从URL获取选中的分类
    if (route.query.category) {
      pageData.selectedCategory = decodeURIComponent(
        route.query.category as string
      );
    } else {
      // 默认选中第一个分类
      pageData.selectedCategory = pageData.categoryList[0];
    }
  }
}

async function onSearchWordPressList(isLoadMore = false) {
  if (pageData.loading) return;
  pageData.loading = true;

  try {
    const res: any = await useSearchWordPressList({
      category:
        pageData.selectedCategory === "Todos" ? "" : pageData.selectedCategory,
      page: {
        current: pageData.pageNum,
        size: pageData.pageSize,
      },
    });

    if (res?.result?.code === 200) {
      if (isLoadMore) {
        pageData.storeList = [...pageData.storeList, ...res.data];
        if (res.data.length === 0) {
          pageData.hasMore = false;
        }
      } else {
        pageData.storeList = res.data;
      }

      pageData.pageCount = res.page.pages;
      pageData.pageItems = res.page.total;

      pageData.hasMore = pageData.pageNum < pageData.pageCount;
    }
  } finally {
    pageData.loading = false;
  }
}

function onCategoryClick(key: string) {
  pageData.selectedCategory = key;
  pageData.pageNum = 1;
  pageData.hasMore = true;
  pageData.storeList = [];
  pageData.showDropdown = false; // 选择后关闭下拉菜单

  // 更新URL参数
  const url = new URL(window.location.href);
  url.searchParams.set("category", encodeURIComponent(key));
  window.history.replaceState(null, "", url.toString());

  // 加载数据
  onSearchWordPressList();
}

// 加载更多数据
const loadMore = async function () {
  if (pageData.loading || !pageData.hasMore) return;

  pageData.pageNum++;
  await onSearchWordPressList(true);
};

function throttle(fn: (...args: any[]) => void, delay: number) {
  let timer: number | null = null;
  let lastTime = 0;

  return function (this: any, ...args: any[]) {
    const now = Date.now();
    if (now - lastTime >= delay) {
      fn.apply(this, args);
      lastTime = now;
    }
  };
}

// 处理滚动事件
const scrollHandler = () => {
  // 检查是否滚动到底部
  const scrollHeight = document.documentElement.scrollHeight;
  const scrollTop =
    document.documentElement.scrollTop || document.body.scrollTop;
  const clientHeight = document.documentElement.clientHeight;
  const footerHeight =
    document.getElementById("footer-wrapper")?.clientHeight || 0;
  if (
    scrollHeight - scrollTop - clientHeight < footerHeight &&
    !pageData.loading &&
    pageData.hasMore
  ) {
    loadMore();
  }
};

const throttledScrollHandler = throttle(scrollHandler, 200);

onMounted(() => {
  window.addEventListener("scroll", throttledScrollHandler);
});
onUnmounted(() => {
  window.removeEventListener("scroll", throttledScrollHandler);
});
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  height: 8rem;
  margin: 0 auto;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/tiendas-panoramicas-en-3d/mobile-header-bg.png");
  background-repeat: no-repeat;
}
</style>
