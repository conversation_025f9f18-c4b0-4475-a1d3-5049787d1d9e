<template>
  <div
    class="page-wrapper"
    :class="pageData.isDrawerFixed ? `min-w-[1500px]` : `min-w-[1280px]`"
  >
    <product-drawer @updateDrawer="onUpdateDrawer"></product-drawer>
    <div class="page-content">
      <!-- 搜索和分类卡片插槽 -->
      <slot name="search-category">
        <div class="w-[1280px] px-[20px] mx-auto">
          <search-cate-card
            :categories="props.categories"
            class="mx-auto"
            searchWidth="1240px"
          ></search-cate-card>
        </div>
      </slot>
      <div class="w-[1280px] mx-auto px-[20px]">
        <!-- 主要内容插槽 -->
        <slot name="main-content"></slot>
      </div>
      <!-- 页脚部分 -->
      <slot name="footer">
        <page-footer footerWidth="1280px"></page-footer>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive(<any>{
  isDrawerFixed: false, // 是否显示侧边栏
});

onMounted(() => {
  window.addEventListener("scroll", onScroll);
  window.addEventListener("resize", onResize);

  // 页面加载后立即设置侧边栏位置
  onSetRightWrapper();
  // 确保初始状态下，滚动位置正确计算
  onScrollX({});
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
  window.removeEventListener("resize", onResize);
});

function onUpdateDrawer(bol: any) {
  pageData.isDrawerFixed = bol;
  if (pageData.isDrawerFixed) {
    onSetRightWrapper();
  }
}

function onResize() {
  onSetRightWrapper();
}

// 设置右侧侧边栏位置
function onSetRightWrapper() {
  nextTick(() => {
    let rightWrapper = document.getElementById("right-wrapper") || <any>{};
    if (!rightWrapper.style) return; // 确保获取到有效元素

    const documentWidth = window.innerWidth;

    // 如果屏幕宽度小于等于1500px(中文内容1280+侧边购物车220)，则右侧位置要动态计算
    if (documentWidth <= 1500) {
      let disx = documentWidth - 1500;
      rightWrapper.style.right = disx + "px";
    } else {
      rightWrapper.style.right = "0px"; // 默认右侧位置为0
    }
  });
}

// 横向滚动时同步更新右侧位置
async function onScroll(e: any) {
  if (e.deltaX !== 0) {
    onScrollX(e);
  }
}

// 页面横向滚动时，调整右侧侧边栏的相对位置
function onScrollX(e: any) {
  let rightWrapper = document.getElementById("right-wrapper") || <any>{};
  const scrollLeft =
    document.documentElement.scrollLeft ||
    document.body.scrollLeft ||
    e?.target?.scrollLeft ||
    0;

  if (rightWrapper.style) {
    const scrollLeftMax = document.body.scrollWidth - document.body.clientWidth;
    rightWrapper.style.right = scrollLeft - scrollLeftMax + "px";
  }
}
</script>

<style scoped lang="scss">
.page-wrapper {
  display: flex;
  justify-content: flex-start;
  flex-direction: row-reverse;
  width: 100%;
}

.page-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0;
  min-width: 1280px;
}
</style>
