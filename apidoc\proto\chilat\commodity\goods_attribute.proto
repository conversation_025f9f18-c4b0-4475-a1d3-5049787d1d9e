syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_attribute_param.proto";
import "chilat/commodity/model/goods_attribute_model.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

// 商品属性管理
service GoodsAttribute {
//  // 查询列表
//  rpc listPage (GoodsAttributePageQueryParam) returns (GoodsAttributePageResp);
//  // 查询详情
//  rpc getDetail (common.IdParam) returns (GoodsAttributeDetailResp);
//  // 保存属性信息（id不存在，则为新增）
//  rpc saveAttribute (GoodsAttributeSaveParam) returns (common.ApiResult);
//  // 查询日志
//  rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
}