<template>
  <div>
    <div class="header-and-searchbar text-white">
      <div class="header-bg">
        <div class="header-bg-wrapper">
          <n-image
            lazy
            preview-disabled
            :src="headerBg"
            style="height: 624px; width: 1920px"
          />
        </div>
      </div>
      <div class="header-wrapper text-white">
        <div
          id="page-header"
          class="page-header"
          :class="
            pageData.isHeaderFixed ? 'page-white page-fixed' : 'page-dark'
          "
        >
          <template v-if="!pageData.isHeaderFixed">
            <div class="header-content">
              <a href="/" class="w-[180px]">
                <n-image
                  lazy
                  preview-disabled
                  object-fit="fill"
                  :src="whiteLogo"
                  class="w-[180px]"
                  data-spm-box="navigation-logo-icon"
                />
              </a>
              <n-button color="#db2221" @click="onLoginClick('top')" round>
                {{ authStore.i18n("cm_common_loginRegister") }}
              </n-button>
            </div>
            <div class="sub-header inline-flex justify-between items-center">
              <search-tabs :noCarouselCate="true">
                <template #default="{ onMouseEnter, onMouseLeave }">
                  <div
                    class="inline-flex items-center cursor-pointer ml-[-24px]"
                    @mouseenter="onMouseEnter"
                    @mouseleave="onMouseLeave"
                  >
                    <icon-card
                      name="system-uicons:list"
                      size="26"
                      class="mr-0.5"
                    ></icon-card>
                    <span class="text-base">{{
                      authStore.i18n("cm_guestHome.allCategory")
                    }}</span>
                  </div>
                </template>
              </search-tabs>

              <div
                class="absolute right-[40px] flex items-center"
                data-spm-box="navigation-top-article"
              >
                <n-space :style="{ gap: '0 28px' }">
                  <a
                    class="hover:underline cursor-pointer"
                    v-for="(news, index) in newsData"
                    :key="index"
                    :href="news.path"
                    target="_blank"
                  >
                    {{ news.title }}
                  </a>
                </n-space>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="header-content">
              <a href="/" class="w-[180px]">
                <n-image
                  lazy
                  object-fit="fill"
                  preview-disabled
                  :src="pageTheme.logo"
                  class="w-[180px]"
                  data-spm-box="navigation-logo-icon"
                />
              </a>

              <div v-if="pageData.isHeaderButtonFixed" class="header-searchbar">
                <div
                  class="header-search"
                  data-spm-box="navigation-keyword-search"
                >
                  <div class="search-bar-inner">
                    <div class="search-bar-input-wrapper">
                      <input
                        class="search-bar-input"
                        type="text"
                        maxlength="50"
                        v-model.trim="pageData.keyword"
                        :placeholder="
                          authStore.i18n('cm_home.searchPlaceholder')
                        "
                        @keyup.enter="onKeywordClick($event)"
                      />
                    </div>
                    <div class="flex items-center">
                      <image-search size="26"></image-search>
                      <button
                        class="search-bar-inner-button ml-2"
                        @click="onKeywordClick($event)"
                      >
                        <icon-card
                          name="majesticons:search-line"
                          size="22"
                          class="mr-0.5"
                        ></icon-card
                        ><span class="text-base font-medium">{{
                          authStore.i18n("cm_home.search")
                        }}</span>
                      </button>
                    </div>

                    <div class="popup common-panel association-popup"></div>
                  </div>
                </div>
              </div>
              <n-button color="#db2221" @click="onLoginClick('top')" round>
                {{ authStore.i18n("cm_common_loginRegister") }}
              </n-button>
            </div>
          </template>
        </div>
      </div>
      <div class="content-container">
        <div>
          <div
            class="cursor-pointer inline-block mb-6 h-[25px]"
            @click="onOpenVideo('TROzVaB3Lr0', 'Learn about Alibaba.com')"
          >
            <!-- <icon-card
              name="icon-park-solid:play"
              size="22"
              class="mr-1"
            ></icon-card>
            <span class="video-title">{{
              authStore.i18n("cm_guestHome.learnAboutUs")
            }}</span> -->
          </div>
          <div class="content-title">
            {{ authStore.i18n("cm_guestHome.international") }}
          </div>
          <div>
            <div class="home-search" data-spm-box="navigation-keyword-search">
              <div class="home-search-inner" id="page-header-button">
                <div class="search-bar-input-wrapper">
                  <input
                    class="search-bar-input"
                    type="text"
                    maxlength="50"
                    v-model.trim="pageData.keyword"
                    @keyup.enter="onKeywordClick($event)"
                    :placeholder="authStore.i18n('cm_home.searchPlaceholder')"
                  />
                </div>
                <div class="flex items-center">
                  <image-search size="30"></image-search
                  ><button
                    class="search-bar-inner-button ml-2"
                    @click="onKeywordClick($event)"
                  >
                    <icon-card
                      name="majesticons:search-line"
                      size="22"
                      class="mr-0.5"
                    ></icon-card
                    ><span class="text-base font-medium">{{
                      authStore.i18n("cm_home.search")
                    }}</span>
                  </button>
                </div>

                <div class="popup common-panel association-popup"></div>
              </div>
              <div
                class="bottom-recommend-wrap"
                v-if="hotKeywords && hotKeywords.length"
              >
                <span class="text-base mr-2"
                  >{{ authStore.i18n("cm_home.frequentlySearched") }}:</span
                ><a
                  target="_blank"
                  v-for="(item, index) in hotKeywords"
                  :key="index"
                  class="keyword"
                  :data-spm-index="index + 1"
                  data-spm-box="navigation-hot-keywords"
                  :href="`/goods/list/all?keyword=${item}`"
                  >{{ item }}</a
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-full min-w-[1280px] bg-[#3a190b] text-white">
      <div class="w-[1200px] flex justify-between mx-auto py-14">
        <!-- @mouseenter="onNavHover(index)" @mouseleave="onNavRemoveHover" -->
        <div
          class="nav-item"
          v-for="(nav, index) in navData"
          :key="index"
          @click="scrollToNav(nav)"
          :click-title="`${index + 1}. ${nav.title} `"
        >
          <div class="icon-container">
            <icon-card :name="nav.icon" size="38"></icon-card>
          </div>
          <div class="nav-title">{{ nav.title }}</div>
          <div class="content-container">
            {{ nav.content }}
          </div>
        </div>
      </div>
    </div>
    <div
      class="category-wrapper"
      id="NavItem1"
      data-spm-box="homepage-top-categories"
    >
      <div class="w-[1200px] mx-auto">
        <div class="h-40 flex justify-between">
          <div class="category-title">
            {{ authStore.i18n("cm_guestHome.categoryTitle") }}
          </div>
          <div class="category-number">
            <div
              class="number-item"
              v-for="(item, index) in categoryIntroData"
              :key="index"
            >
              <span>{{ item.number }}</span>
              <p>
                {{ item.title }}
              </p>
            </div>
          </div>
        </div>
        <div class="category-list">
          <div
            class="slider_arrow_wrapper prev"
            @click="prevSlide"
            v-show="pageData.showPrevArrow"
          >
            <div class="slider_arrow prev">
              <img loading="lazy" :src="arrowIcon" />
              />
            </div>
          </div>
          <div
            class="slider_inner"
            :style="{ transform: `translateX(${pageData.slidDis}px)` }"
          >
            <div
              class="category-row"
              v-for="(cate, index) in pageData.categoryData"
              :key="index"
            >
              <a
                v-for="cateItem in cate"
                :key="cateItem.id"
                v-bind:data-spm-index="index + 1"
                :href="`/goods/list/${cateItem.id}?cateName=${cateItem.name}`"
                target="_blank"
                ><div class="category-item">
                  <n-image
                    lazy
                    preview-disabled
                    :src="cateItem.cateLogo"
                    class="img"
                  />
                  <div class="category-name">
                    <span>{{ cateItem.name }}</span>
                  </div>
                </div></a
              >
            </div>
          </div>
          <div
            class="slider_arrow_wrapper next"
            @click="nextSlide"
            v-show="pageData.showNextArrow"
          >
            <div class="slider_arrow next">
              <img loading="lazy" :src="arrowIcon" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 美客多和义乌推荐 -->
    <div class="w-full min-w-[1280px] bg-gray-100 py-20">
      <n-flex
        vertical
        justify="center"
        class="w-[1200px] flex justify-between mx-auto"
      >
        <n-ellipsis
          :tooltip="false"
          class="font-3xl font-medium font-sans px-4 mb-10"
        >
          {{ authStore.i18n("cm_home.hotTitle") }}
        </n-ellipsis>
        <div class="flex w-full">
          <!-- 美客多推荐 -->
          <div class="w-1/2 px-4" data-spm-box="homepage-hot-mercado">
            <div class="font-2xl pb-[50px]">
              <div class="float-left">
                <n-ellipsis class="font-sans font-medium">
                  {{ authStore.i18n("cm_home.mercadoHotTitle") }}
                </n-ellipsis>
              </div>
              <div
                class="float-right hover:cursor-pointer text-lg py-1 ml-4"
                @click="onMercadoHotMore($event)"
              >
                <n-ellipsis class="font-sans underline">{{
                  authStore.i18n("cm_app.viewMore")
                }}</n-ellipsis>
              </div>
            </div>
            <n-card
              class="bg-white p-2 rounded-xl"
              @mouseenter="onMercadoMouseEnter"
              @mouseleave="onMercadoMouseLeave"
            >
              <n-carousel
                ref="mercadoHotSaleRef"
                :autoplay="true"
                :show-arrow="pageData.mercadoArrow"
                :interval="5000"
                :show-dots="true"
              >
                <n-carousel-item
                  v-for="(item, index) in pageData.mercadoHotSaleGoods"
                  :key="index"
                  class="hover:cursor-pointer"
                  :data-spm-index="index + 1"
                >
                  <n-flex vertical :style="{ gap: '2px' }">
                    <n-ellipsis
                      class="font-medium font-sans font-2lg leading-[26px]"
                      >{{ authStore.i18n("cm_home.hotCake") }}</n-ellipsis
                    >
                    <n-ellipsis
                      class="text-gray-500 font-sans font-lg leading-[22px] mt-1"
                      >{{ item.categoryName }}</n-ellipsis
                    >
                    <n-grid :cols="4">
                      <n-grid-item
                        v-for="(goods, index) in item.hotSaleGoods"
                        :key="index"
                        :span="index === 0 ? 4 : 1"
                        class="p-2"
                        @click="onMercadoHotClick(item, $event)"
                      >
                        <div v-if="index === 0" class="mt-2 flex-center">
                          <n-image
                            lazy
                            preview-disabled
                            :src="goods?.mainImageUrl"
                            class="h-92 rounded-2xl"
                          ></n-image>
                        </div>
                        <div v-else-if="index <= 4">
                          <n-image
                            lazy
                            preview-disabled
                            :src="goods?.mainImageUrl"
                            class="h-36 rounded-2xl"
                          ></n-image>
                        </div>
                      </n-grid-item>
                    </n-grid>
                  </n-flex>
                </n-carousel-item>
                <template #arrow="{ prev, next }">
                  <div class="custom-arrow-hot">
                    <div class="custom-arrow-icon custom-arrow--left">
                      <n-button
                        strong
                        secondary
                        circle
                        @click="prev"
                        class="w-12 h-12"
                      >
                        <template #icon>
                          <icon-card
                            size="48"
                            name="iconamoon:arrow-left-2-thin"
                            color="#000"
                            class="mr-1"
                          ></icon-card>
                        </template>
                      </n-button>
                    </div>
                    <div class="custom-arrow-icon custom-arrow--right">
                      <n-button
                        strong
                        secondary
                        circle
                        @click="next"
                        class="w-12 h-12"
                      >
                        <template #icon>
                          <icon-card
                            size="48"
                            name="iconamoon:arrow-right-2-thin"
                            color="#000"
                            class="mr-1"
                          ></icon-card>
                        </template>
                      </n-button>
                    </div>
                  </div>
                </template>
              </n-carousel>
            </n-card>
          </div>
          <!-- 义乌推荐 -->
          <div class="w-1/2 px-4" data-spm-box="homepage-hot-yiwu">
            <div class="font-2xl pb-[50px]">
              <div class="float-left">
                <n-ellipsis class="font-sans font-medium">{{
                  authStore.i18n("cm_home.yiWuHotTitle")
                }}</n-ellipsis>
              </div>
              <div
                class="float-right hover:cursor-pointer text-lg py-1 ml-4"
                @click="onYiWuHotMore($event)"
              >
                <n-ellipsis class="font-sans underline">{{
                  authStore.i18n("cm_app.viewMore")
                }}</n-ellipsis>
              </div>
            </div>
            <n-card
              class="bg-white p-2 rounded-xl hover:cursor-pointer"
              @click="onYiWuHotClick(pageData.yiwuHotSaleGoods, $event)"
            >
              <n-flex :style="{ gap: '4px' }">
                <n-ellipsis
                  class="h-7 px-4 font-medium font-sans font-2lg leading-[26px]"
                >
                  <span>{{ pageData.yiwuTotalCount }}</span>
                  <span>+&nbsp;</span>
                  <span>{{ authStore.i18n("cm_home.todayGoodsAdded") }}</span>
                </n-ellipsis>
                <n-grid :cols="3">
                  <n-grid-item
                    v-for="(item, index) in pageData.yiwuHotSaleGoods
                      ?.hotSaleGoods"
                    :key="index"
                    class="p-2"
                  >
                    <n-image
                      lazy
                      preview-disabled
                      :src="item?.mainImageUrl"
                      class="h-44 rounded-2xl"
                    ></n-image>
                  </n-grid-item>
                </n-grid>
              </n-flex>
            </n-card>
            <n-card
              class="bg-white p-2 rounded-xl mt-5 hover:cursor-pointer"
              @click="onNewWeekClick($event)"
            >
              <n-grid :cols="3">
                <n-grid-item>
                  <n-image
                    lazy
                    preview-disabled
                    :src="pageData.hotSaleGoods?.mainImageUrl"
                    class="h-36 rounded-2xl"
                  ></n-image>
                </n-grid-item>
                <n-grid-item :span="2" class="h-39">
                  <n-flex vertical class="py-6">
                    <n-ellipsis
                      :line-clamp="2"
                      :tooltip="false"
                      class="text-xl font-medium font-sans"
                      >{{ authStore.i18n("cm_home.newThisWeek") }}</n-ellipsis
                    >
                    <n-ellipsis
                      :tooltip="false"
                      :line-clamp="2"
                      class="text-lg text-gray-400 font-sans"
                      >{{ pageData.hotSaleGoods?.goodsName }}</n-ellipsis
                    >
                  </n-flex>
                </n-grid-item>
              </n-grid>
            </n-card>
          </div>
        </div>
      </n-flex>
    </div>
    <div class="simplify-purchase bg-white" id="NavItem2">
      <div class="w-[1200px] mx-auto flex items-center">
        <img loading="lazy" :src="purchaseImg" class="w-[620px] mr-10" />
        <div class="purchase-title">
          Ahórrese los tediosos y variados pasos,mejore la eficacia de sus
          compras.
        </div>
      </div>
    </div>
    <div
      class="security-floor"
      id="NavItem3"
      :style="`background-image: url(${securityBg})`"
    >
      <div class="security-floor_wrapper">
        <div class="security-floor_title">
          {{ authStore.i18n("cm_guestHome.securityTitle") }}
        </div>
        <div class="flex justify-between">
          <div
            class="security-floor_card"
            v-for="(item, index) in securityData"
            :key="index"
          >
            <div class="flex items-center">
              <n-image
                lazy
                preview-disabled
                class="security-floor_icon"
                :src="item.icon"
              />
              <div class="font-semibold text-2xl">
                {{ item.title }}
              </div>
            </div>
            <div class="security-floor_card_text_2">
              {{ item.desc }}
            </div>
            <!-- <div class="flex absolute bottom-[50px] text-[16px]">
              <div class="security-floor_watch">
                <icon-card
                  name="icon-park-solid:play"
                  size="22"
                  class="mr-1"
                ></icon-card>
                <div class="security-floor_watch_text">
                  {{ authStore.i18n("cm_guestHome.watchVideo") }}
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <div id="NavItem4" class="full-link">
      <div class="full-link_box">
        <div class="full-link_title">
          {{ authStore.i18n("cm_guestHome.fullLinkTitle") }}
        </div>
        <ul class="full-link_item_wrapper">
          <li
            class="full-link_item"
            v-for="(link, index) in fullLinkData"
            :key="index"
            @mouseenter="onLinkHover(index)"
            :class="{
              'full-link_item_enter': pageData.activatedLinkIndex == index,
            }"
            :click-title="`${index + 1}. ${link.title} `"
          >
            <div class="full-link_icon_wrapper">
              <n-image
                lazy
                preview-disabled
                class="full-link_icon"
                :src="
                  pageData.activatedLinkIndex == index
                    ? link.iconActive
                    : link.icon
                "
              />
            </div>
            <div class="full-link_content">
              <div class="full-link_item_title" title="Search for matches">
                {{ link.title }}
              </div>
              <div class="full-link_item_desc">
                {{ link.desc }}
              </div>
            </div>
            <n-image
              lazy
              preview-disabled
              :src="link.imageUrl"
              class="full-link_item_img"
              v-show="pageData.activatedLinkIndex == index"
            />
          </li>

          <div class="full-link_item_tail"></div>
        </ul>
      </div>
    </div>

    <div class="login-guide" :style="`background-image: url(${loginBg})`">
      <div class="login-guide-wrapper">
        <div class="login-title">¿Listo para empezar?</div>
        <div class="login-desc">
          ¡Explore millones de productos de proveedores confiables hoy!
        </div>
        <n-button
          color="#db2221"
          class="section_banner-button"
          @click="onLoginClick"
        >
          Iniciar sesión
        </n-button>
      </div>
    </div>
    <div class="user-video">
      <div class="user-video-wrapper">
        <div class="video-title">CONOZCA LAS PERSONAS QUE ELIGIERON CHILAT</div>
        <div class="flex justify-between">
          <div
            class="video-wrapper"
            v-for="(video, index) in userVideoData"
            :key="video.id"
            @click="onOpenVideo(video.id, index)"
          >
            <n-image lazy preview-disabled :src="video.poster" class="img" />
            <div class="video-icon">
              <icon-card
                name="mingcute:play-fill"
                size="20"
                color="#322623"
              ></icon-card>
            </div>
          </div>
        </div>
      </div>
    </div>
    <page-footer></page-footer>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
  <login-register ref="loginRegisterRef"></login-register>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";

const authStore = useAuthStore();
const loginRegisterRef = ref<any>(null);

const props = defineProps({
  hotKeywords: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive(<any>{
  slidDis: 0,
  showNextArrow: true,
  showPrevArrow: false,
  categoryData: <any>[],
  categoryInRow: 7,
  categoryAllWidth: 0, //所有分类的宽度
  categoryItemWidth: 175, // 分类子项目的宽度
  categoryContainerWidth: 0, //分类容器的宽度
  activatedLinkIndex: 0,
  isHeaderFixed: false,
  isHeaderButtonFixed: false,
  showKwCarousel: true, //是否展示轮播关键词
  mercadoHotSaleGoods: [],
  yiwuHotSaleGoods: {},
  yiwuTotalCount: 0,
  hotSaleGoods: {},
  mercadoArrow: false,
});

onHomePageData();

const videoModalRef = ref<any>(null);
const pageTheme = computed(() => useConfigStore().getPageTheme);
const userInfo = computed(() => useAuthStore().getUserInfo);

import headerBg from "@/assets/icons/guestHome.jpg";
import searchIcon from "@/assets/icons/search.png";
import searchActIcon from "@/assets/icons/search-active.png";
import cartIcon from "@/assets/icons/cart.png";
import cartActIcon from "@/assets/icons/cart-active.png";
import moneyIcon from "@/assets/icons/money.png";
import moneyActIcon from "@/assets/icons/money-active.png";
import boxIcon from "@/assets/icons/box.png";
import boxActIcon from "@/assets/icons/box-active.png";
import userIcon from "@/assets/icons/user.png";
import userActIcon from "@/assets/icons/user-active.png";

const whiteLogo =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/07/19/9f8a7fd6-75e9-43ca-8067-93fc9239a444.png";
const arrowIcon =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/24/47431a43-32e6-4442-8da5-4ef1609e966e.png";
const purchaseImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/4cf7e50f-43d1-4daf-bd90-a589aca10a7c.png";
const securityBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/b9cb4cb8-7189-4a3e-8241-10e39bc0fafa.png";
const loginBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/31/4d30911f-d27e-4b7d-a0ab-2e9281569117.png";

const newsData = [
  {
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/article/about-us",
  },
  {
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/article/quick-guide",
  },
  {
    title: authStore.i18n("cm_news.helpCenter"),
    path: "/article/help-center",
  },
];

const navData = [
  {
    navId: "NavItem1",
    icon: "iconamoon:category",
    title: authStore.i18n("cm_guestHome.navTitle1"),
    content: authStore.i18n("cm_guestHome.navContent1"),
  },
  {
    navId: "NavItem2",
    icon: "bx:dollar",
    title: authStore.i18n("cm_guestHome.navTitle2"),
    content: authStore.i18n("cm_guestHome.navContent2"),
  },
  {
    navId: "NavItem3",
    icon: "clarity:shield-check-line",
    title: authStore.i18n("cm_guestHome.navTitle3"),
    content: authStore.i18n("cm_guestHome.navContent3"),
  },
  {
    navId: "NavItem4",
    icon: "iconamoon:like-light",
    title: authStore.i18n("cm_guestHome.navTitle4"),
    content: authStore.i18n("cm_guestHome.navContent4"),
  },
];

const categoryIntroData = [
  {
    number: authStore.i18n("cm_guestHome.number1"),
    title: authStore.i18n("cm_guestHome.numberDesc1"),
  },
  {
    number: authStore.i18n("cm_guestHome.number2"),
    title: authStore.i18n("cm_guestHome.numberDesc2"),
  },
];

const securityData = [
  {
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/3447a23e-9bb7-4824-a978-97ee506721b8.png",
    title: authStore.i18n("cm_guestHome.securityQual"),
    desc: authStore.i18n("cm_guestHome.securityQualDesc"),
  },
  {
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/001a3c9b-c5b1-42be-8eb9-ca6eb718d918.png",
    title: authStore.i18n("cm_guestHome.securityPay"),
    desc: authStore.i18n("cm_guestHome.securityPayDesc"),
  },
];

const fullLinkData = [
  {
    icon: searchIcon,
    iconActive: searchActIcon,
    title: authStore.i18n("cm_guestHome.linkProducts"),
    desc: authStore.i18n("cm_guestHome.linkProductsDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/c11a9189-8e3a-4d34-92a3-834f072acf0f.png",
  },
  {
    icon: cartIcon,
    iconActive: cartActIcon,
    title: authStore.i18n("cm_guestHome.linkOrder"),
    desc: authStore.i18n("cm_guestHome.linkOrderDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/0ce0487b-35a4-4bf2-9ae9-a06c36c9571a.png",
  },
  {
    icon: moneyIcon,
    iconActive: moneyActIcon,
    title: authStore.i18n("cm_guestHome.linkPay"),
    desc: authStore.i18n("cm_guestHome.linkPayDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/3e8d485d-88f6-43a9-8985-0610e628ead9.png",
  },
  {
    icon: boxIcon,
    iconActive: boxActIcon,
    title: authStore.i18n("cm_guestHome.linkTrans"),
    desc: authStore.i18n("cm_guestHome.linkTransDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/ceb67233-155d-4d1d-94c2-5ccbba29c8ad.png",
  },
  {
    icon: userIcon,
    iconActive: userActIcon,
    title: authStore.i18n("cm_guestHome.linkOrgan"),
    desc: authStore.i18n("cm_guestHome.linkOrganDesc"),
    imageUrl:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/24e1c475-75f9-44ef-9025-87046d08e648.png",
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
  },
  {
    id: "Tj0nrnhxgXw",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
  },
  {
    id: "_omi5a-pHkA",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
  },
  {
    id: "4FVIz0PvEcE",
    poster:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
  },
];

onMounted(() => {
  window.addEventListener("scroll", onScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
});

async function onHomePageData() {
  const recommend: any = await useRecommendGoodsV2({
    goodsCount: 7,
    deviceType: 1,
  });
  if (recommend?.result?.code === 200) {
    pageData.hotSaleGoods = recommend.data?.hotSaleGoods;
    pageData.yiwuTotalCount = recommend.data?.yiwuTotalCount;
    if (recommend.data.yiwuHotSaleGoods?.length > 0) {
      pageData.yiwuHotSaleGoods = recommend.data.yiwuHotSaleGoods[0];
    }
    if (pageData.yiwuHotSaleGoods?.hotSaleGoods?.length > 6) {
      pageData.yiwuHotSaleGoods.hotSaleGoods = useTake(
        pageData.yiwuHotSaleGoods?.hotSaleGoods,
        6
      );
    }
    pageData.mercadoHotSaleGoods = recommend.data?.mercadoHotSaleGoods?.filter(
      (item: any) => {
        return item?.hotSaleGoods?.length >= 5;
      }
    );
  }
}

async function onScroll(e: any) {
  // y轴滚动
  if (e.deltaY !== 0) {
    let scrollTop =
      window.pageYOffset ||
      document.documentElement.scrollTop ||
      document.body.scrollTop;

    const pageHeader = document.querySelector("#page-header") as HTMLElement;
    const headerHeight = pageHeader.offsetHeight;
    const pageSearchButton =
      document.querySelector("#page-header-button") || <any>{};
    // 判断是否显示固定搜索
    if (scrollTop > 0) {
      pageData.isHeaderFixed = true;
    } else {
      pageData.isHeaderFixed = false;
    }

    if (
      scrollTop >
      pageSearchButton.offsetTop + pageSearchButton.offsetHeight / 2
    ) {
      pageData.isHeaderButtonFixed = true;
    } else {
      pageData.isHeaderButtonFixed = false;
    }
  }
}

function nextSlide() {
  const slidItems = Math.ceil(
    Math.abs(pageData.slidDis) / pageData.categoryItemWidth
  ); // 向上取整
  const restItems = pageData.categoryData.length - slidItems;
  const itemsToSlide = Math.min(pageData.categoryInRow - 2, restItems); // 最多滑动5个项目或少于5个

  if (restItems > 0) {
    pageData.slidDis -= pageData.categoryItemWidth * itemsToSlide;

    // 如果滑动后已经到达或超过了总宽度，则隐藏“下一箭头”
    if (
      Math.abs(pageData.slidDis) >=
      pageData.categoryAllWidth - pageData.categoryContainerWidth
    ) {
      pageData.slidDis = -(
        pageData.categoryAllWidth - pageData.categoryContainerWidth
      ); // 保证不会超出边界
      pageData.showNextArrow = false;
    } else {
      pageData.showNextArrow = true;
    }
  } else {
    pageData.showNextArrow = false; // 没有剩余项目，隐藏下一箭头
  }

  // 始终显示上一箭头
  pageData.showPrevArrow = true;
}

function prevSlide() {
  const slidItems = Math.ceil(
    Math.abs(pageData.slidDis) / pageData.categoryItemWidth
  );
  const itemsToSlide = Math.min(pageData.categoryInRow - 2, slidItems);
  if (slidItems > 0) {
    pageData.slidDis += pageData.categoryItemWidth * itemsToSlide;

    // 如果滑动回到第一屏，隐藏“上一箭头”
    if (pageData.slidDis >= 0) {
      pageData.slidDis = 0; // 确保不会超出边界
      pageData.showPrevArrow = false;
    } else {
      pageData.showPrevArrow = true;
    }

    // 始终显示“下一箭头”，除非已经滑到最末尾
    if (
      Math.abs(pageData.slidDis) >=
      pageData.categoryAllWidth - pageData.categoryContainerWidth
    ) {
      pageData.showNextArrow = false;
    } else {
      pageData.showNextArrow = true;
    }
  } else {
    pageData.showPrevArrow = false;
  }
}

onPageCategoryData();

async function onPageCategoryData() {
  const res: any = await useHomePageCategory({});
  if (res?.result?.code === 200) {
    pageData.categoryData = organizeDataByColumns(res?.data);
    pageData.showNextArrow =
      pageData.categoryData.length > pageData.categoryInRow ? true : false;
    nextTick(() => {
      // 分类子项目的宽度
      const element = document.querySelector(".category-row a");
      if (!element) return;
      pageData.categoryItemWidth = element.getBoundingClientRect().width || 175;
      pageData.categoryContainerWidth =
        document.querySelector(".slider_inner").offsetWidth; // 分类容器的宽度
      pageData.categoryAllWidth =
        pageData.categoryData.length * pageData.categoryItemWidth; // 全部分类的宽度
    });
  }
}

function organizeDataByColumns(data: any[]) {
  const numRows = 2;
  const organizedData = [];
  if (data.length % numRows !== 0) {
    throw new Error("数据项数量不是每列所需数量的整数倍");
  }

  for (let i = 0; i < data.length; i += numRows) {
    const column = data.slice(i, i + numRows); // 提取当前列的数据
    organizedData.push(column); // 将当前列添加到结果数组中
  }
  return organizedData;
}

function onOpenVideo(youtubeId: any, index: any, title?: any) {
  // if (videoModalRef.value) {
  //   window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
  //   videoModalRef.value.onOpenVideo(youtubeId, title);
  // }
}

const onLinkHover = (index: any) => {
  pageData.activatedLinkIndex = index;
};

// function onNavHover(index: any) {
//   pageData.actNavIndex = index;
// }
// function onNavRemoveHover() {
//   pageData.actNavIndex = null;
// }

function scrollToNav(nav: any) {
  if (!nav.navId) return;
  const element = document.getElementById(nav.navId);
  if (!element) return;
  element.scrollIntoView({ behavior: "smooth" });
}

const onLoginClick = async (position: any) => {
  let remark =
    position === "top"
      ? "点击顶部导航登录注册，打开账号窗口"
      : "点击首页正文区登录注册";
  window?.MyStat?.addPageEvent("passport_open_home_body_login", remark); // 埋点

  if (loginRegisterRef.value) {
    loginRegisterRef.value.onOpenLogin();
  }
};

function onKeywordClick(event: any) {
  navigateToPage(
    `${goodsListAllPath}`,
    {
      keyword: pageData.keyword?.trim(),
    },
    true,
    event
  );
}

function onKeyWordChange(e: any) {
  if (e.target.value) {
    pageData.showKwCarousel = false;
  } else {
    pageData.showKwCarousel = true;
  }
}

function onSearchInputFocus() {
  searchInputRef.value?.focus();
}

function onGetCurrentKw(index: any) {
  pageData.hotKwIndex = index ? index : 0;
}

function onMercadoHotClick(item: any, event: any) {
  navigateToPage(
    `${goodsListPath}/all`,
    {
      tagId: item.tagId ?? "",
      cateId: item.categoryId,
      cateName: item.categoryName ?? "",
      tag: "mercado",
    },
    true,
    event
  );
}

function onYiWuHotClick(item: any, event: any) {
  navigateToPage(
    `${goodsListPath}/all`,
    { tagId: item.tagId ?? "", tag: "yiwu" },
    true,
    event
  );
}

function onMercadoHotMore(event: any) {
  const first = pageData.mercadoHotSaleGoods?.[0] ?? {};

  navigateToPage(
    `${goodsListPath}/all`,
    { tagId: first.tagId ?? "", tag: "mercado" },
    true,
    event
  );
}

function onYiWuHotMore(event: any) {
  const first = pageData.yiwuHotSaleGoods ?? {};

  navigateToPage(
    `${goodsListPath}/all`,
    { tagId: first.tagId ?? "", tag: "yiwu" },
    true,
    event
  );
}

function onNewWeekClick(event: any) {
  const url = `${goodsDetailPCPath}/${pageData.hotSaleGoods?.goodsId}`;
  navigateToPage(url, {}, true, event);
}

function onMercadoMouseEnter() {
  pageData.mercadoArrow = true;
}

function onMercadoMouseLeave() {
  pageData.mercadoArrow = false;
}
</script>
<style lang="scss" scoped>
:deep(.n-scrollbar) {
  min-height: 380px !important;
}
.header-and-searchbar {
  background-color: #070406;
  margin: 0px auto;
  position: relative;
  width: 100%;
  height: 624px;
  min-width: 1280px;
  .header-wrapper {
    width: 1280px;
    margin: 0px auto;
    position: relative;
    height: 120px;
  }
  .page-header {
    position: relative;
    width: 100%;
    font-size: 14px;
    line-height: 18px;
    z-index: 999;
    .sub-header {
      display: inline-flex;
      width: 1280px;
      padding: 0 40px;
    }
  }
  #page-header.page-fixed {
    position: fixed;
    top: 0;
    left: 0;
    border-bottom: 1px solid #ddd;
    background-color: #fff !important;
    .sub-header {
      display: none;
    }
  }
  #page-header.page-dark {
    background-color: transparent;
    color: #fff;
  }
  #page-header.page-white {
    border-bottom: 1px solid #ddd;
    color: #222;
    background-color: #fff !important;
  }

  .header-bg {
    width: 100%;
    height: 624px;
    overflow: hidden;
    position: absolute;

    .header-bg-wrapper {
      height: 624px;
      left: 50%;
      position: absolute;
      top: 0px;
      transform: translateX(-50%);
      width: 1920px;
    }
  }
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 72px;
    width: 1280px;
    margin: 0 auto;
    font-size: 14px;
    padding: 0 40px;
    .header-searchbar {
      flex-grow: 1;
      flex-shrink: 1;
      margin: 0 40px;
      .header-search {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .search-bar-inner {
          position: relative;
          display: flex;
          width: 100%;
          height: 40px;
          border: 2px solid #db2221;
          border-radius: 100px;
          background-color: #fff;
          justify-content: center;
          align-items: center;
          border: 1px solid #555;
          .search-bar-input-wrapper {
            position: relative;
            display: block;
            width: 100%;
            margin: 0 20px;
          }
          .search-bar-input {
            width: 100%;
            color: #222;
            background-color: #fff;
            line-height: 36px;
            margin: 0;
            padding: 0;
            outline: none;
            border: none;
            background-image: none;
            background-color: transparent;
            box-shadow: none;
            margin: 0;
          }
          .search-bar-inner-button {
            -ms-flex-negative: 0;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 125px;
            height: 36px;
            border-radius: 100px;
            font-size: 14px;
            line-height: 20px;
            background: #db2221;
            color: #fff;
            border: 0 solid transparent;
            cursor: pointer;
            width: 111px;
            height: 32px;
            margin-right: 4px;
          }
        }
      }
    }
  }
  .content-container {
    display: flex;
    align-items: center;
    height: 504px;
    margin: 0 auto;
    position: relative;
    width: 1200px;
    .video-title {
      vertical-align: middle;
      color: rgb(255, 255, 255);
      font-size: 20px;
      font-weight: 400;
      line-height: 26px;
      text-align: center;
    }
    .content-title {
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      color: rgb(255, 255, 255);
      display: -webkit-box;
      font-size: 44px;
      font-weight: 500;
      line-height: 58px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 950px;
    }
    .home-search {
      height: 140px;
      margin-top: 32px;
      width: 786px;
      justify-content: flex-start;
      z-index: 5;
      .home-search-inner {
        position: relative;
        display: flex;
        width: 100%;
        border: 2px solid #db2221;
        border-radius: 100px;
        background-color: #fff;
        justify-content: center;
        align-items: center;
        border-radius: 30px;
        height: 58px;
        border-width: 0;
      }
      .search-bar-input-wrapper {
        position: relative;
        display: block;
        width: 100%;
        margin: 0 28px;
        box-sizing: border-box;
      }
      .search-bar-input {
        font-size: 16px;
        width: 100%;
        color: #222;
        background-color: #fff;
        line-height: 36px;
        margin: 0;
        padding: 0;
        outline: none;
        border: none;
        background-image: none;
        background-color: transparent;
        -webkit-box-shadow: none;
        box-shadow: none;
      }
      .search-bar-inner-button {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 125px;
        height: 36px;
        border-radius: 100px;
        font-size: 14px;
        line-height: 20px;
        background: #db2221;
        color: #fff;
        border: 0 solid transparent;
        cursor: pointer;
        border-radius: 26px;
        height: 48px;
        margin-right: 4px;
      }
      .bottom-recommend-wrap {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        box-sizing: border-box;
        padding: 0 22px;
        width: 100%;
        height: 26px;
        overflow: hidden;
        font-weight: 400;
        font-size: 12px;
        line-height: 17px;
        height: 36px;
        margin-top: 32px;
        padding: 0;
        .keyword {
          background-color: rgba(0, 0, 0, 0.2);
          border: 1px solid #fff;
          border-radius: 22px;
          -webkit-box-sizing: border-box;
          box-sizing: border-box;
          font-size: 14px;
          height: 36px;
          line-height: 36px;
          margin: 0 10px;
          padding: 0 24px;
          text-decoration: none;
          &:hover {
            background-color: rgba(0, 0, 0, 0.5);
            text-decoration: none;
          }
        }
      }
    }
  }
}
.nav-item {
  background-color: hsla(0, 0%, 100%, 0.03);
  border-radius: 16px;
  display: block;
  min-height: 338px;
  padding: 40px;
  text-decoration: none;
  width: 336px;
  padding: 40px 20px;
  width: 276px;
  &:hover {
    background-color: rgba(255, 102, 0, 0.12);
    cursor: pointer;
  }
  .icon-container {
    background-color: hsla(0, 0%, 100%, 0.08);
    border-radius: 30px;
    height: 60px;
    padding: 10px;
    width: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .nav-title {
    color: #fff;
    font-size: 21px;
    letter-spacing: 0;
    line-height: 30px;
    margin: 20px 0;
    min-height: 60px;
    font-weight: 500;
    margin-top: 20px;
    width: 236px;
  }
  .content-container {
    color: #fff;
    font-size: 14px;
    height: auto;
    letter-spacing: 0;
    line-height: 26px;
    min-height: 88px;
    width: 236px;
  }
}
.category-wrapper {
  min-width: 1280px;
  padding: 120px 0;
  min-height: 830px;
  background-color: #fff;
  content-visibility: auto;
  contain-intrinsic-size: auto 1280px auto 830px;
  .category-title {
    font-weight: 500;
    color: #222;
    font-size: 42px;
    line-height: 52px;
    margin: 0;
    width: 800px;
  }
  .category-number {
    width: 500px;
    display: grid;
    row-gap: 20px;
    column-gap: 20px;
    grid-template-columns: 1fr 1fr;
    height: fit-content;
    .number-item {
      padding-left: 16px;
      position: relative;
      &::before {
        background-color: #ddd;
        border-radius: 2px;
        content: "";
        display: inline-block;
        height: 86%;
        left: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
      }
      span {
        color: #db2221;
        font-size: 36px;
        font-weight: 500;
        letter-spacing: -0.73px;
        line-height: 44px;
      }
    }
  }
  .category-list {
    min-width: 1200px;
    height: 320px;
    margin-top: 110px;
    overflow: hidden;
    position: relative;
    .slider_inner {
      display: flex;
      height: 100%;
      left: 0;
      padding-left: 0;
      padding-right: 0;
      position: absolute;
      top: 0;
      transition: transform 0.8s linear 0s;
      width: 100%;
      .category-row {
        height: 100%;
        width: 175px;
        a {
          color: inherit;
          text-decoration: inherit;
        }
        .category-item {
          align-items: center;
          border: 2px solid #eaeaea;
          border-radius: 70px;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          height: 140px;
          justify-content: center;
          margin-bottom: 20px;
          margin-right: 35px;
          width: 140px;
          &:hover {
            border: 2px solid #db2221;
          }
          .img {
            display: initial;
            height: 38px;
            vertical-align: middle;
            width: 38px;
            padding: 5px;
            box-sizing: content-box;
          }
          .category-name {
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              color: #222;
              display: -webkit-box;
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              overflow: hidden;
              padding: 0 4px;
              text-align: center;
              width: 100px;
            }
          }
        }
      }
    }
    .slider_arrow_wrapper {
      align-items: center;
      display: flex;
      height: 100%;
      position: absolute;
      top: 0;
      width: 80px;
      z-index: 9;
      .slider_arrow {
        background-color: #fff;
        border-radius: 48px;
        cursor: pointer;
        display: inline-block;
        font-size: 0;
        height: 48px;
        line-height: 48px;
        text-align: center;
        width: 48px;
        border: 1px solid #e5e5e5;
        -webkit-box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.12);
        box-shadow: 0 2px 6px 2px rgba(0, 0, 0, 0.12);
        img {
          display: inline;
          height: 28px;
          vertical-align: middle;
          width: 28px;
        }
      }
    }
    .slider_arrow_wrapper.prev {
      background-image: linear-gradient(270deg, hsla(0, 0%, 100%, 0), #fff);
      justify-content: start;
      left: 0;
      padding-left: 12px;
      img {
        transform: scaleX(-1);
      }
    }
    .slider_arrow_wrapper.next {
      background-image: linear-gradient(90deg, hsla(0, 0%, 100%, 0), #fff);
      justify-content: end;
      padding-right: 12px;
      right: 0;
    }
  }
}
.simplify-purchase {
  min-width: 1280px;
  padding: 120px 0;
  background-color: #ffffff;
  .purchase-title {
    font-weight: 500;
    color: #222;
    font-size: 42px;
    line-height: 52px;
  }
}
.security-floor {
  min-width: 1280px;
  background-color: #442b20;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 1920px 946px;
  color: #fff;
  margin: auto;
  overflow: hidden;
  width: 100%;
  .security-floor_wrapper {
    width: 1200px;
    margin: 120px auto;
    content-visibility: auto;
    contain-intrinsic-size: auto 1280px auto 892px;
    .security-floor_title {
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      display: -webkit-box;
      font-size: 42px;
      font-weight: 500;
      line-height: 56px;
      margin-bottom: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 800px;
    }
    .security-floor_card {
      backdrop-filter: blur(50px);
      background: hsla(0, 0%, 100%, 0.11);
      border-radius: 20px;
      font-size: 16px;
      height: 300px; //todo340
      line-height: 22px;
      overflow: hidden;
      padding: 50px 46px;
      position: relative;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      width: 584px;
      .security-floor_icon {
        display: block;
        height: 50px;
        margin: 18px 6px 20px 0;
      }
      .security-floor_card_text_2 {
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 16px;
        line-height: 22px;
      }
      .security-floor_watch {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #00000032;
        border: 1px solid #fff;
        border-radius: 999px;
        cursor: pointer;
        font-weight: 500;
        height: 50px;
        margin-inline-end: 20px;
        max-width: 320px;
        padding: 0 20px;
        &:hover {
          background-color: #00000080;
        }
      }
    }
  }
}
.full-link {
  min-width: 1280px;
  background-color: #fff;
  content-visibility: auto;
  contain-intrinsic-size: auto 1280px auto 984px;
  .full-link_box {
    color: #222;
    height: 984px;
    margin: auto;
    overflow: hidden;
    width: 1200px;
    .full-link_title {
      -webkit-box-orient: vertical;
      color: #222;
      display: -webkit-box;
      font-size: 44px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 56px;
      margin: 120px 0 0;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 860px;
    }
    .full-link_item_wrapper {
      -webkit-flex-direction: column;
      flex-direction: column;
      margin-bottom: 120px;
      margin-top: 100px;
      padding-inline-start: 40px;
      position: relative;
      width: 1200px;
      display: flex;
      .full-link_item {
        cursor: pointer;
        width: 624px;
        z-index: 1;
        display: flex;
      }
      .full-link_item_enter:not(:last-of-type) {
        margin-bottom: 16px;
      }
      .full-link_item:not(:last-of-type) {
        margin-bottom: 20px;
      }
      .full-link_item:not(:first-child) {
        margin-top: 20px;
      }

      .full-link_icon_wrapper {
        align-items: center;
        background-color: #fff;
        border-radius: 48px;
        -webkit-box-shadow: 0 0 0 8px #fff;
        box-shadow: 0 0 0 8px #fff;
        display: flex;
        height: 60px;
        justify-content: center;
        margin-inline-end: 28px;
        position: relative;
        width: 60px;
        flex-shrink: 0;
      }
      .full-link_item_title {
        font-size: 28px;
        line-height: 34px;
        margin-top: 13px;
        max-width: 600px;
      }
      .full-link_item_desc {
        color: #767676;
        display: none;
        font-size: 16px;
        height: fit-content;
        line-height: 22px;
        margin-top: 12px;
        max-width: 521px;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .full-link_item_enter .full-link_icon_wrapper {
        border-width: 0;
        -webkit-box-shadow: 0 0 0 6px #fff;
        box-shadow: 0 0 0 6px #fff;
        transform: scale(1.2);
      }
      .full-link_item_enter .full-link_item_title {
        color: #51200b;
        font-size: 30px;
        font-weight: 500;
        line-height: 44px;
        margin-top: -11px;
      }

      .full-link_item_enter .full-link_item_desc {
        display: -webkit-box;
      }
      .full-link_item_tail {
        background-color: #ddd;
        height: 100%;
        left: 68.5px;
        position: absolute;
        width: 2px;
      }
      .full-link_item_img {
        // height: 400px;
        max-width: 450px;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 492px;
      }
    }
    .full-link_item_enter:last-of-type ~ .full-link_item_tail {
      height: calc(100% - 66px);
    }
  }
}

.login-guide {
  min-width: 1280px;
  align-items: center;
  background-color: #473229;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 1920px 456px;
  display: flex;
  flex-direction: column;
  font-size: 20px;
  height: 456px;
  overflow: hidden;
  text-align: center;
  width: 100%;
  color: #222;
  .login-guide-wrapper {
    margin: auto;
    width: 1000px;
    color: #fff;

    .login-title {
      font-size: 44px;
      font-weight: 500;
      line-height: 52px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }
    .login-desc {
      font-size: 20px;
      line-height: 26px;
      margin-top: 32px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }
    .section_banner-button {
      font-size: 22px;
      line-height: 30px;
      padding: 30px 50px;
      border-radius: 30px;
      margin-top: 48px;
      box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
    }
  }
}
.user-video {
  min-width: 1280px;
  padding: 80px 120px;
  .user-video-wrapper {
    margin: auto;
    width: 1000px;
  }
  .video-title {
    font-size: 36px;
    font-weight: 500;
    line-height: 52px;
    text-align: center;
  }
  .video-wrapper {
    width: 220px;
    margin: 50px 0 30px 0;
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      border-radius: 12px;
    }
    .video-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #fff;
      width: 36px;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.custom-arrow-hot {
  .custom-arrow-icon {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff; /* 设置背景颜色为灰色 */
  }
  .custom-arrow--left {
    left: 0;
  }
  .custom-arrow--right {
    right: 0;
  }
}

.n-card :deep(.n-card__content) {
  padding: 0.25rem 0.25rem;
}
</style>
