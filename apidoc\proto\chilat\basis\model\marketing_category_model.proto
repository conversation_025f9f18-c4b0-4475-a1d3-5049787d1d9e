syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";
import "common.proto";


// 营销分类信息
message MidMarketingCategoryModel {
  string id = 1; // 营销分类id
  string parentId = 2; // 营销分类父id
  int32 cateLevel = 3; // 营销分类级别,1:一级类目,2:二级类目,3:三级类目
  int32 idx = 4; // 排序
  string cateName = 5; // 营销分类名称
  string cateAlias = 6; // 营销分类-别名
  string cateLogo = 7; // 营销分类logo
  int32 goodsCount = 8; //商品数量（通过营销分类搜索得到）
  repeated MidMarketingCategoryModel children = 9; // 下级营销分类
  repeated MarketingRuleModel rules = 10;
}

message MidMarketingCategoryTreeResp {
  common.Result result = 1;
  repeated MidMarketingCategoryModel data = 2;
}

message MarketingRuleModel {
  string id = 1;
  string ruleName = 2; //规则名称
}

message MidMarketingCategoryInfoResp {
  common.Result result = 1;
  MidMarketingCategoryInfoModel data = 2;
}

message MidMarketingCategoryInfoModel {
  string id = 10; // 营销分类id
  string categoryName = 20; // 营销分类名称
  bool enabled = 30; // 是否禁用
}