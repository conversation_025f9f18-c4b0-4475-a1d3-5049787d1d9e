syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";
import "chilat/basis/model/config_model.proto";
import "chilat/basis/model/goods_model.proto";
import "chilat/basis/model/cart_model.proto";

message MallConfigMultiQueryResp {
  common.Result result = 1;
  MallConfigMultiQueryModel data = 2;
}

message MallConfigMultiQueryModel {
  ConfigModel globalConfigModel = 10;
  CategoryRelatedBrandsModel categoryRelatedBrandsModel = 20;
  MidCartStatModel cartStatModel = 30;
  MidCartModel cartInfoModel = 35; //购物车数据
  MallCategoryTreeModel categoryTreeModel = 40;
  repeated MallCategoryPathItemModel categoryPathModel = 50; //商品分类路径
}

message CategoryRelatedBrandsModel {
  repeated common.IdNameModel optionalBrands = 10; //可选择的品牌（id为品牌ID，name为品牌名称）
}





