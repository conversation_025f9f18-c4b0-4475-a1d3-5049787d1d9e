<template>
  <div :class="isMobile ? 'mobile-searchbar' : 'header-searchbar'">
    <template v-if="isMobile">
      <div class="header-search" data-spm-box="navigation-keyword-search">
        <div class="search-bar-inner">
          <div class="search-bar-input-wrapper">
            <img
              loading="lazy"
              alt="search"
              class="w-[0.42rem]"
              src="@/assets/icons/searchAc.svg"
              referrerpolicy="no-referrer"
            />
            <input
              type="text"
              maxlength="50"
              class="search-bar-input"
              v-model.trim="pageData.keyword"
              @keyup.enter="onKeywordClick(pageData.keyword, $event)"
              :placeholder="authStore.i18n('cm_home.mobileSearchPlaceholder')"
            />
          </div>
          <button
            class="search-bar-inner-button"
            @click="onKeywordClick(pageData.keyword, $event)"
          >
            <span>{{ authStore.i18n("cm_home.search") }}</span>
          </button>
        </div>
      </div>
      <div>
        <image-search>
          <div class="image-search-inner">
            <img
              loading="lazy"
              src="@/assets/icons/imageSearch.svg"
              alt="search"
              class="image-icon"
              referrerpolicy="no-referrer"
            />
            <span>{{ authStore.i18n("cm_common.imageSearch") }}</span>
          </div>
        </image-search>
      </div>
    </template>
    <template v-else>
      <div class="header-search" data-spm-box="navigation-keyword-search">
        <div class="search-bar-inner">
          <div class="search-bar-input-wrapper">
            <input
              class="search-bar-input"
              type="text"
              maxlength="50"
              v-model.trim="pageData.keyword"
              :placeholder="
                isPcGoodsListTagSearch
                  ? authStore.i18n('cm_search.searchInList')
                  : authStore.i18n('cm_home.searchPlaceholder')
              "
              @keyup.enter="onKeywordClick(pageData.keyword, $event)"
            />
          </div>
          <div class="flex items-center">
            <button
              class="search-bar-inner-button"
              @click="onKeywordClick(pageData.keyword, $event)"
            >
              <img
                loading="lazy"
                src="@/assets/icons/search.svg"
                alt="search"
                class="w-[22px] mr-[3px]"
                referrerpolicy="no-referrer"
              />
              <span>{{ authStore.i18n("cm_home.search") }}</span>
            </button>
          </div>
        </div>
      </div>
      <div>
        <image-search-multi>
          <div class="image-search-inner">
            <img
              loading="lazy"
              src="@/assets/icons/imageSearch.svg"
              alt="search"
              class="image-icon"
              referrerpolicy="no-referrer"
            />
            <span>{{ authStore.i18n("cm_common.imageSearch") }}</span>
          </div>
        </image-search-multi>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts" name="TextImageSearch">
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive<any>({
  keyword: route.query.keyword || "",
});

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const isPcGoodsListTagSearch = computed(() => {
  return (
    /^\/goods\/list/.test(route.path) &&
    (route.query?.type === "recommendSearch" || route.query?.tagId)
  );
});

function onKeywordClick(keyword: string, event: any) {
  const word =
    keyword?.trim() === "" ? pageData.keyword?.trim() : keyword?.trim();

  window?.MyStat?.addPageEvent("click_search", `搜索关键词：${word}`); // 埋点

  if (isMobile.value) {
    navigateToPage(
      `/h5/search/list`,
      {
        keyword,
      },
      false,
      event
    );
  } else {
    // 义乌货盘以及美客多货盘,推荐商品列表是搜索为该货盘搜索
    if (isPcGoodsListTagSearch.value) {
      navigateToPage(
        `${goodsListAllPath}`,
        {
          keyword: word,
          ...(route.query.type === "recommendSearch" && {
            type: "recommendSearch",
          }),
          ...(route.query.tagId && {
            tagId: route.query.tagId,
            tag: route.query.tag,
          }),
        },
        false,
        event
      );
    } else {
      navigateToPage(
        `${goodsListAllPath}`,
        {
          keyword: word,
        },
        true,
        event
      );
    }
  }
}
</script>
<style scoped lang="scss">
.header-searchbar {
  flex: 1;
  margin: 0 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  .header-search {
    flex-basis: 60%;
    height: 44px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .search-bar-inner {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 44px;
      width: 100%;
      border-radius: 200px;
      background-color: #fff;
      border: 1px solid #e50113;
      .search-bar-input-wrapper {
        width: 100%;
        margin: 0 20px;
      }
      .search-bar-input {
        width: 100%;
        margin: 0;
        padding: 0;
        outline: none;
        border: none;
        background-image: none;
        background-color: transparent;
        box-shadow: none;
        color: #222;
        background-color: #fff;
      }
      .search-bar-inner-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 96px;
        height: 38px;
        margin-right: 2px;
        margin-left: 8px;
        font-size: 16px;
        line-height: 16px;
        font-weight: 500;
        color: #fff;
        background: #e50113;
        border-radius: 200px;
        border: 0 solid transparent;
        cursor: pointer;
      }
    }
  }
  .image-search-inner {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 12px;
    padding-right: 12px;
    margin-left: 10px;
    font-size: 16px;
    font-weight: 500;
    line-height: 16px;
    border-radius: 200px;
    background: #e50113;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
    color: #fff;
    cursor: pointer;
    .image-icon {
      width: 28px;
      margin-right: 6px;
    }
  }
}

.mobile-searchbar {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  .header-search {
    flex: 1;
    height: 0.76rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .search-bar-inner {
      width: 100%;
      height: 0.76rem;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4rem;
      background-color: #fff;
      border: 1px solid #e50113;
      .search-bar-input-wrapper {
        flex: 1;
        display: flex;
        margin: 0 0.04rem 0 0.16rem;
      }
      .search-bar-input {
        flex: 1;
        width: 100%;
        margin: 0;
        padding: 0;
        outline: none;
        border: none;
        box-shadow: none;
        background-image: none;
        background-color: transparent;
        font-size: 0.24rem;
        line-height: 0.24rem;
        color: #222;
        background-color: #fff;
        margin-left: 0.12rem;
        margin-right: 0.08rem;
      }
      .search-bar-inner-button {
        width: 1.1rem;
        height: 0.64rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.04rem;
        font-size: 0.26rem;
        line-height: 0.26rem;
        font-weight: 500;
        color: #fff;
        background: #e50113;
        border-radius: 4rem;
        border: 0 solid transparent;
        cursor: pointer;
      }
    }
  }
  .image-search-inner {
    width: 1.8rem;
    height: 0.76rem;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 0.1rem;
    margin-left: 0.08rem;
    font-size: 0.24rem;
    font-weight: 500;
    line-height: 0.23rem;
    border-radius: 4rem;
    background: #e50113;
    box-shadow: 0px 1px 0.04rem 0px rgba(0, 0, 0, 0.2);
    color: #fff;
    cursor: pointer;
    .image-icon {
      width: 0.46rem;
      margin-right: 0.04rem;
    }
    span {
      transform: scale(0.89); /* 仅缩放字体 */
      transform-origin: left;
    }
  }
}

input::placeholder {
  color: #bfbfbf !important;
  opacity: 1;
}
</style>
