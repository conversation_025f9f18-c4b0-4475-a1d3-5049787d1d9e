export const storageCategoryTree = "lxs_user.categoryTree";
export const storageUserInfo = "lxs_user.userInfo";
export const storageLangConfig = "lxs_user.langConfig";
export const storageSiteOption = "lxs_user.siteOption";
export const storagePageThemeMobile = "lxs_user.pageThemeMobile";
export const storagePageThemeDesktop = "lxs_user.pageThemeDesktop";
export const goodsListPath = "/goods/list";
export const goodsListAllPath = "/goods/list/all";
export const goodsDetailMPath = "/h5/goods";
export const goodsDetailPCPath = "/goods";

// 埋点阈值
export const scrollThresholds = [
  { percentage: 5, event: "SCROLL_P05", remark: "页面滚动5%" },
  { percentage: 10, event: "SCROLL_P10", remark: "页面滚动10%" },
  { percentage: 20, event: "SCROLL_P20", remark: "页面滚动20%" },
  { percentage: 30, event: "SCROLL_P30", remark: "页面滚动30%" },
  { percentage: 50, event: "SCROLL_P50", remark: "页面滚动50%" },
  { percentage: 70, event: "SCROLL_P70", remark: "页面滚动70%" },
  { percentage: 90, event: "SCROLL_P90", remark: "页面滚动90%" },
  { percentage: 100, event: "SCROLL_MAX", remark: "页面滚动到底" },
];

// 分类卡片颜色
export const cateColorArr = reactive(<any>[
  {
    textColor: "#FFF",
    cateColor: "#ff3242",
  },
  {
    textColor: "#FFF",
    cateColor: "#ff5e6a",
  },
  {
    textColor: "#FFF",
    cateColor: "#ff7659",
  },
  {
    textColor: "#FFF",
    cateColor: "#ffae42",
  },
  {
    textColor: "#FFF",
    cateColor: "#ffd700",
  },
  {
    textColor: "#FFF",
    cateColor: "#7a7687",
  },
  {
    textColor: "#FFF",
    cateColor: "#9ec8c7",
  },
  {
    textColor: "#FFF",
    cateColor: "#548fad",
  },
  {
    textColor: "#FFF",
    cateColor: "#99aabe",
  },
  {
    textColor: "#FFF",
    cateColor: "#4b928e",
  },
  {
    textColor: "#FFF",
    cateColor: "#8f73c0",
  },
  {
    textColor: "#FFF",
    cateColor: "#4671f3",
  },
  {
    textColor: "#FFF",
    cateColor: "#314a95",
  },
  {
    textColor: "#FFF",
    cateColor: "#66cc99",
  },
  {
    textColor: "#FFF",
    cateColor: "#993366",
  },
]);
