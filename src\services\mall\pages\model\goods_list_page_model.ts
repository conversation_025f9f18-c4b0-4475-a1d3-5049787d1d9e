/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { MidCartModel } from "../../../chilat/basis/model/cart_model";
import { ConfigModel } from "../../../chilat/basis/model/config_model";
import { MallCategoryPathItemModel, MallCategoryTreeModel } from "../../../chilat/basis/model/goods_model";
import { Page, Result } from "../../../common";
import { SeoData } from "../../../common/business";
import {
  GoodsListFilterType,
  goodsListFilterTypeFromJSON,
  goodsListFilterTypeToJSON,
} from "../../commodity/commodity_common";
import { GoodsListDataItemModel } from "../../commodity/model/goods_info_model";

export const protobufPackage = "mall.pages";

/** 浏览器打开页面返回的数据（服务器端渲染） */
export interface GoodsListPageResp {
  result: Result | undefined;
  data: GoodsListPageModel | undefined;
}

export interface GoodsListPageModel {
  /** 商城全局配置（当前语言，翻译信息等） */
  globalConfig:
    | ConfigModel
    | undefined;
  /** passport.LoginUserModel loginUser = 20; //登录用户信息（空值表示未登录） */
  seoData:
    | SeoData
    | undefined;
  /** 类目树 */
  categoryTree:
    | MallCategoryTreeModel
    | undefined;
  /** 商品分类路径 */
  categoryPath: MallCategoryPathItemModel[];
  /** repeated common.IdNameModel optionalBrands = 60; //可选择的品牌（id为品牌ID，name为品牌名称） */
  cartInfo:
    | MidCartModel
    | undefined;
  /** 页面内主要数据内容 */
  pageData: GoodsListDataModel | undefined;
}

/** 页面内Ajax请求的返回的数据对象 */
export interface GoodsListDataResp {
  result: Result | undefined;
  data: GoodsListDataModel | undefined;
}

/** 商品列表数据 */
export interface GoodsListDataModel {
  /** 商品列表的分页信息 */
  page:
    | Page
    | undefined;
  /** 商品列表 */
  goodsList: GoodsListDataItemModel[];
  /** 搜索关键字的分词价格（用空格分隔） */
  tokenizeWords: string;
  /** 选择的过滤项 */
  selectedFilters: GoodsListSelectedFilterModel[];
  /** 商品分类过滤选项 */
  categoryFilters: GoodsListCategoryFilterModel[];
}

/** 商品列表已选择的过滤项 */
export interface GoodsListSelectedFilterModel {
  filterType: GoodsListFilterType;
  /** ID（可为空，比如价格过滤） */
  id: string;
  /** 名称 */
  name: string;
}

/** 商品列表中商品分类过滤选项 */
export interface GoodsListCategoryFilterModel {
  /** 营销分类ID */
  id: string;
  /** 营销分类名称 */
  name: string;
  /** 是否已选中（null表示未选中，字段不输出到前端） */
  selected: boolean;
  /** 下级分类过滤项（null表示不存在，字段不输出到前端） */
  children: GoodsListCategoryFilterModel[];
}

function createBaseGoodsListPageResp(): GoodsListPageResp {
  return { result: undefined, data: undefined };
}

export const GoodsListPageResp = {
  encode(message: GoodsListPageResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GoodsListPageModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListPageResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListPageResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GoodsListPageModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListPageResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GoodsListPageModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GoodsListPageResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GoodsListPageModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListPageResp>, I>>(base?: I): GoodsListPageResp {
    return GoodsListPageResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListPageResp>, I>>(object: I): GoodsListPageResp {
    const message = createBaseGoodsListPageResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GoodsListPageModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGoodsListPageModel(): GoodsListPageModel {
  return {
    globalConfig: undefined,
    seoData: undefined,
    categoryTree: undefined,
    categoryPath: [],
    cartInfo: undefined,
    pageData: undefined,
  };
}

export const GoodsListPageModel = {
  encode(message: GoodsListPageModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.globalConfig !== undefined) {
      ConfigModel.encode(message.globalConfig, writer.uint32(82).fork()).ldelim();
    }
    if (message.seoData !== undefined) {
      SeoData.encode(message.seoData, writer.uint32(242).fork()).ldelim();
    }
    if (message.categoryTree !== undefined) {
      MallCategoryTreeModel.encode(message.categoryTree, writer.uint32(322).fork()).ldelim();
    }
    for (const v of message.categoryPath) {
      MallCategoryPathItemModel.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    if (message.cartInfo !== undefined) {
      MidCartModel.encode(message.cartInfo, writer.uint32(562).fork()).ldelim();
    }
    if (message.pageData !== undefined) {
      GoodsListDataModel.encode(message.pageData, writer.uint32(802).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListPageModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListPageModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.globalConfig = ConfigModel.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.seoData = SeoData.decode(reader, reader.uint32());
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.categoryTree = MallCategoryTreeModel.decode(reader, reader.uint32());
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.categoryPath.push(MallCategoryPathItemModel.decode(reader, reader.uint32()));
          continue;
        case 70:
          if (tag !== 562) {
            break;
          }

          message.cartInfo = MidCartModel.decode(reader, reader.uint32());
          continue;
        case 100:
          if (tag !== 802) {
            break;
          }

          message.pageData = GoodsListDataModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListPageModel {
    return {
      globalConfig: isSet(object.globalConfig) ? ConfigModel.fromJSON(object.globalConfig) : undefined,
      seoData: isSet(object.seoData) ? SeoData.fromJSON(object.seoData) : undefined,
      categoryTree: isSet(object.categoryTree) ? MallCategoryTreeModel.fromJSON(object.categoryTree) : undefined,
      categoryPath: globalThis.Array.isArray(object?.categoryPath)
        ? object.categoryPath.map((e: any) => MallCategoryPathItemModel.fromJSON(e))
        : [],
      cartInfo: isSet(object.cartInfo) ? MidCartModel.fromJSON(object.cartInfo) : undefined,
      pageData: isSet(object.pageData) ? GoodsListDataModel.fromJSON(object.pageData) : undefined,
    };
  },

  toJSON(message: GoodsListPageModel): unknown {
    const obj: any = {};
    if (message.globalConfig !== undefined) {
      obj.globalConfig = ConfigModel.toJSON(message.globalConfig);
    }
    if (message.seoData !== undefined) {
      obj.seoData = SeoData.toJSON(message.seoData);
    }
    if (message.categoryTree !== undefined) {
      obj.categoryTree = MallCategoryTreeModel.toJSON(message.categoryTree);
    }
    if (message.categoryPath?.length) {
      obj.categoryPath = message.categoryPath.map((e) => MallCategoryPathItemModel.toJSON(e));
    }
    if (message.cartInfo !== undefined) {
      obj.cartInfo = MidCartModel.toJSON(message.cartInfo);
    }
    if (message.pageData !== undefined) {
      obj.pageData = GoodsListDataModel.toJSON(message.pageData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListPageModel>, I>>(base?: I): GoodsListPageModel {
    return GoodsListPageModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListPageModel>, I>>(object: I): GoodsListPageModel {
    const message = createBaseGoodsListPageModel();
    message.globalConfig = (object.globalConfig !== undefined && object.globalConfig !== null)
      ? ConfigModel.fromPartial(object.globalConfig)
      : undefined;
    message.seoData = (object.seoData !== undefined && object.seoData !== null)
      ? SeoData.fromPartial(object.seoData)
      : undefined;
    message.categoryTree = (object.categoryTree !== undefined && object.categoryTree !== null)
      ? MallCategoryTreeModel.fromPartial(object.categoryTree)
      : undefined;
    message.categoryPath = object.categoryPath?.map((e) => MallCategoryPathItemModel.fromPartial(e)) || [];
    message.cartInfo = (object.cartInfo !== undefined && object.cartInfo !== null)
      ? MidCartModel.fromPartial(object.cartInfo)
      : undefined;
    message.pageData = (object.pageData !== undefined && object.pageData !== null)
      ? GoodsListDataModel.fromPartial(object.pageData)
      : undefined;
    return message;
  },
};

function createBaseGoodsListDataResp(): GoodsListDataResp {
  return { result: undefined, data: undefined };
}

export const GoodsListDataResp = {
  encode(message: GoodsListDataResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GoodsListDataModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListDataResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListDataResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GoodsListDataModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListDataResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GoodsListDataModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GoodsListDataResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GoodsListDataModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListDataResp>, I>>(base?: I): GoodsListDataResp {
    return GoodsListDataResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListDataResp>, I>>(object: I): GoodsListDataResp {
    const message = createBaseGoodsListDataResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GoodsListDataModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGoodsListDataModel(): GoodsListDataModel {
  return { page: undefined, goodsList: [], tokenizeWords: "", selectedFilters: [], categoryFilters: [] };
}

export const GoodsListDataModel = {
  encode(message: GoodsListDataModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(82).fork()).ldelim();
    }
    for (const v of message.goodsList) {
      GoodsListDataItemModel.encode(v!, writer.uint32(162).fork()).ldelim();
    }
    if (message.tokenizeWords !== "") {
      writer.uint32(242).string(message.tokenizeWords);
    }
    for (const v of message.selectedFilters) {
      GoodsListSelectedFilterModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    for (const v of message.categoryFilters) {
      GoodsListCategoryFilterModel.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListDataModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListDataModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.goodsList.push(GoodsListDataItemModel.decode(reader, reader.uint32()));
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.tokenizeWords = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.selectedFilters.push(GoodsListSelectedFilterModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.categoryFilters.push(GoodsListCategoryFilterModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListDataModel {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      goodsList: globalThis.Array.isArray(object?.goodsList)
        ? object.goodsList.map((e: any) => GoodsListDataItemModel.fromJSON(e))
        : [],
      tokenizeWords: isSet(object.tokenizeWords) ? globalThis.String(object.tokenizeWords) : "",
      selectedFilters: globalThis.Array.isArray(object?.selectedFilters)
        ? object.selectedFilters.map((e: any) => GoodsListSelectedFilterModel.fromJSON(e))
        : [],
      categoryFilters: globalThis.Array.isArray(object?.categoryFilters)
        ? object.categoryFilters.map((e: any) => GoodsListCategoryFilterModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GoodsListDataModel): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.goodsList?.length) {
      obj.goodsList = message.goodsList.map((e) => GoodsListDataItemModel.toJSON(e));
    }
    if (message.tokenizeWords !== "") {
      obj.tokenizeWords = message.tokenizeWords;
    }
    if (message.selectedFilters?.length) {
      obj.selectedFilters = message.selectedFilters.map((e) => GoodsListSelectedFilterModel.toJSON(e));
    }
    if (message.categoryFilters?.length) {
      obj.categoryFilters = message.categoryFilters.map((e) => GoodsListCategoryFilterModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListDataModel>, I>>(base?: I): GoodsListDataModel {
    return GoodsListDataModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListDataModel>, I>>(object: I): GoodsListDataModel {
    const message = createBaseGoodsListDataModel();
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.goodsList = object.goodsList?.map((e) => GoodsListDataItemModel.fromPartial(e)) || [];
    message.tokenizeWords = object.tokenizeWords ?? "";
    message.selectedFilters = object.selectedFilters?.map((e) => GoodsListSelectedFilterModel.fromPartial(e)) || [];
    message.categoryFilters = object.categoryFilters?.map((e) => GoodsListCategoryFilterModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGoodsListSelectedFilterModel(): GoodsListSelectedFilterModel {
  return { filterType: 0, id: "", name: "" };
}

export const GoodsListSelectedFilterModel = {
  encode(message: GoodsListSelectedFilterModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.filterType !== 0) {
      writer.uint32(80).int32(message.filterType);
    }
    if (message.id !== "") {
      writer.uint32(162).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(242).string(message.name);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListSelectedFilterModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListSelectedFilterModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.filterType = reader.int32() as any;
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.id = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListSelectedFilterModel {
    return {
      filterType: isSet(object.filterType) ? goodsListFilterTypeFromJSON(object.filterType) : 0,
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: GoodsListSelectedFilterModel): unknown {
    const obj: any = {};
    if (message.filterType !== 0) {
      obj.filterType = goodsListFilterTypeToJSON(message.filterType);
    }
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListSelectedFilterModel>, I>>(base?: I): GoodsListSelectedFilterModel {
    return GoodsListSelectedFilterModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListSelectedFilterModel>, I>>(object: I): GoodsListSelectedFilterModel {
    const message = createBaseGoodsListSelectedFilterModel();
    message.filterType = object.filterType ?? 0;
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseGoodsListCategoryFilterModel(): GoodsListCategoryFilterModel {
  return { id: "", name: "", selected: false, children: [] };
}

export const GoodsListCategoryFilterModel = {
  encode(message: GoodsListCategoryFilterModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    if (message.selected !== false) {
      writer.uint32(240).bool(message.selected);
    }
    for (const v of message.children) {
      GoodsListCategoryFilterModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsListCategoryFilterModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsListCategoryFilterModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.selected = reader.bool();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.children.push(GoodsListCategoryFilterModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsListCategoryFilterModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      selected: isSet(object.selected) ? globalThis.Boolean(object.selected) : false,
      children: globalThis.Array.isArray(object?.children)
        ? object.children.map((e: any) => GoodsListCategoryFilterModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GoodsListCategoryFilterModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.selected !== false) {
      obj.selected = message.selected;
    }
    if (message.children?.length) {
      obj.children = message.children.map((e) => GoodsListCategoryFilterModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsListCategoryFilterModel>, I>>(base?: I): GoodsListCategoryFilterModel {
    return GoodsListCategoryFilterModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsListCategoryFilterModel>, I>>(object: I): GoodsListCategoryFilterModel {
    const message = createBaseGoodsListCategoryFilterModel();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.selected = object.selected ?? false;
    message.children = object.children?.map((e) => GoodsListCategoryFilterModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
