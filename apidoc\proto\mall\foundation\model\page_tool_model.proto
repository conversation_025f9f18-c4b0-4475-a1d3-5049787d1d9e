syntax = "proto3";
package mall.foundation;

option java_package = "com.chilat.rpc.foundation.model";
import "chilat/support/model/article_category_model.proto";
import "common.proto";


//获取WhatsApp链接返回
message GetWhatsAppLinkResp {
  common.Result result = 1;
  GetWhatsAppLinkModel data = 2;
}

//获取WhatsApp链接结果
message GetWhatsAppLinkModel {
  string jumpUrl = 10; //WhatsApp应用的跳转链接
  string webUrl = 20; //WhatsApp Web的跳转链接
  string whatsapp = 30; //官网WhatsApp服务号
}

