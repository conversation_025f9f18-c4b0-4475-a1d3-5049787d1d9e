<template>
  <div
    class="coupon-card"
    :class="{
      'comm-coupon-card': coupon?.couponType === 'COUPON_TYPE_COMMISSION',
      'grey-coupon-card': coupon?.ticketStatus !== 'TICKET_NOT_USE',
      'mobile-coupon-card': isMobile,
    }"
    :style="{ width: couponWidth }"
  >
    <div class="coupon-type">
      <span v-if="coupon?.couponType === 'COUPON_TYPE_PRODUCT'">{{
        authStore.i18n("cm_coupon.productCoupon")
      }}</span>
      <span v-if="coupon?.couponType === 'COUPON_TYPE_COMMISSION'">{{
        authStore.i18n("cm_coupon.commissionCoupon")
      }}</span>
    </div>
    <div class="coupon-status">
      <img
        loading="lazy"
        v-if="coupon?.ticketStatus === 'TICKET_USE'"
        src="@/assets/icons/marketing/expiredCoupon.svg"
        alt="status"
        referrerpolicy="no-referrer"
      />
      <img
        loading="lazy"
        v-if="coupon?.ticketStatus === 'TICKET_LOSE_EFFICACY'"
        src="@/assets/icons/marketing/invalidCoupon.svg"
        alt="status"
        referrerpolicy="no-referrer"
      />
    </div>

    <!-- 优惠方式 -->
    <div>
      <!-- 满减券 -->
      <div
        class="coupon-content"
        v-if="coupon?.couponWay === 'COUPON_WAY_FULL_REDUCTION'"
      >
        <div class="coupon-title">
          {{ authStore.i18n("cm_coupon.fullReductionCoupon") }}
        </div>
        <div>
          <span class="coupon-unit">{{ currencyUnit }}</span>
          <span class="coupon-amount"
            >$ {{ setNewUnit(coupon?.preferentialAmount, "noUnit") }}</span
          >
        </div>
        <div class="coupon-condition">
          <!-- 满多少金额使用 -->
          <template v-if="coupon?.couponUseConditionsType === 'FULL'">
            <span>
              {{ authStore.i18n("cm_coupon.minimumRequired") }}
            </span>
            {{ setNewUnit(coupon?.useConditionsAmount) }}
          </template>
          <!-- 每满多少金额使用 -->
          <template v-if="coupon?.couponUseConditionsType === 'EVERY_FULL'">
            <span>
              {{ authStore.i18n("cm_coupon.minimumUnmet") }}
            </span>
            {{ setNewUnit(coupon?.useConditionsAmount) }}
            <span>
              {{ authStore.i18n("cm_coupon.minimumUnmetCost") }}
            </span>
          </template>
        </div>
      </div>
      <!-- 直减券 -->
      <div
        class="coupon-content"
        v-if="coupon?.couponWay === 'COUPON_WAY_DIRECT_REDUCTION'"
      >
        <div class="coupon-title">
          {{ authStore.i18n("cm_coupon.directReductionCoupon") }}
        </div>
        <div>
          <span class="coupon-unit">{{ currencyUnit }}</span>
          <span class="coupon-amount"
            >$ {{ setNewUnit(coupon?.preferentialAmount, "noUnit") }}</span
          >
        </div>
        <!-- 不限制金额 -->
        <div class="coupon-condition">
          {{ authStore.i18n("cm_coupon.noLimit") }}
        </div>
      </div>
      <!-- 折扣券 -->
      <div
        class="coupon-content"
        v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'"
      >
        <div class="coupon-title">
          {{ authStore.i18n("cm_coupon.discountCoupon") }}
        </div>
        <div class="flex items-center">
          <div class="flex items-start">
            <div class="coupon-amount coupon-discount-amount">
              {{ discountToPercentage(coupon.discount) }}
            </div>
            <div class="coupon-discount">
              {{ authStore.i18n("cm_coupon.discount") }}
            </div>
          </div>
          <div v-if="coupon?.preferentialAmount" class="coupon-upto">
            {{ authStore.i18n("cm_coupon.upToMoney") }}
            {{ setNewUnit(coupon?.preferentialAmount) }}
          </div>
        </div>

        <div class="coupon-condition">
          <div>
            <!-- 满多少金额使用 -->
            <span v-if="coupon?.couponUseConditionsType === 'FULL'">
              <span>
                {{ authStore.i18n("cm_coupon.minimumRequired") }}
              </span>
              {{ setNewUnit(coupon?.useConditionsAmount) }}
            </span>
          </div>
          <!-- 不限制金额 -->
          <div v-if="coupon?.couponUseConditionsType === 'UNLIMITED'">
            {{ authStore.i18n("cm_coupon.noLimit") }}
          </div>
        </div>
      </div>
    </div>

    <div class="coupon-cycle">
      <div class="coupon-dashed"></div>
      <div class="coupon-left-cycle"></div>
      <div class="coupon-right-cycle"></div>
    </div>

    <!-- 使用说明 -->
    <div class="coupon-desc">
      <div class="flex items-center">
        <div class="coupon-time">
          {{ timeFormatByZone(coupon?.ticketStartExpirationDate, false) }} -
          {{ timeFormatByZone(coupon?.ticketEndExpirationDate, false) }}
        </div>
        <div
          class="coupon-arrow"
          @click="onShowMore"
          v-if="
            !!coupon?.userInstructions ||
            !!onFormatCouponRules(coupon?.couponInfoUseRuleModelList)
          "
        >
          <img
            loading="lazy"
            v-if="pageData.showMore"
            src="@/assets/icons/topArrow.svg"
            alt="arrow"
            referrerpolicy="no-referrer"
          />
          <img
            loading="lazy"
            v-else
            src="@/assets/icons/downArrow.svg"
            alt="arrow"
            referrerpolicy="no-referrer"
          />
        </div>
      </div>
      <div v-if="pageData.showMore">
        <div class="coupon-instructions" v-if="coupon?.userInstructions">
          {{ authStore.i18n("cm_coupon_instructions") }}:
          <span class="whitespace-pre-wrap break-all">{{
            coupon?.userInstructions
          }}</span>
        </div>
        <div
          class="coupon-rules"
          v-if="onFormatCouponRules(coupon?.couponInfoUseRuleModelList)"
        >
          {{ authStore.i18n("cm_coupon_rules") }}:
          {{ onFormatCouponRules(coupon?.couponInfoUseRuleModelList) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="CouponCard">
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const props = defineProps({
  coupon: {
    type: Object,
    default: () => {},
  },
  pageSource: {
    type: String,
    default: "modal",
  },
  couponWidth: {
    type: String,
    default: "",
  },
});

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const pageData = reactive(<any>{
  showMore: false,
});

function onShowMore() {
  pageData.showMore = !pageData.showMore;
}

function onFormatCouponRules(rules: any) {
  if (!Array.isArray(rules) || rules.length === 0) {
    return "";
  }
  const ruleDescriptions = rules.map((rule) => {
    const { useRuleCount, useRuleType } = rule;
    switch (useRuleType) {
      case "COUPON_MAXIMUM_AVAILABLE":
        // 只有在 useRuleCount 不等于 -1 时才拼接字符串
        if (useRuleCount !== -1) {
          return (
            authStore.i18n("cm_coupon.maxUsage") +
            " " +
            useRuleCount +
            " " +
            authStore.i18n("cm_coupon.maxUsageUnit")
          );
        }
        // 如果 useRuleCount 为 -1，返回空字符串或其他逻辑
        return "";
      case "COUPON_SAME_TYPE_OVERLAY":
        return authStore.i18n("cm_coupon.sameTypeOverlay");
      case "COUPON_FIRST_USE":
        return authStore.i18n("cm_coupon.firstOrderOnly");
      default:
        return "";
    }
  });
  return ruleDescriptions.filter((desc) => desc).join(" & ");
}
</script>

<style scoped lang="scss">
.coupon-card {
  width: 100%;
  background: #fef5f67f;
  color: #ff4056;
  position: relative;
  overflow: hidden;
  .coupon-type {
    display: inline-flex;
    padding: 4px 10px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    background: #db6b73;
    color: #fff;
    font-size: 12px;
    font-style: oblique;
    font-weight: 700;
    line-height: 12px;
    text-transform: uppercase;
    position: absolute;
    left: 0;
    top: 0;
    letter-spacing: 1px;
    z-index: 10;
  }
  .coupon-status {
    position: absolute;
    right: 12px;
    top: 110px;
    z-index: 10;
    img {
      width: 68px;
      transform: rotate(-30deg);
    }
  }
  .coupon-content {
    padding: 28px 18px 10px;
    position: relative;
    border: 1px solid #ff94a87f;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;

    .coupon-title {
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
      margin-bottom: 12px;
    }
    .coupon-unit {
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      margin-right: 4px;
    }
    .coupon-amount {
      font-size: 32px;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
    }
    .coupon-discount-amount {
      width: 60px;
      text-align: right;
    }
    .coupon-discount {
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 14px;
      margin-left: 4px;
      margin-right: 8px;
    }
    .coupon-condition {
      color: #b23644;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 14px;
      margin-top: 6px;
    }
    .coupon-upto {
      text-align: center;
      font-family: Roboto;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      color: #b23644;
    }
  }

  .coupon-cycle {
    height: 12px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    .coupon-dashed {
      width: 100%;
      background-image: linear-gradient(
        to right,
        var(--midLineColor, #ff94a8) 0,
        var(--midLineColor, #ff94a8) 50%,
        transparent 50%
      );
      background-repeat: repeat-x;
      background-size: 4px 1px;
      flex: 1;
      height: 1px;
    }
    .coupon-left-cycle {
      position: absolute;
      left: -6px;
      background-color: #fff;
      border-radius: 50%;
      height: 12px;
      position: absolute;
      width: 12px;
      border: 1px solid #ff94a8;
    }
    .coupon-right-cycle {
      position: absolute;
      right: -6px;
      background-color: #fff;
      border-radius: 50%;
      height: 12px;
      position: absolute;
      width: 12px;
      border: 1px solid #ff94a8;
    }
  }

  .coupon-desc {
    padding: 8px 18px 14px;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border: 1px solid #ff94a87f;
    border-top: none;
    color: #737373;
    .coupon-time {
      width: 260px;
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 13px;
      margin-right: 20px;
    }
    .coupon-arrow {
      width: 18px;
      height: 18px;
      flex-shrink: 0;
      border: 1px solid #ccc;
      cursor: pointer;
    }
    .coupon-platform {
      font-size: 13px;
      line-height: 18px;
      margin-top: 6px;
    }
    .coupon-instructions {
      font-size: 13px;
      line-height: 18px;
      margin: 8px 0 2px;
    }
    .coupon-rules {
      font-size: 13px;
      line-height: 18px;
    }
  }
}
.comm-coupon-card {
  background: #fefaf780;
  color: #9c4a2c;
  .coupon-type {
    background: #d1a27a;
  }
  .coupon-content {
    border: 1px solid #ffc19480;
    border-bottom: none;
    .coupon-condition {
      color: #884e24;
    }
    .coupon-upto {
      color: #884e24;
    }
  }
  .coupon-cycle {
    .coupon-dashed {
      background-image: linear-gradient(
        to right,
        var(--midLineColor, #ffc194) 0,
        var(--midLineColor, #ffc194) 50%,
        transparent 50%
      );
    }
    .coupon-left-cycle {
      border: 1px solid #ffc19480;
    }
    .coupon-right-cycle {
      border: 1px solid #ffc19480;
    }
  }
  .coupon-desc {
    border: 1px solid #ffc19480;
    border-top: none;
  }
}
// 已使用/已过期
.grey-coupon-card {
  background: #f2f2f27f;
  color: #7f7f7f;
  .coupon-type {
    background: #7f7f7f;
    color: #f2f2f2;
  }
  .coupon-content {
    border: 1px solid #7f7f7f7f;
    border-bottom: none;
    .coupon-condition {
      color: #7f7f7f;
    }
    .coupon-upto {
      color: #7f7f7f;
    }
  }
  .coupon-cycle {
    .coupon-dashed {
      background-image: linear-gradient(
        to right,
        var(--midLineColor, #7f7f7f) 0,
        var(--midLineColor, #7f7f7f) 50%,
        transparent 50%
      );
    }
    .coupon-left-cycle {
      border: 1px solid #7f7f7f7f;
    }
    .coupon-right-cycle {
      border: 1px solid #7f7f7f7f;
    }
  }
  .coupon-desc {
    border: 1px solid #7f7f7f7f;
    border-top: none;
  }
}

.mobile-coupon-card {
  width: 100%;
  .coupon-type {
    padding: 0.1rem 0.2rem;
    border-radius: 0.08rem;
    font-size: 0.24rem;
    font-weight: 700;
    display: flex;
  }
  .coupon-status {
    right: 0.24rem;
    top: 1.6rem;
    z-index: 10;
    img {
      width: 1.32rem;
      transform: rotate(-30deg);
    }
  }
  .coupon-content {
    padding: 0.56rem 0.24rem 0.12rem;
    border-top-left-radius: 0.08rem;
    border-top-right-radius: 0.08rem;

    .coupon-title {
      font-size: 0.32rem;
      line-height: 0.32rem;
      margin-bottom: 0.2rem;
    }
    .coupon-unit {
      font-size: 0.32rem;
      line-height: 0.32rem;
      margin-right: 0.06rem;
    }
    .coupon-amount {
      font-size: 0.44rem;
      line-height: 0.44rem;
    }
    .coupon-discount-amount {
      width: 1rem;
    }
    .coupon-discount {
      font-size: 0.24rem;
      font-weight: 500;
      line-height: 0.24rem;
      margin-left: 0.04rem;
      margin-right: 0.16rem;
    }
    .coupon-condition {
      font-size: 0.28rem;
      font-weight: 400;
      line-height: 0.28rem;
      margin-top: 0.12rem;
    }
    .coupon-upto {
      font-size: 0.28rem;
      line-height: 0.28rem;
    }
  }

  .coupon-cycle {
    height: 0.24rem;
    .coupon-left-cycle {
      left: -0.12rem;
      height: 0.24rem;
      width: 0.24rem;
    }
    .coupon-right-cycle {
      right: -0.12rem;
      height: 0.24rem;
      width: 0.24rem;
    }
  }

  .coupon-desc {
    padding: 0.04rem 0.24rem 0.16rem;
    border-bottom-left-radius: 0.08rem;
    border-bottom-right-radius: 0.08rem;
    .coupon-time {
      width: auto;
      font-size: 0.24rem;
      line-height: 0.24rem;
      margin-right: 0.4rem;
    }
    .coupon-arrow {
      width: 0.32rem;
      height: 0.32rem;
    }
    .coupon-platform {
      font-size: 0.24rem;
      line-height: 0.34rem;
      margin-top: 0.12rem;
    }
    .coupon-instructions {
      font-size: 0.24rem;
      line-height: 0.34rem;
      margin: 0.1rem 0 0.04rem;
    }
    .coupon-rules {
      font-size: 0.24rem;
      line-height: 0.34rem;
    }
  }
}
</style>
