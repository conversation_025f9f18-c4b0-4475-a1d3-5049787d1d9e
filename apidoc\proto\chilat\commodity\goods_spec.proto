syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_spec_param.proto";
import "chilat/commodity/model/goods_spec_model.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

// 商品属性管理
service GoodsSpec {
//  // 查询列表
//  rpc listPage (GoodsSpecPageQueryParam) returns (GoodsSpecPageResp);
//  // 查询详情
//  rpc getDetail (common.IdParam) returns (GoodsSpecDetailResp);
//  // 保存属性信息（id不存在，则为新增）
//  rpc saveSpec (GoodsSpecSaveParam) returns (common.ApiResult);
//  // 查询日志
//  rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
}