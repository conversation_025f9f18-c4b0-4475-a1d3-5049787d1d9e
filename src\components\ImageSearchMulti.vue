<template>
  <n-popover
    v-model:show="showImageSearch"
    trigger="click"
    :show-arrow="false"
    :raw="true"
    :placement="props.placement"
    class="image-search-popover"
  >
    <template #trigger>
      <div @click="onTriggerClick">
        <slot></slot>
      </div>
    </template>

    <div class="image-search-container">
      <div class="flex justify-center relative">
        <div
          class="text-[18px] leading-[18px] mb-[14px] text-center font-medium"
        >
          {{ authStore.i18n("cm_search.imageSearch") }}
        </div>
        <icon-card
          name="material-symbols:close-rounded"
          size="20"
          color="#333"
          @click="showImageSearch = false"
          class="absolute right-0 cursor-pointer"
        ></icon-card>
      </div>
      <div class="text-[14px] leading-[14px] text-center">
        {{ authStore.i18n("cm_search.imageSearchTitle") }}
      </div>
      <!-- 加载状态 -->
      <div
        v-if="showLoading"
        class="flex items-center justify-center mt-[38px]"
      >
        <n-spin size="large">
          <template #description>
            <div class="text-[14px] leading-[17px] mt-[22px] text-[#666]">
              {{ authStore.i18n("cm_search.uploading") }}
            </div>
          </template>
        </n-spin>
      </div>

      <!-- 三个选项 -->
      <div
        v-else
        class="flex gap-[6px] text-[14px] leading-[16px] text-[#A6A6A6] text-center cursor-pointer mt-[14px]"
      >
        <div
          class="w-[364px] h-[148px] rounded-[6px] bg-[#FAFAFA] border-1 border-dashed border-[#D9D9D9] flex gap-[18px] px-[14px] items-center"
          @dragover="onDragOver"
          @dragleave="onDragLeave"
          @drop="onDrap"
        >
          <!-- 拖拽上传区域 -->
          <div class="flex flex-col items-center gap-[12px]">
            <img
              src="@/assets/icons/common/drag-and-drop.svg"
              alt="Arrastra y suelta una imagen aquí"
              class="w-[56px] h-[56px]"
            />
            <div class="w-[146px]">Arrastra y suelta una imagen aquí</div>
          </div>
          <p class="">o</p>

          <!-- 粘贴图片区域 -->
          <div
            @click="focusForPaste"
            class="flex flex-col items-center gap-[20px]"
          >
            <img
              src="@/assets/icons/common/ctrl-v-paste.svg"
              alt="Pega una imagen que copiaste con CTRL+V"
              class="w-[40px] h-[40px]"
            />
            <div class="w-[146px]">Pega una imagen que copiaste con CTRL+V</div>
          </div>
        </div>

        <!-- 上传文件 -->
        <div
          class="w-[364px] h-[148px] rounded-[6px] bg-[#FAFAFA] border-1 border-dashed border-[#D9D9D9] flex flex-col items-center pt-[24px] gap-[22px]"
          @click="onTriggerUpload"
        >
          <img
            src="@/assets/icons/common/upload-file.svg"
            alt="Cargar un archivo"
            class="w-[36px] h-[36px]"
          />
          <div>Cargar un archivo</div>
        </div>
      </div>
      <input
        ref="fileInputRef"
        type="file"
        accept="image/*"
        style="display: none"
        @change="onFileSelect"
        data-spm-box="navigation-image-search"
      />

      <n-alert
        v-if="errorMessage"
        class="mt-[24px]"
        type="error"
        closable
        @close="errorMessage = ''"
        :bordered="false"
        :title="authStore.i18n('cm_search.uploadError')"
      >
        <div class="text-[14px] text-[#333] mt-[8px]">
          {{ errorMessage }}
        </div>
      </n-alert>
    </div>
  </n-popover>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useAuthStore } from "@/stores/authStore";
import { ImageCompressor } from "@/utils/imageCompressor";

const message = useMessage();
const authStore = useAuthStore();
const showImageSearch = ref(false);
const showLoading = ref(false);
const fileInputRef = ref<HTMLInputElement | null>(null);
const errorMessage = ref("");
const isDragOver = ref(false);
const allowedTypes = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/bmp",
  "image/webp",
];

const props = defineProps({
  placement: {
    type: String,
    default: "bottom",
  },
});

function onTriggerClick() {
  errorMessage.value = "";
  showLoading.value = false;
  showImageSearch.value = true;
}

function focusForPaste() {
  // 聚焦到页面以便接收粘贴事件
  document.body.focus();
}

onMounted(() => {
  window.addEventListener("paste", onPaste);
});

onUnmounted(() => {
  window.removeEventListener("paste", onPaste);
});

function onValidateImage(file: File) {
  if (!allowedTypes.includes(file.type)) {
    errorMessage.value = authStore.i18n("cm_search.imageFormatError");
    return false;
  }
  return true;
}

function onTriggerUpload() {
  fileInputRef.value?.click();
}

async function onPaste(event: ClipboardEvent) {
  const items = event.clipboardData?.items;
  if (!items) return;

  for (const item of items) {
    if (item.type.indexOf("image") === 0) {
      const file = item.getAsFile();
      if (file) {
        await onProcessImage(file, "paste");
      }
      break;
    }
  }
}

function onDragOver(e: DragEvent) {
  e.preventDefault();
  e.stopPropagation();

  // 检查是否包含图片文件
  const hasImageFile = Array.from(e.dataTransfer?.items || []).some((item) =>
    item.type.startsWith("image/")
  );

  if (hasImageFile) {
    isDragOver.value = true;
  }
}

function onDragLeave(e: DragEvent) {
  e.preventDefault();
  e.stopPropagation();

  // 检查是否真的离开了拖拽区域
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
  const x = e.clientX;
  const y = e.clientY;

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false;
  }
}

async function onDrap(e: DragEvent) {
  e.preventDefault();
  e.stopPropagation();
  isDragOver.value = false;

  const files = e.dataTransfer?.files;
  if (!files?.length) return;

  const file = files[0];
  if (file.type.startsWith("image/")) {
    await onProcessImage(file, "drag");
  } else {
    errorMessage.value = authStore.i18n("cm_search.imageFormatError");
  }
}

async function onFileSelect(event: Event) {
  const input = event.target as HTMLInputElement;
  const files = input.files;
  if (files && files.length > 0) {
    const file = files[0];
    if (file.type.startsWith("image/")) {
      await onProcessImage(file, "file", event);
    }
  }
  input.value = "";
}

async function onProcessImage(file: File, uploadType?: any, event?: Event) {
  errorMessage.value = "";
  showLoading.value = true;

  const compressedFile = await ImageCompressor.compress(file, {
    maxSizeKB: 300,
    quality: 1,
    maxWidth: 500,
  });
  await onImageUploadSuccess(compressedFile, uploadType, event);
}

async function onImageUploadSuccess(
  file: File,
  uploadType?: any,
  event?: Event
) {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const BASEURL =
      typeof window == "object"
        ? useRuntimeConfig().public.clientURL
        : useRuntimeConfig().public.baseURL;

    const { data }: any = await useFetch(
      "/commodity/GoodsSearch/uploadImage1688",
      {
        baseURL: BASEURL,
        method: "POST",
        body: formData,
        cache: "no-cache",
      }
    );

    if (data?.value?.result?.code === 200) {
      const baseParams = {
        type: "imgSearch",
        imageId: data?.value?.data?.imageId,
        imageUrl: data?.value?.data?.imageUrl,
      };

      let params: any = { ...baseParams };
      if (uploadType === "paste") {
        params.spm =
          window.MyStat?.getPageSPM?.(event as Event) || "copy-image-search";
      } else if (uploadType === "drag") {
        params.spm =
          window.MyStat?.getPageSPM?.(event as Event) || "drag-image-search";
      }

      navigateToPage(
        "/goods/list/all",
        params,
        false,
        uploadType === "file" ? event : undefined
      );
    } else {
      errorMessage.value = data?.value?.result?.message;
    }
  } catch (error) {
    // errorMessage.value = authStore.i18n("cm_search.imageSearchError");
  } finally {
    showLoading.value = false;
  }
}
</script>

<style scoped lang="scss">
.image-search-popover {
  padding: 0;
  border-radius: 12px;
  box-shadow: 0 10px 25px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -2px rgb(0 0 0 / 0.05);

  :deep(.n-popover) {
    padding: 0;
    max-width: none;
  }
}

.image-search-container {
  width: 774px;
  min-height: 252px;
  background: #fff;
  border-radius: 12px;
  padding: 20px 20px 24px;
}
</style>
