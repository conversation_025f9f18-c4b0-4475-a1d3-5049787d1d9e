<template>
  <div
    :class="['custom-breadcrumb-wrapper', isMobile ? 'mobile-breadcrumb' : '']"
  >
    <ul class="custom-breadcrumb-list">
      <li
        v-for="(item, index) in items"
        :key="index"
        class="custom-breadcrumb-item"
        :class="{ 'last-item': index === items.length - 1 }"
      >
        <template v-if="item.link">
          <a :href="item.link" class="breadcrumb-link">
            <img
              v-if="item.icon"
              :alt="item.alt || 'icon'"
              class="breadcrumb-icon"
              :src="item.icon"
              loading="lazy"
            />
            <span
              v-if="item.text"
              :class="[
                'breadcrumb-text',
                textClass,
                index === items.length - 1 ? 'last-item' : '',
              ]"
              >{{ item.text }}</span
            >
          </a>
        </template>
        <template v-else>
          <div class="breadcrumb-item">
            <img
              v-if="item.icon"
              :alt="item.alt || 'icon'"
              class="breadcrumb-icon"
              :src="item.icon"
              loading="lazy"
            />
            <span
              v-if="item.text"
              :class="[
                'breadcrumb-text',
                textClass,
                index === items.length - 1 ? 'last-item' : '',
              ]"
              >{{ item.text }}</span
            >
          </div>
        </template>

        <!-- 不是最后一项时显示分隔符 -->
        <div
          v-if="index < items.length - 1"
          class="breadcrumb-separator-container"
        >
          <img
            alt="arrow"
            class="breadcrumb-separator"
            src="@/assets/icons/tiendas-panoramicas-en-3d/arrow-right.svg"
            loading="lazy"
          />
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";

interface BreadcrumbItem {
  text?: string;
  link?: string;
  icon?: string;
  alt?: string;
}

const route = useRoute();
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const props = defineProps({
  items: {
    type: Array as () => BreadcrumbItem[],
    required: true,
    default: () => [],
  },
  textClass: {
    type: String,
    default: "text-[#fff]",
  },
});
</script>

<style lang="scss" scoped>
.custom-breadcrumb-wrapper {
  display: flex;
  align-items: center;
  background-color: transparent;
  overflow: hidden;
  white-space: nowrap;
}

.custom-breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
}

.custom-breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  transition: opacity 0.2s ease;
  img {
    opacity: 0.9;
    &:hover {
      opacity: 1;
    }
  }
  span {
    opacity: 0.9;
    &:hover {
      opacity: 1;
      font-weight: 500;
    }
  }
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-icon {
  width: 16px;
  height: 16px;
}

.breadcrumb-text {
  font-size: 14px;
  line-height: 14px;

  &.last-item {
    font-weight: 500;
    opacity: 100 !important;
    color: #fff;
    &:hover {
      opacity: 100 !important;
    }
  }
}

.breadcrumb-separator-container {
  display: flex;
  align-items: center;
  margin: 0 8px;
  flex-shrink: 0;
}

.breadcrumb-separator {
  width: 8px;
  height: 8px;
  flex-shrink: 0;
}

.mobile-breadcrumb {
  font-size: 0.26rem;

  .custom-breadcrumb-list {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .custom-breadcrumb-item {
    display: inline-flex;
    align-items: center;
    flex-shrink: 0;

    &:nth-child(4) {
      flex: 1;
      min-width: 0;

      .breadcrumb-text {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-block;
        max-width: 100%;
      }
    }

    &:nth-child(n + 5) {
      display: none;
    }
  }

  .breadcrumb-link,
  .breadcrumb-item {
    display: inline-flex;
    align-items: center;
    flex-shrink: 0;
    min-width: 0;
    max-width: 100%;
  }

  .breadcrumb-text {
    height: 0.32rem;
    font-size: 0.26rem;
    line-height: 0.32rem;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    max-width: 100%;
  }

  .breadcrumb-icon {
    width: 0.32rem;
    height: 0.32rem;
    margin-right: 0.08rem;
    flex-shrink: 0;
  }

  .breadcrumb-separator-container {
    display: inline-flex;
    margin: 0 0.1rem;
    flex-shrink: 0;
    &:nth-child(n + 4) {
      display: none;
    }
  }

  .breadcrumb-separator {
    width: 0.16rem;
    height: 0.16rem;
    flex-shrink: 0;
  }
}
</style>
