/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Result } from "../../../common";
import {
  CurrencyType,
  currencyTypeFromJSON,
  currencyTypeToJSON,
  OnlinePay,
  onlinePayFromJSON,
  onlinePayToJSON,
  PayMode,
  payModeFromJSON,
  payModeToJSON,
  QuotationMode,
  quotationModeFromJSON,
  quotationModeToJSON,
} from "../../../common/business";
import { SkuModel } from "../../commodity/model/goods_info_model";
import Long from "long";

export const protobufPackage = "mall.pages";

/** 获取订单列表 */
export interface GetOrderListPageResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 返回数据 */
  data: GetOrderListModel | undefined;
}

export interface GetOrderListModel {
  /** 总共多少页 */
  totalPageCount: number;
  /** 总共多少订单 */
  totalOrderCount: number;
  /** 当前页码 */
  curPageNum: number;
  /** 当前页大小 */
  curPageSize: number;
  /** 订单列表 */
  orderList: OrderInfoModel[];
}

export interface OrderInfoModel {
  /** 订单id */
  orderId: string;
  /** 支付类型 一次性支付 or 分开支付 */
  payMode: PayMode;
  /** 订单状态 */
  orderStatus: number;
  /** 商品列表, 为空表示该账单不包含产品价; */
  skuList: SkuModel[];
  /** 下单时间戳 */
  orderTime: number;
}

/** 获取订单详情 */
export interface GetOrderDetailResp {
  /** 错误码 */
  result: Result | undefined;
  data: GetOrderDetailModel | undefined;
}

export interface GetOrderDetailModel {
  /** 订单id */
  orderId: string;
  /** 订单状态 */
  orderStatus: number;
  /** 下单时间,时间戳 */
  orderTime: number;
  /** 地址信息 */
  addressInfo:
    | AddressInfoModel
    | undefined;
  /** 装箱列表 */
  boxList: BoxInfoModel[];
  /** sku总数量 */
  totalCount: number;
  /** sku总价值 */
  totalAmount: number;
  /** 订单备注 */
  orderRemark: string;
  /** 产品成本 */
  productAmount:
    | ProductAmountModel
    | undefined;
  /** 运输线路与预估费用列表 */
  transportAmountList: TransportAmountModel[];
  /** 箱子总重量 */
  totalWeight: number;
  /** 箱子总体积 */
  totalVolume: number;
  /** 支付列表 */
  paymentModelList: PaymentModel[];
  /** 报价模式 */
  quotationMode: QuotationMode;
  /** 支付模式 */
  payMode: PayMode;
}

export interface AddressInfoModel {
  /** 用户名 */
  userName: string;
  /** 电话号码 */
  phone: string;
  /** 地址 */
  address: string;
}

export interface ProductAmountModel {
  /** 产品费用 */
  productAmount: number;
  /** 佣金 */
  serviceAmount: number;
  /** 佣金明细 */
  serviceAmountDetailList: FeeModel[];
}

export interface FeeModel {
  /** 费用名称 */
  feeName: string;
  /** 费用金额 */
  feeAmount: number;
}

export interface TransportAmountModel {
  /** 线路id */
  transportId: number;
  /** 线路名称 */
  name: string;
  /** 预估费用 */
  amount: number;
  /** 费用明细 */
  amountDetailList: FeeModel[];
  /** 预期交货时间 */
  expectDeliveryTime: string;
  /** 备注 */
  transportRemark: string;
}

export interface BoxInfoModel {
  /** 箱id */
  boxId: string;
  /** sku件数 */
  skuCount: number;
  /** 箱子数量 */
  boxCount: number;
  /** 单个箱子重量 */
  unitWeight: number;
  /** 箱子总重量 */
  totalWeight: number;
  /** 单个箱子体积 */
  unitVolume: number;
  /** 箱子总体积 */
  totalVolume: number;
  /** sku列表 */
  skuList: SkuModel[];
}

export interface PaymentModel {
  /** 支付单id */
  paymentId: string;
  /** 需要支付的金额 */
  amount: number;
  /** 为0 表示仅为线下支付 */
  onlinePay: OnlinePay;
  /** 账单内容描述 */
  description: string;
  /** 金额备注 */
  amountRemark: string;
}

/** 获取支付方式列表 */
export interface GetPayMethodListResp {
  /** 支付方式列表 */
  payMethodList: PayMethodModel[];
}

export interface PayMethodModel {
  /** 支付方式code */
  code: string;
  /** 名称，显示用 */
  name: string;
  /** 支付方式图标icon url */
  iconUrl: string;
}

/** 获取支付金额 */
export interface GetPaymentAmountResp {
  amount: number;
  currencyType: CurrencyType;
  feeList: FeeModel[];
}

/** 查询支付结果 */
export interface QueryPaymentStatusResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 返回数据 */
  data: QueryPaymentStatusModel | undefined;
}

export interface QueryPaymentStatusModel {
  orderId: string;
}

/** 提交支付 */
export interface SubmitPaymentResp {
  /** 错误码 */
  result:
    | Result
    | undefined;
  /** 第三方支付的pay url，前端需要跳转过去 */
  payUrl: string;
}

function createBaseGetOrderListPageResp(): GetOrderListPageResp {
  return { result: undefined, data: undefined };
}

export const GetOrderListPageResp = {
  encode(message: GetOrderListPageResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GetOrderListModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderListPageResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderListPageResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GetOrderListModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderListPageResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GetOrderListModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GetOrderListPageResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GetOrderListModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderListPageResp>, I>>(base?: I): GetOrderListPageResp {
    return GetOrderListPageResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderListPageResp>, I>>(object: I): GetOrderListPageResp {
    const message = createBaseGetOrderListPageResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GetOrderListModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetOrderListModel(): GetOrderListModel {
  return { totalPageCount: 0, totalOrderCount: 0, curPageNum: 0, curPageSize: 0, orderList: [] };
}

export const GetOrderListModel = {
  encode(message: GetOrderListModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.totalPageCount !== 0) {
      writer.uint32(80).int32(message.totalPageCount);
    }
    if (message.totalOrderCount !== 0) {
      writer.uint32(160).int32(message.totalOrderCount);
    }
    if (message.curPageNum !== 0) {
      writer.uint32(240).int32(message.curPageNum);
    }
    if (message.curPageSize !== 0) {
      writer.uint32(320).int32(message.curPageSize);
    }
    for (const v of message.orderList) {
      OrderInfoModel.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderListModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderListModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.totalPageCount = reader.int32();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.totalOrderCount = reader.int32();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.curPageNum = reader.int32();
          continue;
        case 40:
          if (tag !== 320) {
            break;
          }

          message.curPageSize = reader.int32();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.orderList.push(OrderInfoModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderListModel {
    return {
      totalPageCount: isSet(object.totalPageCount) ? globalThis.Number(object.totalPageCount) : 0,
      totalOrderCount: isSet(object.totalOrderCount) ? globalThis.Number(object.totalOrderCount) : 0,
      curPageNum: isSet(object.curPageNum) ? globalThis.Number(object.curPageNum) : 0,
      curPageSize: isSet(object.curPageSize) ? globalThis.Number(object.curPageSize) : 0,
      orderList: globalThis.Array.isArray(object?.orderList)
        ? object.orderList.map((e: any) => OrderInfoModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetOrderListModel): unknown {
    const obj: any = {};
    if (message.totalPageCount !== 0) {
      obj.totalPageCount = Math.round(message.totalPageCount);
    }
    if (message.totalOrderCount !== 0) {
      obj.totalOrderCount = Math.round(message.totalOrderCount);
    }
    if (message.curPageNum !== 0) {
      obj.curPageNum = Math.round(message.curPageNum);
    }
    if (message.curPageSize !== 0) {
      obj.curPageSize = Math.round(message.curPageSize);
    }
    if (message.orderList?.length) {
      obj.orderList = message.orderList.map((e) => OrderInfoModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderListModel>, I>>(base?: I): GetOrderListModel {
    return GetOrderListModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderListModel>, I>>(object: I): GetOrderListModel {
    const message = createBaseGetOrderListModel();
    message.totalPageCount = object.totalPageCount ?? 0;
    message.totalOrderCount = object.totalOrderCount ?? 0;
    message.curPageNum = object.curPageNum ?? 0;
    message.curPageSize = object.curPageSize ?? 0;
    message.orderList = object.orderList?.map((e) => OrderInfoModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseOrderInfoModel(): OrderInfoModel {
  return { orderId: "", payMode: 0, orderStatus: 0, skuList: [], orderTime: 0 };
}

export const OrderInfoModel = {
  encode(message: OrderInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    if (message.payMode !== 0) {
      writer.uint32(160).int32(message.payMode);
    }
    if (message.orderStatus !== 0) {
      writer.uint32(240).int32(message.orderStatus);
    }
    for (const v of message.skuList) {
      SkuModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    if (message.orderTime !== 0) {
      writer.uint32(400).int64(message.orderTime);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): OrderInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOrderInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.payMode = reader.int32() as any;
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.orderStatus = reader.int32();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.skuList.push(SkuModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 400) {
            break;
          }

          message.orderTime = longToNumber(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OrderInfoModel {
    return {
      orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "",
      payMode: isSet(object.payMode) ? payModeFromJSON(object.payMode) : 0,
      orderStatus: isSet(object.orderStatus) ? globalThis.Number(object.orderStatus) : 0,
      skuList: globalThis.Array.isArray(object?.skuList) ? object.skuList.map((e: any) => SkuModel.fromJSON(e)) : [],
      orderTime: isSet(object.orderTime) ? globalThis.Number(object.orderTime) : 0,
    };
  },

  toJSON(message: OrderInfoModel): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    if (message.payMode !== 0) {
      obj.payMode = payModeToJSON(message.payMode);
    }
    if (message.orderStatus !== 0) {
      obj.orderStatus = Math.round(message.orderStatus);
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => SkuModel.toJSON(e));
    }
    if (message.orderTime !== 0) {
      obj.orderTime = Math.round(message.orderTime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OrderInfoModel>, I>>(base?: I): OrderInfoModel {
    return OrderInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OrderInfoModel>, I>>(object: I): OrderInfoModel {
    const message = createBaseOrderInfoModel();
    message.orderId = object.orderId ?? "";
    message.payMode = object.payMode ?? 0;
    message.orderStatus = object.orderStatus ?? 0;
    message.skuList = object.skuList?.map((e) => SkuModel.fromPartial(e)) || [];
    message.orderTime = object.orderTime ?? 0;
    return message;
  },
};

function createBaseGetOrderDetailResp(): GetOrderDetailResp {
  return { result: undefined, data: undefined };
}

export const GetOrderDetailResp = {
  encode(message: GetOrderDetailResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(82).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GetOrderDetailModel.encode(message.data, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderDetailResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderDetailResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.data = GetOrderDetailModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderDetailResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GetOrderDetailModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GetOrderDetailResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GetOrderDetailModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderDetailResp>, I>>(base?: I): GetOrderDetailResp {
    return GetOrderDetailResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderDetailResp>, I>>(object: I): GetOrderDetailResp {
    const message = createBaseGetOrderDetailResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GetOrderDetailModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGetOrderDetailModel(): GetOrderDetailModel {
  return {
    orderId: "",
    orderStatus: 0,
    orderTime: 0,
    addressInfo: undefined,
    boxList: [],
    totalCount: 0,
    totalAmount: 0,
    orderRemark: "",
    productAmount: undefined,
    transportAmountList: [],
    totalWeight: 0,
    totalVolume: 0,
    paymentModelList: [],
    quotationMode: 0,
    payMode: 0,
  };
}

export const GetOrderDetailModel = {
  encode(message: GetOrderDetailModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    if (message.orderStatus !== 0) {
      writer.uint32(160).int32(message.orderStatus);
    }
    if (message.orderTime !== 0) {
      writer.uint32(168).int64(message.orderTime);
    }
    if (message.addressInfo !== undefined) {
      AddressInfoModel.encode(message.addressInfo, writer.uint32(242).fork()).ldelim();
    }
    for (const v of message.boxList) {
      BoxInfoModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    if (message.totalCount !== 0) {
      writer.uint32(400).int32(message.totalCount);
    }
    if (message.totalAmount !== 0) {
      writer.uint32(481).double(message.totalAmount);
    }
    if (message.orderRemark !== "") {
      writer.uint32(562).string(message.orderRemark);
    }
    if (message.productAmount !== undefined) {
      ProductAmountModel.encode(message.productAmount, writer.uint32(642).fork()).ldelim();
    }
    for (const v of message.transportAmountList) {
      TransportAmountModel.encode(v!, writer.uint32(722).fork()).ldelim();
    }
    if (message.totalWeight !== 0) {
      writer.uint32(801).double(message.totalWeight);
    }
    if (message.totalVolume !== 0) {
      writer.uint32(881).double(message.totalVolume);
    }
    for (const v of message.paymentModelList) {
      PaymentModel.encode(v!, writer.uint32(962).fork()).ldelim();
    }
    if (message.quotationMode !== 0) {
      writer.uint32(1040).int32(message.quotationMode);
    }
    if (message.payMode !== 0) {
      writer.uint32(1120).int32(message.payMode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderDetailModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderDetailModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.orderStatus = reader.int32();
          continue;
        case 21:
          if (tag !== 168) {
            break;
          }

          message.orderTime = longToNumber(reader.int64() as Long);
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.addressInfo = AddressInfoModel.decode(reader, reader.uint32());
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.boxList.push(BoxInfoModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 400) {
            break;
          }

          message.totalCount = reader.int32();
          continue;
        case 60:
          if (tag !== 481) {
            break;
          }

          message.totalAmount = reader.double();
          continue;
        case 70:
          if (tag !== 562) {
            break;
          }

          message.orderRemark = reader.string();
          continue;
        case 80:
          if (tag !== 642) {
            break;
          }

          message.productAmount = ProductAmountModel.decode(reader, reader.uint32());
          continue;
        case 90:
          if (tag !== 722) {
            break;
          }

          message.transportAmountList.push(TransportAmountModel.decode(reader, reader.uint32()));
          continue;
        case 100:
          if (tag !== 801) {
            break;
          }

          message.totalWeight = reader.double();
          continue;
        case 110:
          if (tag !== 881) {
            break;
          }

          message.totalVolume = reader.double();
          continue;
        case 120:
          if (tag !== 962) {
            break;
          }

          message.paymentModelList.push(PaymentModel.decode(reader, reader.uint32()));
          continue;
        case 130:
          if (tag !== 1040) {
            break;
          }

          message.quotationMode = reader.int32() as any;
          continue;
        case 140:
          if (tag !== 1120) {
            break;
          }

          message.payMode = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderDetailModel {
    return {
      orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "",
      orderStatus: isSet(object.orderStatus) ? globalThis.Number(object.orderStatus) : 0,
      orderTime: isSet(object.orderTime) ? globalThis.Number(object.orderTime) : 0,
      addressInfo: isSet(object.addressInfo) ? AddressInfoModel.fromJSON(object.addressInfo) : undefined,
      boxList: globalThis.Array.isArray(object?.boxList)
        ? object.boxList.map((e: any) => BoxInfoModel.fromJSON(e))
        : [],
      totalCount: isSet(object.totalCount) ? globalThis.Number(object.totalCount) : 0,
      totalAmount: isSet(object.totalAmount) ? globalThis.Number(object.totalAmount) : 0,
      orderRemark: isSet(object.orderRemark) ? globalThis.String(object.orderRemark) : "",
      productAmount: isSet(object.productAmount) ? ProductAmountModel.fromJSON(object.productAmount) : undefined,
      transportAmountList: globalThis.Array.isArray(object?.transportAmountList)
        ? object.transportAmountList.map((e: any) => TransportAmountModel.fromJSON(e))
        : [],
      totalWeight: isSet(object.totalWeight) ? globalThis.Number(object.totalWeight) : 0,
      totalVolume: isSet(object.totalVolume) ? globalThis.Number(object.totalVolume) : 0,
      paymentModelList: globalThis.Array.isArray(object?.paymentModelList)
        ? object.paymentModelList.map((e: any) => PaymentModel.fromJSON(e))
        : [],
      quotationMode: isSet(object.quotationMode) ? quotationModeFromJSON(object.quotationMode) : 0,
      payMode: isSet(object.payMode) ? payModeFromJSON(object.payMode) : 0,
    };
  },

  toJSON(message: GetOrderDetailModel): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    if (message.orderStatus !== 0) {
      obj.orderStatus = Math.round(message.orderStatus);
    }
    if (message.orderTime !== 0) {
      obj.orderTime = Math.round(message.orderTime);
    }
    if (message.addressInfo !== undefined) {
      obj.addressInfo = AddressInfoModel.toJSON(message.addressInfo);
    }
    if (message.boxList?.length) {
      obj.boxList = message.boxList.map((e) => BoxInfoModel.toJSON(e));
    }
    if (message.totalCount !== 0) {
      obj.totalCount = Math.round(message.totalCount);
    }
    if (message.totalAmount !== 0) {
      obj.totalAmount = message.totalAmount;
    }
    if (message.orderRemark !== "") {
      obj.orderRemark = message.orderRemark;
    }
    if (message.productAmount !== undefined) {
      obj.productAmount = ProductAmountModel.toJSON(message.productAmount);
    }
    if (message.transportAmountList?.length) {
      obj.transportAmountList = message.transportAmountList.map((e) => TransportAmountModel.toJSON(e));
    }
    if (message.totalWeight !== 0) {
      obj.totalWeight = message.totalWeight;
    }
    if (message.totalVolume !== 0) {
      obj.totalVolume = message.totalVolume;
    }
    if (message.paymentModelList?.length) {
      obj.paymentModelList = message.paymentModelList.map((e) => PaymentModel.toJSON(e));
    }
    if (message.quotationMode !== 0) {
      obj.quotationMode = quotationModeToJSON(message.quotationMode);
    }
    if (message.payMode !== 0) {
      obj.payMode = payModeToJSON(message.payMode);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderDetailModel>, I>>(base?: I): GetOrderDetailModel {
    return GetOrderDetailModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderDetailModel>, I>>(object: I): GetOrderDetailModel {
    const message = createBaseGetOrderDetailModel();
    message.orderId = object.orderId ?? "";
    message.orderStatus = object.orderStatus ?? 0;
    message.orderTime = object.orderTime ?? 0;
    message.addressInfo = (object.addressInfo !== undefined && object.addressInfo !== null)
      ? AddressInfoModel.fromPartial(object.addressInfo)
      : undefined;
    message.boxList = object.boxList?.map((e) => BoxInfoModel.fromPartial(e)) || [];
    message.totalCount = object.totalCount ?? 0;
    message.totalAmount = object.totalAmount ?? 0;
    message.orderRemark = object.orderRemark ?? "";
    message.productAmount = (object.productAmount !== undefined && object.productAmount !== null)
      ? ProductAmountModel.fromPartial(object.productAmount)
      : undefined;
    message.transportAmountList = object.transportAmountList?.map((e) => TransportAmountModel.fromPartial(e)) || [];
    message.totalWeight = object.totalWeight ?? 0;
    message.totalVolume = object.totalVolume ?? 0;
    message.paymentModelList = object.paymentModelList?.map((e) => PaymentModel.fromPartial(e)) || [];
    message.quotationMode = object.quotationMode ?? 0;
    message.payMode = object.payMode ?? 0;
    return message;
  },
};

function createBaseAddressInfoModel(): AddressInfoModel {
  return { userName: "", phone: "", address: "" };
}

export const AddressInfoModel = {
  encode(message: AddressInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userName !== "") {
      writer.uint32(82).string(message.userName);
    }
    if (message.phone !== "") {
      writer.uint32(162).string(message.phone);
    }
    if (message.address !== "") {
      writer.uint32(242).string(message.address);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AddressInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddressInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.userName = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.phone = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.address = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddressInfoModel {
    return {
      userName: isSet(object.userName) ? globalThis.String(object.userName) : "",
      phone: isSet(object.phone) ? globalThis.String(object.phone) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
    };
  },

  toJSON(message: AddressInfoModel): unknown {
    const obj: any = {};
    if (message.userName !== "") {
      obj.userName = message.userName;
    }
    if (message.phone !== "") {
      obj.phone = message.phone;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddressInfoModel>, I>>(base?: I): AddressInfoModel {
    return AddressInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddressInfoModel>, I>>(object: I): AddressInfoModel {
    const message = createBaseAddressInfoModel();
    message.userName = object.userName ?? "";
    message.phone = object.phone ?? "";
    message.address = object.address ?? "";
    return message;
  },
};

function createBaseProductAmountModel(): ProductAmountModel {
  return { productAmount: 0, serviceAmount: 0, serviceAmountDetailList: [] };
}

export const ProductAmountModel = {
  encode(message: ProductAmountModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.productAmount !== 0) {
      writer.uint32(81).double(message.productAmount);
    }
    if (message.serviceAmount !== 0) {
      writer.uint32(161).double(message.serviceAmount);
    }
    for (const v of message.serviceAmountDetailList) {
      FeeModel.encode(v!, writer.uint32(242).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ProductAmountModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProductAmountModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 81) {
            break;
          }

          message.productAmount = reader.double();
          continue;
        case 20:
          if (tag !== 161) {
            break;
          }

          message.serviceAmount = reader.double();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.serviceAmountDetailList.push(FeeModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ProductAmountModel {
    return {
      productAmount: isSet(object.productAmount) ? globalThis.Number(object.productAmount) : 0,
      serviceAmount: isSet(object.serviceAmount) ? globalThis.Number(object.serviceAmount) : 0,
      serviceAmountDetailList: globalThis.Array.isArray(object?.serviceAmountDetailList)
        ? object.serviceAmountDetailList.map((e: any) => FeeModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ProductAmountModel): unknown {
    const obj: any = {};
    if (message.productAmount !== 0) {
      obj.productAmount = message.productAmount;
    }
    if (message.serviceAmount !== 0) {
      obj.serviceAmount = message.serviceAmount;
    }
    if (message.serviceAmountDetailList?.length) {
      obj.serviceAmountDetailList = message.serviceAmountDetailList.map((e) => FeeModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ProductAmountModel>, I>>(base?: I): ProductAmountModel {
    return ProductAmountModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProductAmountModel>, I>>(object: I): ProductAmountModel {
    const message = createBaseProductAmountModel();
    message.productAmount = object.productAmount ?? 0;
    message.serviceAmount = object.serviceAmount ?? 0;
    message.serviceAmountDetailList = object.serviceAmountDetailList?.map((e) => FeeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFeeModel(): FeeModel {
  return { feeName: "", feeAmount: 0 };
}

export const FeeModel = {
  encode(message: FeeModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.feeName !== "") {
      writer.uint32(82).string(message.feeName);
    }
    if (message.feeAmount !== 0) {
      writer.uint32(161).double(message.feeAmount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FeeModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFeeModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.feeName = reader.string();
          continue;
        case 20:
          if (tag !== 161) {
            break;
          }

          message.feeAmount = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FeeModel {
    return {
      feeName: isSet(object.feeName) ? globalThis.String(object.feeName) : "",
      feeAmount: isSet(object.feeAmount) ? globalThis.Number(object.feeAmount) : 0,
    };
  },

  toJSON(message: FeeModel): unknown {
    const obj: any = {};
    if (message.feeName !== "") {
      obj.feeName = message.feeName;
    }
    if (message.feeAmount !== 0) {
      obj.feeAmount = message.feeAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FeeModel>, I>>(base?: I): FeeModel {
    return FeeModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeeModel>, I>>(object: I): FeeModel {
    const message = createBaseFeeModel();
    message.feeName = object.feeName ?? "";
    message.feeAmount = object.feeAmount ?? 0;
    return message;
  },
};

function createBaseTransportAmountModel(): TransportAmountModel {
  return { transportId: 0, name: "", amount: 0, amountDetailList: [], expectDeliveryTime: "", transportRemark: "" };
}

export const TransportAmountModel = {
  encode(message: TransportAmountModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.transportId !== 0) {
      writer.uint32(80).int32(message.transportId);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    if (message.amount !== 0) {
      writer.uint32(241).double(message.amount);
    }
    for (const v of message.amountDetailList) {
      FeeModel.encode(v!, writer.uint32(322).fork()).ldelim();
    }
    if (message.expectDeliveryTime !== "") {
      writer.uint32(402).string(message.expectDeliveryTime);
    }
    if (message.transportRemark !== "") {
      writer.uint32(482).string(message.transportRemark);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): TransportAmountModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransportAmountModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.transportId = reader.int32();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 241) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.amountDetailList.push(FeeModel.decode(reader, reader.uint32()));
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.expectDeliveryTime = reader.string();
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }

          message.transportRemark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TransportAmountModel {
    return {
      transportId: isSet(object.transportId) ? globalThis.Number(object.transportId) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      amountDetailList: globalThis.Array.isArray(object?.amountDetailList)
        ? object.amountDetailList.map((e: any) => FeeModel.fromJSON(e))
        : [],
      expectDeliveryTime: isSet(object.expectDeliveryTime) ? globalThis.String(object.expectDeliveryTime) : "",
      transportRemark: isSet(object.transportRemark) ? globalThis.String(object.transportRemark) : "",
    };
  },

  toJSON(message: TransportAmountModel): unknown {
    const obj: any = {};
    if (message.transportId !== 0) {
      obj.transportId = Math.round(message.transportId);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.amountDetailList?.length) {
      obj.amountDetailList = message.amountDetailList.map((e) => FeeModel.toJSON(e));
    }
    if (message.expectDeliveryTime !== "") {
      obj.expectDeliveryTime = message.expectDeliveryTime;
    }
    if (message.transportRemark !== "") {
      obj.transportRemark = message.transportRemark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TransportAmountModel>, I>>(base?: I): TransportAmountModel {
    return TransportAmountModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransportAmountModel>, I>>(object: I): TransportAmountModel {
    const message = createBaseTransportAmountModel();
    message.transportId = object.transportId ?? 0;
    message.name = object.name ?? "";
    message.amount = object.amount ?? 0;
    message.amountDetailList = object.amountDetailList?.map((e) => FeeModel.fromPartial(e)) || [];
    message.expectDeliveryTime = object.expectDeliveryTime ?? "";
    message.transportRemark = object.transportRemark ?? "";
    return message;
  },
};

function createBaseBoxInfoModel(): BoxInfoModel {
  return {
    boxId: "",
    skuCount: 0,
    boxCount: 0,
    unitWeight: 0,
    totalWeight: 0,
    unitVolume: 0,
    totalVolume: 0,
    skuList: [],
  };
}

export const BoxInfoModel = {
  encode(message: BoxInfoModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.boxId !== "") {
      writer.uint32(82).string(message.boxId);
    }
    if (message.skuCount !== 0) {
      writer.uint32(160).int32(message.skuCount);
    }
    if (message.boxCount !== 0) {
      writer.uint32(240).int32(message.boxCount);
    }
    if (message.unitWeight !== 0) {
      writer.uint32(321).double(message.unitWeight);
    }
    if (message.totalWeight !== 0) {
      writer.uint32(401).double(message.totalWeight);
    }
    if (message.unitVolume !== 0) {
      writer.uint32(481).double(message.unitVolume);
    }
    if (message.totalVolume !== 0) {
      writer.uint32(561).double(message.totalVolume);
    }
    for (const v of message.skuList) {
      SkuModel.encode(v!, writer.uint32(642).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BoxInfoModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoxInfoModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.boxId = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.skuCount = reader.int32();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.boxCount = reader.int32();
          continue;
        case 40:
          if (tag !== 321) {
            break;
          }

          message.unitWeight = reader.double();
          continue;
        case 50:
          if (tag !== 401) {
            break;
          }

          message.totalWeight = reader.double();
          continue;
        case 60:
          if (tag !== 481) {
            break;
          }

          message.unitVolume = reader.double();
          continue;
        case 70:
          if (tag !== 561) {
            break;
          }

          message.totalVolume = reader.double();
          continue;
        case 80:
          if (tag !== 642) {
            break;
          }

          message.skuList.push(SkuModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BoxInfoModel {
    return {
      boxId: isSet(object.boxId) ? globalThis.String(object.boxId) : "",
      skuCount: isSet(object.skuCount) ? globalThis.Number(object.skuCount) : 0,
      boxCount: isSet(object.boxCount) ? globalThis.Number(object.boxCount) : 0,
      unitWeight: isSet(object.unitWeight) ? globalThis.Number(object.unitWeight) : 0,
      totalWeight: isSet(object.totalWeight) ? globalThis.Number(object.totalWeight) : 0,
      unitVolume: isSet(object.unitVolume) ? globalThis.Number(object.unitVolume) : 0,
      totalVolume: isSet(object.totalVolume) ? globalThis.Number(object.totalVolume) : 0,
      skuList: globalThis.Array.isArray(object?.skuList) ? object.skuList.map((e: any) => SkuModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: BoxInfoModel): unknown {
    const obj: any = {};
    if (message.boxId !== "") {
      obj.boxId = message.boxId;
    }
    if (message.skuCount !== 0) {
      obj.skuCount = Math.round(message.skuCount);
    }
    if (message.boxCount !== 0) {
      obj.boxCount = Math.round(message.boxCount);
    }
    if (message.unitWeight !== 0) {
      obj.unitWeight = message.unitWeight;
    }
    if (message.totalWeight !== 0) {
      obj.totalWeight = message.totalWeight;
    }
    if (message.unitVolume !== 0) {
      obj.unitVolume = message.unitVolume;
    }
    if (message.totalVolume !== 0) {
      obj.totalVolume = message.totalVolume;
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => SkuModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BoxInfoModel>, I>>(base?: I): BoxInfoModel {
    return BoxInfoModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BoxInfoModel>, I>>(object: I): BoxInfoModel {
    const message = createBaseBoxInfoModel();
    message.boxId = object.boxId ?? "";
    message.skuCount = object.skuCount ?? 0;
    message.boxCount = object.boxCount ?? 0;
    message.unitWeight = object.unitWeight ?? 0;
    message.totalWeight = object.totalWeight ?? 0;
    message.unitVolume = object.unitVolume ?? 0;
    message.totalVolume = object.totalVolume ?? 0;
    message.skuList = object.skuList?.map((e) => SkuModel.fromPartial(e)) || [];
    return message;
  },
};

function createBasePaymentModel(): PaymentModel {
  return { paymentId: "", amount: 0, onlinePay: 0, description: "", amountRemark: "" };
}

export const PaymentModel = {
  encode(message: PaymentModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.paymentId !== "") {
      writer.uint32(82).string(message.paymentId);
    }
    if (message.amount !== 0) {
      writer.uint32(161).double(message.amount);
    }
    if (message.onlinePay !== 0) {
      writer.uint32(240).int32(message.onlinePay);
    }
    if (message.description !== "") {
      writer.uint32(322).string(message.description);
    }
    if (message.amountRemark !== "") {
      writer.uint32(402).string(message.amountRemark);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PaymentModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePaymentModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.paymentId = reader.string();
          continue;
        case 20:
          if (tag !== 161) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.onlinePay = reader.int32() as any;
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.description = reader.string();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.amountRemark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PaymentModel {
    return {
      paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      onlinePay: isSet(object.onlinePay) ? onlinePayFromJSON(object.onlinePay) : 0,
      description: isSet(object.description) ? globalThis.String(object.description) : "",
      amountRemark: isSet(object.amountRemark) ? globalThis.String(object.amountRemark) : "",
    };
  },

  toJSON(message: PaymentModel): unknown {
    const obj: any = {};
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.onlinePay !== 0) {
      obj.onlinePay = onlinePayToJSON(message.onlinePay);
    }
    if (message.description !== "") {
      obj.description = message.description;
    }
    if (message.amountRemark !== "") {
      obj.amountRemark = message.amountRemark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PaymentModel>, I>>(base?: I): PaymentModel {
    return PaymentModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaymentModel>, I>>(object: I): PaymentModel {
    const message = createBasePaymentModel();
    message.paymentId = object.paymentId ?? "";
    message.amount = object.amount ?? 0;
    message.onlinePay = object.onlinePay ?? 0;
    message.description = object.description ?? "";
    message.amountRemark = object.amountRemark ?? "";
    return message;
  },
};

function createBaseGetPayMethodListResp(): GetPayMethodListResp {
  return { payMethodList: [] };
}

export const GetPayMethodListResp = {
  encode(message: GetPayMethodListResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.payMethodList) {
      PayMethodModel.encode(v!, writer.uint32(82).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetPayMethodListResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPayMethodListResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.payMethodList.push(PayMethodModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPayMethodListResp {
    return {
      payMethodList: globalThis.Array.isArray(object?.payMethodList)
        ? object.payMethodList.map((e: any) => PayMethodModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetPayMethodListResp): unknown {
    const obj: any = {};
    if (message.payMethodList?.length) {
      obj.payMethodList = message.payMethodList.map((e) => PayMethodModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPayMethodListResp>, I>>(base?: I): GetPayMethodListResp {
    return GetPayMethodListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPayMethodListResp>, I>>(object: I): GetPayMethodListResp {
    const message = createBaseGetPayMethodListResp();
    message.payMethodList = object.payMethodList?.map((e) => PayMethodModel.fromPartial(e)) || [];
    return message;
  },
};

function createBasePayMethodModel(): PayMethodModel {
  return { code: "", name: "", iconUrl: "" };
}

export const PayMethodModel = {
  encode(message: PayMethodModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.code !== "") {
      writer.uint32(82).string(message.code);
    }
    if (message.name !== "") {
      writer.uint32(162).string(message.name);
    }
    if (message.iconUrl !== "") {
      writer.uint32(242).string(message.iconUrl);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PayMethodModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePayMethodModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.code = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.name = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.iconUrl = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PayMethodModel {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      iconUrl: isSet(object.iconUrl) ? globalThis.String(object.iconUrl) : "",
    };
  },

  toJSON(message: PayMethodModel): unknown {
    const obj: any = {};
    if (message.code !== "") {
      obj.code = message.code;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.iconUrl !== "") {
      obj.iconUrl = message.iconUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PayMethodModel>, I>>(base?: I): PayMethodModel {
    return PayMethodModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayMethodModel>, I>>(object: I): PayMethodModel {
    const message = createBasePayMethodModel();
    message.code = object.code ?? "";
    message.name = object.name ?? "";
    message.iconUrl = object.iconUrl ?? "";
    return message;
  },
};

function createBaseGetPaymentAmountResp(): GetPaymentAmountResp {
  return { amount: 0, currencyType: 0, feeList: [] };
}

export const GetPaymentAmountResp = {
  encode(message: GetPaymentAmountResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.amount !== 0) {
      writer.uint32(81).double(message.amount);
    }
    if (message.currencyType !== 0) {
      writer.uint32(160).int32(message.currencyType);
    }
    for (const v of message.feeList) {
      FeeModel.encode(v!, writer.uint32(242).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetPaymentAmountResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPaymentAmountResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 81) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.currencyType = reader.int32() as any;
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.feeList.push(FeeModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPaymentAmountResp {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currencyType: isSet(object.currencyType) ? currencyTypeFromJSON(object.currencyType) : 0,
      feeList: globalThis.Array.isArray(object?.feeList) ? object.feeList.map((e: any) => FeeModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: GetPaymentAmountResp): unknown {
    const obj: any = {};
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.currencyType !== 0) {
      obj.currencyType = currencyTypeToJSON(message.currencyType);
    }
    if (message.feeList?.length) {
      obj.feeList = message.feeList.map((e) => FeeModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPaymentAmountResp>, I>>(base?: I): GetPaymentAmountResp {
    return GetPaymentAmountResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPaymentAmountResp>, I>>(object: I): GetPaymentAmountResp {
    const message = createBaseGetPaymentAmountResp();
    message.amount = object.amount ?? 0;
    message.currencyType = object.currencyType ?? 0;
    message.feeList = object.feeList?.map((e) => FeeModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseQueryPaymentStatusResp(): QueryPaymentStatusResp {
  return { result: undefined, data: undefined };
}

export const QueryPaymentStatusResp = {
  encode(message: QueryPaymentStatusResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(82).fork()).ldelim();
    }
    if (message.data !== undefined) {
      QueryPaymentStatusModel.encode(message.data, writer.uint32(162).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryPaymentStatusResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryPaymentStatusResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.data = QueryPaymentStatusModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryPaymentStatusResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? QueryPaymentStatusModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: QueryPaymentStatusResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = QueryPaymentStatusModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryPaymentStatusResp>, I>>(base?: I): QueryPaymentStatusResp {
    return QueryPaymentStatusResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryPaymentStatusResp>, I>>(object: I): QueryPaymentStatusResp {
    const message = createBaseQueryPaymentStatusResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? QueryPaymentStatusModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseQueryPaymentStatusModel(): QueryPaymentStatusModel {
  return { orderId: "" };
}

export const QueryPaymentStatusModel = {
  encode(message: QueryPaymentStatusModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderId !== "") {
      writer.uint32(82).string(message.orderId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryPaymentStatusModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryPaymentStatusModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryPaymentStatusModel {
    return { orderId: isSet(object.orderId) ? globalThis.String(object.orderId) : "" };
  },

  toJSON(message: QueryPaymentStatusModel): unknown {
    const obj: any = {};
    if (message.orderId !== "") {
      obj.orderId = message.orderId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryPaymentStatusModel>, I>>(base?: I): QueryPaymentStatusModel {
    return QueryPaymentStatusModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryPaymentStatusModel>, I>>(object: I): QueryPaymentStatusModel {
    const message = createBaseQueryPaymentStatusModel();
    message.orderId = object.orderId ?? "";
    return message;
  },
};

function createBaseSubmitPaymentResp(): SubmitPaymentResp {
  return { result: undefined, payUrl: "" };
}

export const SubmitPaymentResp = {
  encode(message: SubmitPaymentResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(82).fork()).ldelim();
    }
    if (message.payUrl !== "") {
      writer.uint32(162).string(message.payUrl);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SubmitPaymentResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitPaymentResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.payUrl = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitPaymentResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      payUrl: isSet(object.payUrl) ? globalThis.String(object.payUrl) : "",
    };
  },

  toJSON(message: SubmitPaymentResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.payUrl !== "") {
      obj.payUrl = message.payUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitPaymentResp>, I>>(base?: I): SubmitPaymentResp {
    return SubmitPaymentResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitPaymentResp>, I>>(object: I): SubmitPaymentResp {
    const message = createBaseSubmitPaymentResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.payUrl = object.payUrl ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
