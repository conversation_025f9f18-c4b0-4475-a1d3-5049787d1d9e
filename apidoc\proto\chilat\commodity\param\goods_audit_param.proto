syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "chilat/commodity/commodity_common.proto";
import "common.proto";

// 审核列表
message GoodsAuditPageQueryParam {
  common.PageParam page = 1;
  GoodsAuditQueryParam query = 2;
}

// 批次列表参数
message AuditBatchPageQueryParam {
  common.PageParam page = 1;
}

// 审核列表参数
message GoodsAuditQueryParam {
  string bucketNo = 1; // 桶号
  repeated GoodsAuditState auditState = 2; // 审核状态
  string leaderId = 3; // 负责人ID
  string categoryId = 4; // 分类ID
  int64 startTime = 5; // 开始时间
  int64 endTime = 6; // 结束时间
  string batchNo = 7; // 批次号
  bool hasIllegal = 8; // 是否侵权
}

// 分享链接列表
message ShareLinkPageQueryParam {
  common.PageParam page = 1;
  ShareLinkQueryParam query = 2;
}

// 分享链接
message ShareLinkQueryParam {
  string batchNo = 1; // 批次号
}

// 生成桶
message CreateBatchParam {
  repeated string categoryIds = 1; // 分类ID
  int32 bucketNum = 2; // 单桶数量
  int32 bucketCount = 3; // 桶数量，不传或0表示全部
}

// 分配任务
message DispatchParam {
  repeated string auditIds = 1; // 商品审核ID
  repeated string leaderIds = 2; // 负责人ID
}

// 审核商品
message AuditGoodsPageQueryParam {
  common.PageParam page = 1;
  AuditGoodsQueryParam query = 2;
}

// 审核商品参数
message AuditGoodsQueryParam {
  string auditId = 1; // 审核ID
  GoodsAuditState state = 2; // 审核状态
  bool isIllegal = 3; // 是否侵权
}

// 提取码访问
message LoginByCodeParam {
  string code = 1; // 提取码
  string name = 2; // 姓名
}

// 创建分享
message CreateLinkParam {
  string code = 1; // 提取码
  repeated string batchIds = 2; // 批次ID
}