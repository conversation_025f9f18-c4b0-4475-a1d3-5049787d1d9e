syntax = "proto3";

package chilat.basis;

import "chilat/coupon/coupon_common.proto";
import "chilat/coupon/coupon_detail_common.proto";
import "chilat/coupon/model/coupon_info_model.proto";
import "common.proto";

option java_package = "com.chilat.rpc.basis.model";

// 我的优惠券列表
message CouponUseModelResp {
    common.Result result = 1;
    repeated CouponUseModel data = 3; //优惠券使用记录
}


//可用优惠券组装Model
message CouponUseModel {
    string id = 1;//主键id
    string couponId = 2;//优惠券id
    string couponName = 3;//优惠券名称
    coupon.CouponTypeStatus couponType = 4; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
    coupon.CouponWayStatus couponWay = 6; //优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券
    double ticketActualPrice = 7;//单张券实际金额（根据订单金额 以及优惠方式，使用条件计算）
}





