syntax = "proto3";

package chilat.basis;

import "chilat/coupon/coupon_common.proto";
import "chilat/coupon/coupon_detail_common.proto";
import "chilat/coupon/model/coupon_info_model.proto";
import "common.proto";

option java_package = "com.chilat.rpc.basis.model";

// 我的优惠券列表
message MyCouponDetailModelResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated MyCouponInfoDetailModel data = 3; //优惠券基本规则参数
}

//RpcService调用 通用状态返回
message CouponBasicResp {
    common.Result result = 1;
    CouponBasicModel data = 2;

}

//我的优惠券详情组装Model
message MyCouponInfoDetailModel {
    string couponId = 1;//优惠券id
    string couponName = 2;//优惠券名称
    string couponAlias = 3; //优惠券别名
    string userInstructions = 4; //用户使用说明
    coupon.CouponTypeStatus couponType = 5; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
    coupon.TicketStatus ticketStatus = 6; //优惠券状态: ticketNotUse 未使用 ticketUse 已使用 ticketLoseEfficacy 已失效
    coupon.CouponWayStatus couponWay = 7; //优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券
    double useConditionsAmount = 8; //使用条件(0不限制，达到多少金额可以使用，每满xxx可用)
    double preferentialAmount = 9; //优惠额度
    double discount = 10; //折扣（券类型为折扣券使用）
    string activeId = 17;//活动编码
    coupon.CouponEffectiveTypeStatus couponEffectiveType = 22;//有效期类型 （distributionDateEffectiveDays 发放日期+有效天数  distributionDateEffectiveHours 发放日期+有效小时 fixedTime 固定时间）
    int64 ticketStartExpirationDate = 23;//优惠券生效日期
    int64 ticketEndExpirationDate = 24;//优惠券结束日期
    int32 effectiveNum = 27; //有效类型为distributionDateEffectiveDays 时取有效天数,有效类型为distributionDateEffectiveHours 时取、有效小时,fixedTime 固定时间
    repeated coupon.CouponInfoUseRuleModel couponInfoUseRuleModelList = 29;//使用规则
    string orderNo = 30; //订单号
    string exchangeCode = 31; //兑换码
    string id =32;//主键id
    coupon.CouponUseConditionsTypestatus couponUseConditionsType = 33;//使用条件类型
}


//通用返回data
message CouponBasicModel {
    string couponId = 1;//优惠券id
    string couponName = 2;//优惠券名称
    string couponAlias = 3; //优惠券别名
}