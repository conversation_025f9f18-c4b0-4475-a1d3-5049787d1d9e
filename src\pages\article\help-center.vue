<template>
  <div class="container">
    <search-cate-card></search-cate-card>
    <div class="cwidth mx-auto py-5">
      <div class="center-title">
        <h1>Centro de ayuda de Chilat shop</h1>
      </div>
      <div class="flex justify-center my-4">
        <a target="_blank" href="/article/frequently-questions">
          <div class="center-card mr-5">
            <icon-card
              name="wpf:faq"
              color="#db2221"
              size="50"
              class="mb-3"
            ></icon-card>
            <div class="text-lg font-medium">
              {{ authStore.i18n("cm_news.askedQuestions") }}
            </div>
          </div>
        </a>
        <a target="_blank" href="/article?code=10002">
          <div class="center-card mr-5">
            <icon-card
              name="wpf:security-checked"
              color="#db2221"
              size="50"
              class="mb-3"
            ></icon-card>
            <div class="text-lg font-medium">
              {{ authStore.i18n("cm_news.warrantyService") }}
            </div>
          </div>
        </a>
        <a target="_blank" href="/article/payment-methods">
          <div class="center-card">
            <icon-card
              name="mingcute:bank-card-fill"
              color="#db2221"
              size="54"
              class="mb-3"
            ></icon-card>
            <div class="text-lg font-medium">
              {{ authStore.i18n("cm_news.paymentMethods") }}
            </div>
          </div>
        </a>
      </div>
      <div class="center-contact">
        <h1>¿No encuentras las respuestas que estás buscando?</h1>
        <h2>Contacta con nosotros, aquí estamos para ayudarte.</h2>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();
</script>
<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 90vh;
}
.center-title {
  margin-top: -20px;
  width: 100%;
  height: 240px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f2f6f7;
  font-size: 36px;
  font-weight: 500;
}
.center-card {
  width: 360px;
  padding: 50px;
  height: 260px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-color: rgba(242, 246, 247, 0.*****************);
  margin-bottom: 20px;
  border-radius: 6px;
  border: 1px solid #eaeaea;
  cursor: pointer;
  box-shadow: 0 3px 4px -2px rgba(0, 0, 0, 0.18);
  transition: 0.4s;
  margin-top: 20px;
  &:hover {
    box-shadow: 0 6px 12px -2px rgba(0, 0, 0, 0.18);
  }
}
.center-contact {
  background-color: #f6f6f8;
  height: 338.69px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  h1 {
    color: #3c4043;
    font-size: 32px;
    font-weight: 500;
    line-height: 1.5em;
    height: 50px;
    margin-bottom: 15px;
  }
  h2 {
    font-size: 24px;
    line-height: 40px;
    margin-bottom: 34px;
  }
}
</style>
