syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";
import "chilat/basis/param/stock_param.proto";

// 库存服务
service StockRpc {
    // 有效订单更新库存（不检查库存是否充足）
    rpc updateStockForOrderSales (UpdateStockForOrderParam) returns (common.ApiResult);
    // 取消订单退还库存
    rpc updateStockForOrderCancel (UpdateStockForCancelParam) returns (common.ApiResult);
}