<template>
  <div>
    <div
      class="category-wrapper"
      :style="{ backgroundColor: props.cateColor.cateColor }"
      v-if="!props.moreButton"
    >
      <a class="category-content" v-bind:href="linkUrl"
        ><div
          class="category-title"
          :style="{ color: props.cateColor.textColor }"
        >
          {{ cateInfo?.categoryName }}
        </div>
        <icon-card
          size="20"
          name="iconoir:arrow-right"
          :color="props.cateColor.textColor"
        />
      </a>
    </div>
    <div class="goods-wrapper">
      <div
        class="goods-item"
        v-for="goods in cateInfo?.goodsList"
        :key="goods?.goodsId"
      >
        <a v-bind:href="`/h5/goods/${goods.goodsId}`">
          <n-flex vertical :style="{ gap: '0px' }">
            <n-image
              lazy
              preview-disabled
              object-fit="fill"
              :src="goods.mainImageUrl"
              class="goods-img"
            />
            <p class="goods-title">
              {{ goods.goodsName }}
            </p>
            <p class="goods-price">
              <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
              <!-- <span v-if="goods.minPrice != goods.maxPrice"
                >{{ setUnit(goods.minPrice) }} - {{ goods.maxPrice }}</span
              >
              <span v-else>{{ setUnit(goods.maxPrice) }}</span> -->
              <span>{{ setUnit(goods.minPrice) }}</span>
            </p>
          </n-flex>
        </a>
      </div>
      <div
        class="px-2 hover:cursor-pointer"
        v-if="props.moreButton"
        @click="onViewMore($event)"
      >
        <n-flex vertical justify="center" class="flex-center h-15">
          <n-button circle color="#E50113" size="large">
            <template #icon>
              <icon-card size="28" name="ep:arrow-right-bold" color="#fff" />
            </template>
          </n-button>
          <n-ellipsis class="max-w-32 font-sans underline">{{
            authStore.i18n("cm_app.viewMore")
          }}</n-ellipsis>
        </n-flex>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GoodsCard">
import { useAuthStore } from "@/stores/authStore";

const router = useRouter();
const authStore = useAuthStore();
const props = defineProps({
  cateInfo: {
    type: Object,
    default: () => {},
  },
  goodsList: {
    type: Array,
    default: () => [],
  },
  cateColor: {
    type: Object,
    default: () => {
      return {
        textColor: "#272727",
        cateColor: "#f6f6f6",
      };
    },
  },
  moreButton: {
    type: Boolean,
    default: false,
  },
  tag: {
    type: String,
    default: "",
  },
});
const linkUrl = computed(() =>
  !props.cateInfo.categoryId
    ? "/h5/search/list"
    : `/h5/search/list?categoryId=${props.cateInfo.categoryId}&cateName=${props.cateInfo.categoryName}`
);

function onViewMore(event: any) {
  navigateToPage(
    "/h5/search/list",
    { tagId: props.cateInfo.tagId, tag: props.tag ?? "" },
    false,
    event
  );
}
</script>

<style scoped lang="scss">
.category-wrapper {
  height: 2.19rem;
  padding: 0.39rem;
  background: #4671f3;
  margin-bottom: -0.96rem;
  color: #fff;
  .category-content {
    display: flex;
    justify-content: space-between;
  }
  .category-title {
    font-size: 0.34rem;
    font-weight: 400;
    color: #fff;
    width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.goods-wrapper {
  display: flex;
  justify-content: space-between;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  margin-bottom: 0.7rem;
  padding: 0 0.2rem;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }
  .goods-item {
    flex: 0 0 2.75rem;
    width: 2.75rem;
    margin-right: 0.2rem;
    padding: 0.2rem;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 0.01rem;
    height: fit-content;
    .goods-img {
      position: relative;
      width: 3.03rem;
      height: 2.31rem;
      margin-bottom: 0.2rem;
    }
    .goods-title {
      width: 100%;
      font-size: 0.26rem;
      font-weight: 400;
      color: #212121;
      line-height: 0.32rem;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: 0.1rem;
    }
    .goods-price {
      font-size: 0.26rem;
      font-weight: 600;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
  }
}

:deep(.n-image img) {
  width: 100%;
  height: 100%;
}
</style>
