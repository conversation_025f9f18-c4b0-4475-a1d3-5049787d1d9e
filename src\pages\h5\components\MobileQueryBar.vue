<template>
  <div class="logo-header">
    <div class="w-full flex justify-center items-center">
      <a href="/h5" data-spm-box="navigation-logo-icon">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          :src="pageTheme.logo"
          class="w-[2.76rem] pt-[0.1rem] pb-[0.16rem]"
          :img-props="{ referrerpolicy: 'no-referrer' }"
        />
      </a>
      <div
        v-if="showCart"
        class="mobile-cart-btn absolute right-[0.16rem]"
        @click="onFindListClick($event)"
        data-spm-box="navigation-cart-icon"
      >
        <img
          loading="lazy"
          class="w-[0.52rem]"
          alt="cart"
          src="@/assets/icons/cart.svg"
          referrerpolicy="no-referrer"
        />
        <div v-if="cartGoodsCount" class="fixed-count">
          {{ cartGoodsCount }}
        </div>
      </div>
    </div>
    <div
      class="w-full flex items-center justify-between p-[0.16rem] border-t-1 border-b-1 border-[#F2F2F2]"
    >
      <a href="/h5" v-if="showHomeIcon" data-spm-box="navigation-top-homepage">
        <img
          loading="lazy"
          src="@/assets/icons/home.svg"
          alt="home"
          class="w-[0.52rem] mr-[0.6rem]"
          referrerpolicy="no-referrer"
        />
      </a>
      <div class="flex items-center ml-auto flex-1 justify-end">
        <img
          loading="lazy"
          src="@/assets/icons/searchLine.svg"
          alt="search"
          class="w-[0.42rem] flex-shrink-0 mr-[0.16rem]"
          @click="onShowSearchInput"
          v-if="!pageData.showSearchInput"
          referrerpolicy="no-referrer"
        />
        <div
          v-else
          class="header-search flex-1 mr-[0.16rem]"
          data-spm-box="navigation-keyword-search"
        >
          <div class="search-bar-inner">
            <div class="search-bar-input-wrapper">
              <img
                loading="lazy"
                src="@/assets/icons/searchAc.svg"
                alt="search"
                class="w-[0.44rem] mr-[0.06rem]"
                referrerpolicy="no-referrer"
              />
              <input
                class="search-bar-input"
                type="text"
                maxlength="50"
                v-model.trim="pageData.keyword"
                :placeholder="
                  isTagSearch
                    ? authStore.i18n('cm_search.searchInList')
                    : authStore.i18n('cm_search.searchPlaceholder')
                "
                @keyup.enter="onKeywordClick(pageData.keyword, $event)"
              />
            </div>
            <div class="flex items-center">
              <button
                class="search-bar-inner-button"
                @click="onKeywordClick(pageData.keyword, $event)"
              >
                <span>{{ authStore.i18n("cm_home.search") }}</span>
              </button>
            </div>
          </div>
        </div>
        <image-search class="w-[0.42rem] h-[0.42rem]">
          <img
            loading="lazy"
            src="@/assets/icons/imageSearchLine.svg"
            alt="search"
            class="w-[0.42rem]"
            referrerpolicy="no-referrer"
          />
        </image-search>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useConfigStore } from "@/stores/configStore";

const config = useRuntimeConfig();
const userInfo = ref<object>({});
userInfo.value = config.public.userInfo as object;

const props = defineProps({
  type: {
    type: String,
    default: "logoHeader", //H5页面有两个固定的头部 logoHeader 和 backHeader
  },
  showCart: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const pageTheme = computed(() => useConfigStore().getPageTheme);
const cartGoodsCount = computed(() => {
  const count = authStore.cartTotalCount;
  return count && count > 99 ? "99+" : count;
});

const showHomeIcon = computed(() => {
  return route.path !== "/h5" && route.path !== "/h5/";
});

const isTagSearch = computed(
  () => route.query.type === "recommendSearch" || !!route.query.tagId
);

const pageData = reactive({
  keyword: route.query.keyword || "",
  showSearchInput: false,
});

onMounted(() => {
  window.addEventListener("scroll", onScroll);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
});

// 加载下一页
async function onScroll() {
  pageData.showSearchInput = false;
}

function onShowSearchInput() {
  pageData.showSearchInput = true;
}

function onFindListClick(event: any) {
  // 未登录 去登陆
  if (isEmptyObject(userInfo.value)) {
    navigateToPage("/h5/user/login", { pageSource: "/h5/find" }, false, event);
  } else {
    navigateToPage("/h5/find", {}, false, event);
  }
}

function onKeywordClick(keyword: any, event: any) {
  const query = {
    ...(route.query.type === "recommendSearch" && { type: "recommendSearch" }),
    ...(route.query.tagId && {
      tagId: route.query.tagId,
      tag: route.query.tag,
    }),
  };
  const word =
    keyword?.trim() === "" ? pageData.keyword?.trim() : keyword?.trim();
  window?.MyStat?.addPageEvent("click_search", `搜索关键词：${word}`); // 埋点

  navigateToPage(
    `/h5/search/list`,
    {
      keyword,
      ...query,
    },
    false,
    event
  );
}
</script>

<style scoped lang="scss">
.logo-header {
  width: 100%;
  // height: 2.08rem;
  padding: 0.16rem 0;
  background-color: #fff;
}

.back-header {
  width: 100%;
  height: 1.12rem;
  position: fixed;
  top: 0.8rem;
  left: 0;
  padding: 0.16rem 0.08rem;
  background-color: #fff;
  border: 0.02rem solid #e5e7eb;
  z-index: 10;
}
.back-header :deep(.search-bar-input) {
  margin-left: 0.02rem !important;
  margin-right: 0 !important;
}
:deep(.n-upload) {
  width: 0.42rem;
  height: 0.42rem;
}
.header-search {
  flex: 1;
  height: 0.6rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .search-bar-inner {
    width: 100%;
    height: 0.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4rem;
    background-color: #fff;
    border: 0.02rem solid #e50113;
    .search-bar-input-wrapper {
      flex: 1;
      display: flex;
      margin: 0 0.04rem 0 0.16rem;
    }
    .search-bar-input {
      flex: 1;
      width: 100%;
      margin: 0;
      padding: 0;
      outline: none;
      border: none;
      box-shadow: none;
      background-image: none;
      background-color: transparent;
      font-size: 0.24rem;
      line-height: 0.24rem;
      color: #222;
      background-color: #fff;
      margin-left: 0.12rem;
      margin-right: 0.08rem;
    }
    .search-bar-inner-button {
      width: 1.2rem;
      height: 0.48rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.04rem;
      font-size: 0.28rem;
      line-height: 0.48rem;
      font-weight: 500;
      color: #fff;
      background: #e50113;
      border-radius: 4rem;
      border: 0 solid transparent;
      cursor: pointer;
    }
  }
}
</style>
