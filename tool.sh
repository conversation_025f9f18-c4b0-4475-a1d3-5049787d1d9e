#! /bin/bash

printHelp() {
    echo -e "-g/--gen: \033[31m 编译Protobuf并生成TS \033[0m"
    echo -e "-p/--prod: \033[31m 同步代码并提交SVN \033[0m"
    echo -e "-r/--run: \033[31m 运行当前分支 \033[0m"
    echo -e "-px2rem: \033[31m 将移动端页面的px转换为rem \033[0m"
}

devRun() {
  # 先编译&PM2杀进程&pm2启动配置
  pnpm build
  pm2 delete all
  pm2 start ecosystem.config.cjs
}

# px转rem转换函数
convertPxToRem() {
  echo -e "\033[32m 开始转换移动端页面的px为rem... \033[0m"
  
  # 确保依赖已安装
  if [ ! -d "node_modules/glob" ]; then
    echo "安装依赖..."
    pnpm add glob --save-dev
  fi
  
  # 调用外部JavaScript脚本
  node scripts/px2rem.cjs
  
  echo -e "\033[32m px转rem转换完成! \033[0m"
}

generateProto() {
  dir=./src/services
  mkdir -p $dir
  # protobufServices=./apidoc/proto/mall/commodity/goods_info.proto
  # protobufServices=./apidoc/proto/mall/main/mall_config.proto
  # protobufServices=./apidoc/proto/mall/pages/goods_list_page.proto
  # protobufServices=./apidoc/proto/mall/pages/home_page.proto
  # protobufServices=./apidoc/proto/mall/pages/search_page.proto
  # protobufServices=./apidoc/proto/mall/pages/goods_detail_page.proto
  # protobufServices=./apidoc/proto/mall/pages/home_page.proto
  # protobufServices=./apidoc/proto/mall/marketing/coupon_detail.proto
  protobufServices=./apidoc/proto/mall/passport/user.proto
  
	protoc --plugin=protoc-gen-ts_proto=.\\node_modules\\.bin\\protoc-gen-ts_proto.cmd --ts_proto_out=:$dir $protobufServices -I ./apidoc/proto
	/usr/bin/find $dir -type f -exec sed -i 's/= require("long")/from "long"/g' {} \;
}

prodSync() {
  # 判断分支
  br=`git branch | grep "*"`
	currentbranch=${br/* /}
  if [ "$currentbranch"x != "master"x ];then
    echo -e "\033[31m 请切换到：master 分支再编译提交 \033[0m"
    exit 1
  fi

  dir=/c/workspace/project/chilat/chilat-mall-ssr
	/usr/bin/rm -rf $dir
  /usr/bin/mkdir -p /c/workspace/project/chilat/chilat-mall-ssr/src
	/usr/bin/cp -rf src/* /c/workspace/project/chilat/chilat-mall-ssr/src
  /usr/bin/cp ./.env.development $dir
	/usr/bin/cp ./.env.production $dir
	/usr/bin/cp ecosystem.config.cjs $dir
	/usr/bin/cp ./.npmrc $dir
	/usr/bin/cp nuxt.config.ts $dir
	/usr/bin/cp package.json $dir
	/usr/bin/cp pnpm-lock.yaml $dir
	/usr/bin/cp tsconfig.json $dir
	/usr/bin/cp uno.config.ts $dir
	/usr/bin/cp Dockerfile $dir
	/usr/bin/cp DockerRun.sh $dir
	/usr/bin/cp startService.sh $dir
	/usr/bin/cp stopService.sh $dir
  # 提交SVN代码
  cd /c/workspace/project/chilat
  read -p "请输入提交内容:" cc
  svn st | grep ! | cut -d! -f2| sed 's/^ *//' | sed 's/^/"/g' | sed 's/$/"/g' | xargs svn rm
	svn add . --no-ignore --force
  svn commit -m "$cc"
  echo -e "\033[32m SVN提交完成 \033[0m"
  start /c/workspace/project/chilat
  # start https://jenkins.xpormayor.com.mx/jenkins/job/%E3%80%90Chilat-%E6%AD%A3%E5%BC%8F-Vue%E3%80%91Chilat%202.0%20Mall%20SSR%EF%BC%88%E5%95%86%E5%9F%8EVue%E5%89%8D%E7%AB%AF%EF%BC%89/
  start https://jenkins81.xpormayor.com.mx/job/%E3%80%90%E5%89%8D%E7%AB%AF-Vue%E3%80%91Chilat%20Mall%20SSR%EF%BC%88Chilat%20Shop%EF%BC%89/
  cd -
}

case $1 in
    "-h" | "--help")
        printHelp
    ;;
    "-g" | "--gen")
        generateProto
    ;;
    "-p" | "--prod")
        prodSync
    ;;
    "-d" | "--dev")
        devRun
    ;;
    "-px2rem")
        convertPxToRem
    ;;
    *)
        printHelp
    ;;
esac