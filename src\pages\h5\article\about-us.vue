<template>
  <div class="page-wrapper">
    <mobile-search-bar></mobile-search-bar>
    <div class="header_banner" :style="`background-image: url(${headerBg})`">
      <div class="flex items-center h-full px-[0.32rem]">
        <div class="header-content">
          <div class="header-title">Haga la importación más fácil y seguro</div>
          <div class="header-desc">
            Chilat shop es la plataforma líder de agente de compras
            internacional. Chilat shop es la plataforma de importación todo en
            uno donde gestionamos por ti todo el proceso de compra, logística e
            importación.
          </div>
          <n-button
            color="#db2221"
            @click="onLoginClick"
            class="header-button"
            v-if="!userInfo?.username"
          >
            {{ authStore.i18n("cm_common_registerNow") }}
          </n-button>
        </div>
      </div>
    </div>
    <div class="section-banner">
      <div class="section-wrapper">
        <div class="section-title">
          Disfrute de una forma diferente de importar
        </div>
        <div class="section-desc">
          Chilat es un líder en servicios del idioma español en China y llevamos
          22 años enfocado en servicios de agente de compras en Latinoamérica.
        </div>
        <div class="section-desc">
          Estamos comprometidos a permitir que más clientes comerciales compren
          directamente a proveedores de primera mano en China a través de
          métodos de importación más sencillos y convenientes, obteniendo así
          precios de compra más competitivos!
        </div>
        <div class="section-desc">
          Nuestra sede central se encuentra en Yiwu, China, y contamos con
          sucursales en México, Shanghai, Guangzhou y Hangzhou, con una
          superficie operativa de oficinas de más de 2000 metros cuadrados, una
          superficie de almacén de 1500 metros cuadrados para nuestro grupo de
          empresas y más de 100 empleados.
        </div>
        <div class="section-desc flex">
          <div class="number-item">
            <span>+2,000m²</span>
            <p>de oficinas</p>
          </div>
          <div class="number-item">
            <span>+1,500m²</span>
            <p>almacenes</p>
          </div>
          <div class="number-item">
            <span>+100</span>
            <p>empleados</p>
          </div>
        </div>
        <img loading="lazy" lazy class="section-img" :src="bannerBg">
      </div>
      <div class="section-wrapper section-bg">
        <div class="section-title">
          Nuestro negocio se extiende por toda América Latina, y nuestra visión
          es convertirnos en el líder de las marcas chinas que salen al exterior
          en América Latina.
        </div>
        <div class="section-desc">
          Nuestra misión es ayudar a las marcas chinas a liderar el comercio
          latinoamericano. Ser reconocida en Latinoamérica como la empresa
          internacional líder que genera las mejores oportunidades de negocios
          con China basándose en el respeto por el cliente, la transparencia y
          honestidad en todo acuerdo comercial, agilidad, innovación y
          profesionalismo en los servicios que ofrece y por su calidez humana y
          relaciones de confianza a largo plazo.
        </div>
        <img loading="lazy" lazy class="section-img" :src="bannerBg1">
      </div>
    </div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const userInfo = computed(() => useAuthStore().getUserInfo);

const headerBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/05/cd4d768f-a83d-4dfc-a5d8-7f8cd92a93e6.jpg";
const bannerBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/07/ddac5ec3-bf81-4671-9550-fb8e427b6bc6.png";
const bannerBg1 =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/06/3b696a30-86fe-42ea-b608-c19f243950f5.png";

onBeforeMount(() => {
  document.title = authStore.i18n("cm_common.documentTitle");
});

const onLoginClick = async () => {
  window?.MyStat?.addPageEvent(
    "passport_open_nav_register",
    "点击顶部导航注册，打开注册窗口"
  ); // 埋点
  navigateToPage(
    "/h5/user/register",
    { pageSource: window.location.href },
    false
  );
};
</script>

<style scoped lang="scss">
.page-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
  font-size: 0.28rem;
}
.header_banner {
  height: 6.6rem;
  width: 100%;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100%100%;
  .header-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    text-align: center;
    .header-title {
      font-size: 0.52rem;
      line-height: 0.7rem;
      font-weight: 500;
    }
    .header-desc {
      font-size: 0.3rem;
      line-height: 0.52rem;
      width: 6.8rem;
      margin: 0.2rem 0 0.6rem;
    }
  }

  .header-button {
    height: 0.68rem;
    border-radius: 0.6rem;
    padding: 0.2rem 0.4rem;
    font-size: 0.28rem;
  }
}
.section-banner {
  .section-wrapper {
    padding: 0.6rem 0.32rem 0.6rem;
    .section-title {
      font-size: 0.4rem;
      line-height: 0.72rem;
      font-weight: 500;
    }
    .section-desc {
      font-size: 0.3rem;
      line-height: 0.6rem;
      margin-top: 0.28rem;
      margin-bottom: 0.6rem;
      .number-item {
        padding-left: 0.2rem;
        position: relative;
        margin-right: 0.4rem;
        &::before {
          background-color: #ddd;
          border-radius: 0.04rem;
          content: "";
          display: inline-block;
          height: 86%;
          left: 0;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          width: 0.04rem;
        }
        p {
          color: #000;
          font-weight: 500;
          font-size: 0.28rem;
        }
        span {
          color: #db2221;
          font-size: 0.38rem;
          font-weight: 500;
          letter-spacing: 0.0146rem;
          line-height: 0.88rem;
        }
      }
    }
    .section-img {
      width: 100%;
      margin: 0 auto;
    }
  }
  .section-bg {
    width: 100%;
    background-color: #f1eded;
  }
}
</style>
