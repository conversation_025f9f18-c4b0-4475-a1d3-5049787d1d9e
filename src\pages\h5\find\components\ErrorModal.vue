<template>
  <n-modal :show="visible" :show-icon="false" @update:show="onClose">
    <n-card style="width: 7.2rem" :bordered="false">
      <div>
        <icon-card
          size="0.45rem"
          name="mingcute:warning-line"
          class="add-btn-check"
          color="#E50113"
        ></icon-card>
        {{ props.message }}
      </div>
      <a href="/h5">
        <n-button
          round
          size="small"
          color="#E50113"
          text-color="#fff"
          class="mt-[0.32rem]"
        >
          {{ authStore.i18n("cm_find_shopAgain") }}
        </n-button>
      </a>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  message: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:visible"]);

function onClose(value: boolean) {
  emit("update:visible", value);
}
</script>

<style scoped>
.add-btn-check {
  margin-bottom: 0.16rem;
}
</style>
