syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.common";

import "common.proto";

enum CrawlSourcePlatform {
  CRAWL_SOURCE_PLATFORM_UNKNOWN = 0;
  CRAWL_SOURCE_PLATFORM_ALIBABA = 1; // 阿里国际
  CRAWL_SOURCE_PLATFORM_1688 = 2; // 1688
  CRAWL_SOURCE_PLATFORM_ALIEXPRESS = 3; // 速卖通
  CRAWL_SOURCE_PLATFORM_TEMU = 4; // Temu
  CRAWL_SOURCE_PLATFORM_AMAZON = 5; // 亚马逊
  CRAWL_SOURCE_PLATFORM_CHILAT = 6; // chilat
}

enum CrawlSyncState {
  CRAWL_SYNC_STATE_UNKNOWN = 0;
  CRAWL_SYNC_STATE_NOT_SYNC = 1; // 未同步
  CRAWL_SYNC_STATE_RE_SYNC = 2; // 重新同步
  CRAWL_SYNC_STATE_SYNCED = 3; // 已同步
}

enum CrawlSupplyState {
  CRAWL_SUPPLY_STATE_UNKNOWN = 0;
  CRAWL_SUPPLY_STATE_ABSENT = 1; // 无货源
  CRAWL_SUPPLY_STATE_PRESENT = 2; // 有货源
}

enum GoodsSource {
  GOODS_SOURCE_UNKNOWN = 0;
  GOODS_SOURCE_CRAWL = 1; // 采集箱
  GOODS_SOURCE_GOODS = 2; // 商品管理
}

enum CrawlMethod {
  CRAWL_METHOD_UNKNOWN = 0;
  CRAWL_METHOD_COLLECT = 1; // 采集工具
  CRAWL_METHOD_1688API = 2; // 1688API
  CRAWL_METHOD_CHILAT = 3; // chilat
  CRAWL_METHOD_IMAGE = 4; // 以图搜图
}

enum TaskType {
  TASK_TYPE_UNKNOWN = 0;
  TASK_TYPE_IMPORT_ORDER = 1; // 导入订单
  TASK_TYPE_EXPORT_ORDER = 2; // 导出订单
}

enum TaskState {
  TASK_STATE_UNKNOWN = 0;
  TASK_STATE_CREATED = 1; // 已创建
  TASK_STATE_RUNNING = 2; // 运行中
  TASK_STATE_PAUSED = 3; // 已暂停
  TASK_STATE_DONE = 4; // 已完成
  TASK_STATE_CANCEL = 5; // 已取消
  TASK_STATE_ERROR = 6; // 异常
}

enum CrawlTaskState {
  CRAWL_TASK_STATE_UNKNOWN = 0;
  CRAWL_TASK_STATE_INIT = 1; // 已创建
  CRAWL_TASK_STATE_RUNNING = 2; // 运行中
  CRAWL_TASK_STATE_PAUSED = 3; // 已暂停
  CRAWL_TASK_STATE_DONE = 4; // 已完成
  CRAWL_TASK_STATE_CANCEL = 5; // 已取消
  CRAWL_TASK_STATE_ERROR = 6; // 异常
}