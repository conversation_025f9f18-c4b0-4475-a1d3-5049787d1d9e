syntax = "proto3";

package chilat.inquiry;


option java_package = "com.chilat.rpc.inquiry.param";

message InquiryCbmEditParam {
    repeated InquiryCbmEditParamModel inquiryCbmEditParam = 1;//商品编辑参数
}

message InquiryCbmEditParamModel {
    string inquiryBatchNo = 1; // 询价批次号，用于唯一标识一个询价批次
    string skuId = 2; // SKU_ID
    string storeId = 3; // 店铺id
    string skuNo = 4; // SKU_NO
    int32 packingCount = 14; // 装箱数，表示每个包装箱中包含的商品数量
    double goodsLength = 15; // 商品长（单位：cm）
    double goodsWidth = 16; // 商品宽（单位：cm）
    double goodsHeight = 17; // 商品高（单位：cm）
    double dimensionCm = 18; // 长*宽*高(立方米)，单箱体积
    double goodsWeight = 19; // 重量（单位：kg）
    int32 stockAvailability = 21; // 有无现货，表示商品是否有现货1有 0无
    double shippingCostPerBox = 22; // 单箱运费(元)，表示每个包装箱的运输费用
    string purchaseRemarks = 23; // 采购备注，用于记录采购过程中的额外信息或说明
    bool updateOnlyCBM = 24; // 是否仅更新装箱信息（默认：否）
}