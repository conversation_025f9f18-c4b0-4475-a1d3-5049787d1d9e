syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing.model";

import "common.proto";
import "common/business.proto";
import "mall/commodity/model/goods_info_model.proto";

// 促销活动商品索引页返回
message PromotionGoodsIndexResp {
  common.Result result = 1;
  PromotionGoodsIndexModel data = 2;
}
// 促销活动商品索引页信息
message PromotionGoodsIndexModel {
  string id = 10; // 促销活动ID
  common.GoodsSelectorType selectorType = 110; // 商品筛选器类型
  repeated PromotionGoodsSelectorModel selectorList = 120; // 商品筛选器列表
}

// 促销活动商品的楼层信息
message PromotionGoodsSelectorModel {
  string selectorId = 10; // 商品筛选器ID（根据 PromotionGoodsIndexModel.selectorType 确定：营销规则ID or 商品标签ID）
  string selectorName = 20; // 商品筛选器名称
  string bannerUrl = 30; // 横幅广告图片URL（空值表示不显示banner图片）
  int32 goodsCount = 40; // 商品数量（检索到的商品总数）
  repeated commodity.GoodsListDataItemModel goodsList = 50; //商品列表
}

