<template>
  <Carousel
    :content="authStore.i18n('cm_common.tip')"
    color="#e50113"
    height="0.8rem"
  ></Carousel>
  <div class="back-header">
    <a
      href="/h5"
      v-if="pageData.showGoHomeIcon"
      data-spm-box="navigation-top-homepage"
    >
      <img
        loading="lazy"
        src="@/assets/icons/home.svg"
        alt="home"
        class="w-[0.52rem] mr-[0.05rem]"
        referrerpolicy="no-referrer"
      />
    </a>
    <img
      loading="lazy"
      v-else
      alt="back"
      class="w-[0.22rem] mr-[0.1rem]"
      @click="onBackClick"
      src="@/assets/icons/arrowLeft.svg"
      referrerpolicy="no-referrer"
    />
    <div class="flex items-center ml-[0.6rem] flex-1 justify-end">
      <img
        loading="lazy"
        src="@/assets/icons/searchLine.svg"
        alt="search"
        class="w-[0.42rem] flex-shrink-0 mr-[0.16rem]"
        @click="onShowSearchInput"
        v-if="!pageData.showSearchInput"
        referrerpolicy="no-referrer"
      />
      <div
        v-else
        class="header-search flex-1 mr-[0.16rem]"
        data-spm-box="navigation-keyword-search"
      >
        <div class="search-bar-inner">
          <div class="search-bar-input-wrapper">
            <img
              loading="lazy"
              src="@/assets/icons/searchAc.svg"
              alt="search"
              class="w-[0.44rem] mr-[0.06rem]"
              referrerpolicy="no-referrer"
            />
            <input
              class="search-bar-input"
              type="text"
              maxlength="50"
              v-model.trim="pageData.keyword"
              :placeholder="
                isTagSearch
                  ? authStore.i18n('cm_search.searchInList')
                  : authStore.i18n('cm_search.searchPlaceholder')
              "
              @keyup.enter="onKeywordClick(pageData.keyword, $event)"
            />
          </div>
          <div class="flex items-center">
            <button
              class="search-bar-inner-button"
              @click="onKeywordClick(pageData.keyword, $event)"
            >
              <span>{{ authStore.i18n("cm_home.search") }}</span>
            </button>
          </div>
        </div>
      </div>
      <image-search class="w-[0.42rem] h-[0.42rem]">
        <img
          loading="lazy"
          src="@/assets/icons/imageSearchLine.svg"
          alt="search"
          class="w-[0.42rem]"
          referrerpolicy="no-referrer"
        />
      </image-search>
      <div class="ml-[0.32rem]" v-if="props.listType">
        <img
          loading="lazy"
          v-if="props.listType === 'list'"
          alt="grid"
          class="w-[0.42rem] h-[0.42rem]"
          src="@/assets/icons/grid.svg"
          @click="onChangeListType('grid')"
          referrerpolicy="no-referrer"
        />
        <img
          loading="lazy"
          v-else
          alt="list"
          class="w-[0.42rem] h-[0.42rem]"
          src="@/assets/icons/list.svg"
          @click="onChangeListType('list')"
          referrerpolicy="no-referrer"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["onChangeListType"]);

import { useConfigStore } from "@/stores/configStore";

const props = defineProps({
  listType: {
    type: String,
    default: "",
  },
});

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const pageData = reactive({
  showGoHomeIcon: false,
  showSearchInput: false,
  keyword: route.query.keyword || "",
});

const isTagSearch = computed(
  () => route.query.type === "recommendSearch" || !!route.query.tagId
);

function onShowSearchInput() {
  pageData.showSearchInput = true;
}

function onChangeListType(val: any) {
  emit("onChangeListType", val);
}

onBeforeMount(() => {
  // 获取当前页面的域名
  const currentDomain = window.location.hostname;
  // 获取来源页面的 referrer URL
  const referrer = document.referrer;
  if (referrer) {
    // 提取来源页面的域名
    const referrerDomain = new URL(referrer).hostname;
    // 比较当前页面和来源页面的域名
    if (currentDomain === referrerDomain) {
      pageData.showGoHomeIcon = false;
    } else {
      pageData.showGoHomeIcon = true;
    }
  } else {
    pageData.showGoHomeIcon = true;
  }
});

function onKeywordClick(keyword: any, event: any) {
  const query = {
    ...(route.query.type === "recommendSearch" && { type: "recommendSearch" }),
    ...(route.query.tagId && {
      tagId: route.query.tagId,
      tag: route.query.tag,
    }),
  };
  const word =
    keyword?.trim() === "" ? pageData.keyword?.trim() : keyword?.trim();
  window?.MyStat?.addPageEvent("click_search", `搜索关键词：${word}`); // 埋点

  navigateToPage(
    `/h5/search/list`,
    {
      keyword,
      ...query,
    },
    false,
    event
  );
}

onMounted(() => {
  window.addEventListener("scroll", onScroll);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
});
// 加载下一页
async function onScroll() {
  pageData.showSearchInput = false;
}

/** 返回上一页 */
function onBackClick() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.back-header {
  width: 100%;
  height: 0.88rem;
  padding: 0.2rem;
  background-color: #fff;
  border: 0.02rem solid #e5e7eb;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.back-header :deep(.search-bar-input) {
  margin-left: 0.02rem !important;
  margin-right: 0 !important;
}
img {
  max-width: initial;
}
:deep(.n-upload) {
  width: 0.42rem;
  height: 0.42rem;
}
.header-search {
  flex: 1;
  height: 0.6rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .search-bar-inner {
    width: 100%;
    height: 0.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4rem;
    background-color: #fff;
    border: 0.02rem solid #e50113;
    .search-bar-input-wrapper {
      flex: 1;
      display: flex;
      margin: 0 0.04rem 0 0.16rem;
    }
    .search-bar-input {
      flex: 1;
      width: 100%;
      margin: 0;
      padding: 0;
      outline: none;
      border: none;
      box-shadow: none;
      background-image: none;
      background-color: transparent;
      font-size: 0.24rem;
      line-height: 0.24rem;
      color: #222;
      background-color: #fff;
      margin-left: 0.12rem;
      margin-right: 0.08rem;
    }
    .search-bar-inner-button {
      width: 1.2rem;
      height: 0.48rem;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0.04rem;
      font-size: 0.28rem;
      line-height: 0.48rem;
      font-weight: 500;
      color: #fff;
      background: #e50113;
      border-radius: 4rem;
      border: 0 solid transparent;
      cursor: pointer;
    }
  }
}
</style>
