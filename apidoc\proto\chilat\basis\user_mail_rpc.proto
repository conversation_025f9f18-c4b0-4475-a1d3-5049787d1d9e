syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/param/user_mail_rpc_param.proto";
import "common.proto";

service UserMailRpc {

  //给用户发送验证邮箱的邮件
  rpc sendVerifyMail (SendVerifyMailRpcParam) returns (common.ApiResult);

  //新用户验证成功后发送邮件通知给老用户去领券
  rpc sendCouponNotifyMailToOldUser (SendCouponNotifyMailToOldUserRpcParam) returns (common.ApiResult);
}