@font-face {
  font-family: "Roboto";
  src: url("../fonts/Roboto-Regular.ttf");
  font-weight: normal;
}

@font-face {
  font-family: "Roboto";
  src: url("../fonts/Roboto-Medium.ttf");
  font-weight: 500;
}

body {
  font-family: "Roboto", sans-serif;
  font-weight: normal;
}


/* 页面切换动画*/
.page-enter-active,
.page-leave-active {
  will-change: transform;
  transition: all 0.3s ease-in-out;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

/* 1. 声明过渡效果 fade  */
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease-in-out;
}
/* 2. 声明进入和离开的状态 */
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
/*!* 3. 确保离开的项目被移除出了布局流*/
/*      以便正确地计算移动时的动画效果。 *!*/
.fade-leave-active {
  position: absolute;
}

.w1200 {
  width: 1200px;
  margin: 0 auto;
}

.text-primary {
  color: #e50113;
}

.warning-row td {
  background: rgb(51, 167, 212) !important;
  color: rgb(15, 124, 233) !important;
}

.success-row td {
  background: #e50113 !important;
  color: rgb(255, 255, 255) !important;
}

.custom-dots {
  display: flex;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: 0.15rem;
  left: 44vw;
}

.custom-dots li {
  display: inline-block;
  width: 4vw;
  max-width: 0.24rem;
  height: 0.08rem;
  margin: 0 0.02rem;
  border-radius: 0.08rem;
  background-color: #d7d7d7;
  cursor: pointer;
}

.custom-dots li.is-active {
  width: 8vw;
  max-width: 0.48rem;
  background: #e50113;
}

.enlarge-image {
  overflow: hidden;
  position: relative;
  img {
    transition: transform 0.25s ease;
  }
  &:hover img {
    transform: scale(1.1);
  }
}
/* 商品详情 */
.goods-desc,
.goods-desc div,
.goods-desc p,
.goods-desc table,
.goods-desc tbody {
  width: 100% !important;
}

.mobile-back-top {
  position: relative;
  border-radius: 50%;
  cursor: pointer;
}
.mobile-back-top::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  transform: translate(-50%, -50%);
  height: 150%;
  border-radius: 100%;
  background: #fff;
  z-index: -1;
}

.mobile-cart-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
  .fixed-count {
    padding: 0.02rem 0.12rem;
    position: absolute;
    right: -0.08rem;
    top: -0.02rem;
    color: #fff;
    background: #e50113;
    border-radius: 0.12rem;
    font-size: 0.24rem;
    line-height: 0.24rem;
  }
}

.article-page {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: revert !important;
    font-weight: revert !important;
  }
}

.vjs-big-play-button {
  width: 70px !important;
  height: 48px !important;
  line-height: 48px !important;
  font-size: 38px !important;
  border-radius: 14px !important;
  background-color: #e50113 !important;
  border: none !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.vjs-youtube-mobile .vjs-big-play-button {
  display: block !important;
}

.vjs-waiting .vjs-big-play-button {
  display: none !important;
}
.vjs-youtube-mobile.vjs-has-started .vjs-big-play-button {
  display: none !important;
}
