syntax = "proto3";
package chilat.support;

option java_package = "com.chilat.rpc.support";

import "common.proto";
import "chilat/basis/param/sys_dict_param.proto";
import "chilat/basis/model/sys_dict_model.proto";


// 字典查询
service SysDict {
  // 查询字典项
  rpc listDictItem (chilat.basis.DictItemListParam) returns (chilat.basis.DictItemListResp);
  // 过滤前端未配置的翻译KEY（参数：用换行符分隔多个翻译KEY）
  rpc filterVueNotSetTranCode(common.StringParam) returns (common.StringResult);
}