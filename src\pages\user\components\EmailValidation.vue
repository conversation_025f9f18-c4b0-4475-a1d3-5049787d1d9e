<template>
  <div class="email-validation">
    <div class="validation-content">
      <div class="validation-title">
        {{ authStore.i18n("cm_common_verifyEmail") }}
      </div>
      <div class="validation-subtitle">
        {{ authStore.i18n("cm_common_unlockBundle") }}
      </div>
      <div class="validation-desc">
        {{ authStore.i18n("cm_common_verifyEmailPrompt") }}
      </div>
      <div class="validation-coupon px-[70px] text-[15px] leading-[15px]">
        <n-grid
          x-gap="20"
          :cols="3"
          :class="{ '!flex !justify-center': props.couponList.length < 3 }"
        >
          <n-gi v-for="coupon in props.couponList" :key="coupon?.id">
            <div
              class="flex items-center text-[#e50113] mb-[10px]"
              :class="{
                'comm-coupon': coupon?.couponType === 'COUPON_TYPE_COMMISSION',
              }"
            >
              <div class="coupon-card mr-[4px] text-[#fff]">
                <div
                  class="w-full h-[20px] mb-[6px] flex items-center justify-center"
                >
                  <template v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
                    <div>
                      <span class="text-[18px] leading-[18px] font-medium">
                        {{ discountToPercentage(coupon?.discount) }}
                      </span>
                      <span class="coupon-discount">
                        {{ authStore.i18n("cm_coupon.discount") }}
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <span class="coupon-unit">
                      <span>{{ currencyUnit }}</span>
                    </span>
                    <span class="text-[18px] leading-[18px] font-medium"
                      >$
                      {{
                        setNewUnit(coupon?.preferentialAmount, "noUnit")
                      }}</span
                    >
                  </template>
                </div>
                <div class="coupon-type">
                  <span v-if="coupon?.couponType === 'COUPON_TYPE_PRODUCT'">
                    {{ authStore.i18n("cm_coupon.productCoupon") }}
                  </span>
                  <span v-if="coupon?.couponType === 'COUPON_TYPE_COMMISSION'">
                    {{ authStore.i18n("cm_coupon.commissionCoupon") }}
                  </span>
                </div>
              </div>
              <div class="flex w-[30px] text-[20px] tracking-[-1px">
                <span class="mr-[2px]">x</span>
                {{ coupon?.count }}
              </div>
            </div></n-gi
          >
        </n-grid>
      </div>
      <n-button
        color="#E50113"
        text-color="#fff"
        :loading="pageData.loading"
        @click="resendVerification"
        class="rounded-[50px] w-[200px] h-[38px] text-[16px] mr-[12px]"
      >
        <div>{{ authStore.i18n("cm_common_emailActivateNow") }}</div>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="emailValidation">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();

const props = defineProps({
  couponList: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive(<any>{
  loading: false,
});

// 发送验证邮箱的邮件
async function resendVerification() {
  pageData.loading = true;
  const res: any = await useSendVerifyMail({
    verifyMailScene: "MY_COUPON_LIST",
  });
  pageData.loading = false;
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.reload();
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); //
      // 跳转邮箱
      navigateToEmail();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>

<style scoped lang="scss">
.email-validation {
  width: 960px;
  margin: 18px auto 0;
  padding: 20px 158px 22px 158px;
  border-radius: 4px;
  background: #fafafa;
  .validation-content {
    padding: 18px 7px;
    border-radius: 4px;
    text-align: center;
    .validation-title {
      color: #333;
      font-size: 20px;
      font-weight: 500;
      line-height: 20px;
    }
    .validation-subtitle {
      padding: 0 40px;
      margin: 17px 0;
      color: #e50113;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
    }
    .validation-desc {
      padding: 0 20px;
      color: #4d4d4d;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .validation-coupon {
      margin: 20px 0 34px;
    }
  }
}

.coupon-card {
  width: 120px;
  height: 58px;
  background: url("@/assets/icons/marketing/productCoupon.png");
  background-size: 100%100%;
  padding: 14px 4px 4px 36px;
}
.comm-coupon {
  color: #dd4f12;
  .coupon-card {
    background: url("@/assets/icons/marketing/commissionCoupon.png");
    background-size: 100%100%;
  }
}
.coupon-type {
  width: 100%;
  text-transform: uppercase;
  letter-spacing: -1px;
  text-align: center;
  text-wrap: nowrap;

  span {
    width: 100%;
    display: inline-block;
    font-size: 12px;
    line-height: 10px;
    transform: scale(0.74);
    transform-origin: left top;
  }
}
.coupon-unit {
  display: inline-block;
  transform: rotate(-90deg);
  font-weight: 500;
  span {
    display: inline-block;
    font-size: 12px;
  }
}
.coupon-discount {
  display: inline-block;
  font-size: 12px;
  margin-left: 2px;
  font-weight: 500;
}
</style>
