syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

message GoodsTagListPageResp {
  common.Result result = 1;
  GoodsTagListPageModel data = 2;
}

message GoodsTagListPageModel {
  common.Page page = 1;
  repeated GoodsTagModel data =2;
}

message GoodsTagModel {
  string tagId = 1;   // 标签ID
  string tagName = 2; //  标签名称
  int64 onSaleGoodsCount = 3; // 上架商品数量
  common.GoodsTagTypeEnum goodsTagType = 4; // 标签类型
  string tagUrl = 5;    // 标签链接
  string salesManId = 6; // 业务员ID
  string salesManName = 7; // 业务员名称
  string creatorId = 8; // 创建人ID
  string creatorName = 9; // 创建人名称
  string createTime = 10; // 创建时间 yyyy-MM-dd HH:mm:ss
  string productType = 11; // 产品类型
  string departmentName = 12; // 所属部门
}

message GoodsTagEditLogResp {
  common.Result result = 1;
  repeated GoodsTagEditLog data = 2;
}

message GoodsTagEditLog {
  string operatorTime = 1; // 操作时间
  string operator = 2; // 操作人
  string description = 3; // 操作内容
  string id = 4; // 操作记录ID
}

message DeleteTagResp {

  common.Result result = 1;
  DeleteTagModel data = 2;
}

message DeleteTagModel {
  bool isDeleted = 1; // 是否删除成功
  repeated common.DeleteTagCheckResultEnum checkResultList = 2; // 检查结果
}

message GoodsTagListResp {
  common.Result result = 1;
  repeated GoodsTagModel data = 2;
}