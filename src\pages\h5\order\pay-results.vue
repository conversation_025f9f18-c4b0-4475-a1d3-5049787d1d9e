<template>
  <div class="mobile-container">
    <!-- 头部信息 -->
    <mobile-search-bar :showCart="true"></mobile-search-bar>
    <!-- 信息提示 -->
    <n-card class="py-[0.32rem] mt-[0.4rem]" :bordered="false">
      <!-- 支付成功 -->
      <template v-if="pageData.payResult.payStatus === 'PAID_SUCCESS'">
        <!-- 产品成本支付成功 || 订单费用支付成功 -->
        <template
          v-if="
            pageData.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT' ||
            pageData.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE'
          "
        >
          <div class="text-center">
            <img
              loading="lazy"
              alt="paySuccess"
              src="@/assets/icons/order/paySuccess.svg"
              class="w-[1.04rem] h-[1.04rem] mx-auto mb-[0.36rem]"
              referrerpolicy="no-referrer"
            />
            <span
              class="font-semibold text-[0.52rem] leading-[0.24rem] text-[#000]"
              >{{ authStore.i18n("cm_order.paySuccess") }}</span
            >
          </div>
          <div
            class="text-center mt-[0.24rem] pb-[0.52rem] mb-[0.36rem] text-[0.28rem] leading-[0.4rem] text-[##939393] border-b-1 border-[#BBB]"
          >
            {{ authStore.i18n("cm_order.payOrderNo") }}
            {{ pageData.orderNo }},
            <span v-if="pageData.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT'">{{
              authStore.i18n("cm_order.payProductCost")
            }}</span>
            <span v-if="pageData.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE'">{{
              authStore.i18n("cm_order.payOrderCost")
            }}</span>
          </div>
          <div class="w-full">
            <n-button
              color="#E50113"
              @click="goHome"
              class="w-[6.56rem] h-[0.72rem] rounded-[4rem] mb-[0.24rem]"
            >
              <span class="text-[0.32rem] leading-[0.32rem]">{{
                authStore.i18n("cm_order.keepShopping")
              }}</span>
            </n-button>
            <n-button
              @click="goOrderDetails"
              data-spm-box="pay-result-go-detail"
              class="w-[6.56rem] h-[0.72rem] rounded-[4rem]"
            >
              <span class="text-[0.32rem] leading-[0.32rem]">{{
                authStore.i18n("cm_order.viewOrderDetails")
              }}</span>
            </n-button>
          </div>
        </template>
        <!-- 国际费用支付成功 -->
        <template v-if="pageData.mallOrderStatus === 'MALL_WAIT_PAY_INTER_FEE'">
          <div class="text-center">
            <img
              loading="lazy"
              alt="paySuccess"
              src="@/assets/icons/order/paySuccess.svg"
              class="w-[1.04rem] h-[1.04rem] mx-auto mb-[0.36rem]"
              referrerpolicy="no-referrer"
            />
            <span
              class="font-semibold text-[0.52rem] leading-[0.24rem] text-[#000]"
              >{{ authStore.i18n("cm_order.paySuccess") }}</span
            >
          </div>
          <div
            class="text-center mt-[0.24rem] pb-[0.52rem] mb-[0.36rem] text-[0.28rem] leading-[0.4rem] text-[##939393] border-b-1 border-[#BBB]"
          >
            {{ authStore.i18n("cm_order.payOrderNo") }}
            {{ pageData.orderNo }},
            {{ authStore.i18n("cm_order.patientlyTransportation") }}
          </div>
          <div class="w-full">
            <n-button
              color="#E50113"
              @click="goOrderDetails"
              data-spm-box="pay-result-go-detail"
              class="w-[6.56rem] h-[0.72rem] rounded-[4rem]"
            >
              <span class="text-[0.32rem] leading-[0.32rem]">{{
                authStore.i18n("cm_order.viewOrderDetails")
              }}</span>
            </n-button>
          </div>
        </template>
      </template>

      <!-- 支付中-->
      <template v-if="pageData.payResult.payStatus === 'PAYING'">
        <div
          class="flex flex-col items-center justify-center text-center mb-[0.36rem] pb-[0.52rem] border-b-1 border-[#BBB]"
        >
          <n-spin
            :size="50"
            stroke="#e50113"
            class="w-[1.04rem] h-[1.04rem] mx-auto mb-[0.36rem]"
          />
          <span
            class="font-semibold text-[0.4rem] leading-[0.56rem] text-[#000]"
            >{{ authStore.i18n("cm_order.payPending") }}</span
          >
        </div>
        <div class="w-full">
          <n-button
            color="#E50113"
            @click="goOrderPay"
            data-spm-box="pay-result-buy-again"
            class="w-[6.56rem] h-[0.72rem] rounded-[4rem] mb-[0.24rem]"
          >
            <span class="text-[0.32rem] leading-[0.32rem]">{{
              authStore.i18n("cm_order.orderPayAgain")
            }}</span>
          </n-button>
          <n-button
            @click="goOrderDetails"
            data-spm-box="pay-result-go-detail"
            class="w-[6.56rem] h-[0.72rem] rounded-[4rem]"
          >
            <span class="text-[0.32rem] leading-[0.32rem]">{{
              authStore.i18n("cm_order.viewOrderDetails")
            }}</span>
          </n-button>
        </div>
      </template>

      <!-- 支付失败 -->
      <template v-if="pageData.payResult.payStatus === 'PAID_FAIL'">
        <div class="text-center">
          <img
            loading="lazy"
            alt="payError"
            src="@/assets/icons/order/payError.svg"
            class="w-[1.04rem] h-[1.04rem] mx-auto mb-[0.36rem]"
            referrerpolicy="no-referrer"
          />
          <span
            class="font-semibold text-[0.52rem] leading-[0.24rem] text-[#000]"
            >{{ authStore.i18n("cm_order.payFailure") }}</span
          >
        </div>
        <div
          class="text-center mt-[0.24rem] pb-[0.52rem] mb-[0.36rem] text-[0.28rem] leading-[0.4rem] text-[##939393] border-b-1 border-[#BBB]"
        >
          {{ pageData.payResult.errorMsg }}
        </div>
        <div class="w-full">
          <n-button
            color="#E50113"
            @click="goOrderPay"
            data-spm-box="pay-result-buy-again"
            class="w-[6.56rem] h-[0.72rem] rounded-[4rem] mb-[0.24rem]"
          >
            <span class="text-[0.32rem] leading-[0.32rem]">{{
              authStore.i18n("cm_order.orderPayAgain")
            }}</span>
          </n-button>
          <n-button
            @click="goOrderDetails"
            data-spm-box="pay-result-go-detail"
            class="w-[6.56rem] h-[0.72rem] rounded-[4rem]"
          >
            <span class="text-[0.32rem] leading-[0.32rem]">{{
              authStore.i18n("cm_order.viewOrderDetails")
            }}</span>
          </n-button>
        </div>
      </template>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();

const pageData = reactive({
  orderNo: route?.query?.orderNo || "",
  paymentId: route?.query?.paymentId || "",
  payResult: <any>{}, // 支付结果 返回支付状态 失败原因
  mallOrderStatus: "", // 订单状态
  intervalId: null as any,
});

// 查询支付结果
getQueryPayResult();
async function getQueryPayResult() {
  const res: any = await useQueryPayResult({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
  });

  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    // 未支付 跳转收银台
    if (pageData.payResult.payStatus === "INIT") {
      navigateTo(
        `/h5/order/payment?orderNo=${pageData.orderNo}&paymentId=${pageData.paymentId}`
      );
    }
    // "支付中"时,定时查询支付结果
    if (pageData.payResult.payStatus === "PAYING") {
      if (pageData.intervalId === null) {
        pageData.intervalId = setInterval(() => {
          getQueryPayResult();
        }, 3000); // 每3秒查询一次
      }
    } else {
      // 非"支付中"，停止定时查询
      if (pageData.intervalId !== null) {
        clearInterval(pageData.intervalId);
        pageData.intervalId = null;
      }
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message);
  }
}

onBeforeUnmount(() => {
  if (pageData.intervalId !== null) {
    clearInterval(pageData.intervalId);
  }
});

function goHome() {
  window.location.replace("/h5");
}

function goOrderDetails(event: any) {
  const spm = window.MyStat.getPageSPM(event);
  window.location.replace(
    `/h5/order/details?orderNo=${pageData.orderNo}&spm=${spm}`
  );
}

function goOrderPay(event: any) {
  const spm = window.MyStat.getPageSPM(event);
  window.location.replace(
    `/h5/order/payment?orderNo=${pageData.orderNo}&paymentId=${pageData.paymentId}&spm=${spm}`
  );
}
</script>

<style scoped lang="scss"></style>
