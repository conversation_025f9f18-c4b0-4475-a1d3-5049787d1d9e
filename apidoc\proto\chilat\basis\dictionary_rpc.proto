syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/dictionary_model.proto";
import "chilat/basis/param/dictionary_param.proto";
import "common.proto";
import "chilat/basis/param/config_param.proto";

// 字典服务
service DictionaryRpc {
  // 更新中间件缓存（由admin调用）
  rpc updateProviderCache (ProviderCacheUpdateParam) returns (common.IntResult);
  // 获取下一个顺序号（参数为SeqNoFieldEnum中的code）
  rpc getNextSeqNo(common.IntParam) returns (common.LongResult);
  // 获取所有 BaseSimpleJob
  rpc getAllMappedJob (common.EmptyParam) returns (JobStatListResp);
  // 获取网站配置
  rpc getWebsiteConfig (GetWebsiteConfigParam) returns (common.StringResult);
  // 获取站点配置
  rpc getSiteList (common.EmptyParam) returns (GetSiteListResp);
}