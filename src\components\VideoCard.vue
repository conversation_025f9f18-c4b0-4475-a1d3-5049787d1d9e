/**
 * 本地视频播放器
 * 
 * 技术: video.js
 * 功能: 
 * - 播放本地视频文件,不支持 YouTube 链接
 * - 支持封面图
 * - 用户交互后可自动播放
 */

<template>
  <component
    v-if="VideoPlayerComponent"
    :is="VideoPlayerComponent"
    :src="props.src"
    :poster="props.poster"
    controls
    :loop="props.loop"
    :volume="0.6"
    :width="props.width"
    :height="props.height"
    :videoWidth="props.width"
    :videoHeight="props.height"
    @ended="onEndedEvent($event)"
    :autoplay="props.autoplay"
    playsinline
    :options="{
      playsinline: true,
      html5: {
        hls: {
          overrideNative: true
        },
        nativeVideoTracks: false,
        nativeAudioTracks: false,
        nativeTextTracks: false
      }
    }"
  />
</template>

<script setup lang="ts" name="VideoCard">
import { defineProps, defineEmits, onMounted, ref } from "vue";
import "video.js/dist/video-js.css";

const emit = defineEmits(["videoEvent", "videoEnded"]);

const props = defineProps({
  src: {
    type: String,
    default: "",
  },
  poster: {
    type: String,
    default: "",
  },
  width: {
    type: Number,
    default: 508,
  },
  height: {
    type: Number,
    default: 508,
  },
  loop: {
    type: Boolean,
    default: false,
  },
  autoplay: {
    type: Boolean,
    default: false,
  },
});

const VideoPlayerComponent = ref(null);

onMounted(() => {
  if (process.client) {
    import("@videojs-player/vue").then((module) => {
      VideoPlayerComponent.value = module.VideoPlayer;
    });
  }
});

function onEndedEvent(event: any) {
  emit("videoEvent", true);
}
</script>
