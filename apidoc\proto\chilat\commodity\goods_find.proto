syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/commodity_common.proto";
import "chilat/basis/param/goods_find_param.proto";
import "chilat/basis/model/goods_find_model.proto";
import "chilat/commodity/param/goods_find_param.proto";
import "common.proto";

service GoodsFind {
  // 找货列表
  rpc listPage (basis.GoodsFindListPageQueryParam) returns (basis.GoodsFindListPageResp);

  // 找货详情
  rpc getDetail (common.IdParam) returns (basis.GoodsFindDetailResp);

  // 分配负责人
  rpc assign (basis.GoodsFindAssignParam) returns (common.ApiResult);

  // 同步负责人
  rpc syncChargePerson (basis.SyncChargePersonParam) returns (common.ApiResult);

  // 查看找货备注
  rpc remarkList (common.IdParam) returns (basis.RemarkListResp);

  // 操作:保存找货备注、开始找货、通知客户、找货完成
  rpc operate (basis.OperateParam) returns (common.ApiResult);

  //找货导出
  rpc exportExcel(basis.ExportExcelParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };

  // 将找货信息同步到询盘云（参数为找货单号）
  rpc pushXPY (GoodsFindPushXpyParam) returns (common.ApiResult);

}
