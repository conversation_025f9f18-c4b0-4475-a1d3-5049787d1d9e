syntax = "proto3";
package chilat.trade;

option java_package = "com.chilat.rpc.trade.common";

import "common.proto";

enum PurchasePayState {
  PURCHASE_PAY_STATE_UNPAID = 0; // 未支付
  PURCHASE_PAY_STATE_PAID = 1; // 已支付
}

enum PurchaseOrderState {
  PURCHASE_ORDER_STATE_WAIT_ORDERED = 0; // 待下单
  PURCHASE_ORDER_STATE_WAIT_PAID = 1; // 待付款
  PURCHASE_ORDER_STATE_WAIT_SHIPPING = 2; // 待发货
  PURCHASE_ORDER_STATE_WAIT_RECEIVE = 3; // 待收货
  PURCHASE_ORDER_STATE_FINISHED = 4; // 已完成
  PURCHASE_ORDER_STATE_CANCELED = 5; // 已取消
}

enum PurchaseOrderDeliveryType {
  THIRD_PARTY = 0; // 第三方派送
  SELLER_SELF = 1; // 商家自配
  PURCHASER_SELF = 2; // 采购自提
}