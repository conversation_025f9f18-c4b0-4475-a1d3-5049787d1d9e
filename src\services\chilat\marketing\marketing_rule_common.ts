/* eslint-disable */

export const protobufPackage = "chilat.marketing";

export enum GoodsOnlineDays {
  GOODS_ONLINE_DAYS_UNKNOWN = 0,
  /** GOODS_ONLINE_DAYS_TODAY - 近24小时 */
  GOODS_ONLINE_DAYS_TODAY = 1,
  /** GOODS_ONLINE_DAYS_SEVEN - 近7天 */
  GOODS_ONLINE_DAYS_SEVEN = 2,
  /** GOODS_ONLINE_DAYS_FOURTEEN - 近14天 */
  GOODS_ONLINE_DAYS_FOURTEEN = 3,
  UNRECOGNIZED = -1,
}

export function goodsOnlineDaysFromJSON(object: any): GoodsOnlineDays {
  switch (object) {
    case 0:
    case "GOODS_ONLINE_DAYS_UNKNOWN":
      return GoodsOnlineDays.GOODS_ONLINE_DAYS_UNKNOWN;
    case 1:
    case "GOODS_ONLINE_DAYS_TODAY":
      return GoodsOnlineDays.GOODS_ONLINE_DAYS_TODAY;
    case 2:
    case "GOODS_ONLINE_DAYS_SEVEN":
      return GoodsOnlineDays.GOODS_ONLINE_DAYS_SEVEN;
    case 3:
    case "GOODS_ONLINE_DAYS_FOURTEEN":
      return GoodsOnlineDays.GOODS_ONLINE_DAYS_FOURTEEN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GoodsOnlineDays.UNRECOGNIZED;
  }
}

export function goodsOnlineDaysToJSON(object: GoodsOnlineDays): string {
  switch (object) {
    case GoodsOnlineDays.GOODS_ONLINE_DAYS_UNKNOWN:
      return "GOODS_ONLINE_DAYS_UNKNOWN";
    case GoodsOnlineDays.GOODS_ONLINE_DAYS_TODAY:
      return "GOODS_ONLINE_DAYS_TODAY";
    case GoodsOnlineDays.GOODS_ONLINE_DAYS_SEVEN:
      return "GOODS_ONLINE_DAYS_SEVEN";
    case GoodsOnlineDays.GOODS_ONLINE_DAYS_FOURTEEN:
      return "GOODS_ONLINE_DAYS_FOURTEEN";
    case GoodsOnlineDays.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum StockLocation {
  STOCK_LOCATION_UNKNOWN = 0,
  /** STOCK_LOCATION_CHINA - 中国有库存 */
  STOCK_LOCATION_CHINA = 1,
  /** STOCK_LOCATION_LOCAL - 墨西哥本地有库存 */
  STOCK_LOCATION_LOCAL = 2,
  UNRECOGNIZED = -1,
}

export function stockLocationFromJSON(object: any): StockLocation {
  switch (object) {
    case 0:
    case "STOCK_LOCATION_UNKNOWN":
      return StockLocation.STOCK_LOCATION_UNKNOWN;
    case 1:
    case "STOCK_LOCATION_CHINA":
      return StockLocation.STOCK_LOCATION_CHINA;
    case 2:
    case "STOCK_LOCATION_LOCAL":
      return StockLocation.STOCK_LOCATION_LOCAL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return StockLocation.UNRECOGNIZED;
  }
}

export function stockLocationToJSON(object: StockLocation): string {
  switch (object) {
    case StockLocation.STOCK_LOCATION_UNKNOWN:
      return "STOCK_LOCATION_UNKNOWN";
    case StockLocation.STOCK_LOCATION_CHINA:
      return "STOCK_LOCATION_CHINA";
    case StockLocation.STOCK_LOCATION_LOCAL:
      return "STOCK_LOCATION_LOCAL";
    case StockLocation.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}
