<template>
  <div class="container">
    <search-cate-card></search-cate-card>
    <div class="w-[740px] flex mx-auto">
      <div
        data-spm-box="article-inner-link"
        v-if="pageData.articleDetail?.content"
        v-html="pageData.articleDetail?.content"
        class="article-page whitespace-pre-wrap"
      ></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive<any>({
  articleDetail: "",
});

onGetArticle();
async function onGetArticle() {
  const res: any = await useArticleDetail({
    id: route.query.id,
    title: route.query.title,
    articleCode: route.query.code,
  });
  if (res?.result?.code === 200) {
    pageData.articleDetail = res?.data;
  }
}
</script>
<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
</style>
