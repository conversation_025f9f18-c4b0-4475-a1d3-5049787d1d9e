syntax = "proto3";
package mall.marketing;

option java_package = "com.chilat.rpc.marketing.param";

import "common.proto";
import "mall/marketing/marketing_common.proto";

//潜客信息表单提交参数
message PotentialUserSaveParam {
  string userSource = 10; // 用户来源（活动的中文说明）
  string contactName = 20; // 姓名（最长100字；必填）
  string countryId = 30; // 国家ID（必填）
  string whatsapp = 60; // whatsapp账号（最长64字）
  string email = 70; // 邮箱地址（最长64字）
  bool withImportExperience = 75; // 是否有进口经验（true:有，false:无）
  PotentialUserType userType = 80; // 用户类型
  string userTypeRemark = 90; // 用户类型备注（用户类型为“其它”，用户输入类型说明；最长200字）
  PotentialServiceType serviceType = 100; // 服务类型
  string serviceTypeRemark = 110; // 服务类型备注（服务类型为“其它”，用户输入类型说明；最长200字；保留字段）
  PotentialPurchaseQuantity purchaseQuantity = 115; //采购数量
  string buyerRemark = 120; // 用户留言（最长6万字）
  bool reCaptchaLoadSuccess = 130; //Google reCAPTCHA是否加载成功
  string reCaptchaToken = 140; //Google reCAPTCHA Token
  string captchaPageSource = 150; //显示验证码页面的来源代码（32个字节以内；例子：notas；必须与GetCaptchaImageParam中的代码相同）
  string captchaCode = 160; //用户输入的验证码（验证码错误的错误码：952401，若遇到验证码错误，前端刷新验证码后重试）
}

//潜客直接点击whatsapp
message PotentialUserClickParam {
  string userSource = 10; // 用户来源（活动的中文说明）
  string clickSource = 20; // 点击来源（与事件描述相同）
  bool reCaptchaLoadSuccess = 130; //Google reCAPTCHA是否加载成功
  string reCaptchaToken = 140; //Google reCAPTCHA Token
}