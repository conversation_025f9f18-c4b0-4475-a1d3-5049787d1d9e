syntax = "proto3";
package chilat.coupon;


option java_package = "com.chilat.rpc.coupon.common";

/*------------------------------ 业务相关公共参数 ------------------------------*/
// 优惠券主体状态
enum CouponStatus {
     COUPON_DRAFT = 0; //草稿
     COUPON_NOT_ACTIVE = 1; //卡券失效
     COUPON_ACTIVE = 2; //卡券生效
}
// 用户单张券状态
enum CouponTicketStatus {
     COUPON_TICKET_NORMAL = 0; //正常
     COUPON_TICKET_LOCK = 1; //卡券锁定
     COUPON_TICKET_USED = 2; //卡券使用
     COUPON_TICKET_EXPIRE = 3; //卡券过期/失效
     COUPON_TICKET_CANCEL = 4; //卡券作废
}
// 优惠券发放方式
enum CouponDistributionStatus {
     COUPON_DISTRIBUTION_ACTIVE = 0; //主动领取
     COUPON_DISTRIBUTION_AUTOMATIC = 1; //自动发放
     COUPON_DISTRIBUTION_EXCHANGE = 2; //优惠码兑换
}

// 优惠方式
enum CouponWayStatus {
     COUPON_WAY_FULL_REDUCTION = 0; //满减券
     COUPON_WAY_DIRECT_REDUCTION = 1; //直减券
     COUPON_WAY_DISCOUNT = 2; //折扣券
}

// 优惠券类型
enum CouponTypeStatus {
     COUPON_TYPE_PRODUCT = 0; //产品券
     COUPON_TYPE_COMMISSION = 1; //佣金券
}


// 优惠券使用规则
enum CouponUseRuleStatus {
     COUPON_MAXIMUM_AVAILABLE = 0; //1、本券最多可使用
     COUPON_SAME_TYPE_OVERLAY = 1; //2、可与同类型叠加
     COUPON_FIRST_USE = 2; //3、首单使用
}

// 优惠券有效期类型
enum CouponEffectiveTypeStatus {
     DISTRIBUTION_DATE_EFFECTIVEDAYS = 0; // 1、发放日期+有效天数
     DISTRIBUTION_DATE_EFFECTIVEHOURS = 1; //2、发放日期+有效小时
     FIXEDTIME = 2; //3、固定时间
}

// 优惠券使用条件类型
enum CouponUseConditionsTypestatus {
     FULL = 0; // 1、满
     EVERY_FULL = 1; //2、每满
     UNLIMITED = 2; //3、无限制
}



