<template>
  <div class="header-searchbar gap-[10px] flex-1">
    <div class="header-search" data-spm-box="navigation-keyword-search">
      <div class="search-bar-inner">
        <div class="search-bar-input-wrapper">
          <input
            class="search-bar-input"
            type="text"
            maxlength="50"
            v-model.trim="pageData.keyword"
            :placeholder="
              isPcGoodsListTagSearch
                ? authStore.i18n('cm_search.searchInList')
                : authStore.i18n('cm_home.searchPlaceholder')
            "
            @keyup.enter="onKeywordClick(pageData.keyword, $event)"
          />
        </div>
        <div class="flex items-center">
          <button
            class="search-bar-inner-button"
            @click="onKeywordClick(pageData.keyword, $event)"
          >
            <img
              loading="lazy"
              src="@/assets/icons/search.svg"
              alt="search"
              class="w-[22px] mr-[3px]"
              referrerpolicy="no-referrer"
            />
            <span>{{ authStore.i18n("cm_home.search") }}</span>
          </button>
        </div>
      </div>
    </div>
    <div>
      <image-search-multi placement="left-start">
        <div
          class="w-[46px] h-[46px] rounded-full bg-[#e50113] flex items-center justify-center cursor-pointer image-search"
        >
          <img
            src="@/assets/icons/common/image-white.svg"
            alt="search"
            class="w-[28px] h-[28px] mb-[2px]"
          />
        </div>
      </image-search-multi>
    </div>
  </div>
</template>

<script setup lang="ts" name="TextImageSearch">
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive<any>({
  keyword: route.query.keyword || "",
});

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const isPcGoodsListTagSearch = computed(() => {
  return (
    /^\/goods\/list/.test(route.path) &&
    (route.query?.type === "recommendSearch" || route.query?.tagId)
  );
});

function onKeywordClick(keyword: string, event: any) {
  const word =
    keyword?.trim() === "" ? pageData.keyword?.trim() : keyword?.trim();

  window?.MyStat?.addPageEvent("click_search", `搜索关键词：${word}`); // 埋点

  if (isMobile.value) {
    navigateToPage(
      `/h5/search/list`,
      {
        keyword,
      },
      false,
      event
    );
  } else {
    // 义乌货盘以及美客多货盘,推荐商品列表是搜索为该货盘搜索
    if (isPcGoodsListTagSearch.value) {
      navigateToPage(
        `${goodsListAllPath}`,
        {
          keyword: word,
          ...(route.query.type === "recommendSearch" && {
            type: "recommendSearch",
          }),
          ...(route.query.tagId && {
            tagId: route.query.tagId,
            tag: route.query.tag,
          }),
        },
        false,
        event
      );
    } else {
      navigateToPage(
        `${goodsListAllPath}`,
        {
          keyword: word,
        },
        true,
        event
      );
    }
  }
}
</script>
<style scoped lang="scss">
.header-searchbar {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  .header-search {
    flex-basis: 60%;
    height: 44px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-width: 380px;
    .search-bar-inner {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 44px;
      width: 100%;
      border-radius: 200px;
      background-color: #fff;
      border: 1px solid #e50113;
      .search-bar-input-wrapper {
        width: 100%;
        margin: 0 20px;
      }
      .search-bar-input {
        width: 100%;
        margin: 0;
        padding: 0;
        outline: none;
        border: none;
        background-image: none;
        background-color: transparent;
        box-shadow: none;
        color: #222;
        background-color: #fff;
      }
      .search-bar-inner-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 96px;
        height: 40px;
        margin-right: 2px;
        margin-left: 8px;
        font-size: 16px;
        line-height: 16px;
        font-weight: 500;
        color: #fff;
        background: #e50113;
        border-radius: 200px;
        border: 0 solid transparent;
        cursor: pointer;
      }
    }
  }
  .image-search-inner {
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 12px;
    padding-right: 12px;
    margin-left: 10px;
    font-size: 16px;
    font-weight: 500;
    line-height: 16px;
    border-radius: 200px;
    background: #e50113;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.15);
    color: #fff;
    cursor: pointer;
    .image-icon {
      width: 28px;
      margin-right: 6px;
    }
  }
}

.image-search {
  box-shadow: 2px 2px 4px 0 rgba(0, 0, 0, 0.1);
}

input::placeholder {
  color: #a6a6a6;
  opacity: 1;
}
</style>
