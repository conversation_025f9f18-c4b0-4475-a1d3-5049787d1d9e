syntax = "proto3";
package chilat.report;

import "chilat/report/model/inquiry_model.proto";
import "chilat/report/param/inquiry_param.proto";
import "common.proto";

option java_package = "com.chilat.rpc.report";

service InquiryReport {
  // 根据类目查询询盘情况
  rpc queryGroupByCategory(InquiryReportPageParam) returns (QueryGroupByCategoryResp);
  // 根据商品查询询盘情况
  rpc queryGroupByGoods(InquiryReportPageParam) returns (QueryGroupByGoodsResp);
  // 根据地区查询询盘情况
  rpc queryGroupByCountry(InquiryReportParam) returns (QueryGroupByCountryResp);
  // 根据用户查询询盘情况
  rpc queryGroupByCustomer(InquiryReportPageParam) returns (QueryGroupByCustomerResp);
  // 站内过程指标
  rpc queryProcessIndicator(ProcessIndicatorParam) returns (ProcessIndicatorResp);
  // 导出站内过程指标
  rpc exportProcessIndicator(ProcessIndicatorParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };

  // 基于SPM补充询盘单来源的标签ID
  rpc fixGoodsTagBySpm(common.CodesParam) returns (common.StringsResult);

}