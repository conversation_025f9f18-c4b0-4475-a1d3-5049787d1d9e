<template>
  <div class="mobile-container">
    <n-flex vertical>
      <div
        class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] items-center justify-center bg-white fixed top-0"
      >
        <icon-card
          color="#555"
          size="0.48rem"
          name="ep:arrow-left-bold"
          class="fixed left-[0.2rem]"
          data-spm-box="navigation-back-icon"
          @click="onBackClick"
        ></icon-card>
        <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
          {{ authStore.i18n("cm_user.invite") }}
        </div>
      </div>

      <div class="font-medium text-[0.4rem] text-center">
        {{
          `&nbsp;&nbsp;&nbsp;&nbsp;${authStore.i18n(
            "cm_invite.inviteFriendTitle"
          )}`
        }}
      </div>
      <div class="text-[0.28rem] text-center">
        {{ authStore.i18n("cm_invite.inviteFriendDesc") }}
      </div>
      <div class="flex justify-center mt-[0.12rem]">
        <span class="mr-[0.2rem]">
          <img
            loading="lazy"
            class="min-w-[0.8rem] w-[0.8rem]"
            alt="image-search"
            src="@/assets/icons/inviteCouponSelf.svg"
            referrerpolicy="no-referrer"
          />
        </span>
        <div>
          <span class="text-[0.32rem] font-medium">{{
            authStore.i18n("cm_invite.selfGain")
          }}</span>
          <div class="text-[0.28rem]">
            {{ authStore.i18n("cm_invite.selfGainUseDesc") }}
          </div>
        </div>
      </div>
      <div class="flex justify-center pt-1">
        <span class="mr-[0.2rem]">
          <img
            loading="lazy"
            class="min-w-[0.8rem] w-[0.8rem]"
            alt="image-search"
            src="@/assets/icons/inviteCouponOther.svg"
            referrerpolicy="no-referrer"
          />
        </span>
        <div>
          <span class="text-[0.32rem] font-medium">{{
            authStore.i18n("cm_invite.friendGain")
          }}</span>
          <div class="text-[0.28rem]">
            {{ authStore.i18n("cm_invite.friendGainUseDesc") }}
          </div>
        </div>
      </div>
      <div
        class="invite-link bg-[#F2F2F2]"
        v-if="!pageData.verifyMailResult?.isMailVerified"
      >
        <n-grid :cols="24">
          <n-grid-item :span="3">
            <img
              loading="lazy"
              class="min-w-[0.72rem] w-[0.72rem]"
              alt="image-search"
              src="@/assets/icons/inviteWarning.svg"
              referrerpolicy="no-referrer"
            />
          </n-grid-item>
          <n-grid-item :span="20">
            {{ authStore.i18n("cm_invite.verificationMailbox") }}
          </n-grid-item>
        </n-grid>
        <div class="text-center">
          <n-button
            class="px-[0.6rem] mt-1"
            color="#E50113"
            round
            @click="onMailVerified"
            :loading="pageData.verifyLoading"
            >{{ authStore.i18n("cm_invite.viewNow") }}</n-button
          >
        </div>
      </div>
      <div v-else>
        <div class="invite-link bg-[#F2F2F2]">
          <div class="text-[0.32rem] text-gray-500">
            {{ authStore.i18n("cm_user.inviteLink") }}:
          </div>
          <div class="flex justify-between items-center">
            <div class="text-[0.28rem] text-black-800 break-all mr-[0.28rem]">
              {{ pageData.inviteLink }}
            </div>
            <n-button
              size="small"
              round
              color="#F6D2D4"
              text-color="#E50113"
              @click="onHandleCopyText(pageData.inviteLink)"
            >
              {{ authStore.i18n("cm_user.copy") }}
            </n-button>
          </div>
        </div>
        <div class="invite-link !mt-[0.24rem] bg-[#fafafa]">
          <n-flex vertical>
            <div>
              <span>
                <n-button
                  text
                  class="text-[0.28rem] !text-[#333]"
                  @click="onVerifyClick(0)"
                  ><span
                    :class="
                      pageData.verifySelect === 0
                        ? 'text-[0.32rem] font-medium underline decoration-1.5 underline-offset-6'
                        : ''
                    "
                    >{{ authStore.i18n("cm_invite.verify") }}</span
                  ></n-button
                >
              </span>
              <span class="px-1">
                <n-divider vertical />
              </span>
              <n-button
                text
                class="text-[0.28rem] !text-[#333]"
                @click="onVerifyClick(1)"
                ><span
                  :class="
                    pageData.verifySelect === 1
                      ? 'text-[0.32rem] font-medium underline decoration-1.5 underline-offset-6'
                      : ''
                  "
                  >{{ authStore.i18n("cm_invite.notVerify") }}</span
                ></n-button
              >
            </div>
            <div class="mt-1">
              <n-select
                v-model:value="pageData.selectDate"
                :options="pageData.dateOpts"
                :on-update:value="onSelectDateUpdate"
              />
            </div>
            <div>
              <n-data-table
                remote
                :columns="userCols"
                :data="pageData.inviteFriendList"
                size="small"
                :scroll-x="300"
                max-height="25vh"
                :loading="pageData.isLoading"
              ></n-data-table>
            </div>
          </n-flex>
        </div>
      </div>
    </n-flex>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";

import { useAuthStore } from "@/stores/authStore";
import { type DataTableColumns } from "naive-ui";
import { VerifyMailSceneEnum } from "@/services/common/business";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});
const containerRef = ref(null);
const isBottom = ref(false);
const userInfo = computed(() => useAuthStore().getUserInfo);
const pageData = reactive<any>({
  loading: false,
  selectDate: 0,
  verifySelect: 0,
  userInfo: {},
  datetime: onDateTimestamp(1),
  inviteFriendList: [],
  verifyMailResult: {},
  dateOpts: [
    {
      label: authStore.i18n("cm_invite.lastMonth"),
      value: 0,
    },
    {
      label: authStore.i18n("cm_invite.pastThreeMonths"),
      value: 1,
    },
    {
      label: authStore.i18n("cm_invite.pastSixMonths"),
      value: 2,
    },
    {
      label: authStore.i18n("cm_invite.withinOneYear"),
      value: 3,
    },
    {
      label: authStore.i18n("cm_invite.previous"),
      value: 4,
    },
  ],
});

const userCols: DataTableColumns<any> = [
  {
    title: authStore.i18n("cm_invite.registerTime"),
    key: "registerTime",
    width: 140,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return timezoneFormat(row.registerTime);
    },
  },
  {
    title: authStore.i18n("cm_invite.friendEmail"),
    key: "email",
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
];

const pagination = reactive({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
    onPageList();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.page = 1;
    pagination.pageSize = pageSize;
    onPageList();
  },
  pageCount: 0,
  itemCount: 0,
  prefix(item: any) {
    return `Total ${item.itemCount}`;
  },
});

onMounted(() => {
  window.addEventListener("scroll", onScrollBottom);
});

await onVerifyMailResult();
onBeforeMount(async () => {
  await Promise.all([onUserDetail(), onPageList()]);
});

function onScrollBottom() {
  if (pageData.isLoading || pageData.noData) return;
  if (window.innerHeight + window.scrollY < document.body.scrollHeight) return; // 判断是否滚动到底部
  pageData.isLoading = true;
  pagination.page++;
  onPageList("scroll");
}

// 发送验证邮箱的邮件
async function onMailVerified() {
  pageData.verifyLoading = true;
  const res: any = await useSendVerifyMail({
    verifyMailScene: "INVITE_FRIEND",
  });
  pageData.verifyLoading = false;
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.reload();
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      // 跳转邮箱
      navigateToEmail();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

async function onPageList(scroll?: any) {
  pageData.isLoading = false;
  const res: any = await useInvitedUserMailStatus({
    datetime: pageData.datetime,
    isMailVerified: pageData.verifySelect === 0,
    page: {
      current: pagination.page,
      size: pagination.pageSize,
    },
  });
  if (res?.result?.code === 200) {
    if (scroll && !res?.data?.userList?.length) {
      pageData.noData = true;
      pageData.isLoading = false;
      return;
    }
    if (scroll) {
      pageData.inviteFriendList = pageData.inviteFriendList.concat(res?.data);
    } else {
      pageData.inviteFriendList = res?.data;
      pagination.pageCount = res?.page?.pages;
      pagination.itemCount = res?.page?.total;
    }
  }
  pageData.isLoading = false;
}

async function onVerifyMailResult() {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo.value?.username,
    isNeedCoupon: false,
  });
  if (res?.result?.code === 200) {
    pageData.verifyMailResult = res?.data;
  }
}

async function onUserDetail() {
  const res: any = await useUserDetail({});
  if (res?.result?.code === 200) {
    pageData.userInfo = res?.data;
    const host = window.location.host;
    const protocol = window.location.protocol;
    pageData.inviteLink = `${protocol}//${host}/?utm_source=invite_code_${pageData.userInfo.inviteCode}`;
  } else if (res?.result?.code === 403) {
    window.location.href = `/h5/user/login?pageSource=${window.location.href}`;
  }
}

function onHandleCopyText(val: any, type?: any) {
  let eventName, remark;
  if (type === "code") {
    eventName = "copy_invite_code";
    remark = "复制邀请代码";
  } else {
    eventName = "copy_invite_link";
    remark = "复制邀请链接";
  }
  window?.MyStat?.addPageEvent(eventName, remark); // 埋点
  onCopyText(val);
}

function onSelectDateUpdate(val: any) {
  pagination.page = 1;
  pageData.selectDate = val;
  if (val === 0) {
    pageData.datetime = onDateTimestamp(1);
  } else if (val === 1) {
    pageData.datetime = onDateTimestamp(3);
  } else if (val === 2) {
    pageData.datetime = onDateTimestamp(6);
  } else if (val === 3) {
    pageData.datetime = onDateTimestamp(12);
  } else {
    pageData.datetime = 0;
  }
  onPageList();
}

function onVerifyClick(val: any) {
  if (val === 0) {
    window?.MyStat?.addPageEvent(
      "invite_select_verified_user",
      `选择已验证的邀请用户`
    ); // 埋点
  } else {
    window?.MyStat?.addPageEvent(
      "invite_select_unverified_user",
      `选择未验证的邀请用户`
    ); // 埋点
  }
  pagination.page = 1;
  pageData.verifySelect = val;

  onPageList();
}

function onDateTimestamp(val: any) {
  // 获取当前时间的日期对象
  const currentDate = new Date();

  // 获取N个月之前的日期对象，并将时间设置为当天的零点
  const sixMonthsAgo = new Date(currentDate);
  sixMonthsAgo.setMonth(currentDate.getMonth() - val);
  sixMonthsAgo.setHours(0, 0, 0, 0);

  // 获取当前时间零点的日期对象
  const currentZeroDate = new Date(currentDate);
  currentZeroDate.setHours(0, 0, 0, 0);

  // 获取六个月前零点的时间戳（单位是毫秒）
  const startTimestamp = sixMonthsAgo.getTime();
  return startTimestamp;
}

// 返回
function onBackClick() {
  router.go(-1);
}
</script>

<style lang="scss" scoped>
.mobile-container {
  padding: 1.2rem 0.16rem 0.32rem;
}
.invite-link {
  margin: 0.1rem 0;
  padding: 0.25rem;
  border-radius: 0.08rem;
}
</style>
