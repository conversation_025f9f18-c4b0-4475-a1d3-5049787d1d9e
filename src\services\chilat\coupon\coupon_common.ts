/* eslint-disable */

export const protobufPackage = "chilat.coupon";

/** 优惠券主体状态 */
export enum CouponStatus {
  /** COUPON_DRAFT - 草稿 */
  COUPON_DRAFT = 0,
  /** COUPON_NOT_ACTIVE - 卡券失效 */
  COUPON_NOT_ACTIVE = 1,
  /** COUPON_ACTIVE - 卡券生效 */
  COUPON_ACTIVE = 2,
  /** COUPON_LOCK - 卡券锁定 */
  COUPON_LOCK = 3,
  /** COUPON_UN_LOCK - 卡券解锁 */
  COUPON_UN_LOCK = 4,
  /** COUPON_EXPIRE - 卡券过期 */
  COUPON_EXPIRE = 5,
  /** COUPON_CANCEL - 卡券删除 */
  COUPON_CANCEL = 6,
  UNRECOGNIZED = -1,
}

export function couponStatusFromJSON(object: any): CouponStatus {
  switch (object) {
    case 0:
    case "COUPON_DRAFT":
      return CouponStatus.COUPON_DRAFT;
    case 1:
    case "COUPON_NOT_ACTIVE":
      return CouponStatus.COUPON_NOT_ACTIVE;
    case 2:
    case "COUPON_ACTIVE":
      return CouponStatus.COUPON_ACTIVE;
    case 3:
    case "COUPON_LOCK":
      return CouponStatus.COUPON_LOCK;
    case 4:
    case "COUPON_UN_LOCK":
      return CouponStatus.COUPON_UN_LOCK;
    case 5:
    case "COUPON_EXPIRE":
      return CouponStatus.COUPON_EXPIRE;
    case 6:
    case "COUPON_CANCEL":
      return CouponStatus.COUPON_CANCEL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponStatus.UNRECOGNIZED;
  }
}

export function couponStatusToJSON(object: CouponStatus): string {
  switch (object) {
    case CouponStatus.COUPON_DRAFT:
      return "COUPON_DRAFT";
    case CouponStatus.COUPON_NOT_ACTIVE:
      return "COUPON_NOT_ACTIVE";
    case CouponStatus.COUPON_ACTIVE:
      return "COUPON_ACTIVE";
    case CouponStatus.COUPON_LOCK:
      return "COUPON_LOCK";
    case CouponStatus.COUPON_UN_LOCK:
      return "COUPON_UN_LOCK";
    case CouponStatus.COUPON_EXPIRE:
      return "COUPON_EXPIRE";
    case CouponStatus.COUPON_CANCEL:
      return "COUPON_CANCEL";
    case CouponStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 用户单张券状态 */
export enum CouponTicketStatus {
  /** COUPON_TICKET_NORMAL - 正常 */
  COUPON_TICKET_NORMAL = 0,
  /** COUPON_TICKET_LOCK - 卡券锁定 */
  COUPON_TICKET_LOCK = 1,
  /** COUPON_TICKET_USED - 卡券使用 */
  COUPON_TICKET_USED = 2,
  /** COUPON_TICKET_EXPIRE - 卡券过期/失效 */
  COUPON_TICKET_EXPIRE = 3,
  /** COUPON_TICKET_CANCEL - 卡券作废 */
  COUPON_TICKET_CANCEL = 4,
  UNRECOGNIZED = -1,
}

export function couponTicketStatusFromJSON(object: any): CouponTicketStatus {
  switch (object) {
    case 0:
    case "COUPON_TICKET_NORMAL":
      return CouponTicketStatus.COUPON_TICKET_NORMAL;
    case 1:
    case "COUPON_TICKET_LOCK":
      return CouponTicketStatus.COUPON_TICKET_LOCK;
    case 2:
    case "COUPON_TICKET_USED":
      return CouponTicketStatus.COUPON_TICKET_USED;
    case 3:
    case "COUPON_TICKET_EXPIRE":
      return CouponTicketStatus.COUPON_TICKET_EXPIRE;
    case 4:
    case "COUPON_TICKET_CANCEL":
      return CouponTicketStatus.COUPON_TICKET_CANCEL;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponTicketStatus.UNRECOGNIZED;
  }
}

export function couponTicketStatusToJSON(object: CouponTicketStatus): string {
  switch (object) {
    case CouponTicketStatus.COUPON_TICKET_NORMAL:
      return "COUPON_TICKET_NORMAL";
    case CouponTicketStatus.COUPON_TICKET_LOCK:
      return "COUPON_TICKET_LOCK";
    case CouponTicketStatus.COUPON_TICKET_USED:
      return "COUPON_TICKET_USED";
    case CouponTicketStatus.COUPON_TICKET_EXPIRE:
      return "COUPON_TICKET_EXPIRE";
    case CouponTicketStatus.COUPON_TICKET_CANCEL:
      return "COUPON_TICKET_CANCEL";
    case CouponTicketStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 优惠券发放方式 */
export enum CouponDistributionStatus {
  /** COUPON_DISTRIBUTION_ACTIVE - 主动领取 */
  COUPON_DISTRIBUTION_ACTIVE = 0,
  /** COUPON_DISTRIBUTION_AUTOMATIC - 自动发放 */
  COUPON_DISTRIBUTION_AUTOMATIC = 1,
  /** COUPON_DISTRIBUTION_EXCHANGE - 优惠码兑换 */
  COUPON_DISTRIBUTION_EXCHANGE = 2,
  UNRECOGNIZED = -1,
}

export function couponDistributionStatusFromJSON(object: any): CouponDistributionStatus {
  switch (object) {
    case 0:
    case "COUPON_DISTRIBUTION_ACTIVE":
      return CouponDistributionStatus.COUPON_DISTRIBUTION_ACTIVE;
    case 1:
    case "COUPON_DISTRIBUTION_AUTOMATIC":
      return CouponDistributionStatus.COUPON_DISTRIBUTION_AUTOMATIC;
    case 2:
    case "COUPON_DISTRIBUTION_EXCHANGE":
      return CouponDistributionStatus.COUPON_DISTRIBUTION_EXCHANGE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponDistributionStatus.UNRECOGNIZED;
  }
}

export function couponDistributionStatusToJSON(object: CouponDistributionStatus): string {
  switch (object) {
    case CouponDistributionStatus.COUPON_DISTRIBUTION_ACTIVE:
      return "COUPON_DISTRIBUTION_ACTIVE";
    case CouponDistributionStatus.COUPON_DISTRIBUTION_AUTOMATIC:
      return "COUPON_DISTRIBUTION_AUTOMATIC";
    case CouponDistributionStatus.COUPON_DISTRIBUTION_EXCHANGE:
      return "COUPON_DISTRIBUTION_EXCHANGE";
    case CouponDistributionStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 优惠方式 */
export enum CouponWayStatus {
  /** COUPON_WAY_FULL_REDUCTION - 满减券 */
  COUPON_WAY_FULL_REDUCTION = 0,
  /** COUPON_WAY_DIRECT_REDUCTION - 直减券 */
  COUPON_WAY_DIRECT_REDUCTION = 1,
  /** COUPON_WAY_DISCOUNT - 折扣券 */
  COUPON_WAY_DISCOUNT = 2,
  UNRECOGNIZED = -1,
}

export function couponWayStatusFromJSON(object: any): CouponWayStatus {
  switch (object) {
    case 0:
    case "COUPON_WAY_FULL_REDUCTION":
      return CouponWayStatus.COUPON_WAY_FULL_REDUCTION;
    case 1:
    case "COUPON_WAY_DIRECT_REDUCTION":
      return CouponWayStatus.COUPON_WAY_DIRECT_REDUCTION;
    case 2:
    case "COUPON_WAY_DISCOUNT":
      return CouponWayStatus.COUPON_WAY_DISCOUNT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponWayStatus.UNRECOGNIZED;
  }
}

export function couponWayStatusToJSON(object: CouponWayStatus): string {
  switch (object) {
    case CouponWayStatus.COUPON_WAY_FULL_REDUCTION:
      return "COUPON_WAY_FULL_REDUCTION";
    case CouponWayStatus.COUPON_WAY_DIRECT_REDUCTION:
      return "COUPON_WAY_DIRECT_REDUCTION";
    case CouponWayStatus.COUPON_WAY_DISCOUNT:
      return "COUPON_WAY_DISCOUNT";
    case CouponWayStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 优惠券类型 */
export enum CouponTypeStatus {
  /** COUPON_TYPE_PRODUCT - 产品券 */
  COUPON_TYPE_PRODUCT = 0,
  /** COUPON_TYPE_COMMISSION - 佣金券 */
  COUPON_TYPE_COMMISSION = 1,
  UNRECOGNIZED = -1,
}

export function couponTypeStatusFromJSON(object: any): CouponTypeStatus {
  switch (object) {
    case 0:
    case "COUPON_TYPE_PRODUCT":
      return CouponTypeStatus.COUPON_TYPE_PRODUCT;
    case 1:
    case "COUPON_TYPE_COMMISSION":
      return CouponTypeStatus.COUPON_TYPE_COMMISSION;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponTypeStatus.UNRECOGNIZED;
  }
}

export function couponTypeStatusToJSON(object: CouponTypeStatus): string {
  switch (object) {
    case CouponTypeStatus.COUPON_TYPE_PRODUCT:
      return "COUPON_TYPE_PRODUCT";
    case CouponTypeStatus.COUPON_TYPE_COMMISSION:
      return "COUPON_TYPE_COMMISSION";
    case CouponTypeStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 优惠券使用规则 */
export enum CouponUseRuleStatus {
  /** COUPON_MAXIMUM_AVAILABLE - 1、本券最多可使用 */
  COUPON_MAXIMUM_AVAILABLE = 0,
  /** COUPON_SAME_TYPE_OVERLAY - 2、可与同类型叠加 */
  COUPON_SAME_TYPE_OVERLAY = 1,
  /** COUPON_FIRST_USE - 3、首单使用 */
  COUPON_FIRST_USE = 2,
  UNRECOGNIZED = -1,
}

export function couponUseRuleStatusFromJSON(object: any): CouponUseRuleStatus {
  switch (object) {
    case 0:
    case "COUPON_MAXIMUM_AVAILABLE":
      return CouponUseRuleStatus.COUPON_MAXIMUM_AVAILABLE;
    case 1:
    case "COUPON_SAME_TYPE_OVERLAY":
      return CouponUseRuleStatus.COUPON_SAME_TYPE_OVERLAY;
    case 2:
    case "COUPON_FIRST_USE":
      return CouponUseRuleStatus.COUPON_FIRST_USE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponUseRuleStatus.UNRECOGNIZED;
  }
}

export function couponUseRuleStatusToJSON(object: CouponUseRuleStatus): string {
  switch (object) {
    case CouponUseRuleStatus.COUPON_MAXIMUM_AVAILABLE:
      return "COUPON_MAXIMUM_AVAILABLE";
    case CouponUseRuleStatus.COUPON_SAME_TYPE_OVERLAY:
      return "COUPON_SAME_TYPE_OVERLAY";
    case CouponUseRuleStatus.COUPON_FIRST_USE:
      return "COUPON_FIRST_USE";
    case CouponUseRuleStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 优惠券有效期类型 */
export enum CouponEffectiveTypeStatus {
  /** DISTRIBUTION_DATE_EFFECTIVEDAYS - 1、发放日期+有效天数 */
  DISTRIBUTION_DATE_EFFECTIVEDAYS = 0,
  /** DISTRIBUTION_DATE_EFFECTIVEHOURS - 2、发放日期+有效小时 */
  DISTRIBUTION_DATE_EFFECTIVEHOURS = 1,
  /** FIXEDTIME - 3、固定时间 */
  FIXEDTIME = 2,
  UNRECOGNIZED = -1,
}

export function couponEffectiveTypeStatusFromJSON(object: any): CouponEffectiveTypeStatus {
  switch (object) {
    case 0:
    case "DISTRIBUTION_DATE_EFFECTIVEDAYS":
      return CouponEffectiveTypeStatus.DISTRIBUTION_DATE_EFFECTIVEDAYS;
    case 1:
    case "DISTRIBUTION_DATE_EFFECTIVEHOURS":
      return CouponEffectiveTypeStatus.DISTRIBUTION_DATE_EFFECTIVEHOURS;
    case 2:
    case "FIXEDTIME":
      return CouponEffectiveTypeStatus.FIXEDTIME;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponEffectiveTypeStatus.UNRECOGNIZED;
  }
}

export function couponEffectiveTypeStatusToJSON(object: CouponEffectiveTypeStatus): string {
  switch (object) {
    case CouponEffectiveTypeStatus.DISTRIBUTION_DATE_EFFECTIVEDAYS:
      return "DISTRIBUTION_DATE_EFFECTIVEDAYS";
    case CouponEffectiveTypeStatus.DISTRIBUTION_DATE_EFFECTIVEHOURS:
      return "DISTRIBUTION_DATE_EFFECTIVEHOURS";
    case CouponEffectiveTypeStatus.FIXEDTIME:
      return "FIXEDTIME";
    case CouponEffectiveTypeStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 优惠券使用条件类型 */
export enum CouponUseConditionsTypestatus {
  /** FULL - 1、满 */
  FULL = 0,
  /** EVERY_FULL - 2、每满 */
  EVERY_FULL = 1,
  /** UNLIMITED - 3、无限制 */
  UNLIMITED = 2,
  UNRECOGNIZED = -1,
}

export function couponUseConditionsTypestatusFromJSON(object: any): CouponUseConditionsTypestatus {
  switch (object) {
    case 0:
    case "FULL":
      return CouponUseConditionsTypestatus.FULL;
    case 1:
    case "EVERY_FULL":
      return CouponUseConditionsTypestatus.EVERY_FULL;
    case 2:
    case "UNLIMITED":
      return CouponUseConditionsTypestatus.UNLIMITED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CouponUseConditionsTypestatus.UNRECOGNIZED;
  }
}

export function couponUseConditionsTypestatusToJSON(object: CouponUseConditionsTypestatus): string {
  switch (object) {
    case CouponUseConditionsTypestatus.FULL:
      return "FULL";
    case CouponUseConditionsTypestatus.EVERY_FULL:
      return "EVERY_FULL";
    case CouponUseConditionsTypestatus.UNLIMITED:
      return "UNLIMITED";
    case CouponUseConditionsTypestatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}
