syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param.payment";

import "common.proto";
import "common/business.proto";
import "mall/pages/param/mall_order_pages_param.proto";

//提交支付
message SubmitPaymentParam {
  string orderNo = 10; //订单号
  string paymentId = 20; //跳转收银台时后端返回的支付单id
  double amount = 30; //支付金额
  string payMethod = 40; //支付方式code
  string userId = 50; //用户id
  string payMethodId = 60; //支付方式id
  common.PayType payType= 70; //线上 or 线下
}

//查询支付结果
message QueryPayResultParam {
  string orderNo = 10; //订单号
  string paymentId = 20; //支付单id
  string userId = 30; //用户id
}

//未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
message OpenCashDeskParam {
  string orderNo = 10; //订单号
  double amount = 20; //待支付金额
  string orderRemark = 30; //订单备注
  string  transportId = 40; //线路id
  string userId = 50; //用户id
  repeated string productIdsList = 51;//产品集合
  repeated string commissionIdsList = 52;//佣金集合
}

//线上支付第三方支付平台回调通知支付结果
message UpdateOnlinePayResultParam {
  string tradeNo = 10;
  string outTradeNo = 20;
  string appid = 30;
  string tradeStatus = 40;
  string amount = 50;
  string currency = 60;
  string timestamp = 70;
  string exception = 80;
}

message SubmitOfflinePayParam {
  string orderNo = 10; //订单号
  double amount = 30; //金额
  string payMethod = 40; //支付方式
  string routeId = 50; //线路id,没有线路的支付模式不用填
  string remark = 60; //备注
  common.PayType payType= 61; //线上 or 线下
  repeated string payOrderPicList = 70; //支付凭证url列表
  bool isFinish = 80; //是否本单支付完成
  bool isPurchase = 90; //是否采购下单
}

message QueryCancelReasonConfigParam {
  int32 cancelType = 10; //0.平台取消 or 1.用户取消
}

message GetCashDeskInfoParam {
  string orderNo = 10; //订单号
  string paymentId = 20; //支付单id
  string userId = 30; //用户id
}

message PagSmileNotifyParam {
  string signature = 10; //header 中的 Pagsmile-Signature
  string requestBody = 20; //body部分
}

message QueryPagSmilePayDetailParam {
  string orderNo = 10; //订单号
  string paymentId = 20; //支付单id
  string localTradeNo = 30; //在线支付唯一id
  bool updatePayStatus = 40; //是否更新本地支付状态
  string reqSource = 50; //请求来源
}

message GetPayingTradeListParam {
  int64 createTime = 10; //查询创建时间比指定时间早的未支付在线支付记录
}

message GetOnlineTradeListParam {
  repeated string orderNoList = 10; //订单列表
}