syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

// SKU列表参数
message GoodsSkuPageQueryParam {
    common.PageParam page = 1;
    GoodsSkuQueryParam query = 2;
}

// SKU列表查询参数
message GoodsSkuQueryParam {
    bool isOnline = 1; // 是否上架（必填）
    string skuNo = 2; // SKU货号
    string goodsNo = 3; // 商品编码
    string goodsName = 4; // 商品名称
    double minSalePrice = 5; //最低销售价（即单箱价格）
    double maxSalePrice = 6; //最高销售价（即单箱价格）
    int32 minStockQty = 61; //最低库存数（可用库存数，包含）
    int32 maxStockQty = 62; //最高库存数（可用库存数，包含）
    string categoryId = 8; // 分类ID
}

message ChangeSkuOnlineStateParam {
    bool isOnline = 1; // 是否上架（必填）
    repeated string skuIds = 2; // SKU ID列表
}


message OrderSkuQueryParam {
    string keyword = 1;//skuNo or GoodsNo
}
