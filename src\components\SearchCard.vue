<template>
  <div class="m-auto bg-white" :style="{ width: searchWidth }">
    <div class="header-tip">
      {{ authStore.i18n("cm_common.tip") }}
    </div>
    <div class="w-full flex items-center justify-between py-4">
      <a href="/" data-spm-box="navigation-logo-icon"
        ><n-image
          object-fit="fill"
          preview-disabled
          :src="pageTheme.logo"
          class="w-[200px]"
          :img-props="{ referrerpolicy: 'no-referrer' }"
      /></a>
      <text-image-search></text-image-search>
      <div class="mr-6 w-[70px]" v-if="!isHideCountrySelect">
        <country-select
          mode="popover"
          @save="onSaveCountry"
          spm="select_site_from_nav"
        />
      </div>
      <div class="flex items-center">
        <!-- 未登录显示注册登录 已登录显示个人中心 -->
        <div>
          <div
            v-if="userInfo?.username"
            class="text-[13px] mr-4 cursor-pointer flex items-center justify-end"
          >
            <div
              @click="onGoAccount($event)"
              data-spm-box="navigation-top-myhome"
              class="flex items-center cursor-pointer"
            >
              <span
                class="inline-block w-[24px] h-[24px] rounded-full bg-[#e50113] text-[#fff] flex justify-center items-center"
                >{{ userInfo?.username.charAt(0) }}</span
              >
              <span class="ml-2">{{
                authStore.i18n("cm_common_myAccount")
              }}</span>
            </div>
            <span
              @click="onLogoutClick($event)"
              data-spm-box="navigation-top-logout"
              class="border-l-1 border-[#96979b] ml-3 pl-3 cursor-pointer leading-4"
              >{{ authStore.i18n("cm_common_loginOut") }}</span
            >
          </div>
          <div
            v-else
            class="flex justify-end items-center cursor-pointer text-[13px]"
          >
            <icon-card
              size="29"
              class="mr-1"
              name="ph:user"
              color="grey"
            ></icon-card>

            <div @click="onLoginClick(1)" class="hover:underline">
              {{ authStore.i18n("cm_common_login") }}
            </div>
            <span class="mx-1">/</span>
            <div
              @click="onLoginClick(0)"
              class="text-[#e50113] hover:underline"
            >
              {{ authStore.i18n("cm_common_register") }}
            </div>
          </div>
        </div>
        <div
          class="flex justify-center items-center ml-4 mr-2 cursor-pointer"
          data-spm-box="navigation-cart-icon"
          @click="onFindListClick($event)"
        >
          <img
            alt="cart"
            loading="lazy"
            src="@/assets/icons/common/cart.svg"
            class="w-[28px] h-[28px] mr-[4px]"
          />
          <div class="mr-1 text-[13px]">
            {{ authStore.i18n("cm_find_cart") }}
          </div>
          <div v-if="cartGoodsCount" class="text-[#E50113] text-[13px]">
            ({{ cartGoodsCount }})
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import { h } from "vue";

const props = defineProps({
  searchWidth: {
    type: String,
    default: "1280px",
  },
});

const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const loginRegister = inject<any>("loginRegister");
const pageTheme = computed(() => useConfigStore().getPageTheme);
const cartGoodsCount = computed(() => {
  const count = authStore.cartTotalCount;
  return count && count > 99 ? "99+" : count;
});

const userInfo = ref<any>({});
userInfo.value = config.public.userInfo;

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};

const isHideCountrySelect = computed(() => {
  return normalizePath(route.path) === "/find/submit";
});

function onFindListClick(event: any) {
  // 未登录 打开登录弹窗
  if (isEmptyObject(userInfo.value)) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_cart",
      "点击顶部导航购物车，打开账号窗口"
    ); // 埋点
    loginRegister?.openLogin();
  } else {
    navigateToPage("/find", {}, true, event);
  }
}

const onLoginClick = async (type: any) => {
  // 注册
  if (type === 0) {
    window?.MyStat?.addPageEvent(
      "passport_open_nav_register",
      "点击顶部导航注册，打开注册窗口"
    );
  } else {
    //登录
    window?.MyStat?.addPageEvent(
      "passport_open_nav_login",
      "点击顶部导航登录，打开登录窗口"
    );
  }
  loginRegister?.openLogin("", type);
};

async function onLogoutClick(event: any) {
  try {
    const res: any = await useLogout({});
    if (res?.result?.code === 200) {
      authStore.setUserInfo({});
      navigateToPage(`/`, {}, false, event);
    } else {
      showToast(res?.result?.message);
    }
  } catch (error) {
    showToast(error);
  }
}

function onGoAccount(event: any) {
  navigateToPage(`/user/account`, {}, true, event);
}
</script>
<style scoped lang="scss">
.n-divider {
  margin: 0px;
}
.header-tip {
  width: 100%;
  margin: 0 auto;
  height: 50px;
  font-size: 15px;
  line-height: 50px;
  color: #e50113;
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  text-align: center;
}

:deep(.country-delivery) {
  font-size: 12px !important;
  margin-right: 10px;
}
:deep(.country-code) {
  font-size: 14px !important;
}
</style>
