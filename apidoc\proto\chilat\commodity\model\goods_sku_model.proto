syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";
import "chilat/commodity/param/goods_sku_param.proto";
import "chilat/basis/model/sales_order_model.proto";

message GoodsSkuPageData {
    common.Page page = 1;
    repeated GoodsSkuListDataModel data = 2; // 商品列表数据
    int32 onlineCount = 3; // 上架商品数
    int32 offlineCount = 4; // 下架商品数
}

message GoodsSkuPageResp {
    common.Result result = 1;
    GoodsSkuPageData data = 2;
}

// 商品SKU信息
message GoodsSkuListDataModel {
    string id = 10; //SKU ID
    string mainImageUrl = 20; //商品主图
    string skuNo = 30; // SKU商品货号
    repeated GoodsExtendItem specList = 40; // 规格信息
    double salePrice = 65; //销售价（即单箱价格）
    int32 stockQuantity = 70; //可用库存数
    string goodsNo = 80; // 商品编号
    string goodsName = 90; // 商品名称
    bool isOnline = 100; // 是否上架(skuState）
    string categoryName = 110; //商品分类名称
    double insideOnePrice = 120; //单件价格
}

message ChangeSkuOnlineStateResp {
    common.Result result = 1;
    ChangeSkuOnlineStateModel data = 2;
}
message ChangeSkuOnlineStateModel {
    bool successNotNeedConfirm = 1; //更新成功，不需要再次确认（false表示需要弹出确认框）
    repeated ChangeSkuOnlineFailInfoModel failSkuList = 2; //操作失败的SKU列表
    ChangeSkuOnlineStateParam confirmParam = 3; //确认后，再次提交需要使用的参数
}

message ChangeSkuOnlineFailInfoModel {
    string id = 10; //SKU ID
    string skuNo = 20; //SKU编码
    string goodsName = 30 ; //商品名称
    string imageUrl = 40; //商品图片
}



message OrderSkuListModel {
    string id = 1; //SKU ID
    string imageUrl = 2; //商品图
    string skuNo = 3; // SKU商品货号
    repeated basis.SpecInfoModel specInfo = 4; //商品规格
    double salePrice = 5; //销售价（价格单位：USD）
    string goodsNo = 6; // 商品编号
    bool isOnline = 8; // 是否上架(skuState）
}


message OrderSkuQueryResp {
    common.Result result = 1;
    repeated OrderSkuListModel data = 2;
}

