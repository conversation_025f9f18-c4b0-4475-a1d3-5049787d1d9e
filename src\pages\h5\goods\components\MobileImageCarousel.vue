<template>
  <div
    class="list-wrapper relative pr-[0.4rem] pl-[0.1rem] border-b-1 border-solid border-gray-200 flex items-center"
    ref="listWrapper"
  >
    <n-carousel
      keyboard
      :touchable="showArrow ? true : false"
      class="static"
      :show-arrow="showArrow"
      :show-dots="false"
      slides-per-view="auto"
      :space-between="9"
      :loop="false"
      direction="horizontal"
      :class="{ carouselOne: !!(props?.images && props?.images.length === 1) }"
    >
      <n-carousel-item v-for="(image, index) in props?.images" :key="index">
        <div class="relative">
          <n-image
            lazy
            preview-disabled
            object-fit="fill"
            :style="{
              width: props.imgWidth,
              height: props.imgHeight,
            }"
            :src="image as string"
            @click="onImageSelect(index)"
            class="container my-[0.08rem]"
            :class="{ selected: selectedNum === index }"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          >
          </n-image>
          <icon-card
            size="26"
            color="#F2F2F2"
            name="solar:play-bold"
            class="absolute"
            style="top: 50%; left: 50%; transform: translate(-50%, -50%)"
            v-if="props.showVideoIcon && index == 0"
          />
        </div>
      </n-carousel-item>
      <template #arrow="{ next }">
        <div class="custom-arrow">
          <div class="arrow-icon arrow--right">
            <icon-card
              color="#AAA"
              name="ri:arrow-right-s-line"
              size="30"
              @click="next"
            />
          </div>
        </div>
      </template>
    </n-carousel>
  </div>
</template>

<script setup lang="ts" name="MobileImageCarousel">
const emit = defineEmits(["selectEvent"]);
const listWrapper = ref<HTMLElement | undefined>(undefined);
const props = defineProps({
  images: {
    type: Array,
    default: () => [],
  },
  imgWidth: {
    type: String,
    default: "1.12rem",
  },
  imgHeight: {
    type: String,
    default: "1.12rem",
  },
  imgSelected: {
    type: Number,
    default: 0,
  },
  showVideoIcon: {
    type: Boolean,
    default: false,
  },
});
const selectedNum = ref(props.imgSelected);

onMounted(() => {});

watch(
  () => props.imgSelected,
  (newVal: any) => {
    selectedNum.value = newVal;
  }
);

// 超出一屏显示按钮
const showArrow = computed(() => {
  return (
    props?.images?.length * (parseInt(props.imgWidth) * 50 + 10) >
    (listWrapper?.value?.offsetWidth ?? 0) - 20
  );
});

function onImageSelect(index: number) {
  selectedNum.value = index;
  emit("selectEvent", index);
}
</script>
<style scoped lang="scss">
.custom-arrow {
  .arrow-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 9;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #f7f7f7;
    width: 0.4rem;
    height: 1.12rem;
  }
  .arrow--right {
    right: -0.06rem;
  }
}
:deep(.carouselOne .n-carousel__slides) {
  transform: none !important;
}
:deep(.n-carousel__slide) {
  width: auto !important;
  height: auto !important;
}
.list-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 0.4rem;
}

:deep(.n-image) {
  background: "#F7F7F7";
  cursor: pointer;
}
.container {
  object-fit: cover; /* 指定图片如何适应容器，cover表示保持纵横比并填充整个容器 */
}

.container:hover {
  box-shadow: inset 0 0 0 0.48rem #c3c4c5;
}

.selected {
  border: 0.02rem solid red !important;
}
</style>
