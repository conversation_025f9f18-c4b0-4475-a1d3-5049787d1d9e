<template>
  <div class="page-wrapper">
    <div class="bg-white">
      <search-card></search-card>
    </div>
    <div class="page-content">
      <!-- 信息提示 -->
      <n-card class="px-[328px] py-[100px] my-4 min-h-[80vh]" :bordered="false">
        <!-- 支付成功 -->
        <template v-if="pageData.payResult.payStatus === 'PAID_SUCCESS'">
          <!-- 产品成本支付成功 || 订单费用支付成功 -->
          <template
            v-if="
              pageData.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT' ||
              pageData.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE'
            "
          >
            <div class="text-center">
              <img
                loading="lazy"
                alt="paySuccess"
                src="@/assets/icons/order/paySuccess.svg"
                class="w-[64px] h-[64px] mx-auto mb-[24px]"
                referrerpolicy="no-referrer"
              />
              <span
                class="font-medium text-[26px] leading-[26px] text-[#000]"
                >{{ authStore.i18n("cm_order.paySuccess") }}</span
              >
            </div>
            <div
              class="mt-[16px] pb-[30px] mb-[24px] text-[16px] leading-[20px] text-[##939393] border-b-1 border-[#BBB]"
            >
              <div class="text-center px-[24px]">
                {{ authStore.i18n("cm_order.payOrderNo") }}
                {{ pageData.orderNo }},
                <span
                  v-if="pageData.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT'"
                  >{{ authStore.i18n("cm_order.payProductCost") }}</span
                >
                <span
                  v-if="pageData.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE'"
                  >{{ authStore.i18n("cm_order.payOrderCost") }}</span
                >
              </div>
            </div>
            <div class="w-full flex justify-between">
              <n-button
                @click="goOrderDetails"
                data-spm-box="pay-result-go-detail"
                class="w-[274px] h-[42px] rounded-[200px]"
              >
                <span class="text-[22px] leading-[22px]">{{
                  authStore.i18n("cm_order.viewOrderDetails")
                }}</span>
              </n-button>
              <n-button
                @click="goHome"
                color="#E50113"
                class="w-[274px] h-[42px] rounded-[200px]"
              >
                <span class="text-[22px] leading-[22px]">{{
                  authStore.i18n("cm_order.keepShopping")
                }}</span>
              </n-button>
            </div>
          </template>
          <!-- 国际费用支付成功 -->
          <template
            v-if="pageData.mallOrderStatus === 'MALL_WAIT_PAY_INTER_FEE'"
          >
            <div class="text-center">
              <img
                loading="lazy"
                alt="paySuccess"
                src="@/assets/icons/order/paySuccess.svg"
                class="w-[64px] h-[64px] mx-auto mb-[24px]"
                referrerpolicy="no-referrer"
              />
              <span
                class="font-medium text-[26px] leading-[26px] text-[#000]"
                >{{ authStore.i18n("cm_order.paySuccess") }}</span
              >
            </div>
            <div
              class="mt-[16px] pb-[30px] mb-[24px] text-[16px] leading-[20px] text-[##939393] border-b-1 border-[#BBB]"
            >
              <div class="text-center px-[24px]">
                {{ authStore.i18n("cm_order.payOrderNo") }}
                {{ pageData.orderNo }},
                {{ authStore.i18n("cm_order.patientlyTransportation") }}
              </div>
            </div>
            <div class="w-full text-center">
              <n-button
                color="#E50113"
                @click="goOrderDetails"
                data-spm-box="pay-result-go-detail"
                class="w-[274px] h-[42px] rounded-[200px]"
              >
                <span class="text-[22px] leading-[22px]">{{
                  authStore.i18n("cm_order.viewOrderDetails")
                }}</span>
              </n-button>
            </div>
          </template>
        </template>

        <!-- 支付中-->
        <template v-if="pageData.payResult.payStatus === 'PAYING'">
          <div
            class="flex flex-col items-center justify-center text-center mb-[24px] pb-[30px] border-b-1 border-[#BBB]"
          >
            <n-spin
              :size="60"
              stroke="#e50113"
              class="w-[64px] h-[64px] mx-auto mb-[24px]"
            />
            <span class="font-medium text-[24px] leading-[28px] text-[#000]">{{
              authStore.i18n("cm_order.payPending")
            }}</span>
          </div>
          <div class="w-full flex justify-between">
            <n-button
              @click="goOrderDetails"
              data-spm-box="pay-result-go-detail"
              class="w-[274px] h-[42px] rounded-[200px]"
            >
              <span class="text-[22px] leading-[22px]">{{
                authStore.i18n("cm_order.viewOrderDetails")
              }}</span>
            </n-button>

            <n-button
              color="#E50113"
              @click="goOrderPay"
              data-spm-box="pay-result-buy-again"
              class="w-[274px] h-[42px] rounded-[200px]"
            >
              <span class="text-[22px] leading-[22px]">{{
                authStore.i18n("cm_order.orderPayAgain")
              }}</span>
            </n-button>
          </div>
        </template>

        <!-- 支付失败 -->
        <template v-if="pageData.payResult.payStatus === 'PAID_FAIL'">
          <div class="text-center">
            <img
              loading="lazy"
              alt="payError"
              src="@/assets/icons/order/payError.svg"
              class="w-[64px] h-[64px] mx-auto mb-[24px]"
              referrerpolicy="no-referrer"
            />
            <span class="font-medium text-[26px] leading-[26px] text-[#000]">{{
              authStore.i18n("cm_order.payFailure")
            }}</span>
          </div>
          <div
            class="mt-[16px] pb-[30px] mb-[24px] text-[16px] leading-[20px] text-[##939393] border-b-1 border-[#BBB]"
          >
            <div class="text-center px-[24px]">
              {{ pageData.payResult.errorMsg }}
            </div>
          </div>
          <div class="w-full flex justify-between">
            <n-button
              @click="goOrderDetails"
              data-spm-box="pay-result-go-detail"
              class="w-[274px] h-[42px] rounded-[200px]"
            >
              <span class="text-[22px] leading-[22px]">{{
                authStore.i18n("cm_order.viewOrderDetails")
              }}</span>
            </n-button>
            <n-button
              color="#E50113"
              @click="goOrderPay"
              data-spm-box="pay-result-buy-again"
              class="w-[274px] h-[42px] rounded-[200px]"
            >
              <span class="text-[22px] leading-[22px]">{{
                authStore.i18n("cm_order.orderPayAgain")
              }}</span>
            </n-button>
          </div>
        </template>
      </n-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();

const pageData = reactive({
  orderNo: route?.query?.orderNo || "",
  paymentId: route?.query?.paymentId || "",
  payResult: <any>{}, // 支付结果 返回支付状态 失败原因
  mallOrderStatus: "", // 订单状态
  intervalId: null as any,
});

// 查询支付结果
getQueryPayResult();
async function getQueryPayResult() {
  const res: any = await useQueryPayResult({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
  });

  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    // 未支付 跳转收银台
    if (pageData.payResult.payStatus === "INIT") {
      navigateTo(
        `/order/payment?orderNo=${pageData.orderNo}&paymentId=${pageData.paymentId}`
      );
    }
    // "支付中"时,定时查询支付结果
    if (pageData.payResult.payStatus === "PAYING") {
      if (pageData.intervalId === null) {
        pageData.intervalId = setInterval(() => {
          getQueryPayResult();
        }, 3000); // 每3秒查询一次
      }
    } else {
      // 非"支付中"，停止定时查询
      if (pageData.intervalId !== null) {
        clearInterval(pageData.intervalId);
        pageData.intervalId = null;
      }
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res.result?.message);
  }
}

onBeforeUnmount(() => {
  if (pageData.intervalId !== null) {
    clearInterval(pageData.intervalId);
  }
});

function goHome() {
  window.location.replace("/");
}

function goOrderDetails(event: any) {
  const spm = window.MyStat.getPageSPM(event);
  window.location.replace(
    `/order/details?orderNo=${pageData.orderNo}&spm=${spm}`
  );
}

function goOrderPay(event: any) {
  const spm = window.MyStat.getPageSPM(event);
  window.location.replace(
    `/order/payment?orderNo=${pageData.orderNo}&paymentId=${pageData.paymentId}&spm=${spm}`
  );
}
</script>
<style scoped lang="scss">
.page-wrapper {
  width: 100%;
  color: #222;
  min-height: 100vh;
  background-color: #f2f2f2;
}

.page-content {
  width: 1280px;
  margin: 0 auto;
  padding-bottom: 40px;
}
:deep(.n-card__content) {
  padding: 0;
}
</style>
