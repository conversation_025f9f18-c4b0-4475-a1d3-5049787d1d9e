syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "chilat/commodity/commodity_common.proto";
import "common.proto";

message GoodsAuditPageResp {
    common.Result result = 1;
    GoodsAuditDataModel data = 2;
}

message AuditGoodsPageResp {
    common.Result result = 1;
    AuditGoodsDataModel data = 2;
}

message AuditBatchPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated AuditBatchModel data = 3;
}

message ShareLinkPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated ShareLinkModel data = 3;
}

message ShareLinkResp {
    common.Result result = 1;
    ShareLinkModel data = 2;
}

message LeaderListResp {
    common.Result result = 1;
    repeated LeaderModel data = 2;
}

message GoodsAuditDataModel {
    common.Page page = 1;
    repeated GoodsAuditModel data = 2; // 商品列表数据
    int32 toAssignCount = 3; // 待分配数量
    int32 toAuditCount = 4; // 待校验数量
    int32 auditingCount = 5; // 校验中数量
    int32 finishCount = 6; // 校验完成数量
}

message GoodsAuditModel {
    string id = 1;
    string batchNo = 2; // 批次号
    string bucketNo = 3; // 桶号
    GoodsAuditState auditState = 4; // 校验状态
    string auditStateDesc = 5; // 校验状态描述
    int32 auditCount = 6; // 已校验商品数
    int32 totalCount = 7; // 商品总数
    int32 illegalCount = 8; // 侵权商品数
    string leaderId = 9; // 负责人ID
    string leaderName = 10; // 负责人姓名
    string categories = 11; // 关联分类
    int64 cdate = 12; // 生成时间
    int64 finishTime = 13; // 完成时间
    string claimerName = 14; // 领取人
}

message AuditGoodsDataModel {
    common.Page page = 1;
    repeated AuditGoodsModel data = 2; // 审核商品数据
    int32 toAuditCount = 3; // 待校验数量
    int32 finishCount = 4; // 校验完成数量
    int32 illegalCount = 5; // 侵权商品数
}

message AuditGoodsModel {
    string id = 1;
    string goodsImage = 2; // 商品图片
    string goodsNo = 3; // 商品编号
    bool isAudit = 4; // 是否校验
    bool isIllegal = 5; // 是否侵权
    string goodsName = 6; // 商品名称
    string categoryId = 7; // 分类ID
    string categoryName = 8; // 分类名称
    string auditorName = 9; // 审核人
    GoodsAuditModel goodsAudit = 10; // 审核任务
}

message AuditBatchModel {
    string id = 1;
    string batchNo = 2; // 批次号
    int32 bucketCount = 3; // 桶数
    int32 goodsCount = 4; // 商品数
    bool hasLeaders = 5; // 是否设置负责人
}

message ShareLinkModel {
    string id = 1;
    bool enabled = 2; // 是否有效
    int32 visitCount = 3; // 访问次数
    repeated string batchNos = 4; // 批次号
    int32 bucketCount = 5; // 桶数
    int32 goodsCount = 6; // 商品数
    string visitPath = 7; // 分享路径
    string visitCode = 8; // 提取码
    string coperator = 9; // 分享人
    int64 cdate = 10; // 分享时间
}

message LeaderModel {
    string userId = 1; // 用户ID
    string username = 2; // 用户名
    int32 toAuditCount = 3; // 待校验数量
    int32 auditingCount = 4; // 校验中数量
    int32 finishCount = 5; // 校验完成数量
}