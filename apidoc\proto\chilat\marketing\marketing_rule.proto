syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing";

import "chilat/marketing/param/marketing_param.proto";
import "chilat/marketing/model/marketing_model.proto";
import "common.proto";

// 营销规则
service MarketingRule {
  // 查询列表
  rpc pageList (MarketingRulePageQueryParam) returns (MarketingRulePageResp);
  // 保存营销规则
  rpc saveMarketingRule (MarketingRuleSaveParam) returns (common.ApiResult);
  // 查询所有营销规则
  rpc listAll (common.EmptyParam) returns (MarketingRuleListResp);
  // 查询详情
  rpc detail (common.IdParam) returns (MarketingRuleResp);
  // 更新商品数量（若传空值，则更新全部）
  rpc updateGoodsCount (common.IdsParam) returns (common.MapResult);
  // 查询日志
  rpc listLog (common.IdParam) returns (common.LogResult);
  // 移除营销规则和分类的关联关系
  rpc removeCategory(MarketingRuleRemoveCategoryParam) returns (common.ApiResult);
}

