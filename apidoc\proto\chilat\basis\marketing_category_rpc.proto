syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/marketing_category_model.proto";
import "common.proto";

service MarketingCategoryRpc {
  // 获取商品分类树
  rpc getMarketingCategoryTree (common.IdParam) returns (MidMarketingCategoryTreeResp);
  // 获取商品分类信息（含禁用）
  rpc getMarketingCategoryInfo (common.IdParam) returns (MidMarketingCategoryInfoResp);

}
