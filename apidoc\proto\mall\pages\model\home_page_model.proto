syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";

import "common.proto";
import "common/business.proto";
import "chilat/basis/model/config_model.proto";
import "chilat/basis/model/cart_model.proto";
import "chilat/basis/model/goods_model.proto";
import "chilat/basis/model/mall_config_model.proto";
import "mall/commodity/model/goods_info_model.proto";
import "mall/pages/model/goods_list_page_model.proto";


message HomePageResp {
    common.Result result = 1;
    HomePageModel data = 2;
}

message HomePageModel {
    chilat.basis.ConfigModel globalConfig = 10; //商城全局配置（当前语言，翻译信息等）
    //passport.LoginUserModel loginUser = 20; //登录用户信息（空值表示未登录）
    common.SeoData seoData = 30; //SEO设置信息
    chilat.basis.MallCategoryTreeModel categoryTree = 80; //类目树
    repeated string hotKeywords = 90; //热搜词
    GoodsListDataModel newestGoodsData = 100; //最新商品数据（第1页数据，分页信息在page属性中）
    string newestGoodsRuleCode = 110; //查询最新商品的营销规则代码（调用GoodsListPage.searchGoods查询第2页及之后的页码）
    repeated string recommendedGoods = 120; //推荐商品
}

message RecommendGoodsV2Resp {
    common.Result result = 1;
    RecommendGoodsV2Model data = 2;
}

message RecommendGoodsV2Model {
    //repeated commodity.GoodsListDataItemModel recommendGoods = 1; //今日特价（待移除对象）
    repeated chilat.basis.MidActivityGoodsModel topRecommendActivities = 100; //头部推荐活动列表
    commodity.RecommendGoodsResultModel recommendPackingGoods = 110; //今日特价（含tagId返回）
    commodity.RecommendGoodsResultModel recommendHotGoods = 120; //畅销商品推荐（含tagId返回）
    commodity.RecommendGoodsResultModel recommendSupplierGoods = 125; //“优质供应商”商品推荐（含tagId返回）
    repeated chilat.basis.HotSaleGoodsModel mercadoHotSaleGoods = 2; //美客多热销
    repeated chilat.basis.HotSaleGoodsModel yiwuHotSaleGoods = 3; //义乌热销
    int32 yiwuTotalCount = 4;
    chilat.basis.MidSearchGoodsModel hotSaleGoods = 5;
    chilat.basis.HotSaleGoodsModel h5MercadoHotSaleGoods = 6; //h5美客多热销
    chilat.basis.HotSaleGoodsModel h5YiwuHotSaleGoods = 7; //h5义乌热销
    chilat.basis.HotSaleGoodsModel h5BackSchoolHotSaleGoods = 10; //开学季热销
    chilat.basis.HotSaleGoodsModel h5CameraHotSaleGoods = 20; //摄像头热销
    chilat.basis.HotSaleGoodsModel h5HumidifierHotSaleGoods = 30; //反重力加湿器热销
    repeated chilat.basis.PcTagPageGoodsModel tagGoodsList = 40; //目前包含pc开学季热销,没有系统内的类目id,有tagid,其他跟PcHomePageGoodsModel一样
    repeated chilat.basis.MidActivityGoodsModel customerExclusiveActivities = 50; //客户专属活动列表（无专属活动，值为null；可有多个专属活动）
    map<string, commodity.RecommendGoodsResultModel> goodsPlugTagMap = 60; //商品补充标签对象（KEY为tagId；若找不到商品，也会返回KEY）
}

