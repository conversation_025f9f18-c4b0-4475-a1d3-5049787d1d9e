syntax = "proto3";
package chilat.support;

option java_package = "com.chilat.rpc.support";

import "common.proto";
import "chilat/support/param/article_category_param.proto";
import "chilat/support/model/article_category_model.proto";

// 文章栏目
service ArticleCategory {
  // 文章栏目树
  rpc articleCategoryTree(common.EmptyParam) returns (ArticleCategoryTreeResp);
  // 保存文章栏目树
  rpc save(ArticleCategorySaveParam) returns (common.ApiResult);
  // 删除文章目录树
  rpc remove(common.IdParam) returns (common.ApiResult);
  // 修改排序
  rpc updateIdx(BatchUpdateIdxParam) returns (common.ApiResult);
  // 保存文章
  rpc saveArticle(ArticleSaveParam) returns (common.ApiResult);
  // 删除文章
  rpc removeArticle(common.IdsParam) returns (common.ApiResult);
  // 查询文章列表
  rpc articlePageList(ArticlePageQueryParam) returns (ArticlePageListResp);
}