syntax = "proto3";
package mall.passport;

option java_package = "com.chilat.rpc.passport.param";
import "common/business.proto";
import "common.proto";

message UpdatePasswordParam {
  string oldPassword = 1;
  string newPassword = 2;
}

message SaveUserAddressParam {
  string id = 1;
  string countryId = 2; //国家
  string provinceCode = 3; //省
  string cityCode = 4; //市
  string regionCode = 5; //区
  string address = 6; //详细地址
  string houseNo = 7; //门牌号
  string postcode = 8; //邮编
  string contactName = 9; //联系人
  string phone = 10; //手机号
  bool isDefault = 11; //是否是默认地址
  string referLandmark = 12; //参考地标
  common.AddressLabel addressLabel = 13; //地址标签
  string street = 14;
  string province = 15; //省名称
  string city = 16; //市名称
  string region = 17; //县名称
}

message SendVerifyMailParam {
  common.VerifyMailSceneEnum verifyMailScene = 10;
}

message QueryVerifyMailResultParam {
  string email = 10;
  common.VerifyMailSceneEnum verifyMailScene = 20;
}

message InvitedUserMailStatusParam {
  common.PageParam page = 10;//分页信息
  bool isMailVerified = 20; //是否已验证
  int64 datetime = 30; //筛选该时间点后注册的用户
}

message queryVerifyMailResultParam {
  string email = 10;
  bool isNeedCoupon = 20;
  common.VerifyMailSceneEnum verifyMailScene=30;
}