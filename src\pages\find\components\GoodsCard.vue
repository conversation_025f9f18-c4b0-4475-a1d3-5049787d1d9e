<template>
  <div
    :data-spm-box="props.spmCode"
    :data-spm-index="props.spmIndex + 1"
    @click="goToGoodsDetail($event)"
  >
    <div class="flex">
      <n-image
        lazy
        preview-disabled
        object-fit="fill"
        class="w-[100px] h-[100px] shrink-0 mr-3 cursor-pointer"
        :src="goods.mainImageUrl"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
      <div>
        <n-ellipsis
          :line-clamp="2"
          class="w-full h-12 text-base hover:underline cursor-pointer"
          :tooltip="false"
        >
          {{ goods.goodsName }}
        </n-ellipsis>
        <div class="flex gap-[12px]">
          <div class="text-gray-500">Código: {{ goods.goodsNo }}</div>
          <div
            v-if="goods.paName"
            class="flex items-center cursor-pointer"
            @click.stop="goToGoodsList($event)"
          >
            <div
              class="h-[17px] text-[12px] leading-[17px] border border-[#FF4056] rounded-tl-[4px] rounded-bl-[4px] border-r-0 text-[#FF4056] pl-[4px] pr-[2px]"
            >
              {{ goods.paName }}
            </div>

            <img
              src="@/assets/icons/common/tag.svg"
              :alt="goods.paName"
              class="h-[17px]"
              referrerpolicy="no-referrer"
            />
          </div>
        </div>
        <div
          v-if="props.from === 'submit' && goods.selectedGoodsQty"
          class="text-gray-500"
        >
          {{ authStore.i18n("cm_goods.orderQuantity") }}
          {{ goods.selectedGoodsQty }}
          {{ goods.goodsPriceUnitName }}
        </div>
        <div class="flex" v-else>
          <div class="text-gray-500 shrink-0">
            {{ authStore.i18n("cm_goods.minOrder") }}:
            {{ goods.minBuyQuantity }}
            {{ goods.goodsPriceUnitName }}
          </div>
          <div
            v-if="
              goods.skuSelected &&
              goods.skuSelectedQuantity < goods.minBuyQuantity
            "
            class="bg-[#FFF1F1] px-2 py-1 ml-2 flex items-center"
          >
            <icon-card
              size="20"
              name="mingcute:warning-line"
              color="#E52828"
              class="mr-2"
            ></icon-card>
            {{ authStore.i18n("cm_find.minBuyQuantity") }}
            {{ goods.minBuyQuantity }}
          </div>
        </div>
        <!-- 预估运费 -->
        <div class="flex text-gray-500">
          <div v-if="goods.supportOnlineOrder">
            <div v-if="goods.estimateFreight">
              {{ authStore.i18n("cm_goods.finalShippingCost") }}:
              <span>{{ setUnit(goods.estimateFreight) }}</span>
              <span class="ml-[4px]">
                {{ authStore.i18n("cm_goods.perUnit") }}
                {{ goods.selectedGoodsQty }}
                {{ goods?.goodsPriceUnitName }}
              </span>
            </div>
          </div>
          <div class="flex gap-[4px]" v-else>
            {{ authStore.i18n("cm_goods.shippingCost") }}:
            <div v-if="goods.estimateFreight">
              <span>{{ setUnit(goods.estimateFreight) }}</span>
              <span class="ml-[4px]">
                {{ authStore.i18n("cm_goods.perUnit") }}
                {{ goods.selectedGoodsQty }}
                {{ goods?.goodsPriceUnitName }}
              </span>
            </div>
            <n-popover trigger="hover" raw v-else>
              <template #trigger>
                <div class="cursor-pointer">
                  <span>{{
                    authStore.i18n("cm_goods.pendingConfirmation")
                  }}</span>
                  <icon-card
                    size="16"
                    color="#F7BA2A"
                    class="ml-[2px]"
                    name="mingcute:warning-line"
                  ></icon-card>
                </div>
              </template>
              <div
                style="
                  z-index: 1;
                  padding: 6px 14px;
                  background-color: #fff4d4;
                  transform-origin: inherit;
                  border: 1px solid #f7ba2a;
                "
              >
                {{ authStore.i18n("cm_goods.freightConfirmation") }}
              </div>
            </n-popover>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="GoodsCard">
import { goodsDetailPCPath } from "@/utils/constant";
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  spmCode: {
    type: String,
    default: () => {},
  },
  spmIndex: {
    type: Number,
    default: 0,
  },
  from: {
    type: String,
    default: "list",
  },
});

function goToGoodsDetail(event: Event) {
  const { goodsId, padc } = props.goods;
  const url = `${goodsDetailPCPath}/${goodsId}`;
  const params = padc ? { padc } : {};

  navigateToPage(url, params, true, event);
}

function goToGoodsList(event: Event) {
  if (props.goods.padc) {
    navigateToPage(`/goods/list/all`, { padc: props.goods.padc }, true, event);
  }
}
</script>

<style scoped lang="scss"></style>
