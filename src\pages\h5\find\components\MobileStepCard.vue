<template>
  <div class="pb-[0.16rem] px-[0.24rem] relative">
    <div
      ref="containerRef"
      class="step-progress-container overflow-x-auto overflow-y-hidden"
      @scroll="handleScroll"
    >
      <div ref="stepProgressRef" class="step-progress-wrapper flex">
        <div class="step-progress w-[5.56rem] relative">
          <div class="step-progress-line"></div>

          <div
            class="step-progress-line completed"
            :style="{ width: activeLineWidth }"
          ></div>

          <div
            v-for="(step, index) in steps"
            :key="index"
            class="step-item"
            :class="[
              {
                completed: index < currentStep,
                active: index === currentStep,
              },
              index === steps.length - 1
                ? '!w-[1.3rem] !flex-none'
                : '!w-[2.12rem]',
            ]"
          >
            <div
              class="step-circle w-[0.96rem] h-[0.96rem] flex items-center mt-[0.08rem] z-10 bg-transparent"
            >
              <template v-if="index === currentStep && step?.iconActive">
                <div
                  class="w-[0.68rem] h-[0.68rem] rounded-full flex items-center justify-center bg-[#e50113]"
                >
                  <img
                    :src="step.iconActive"
                    :alt="step.label"
                    class="w-[0.48rem] h-[0.48rem] flex-shrink-0"
                    referrerpolicy="no-referrer"
                  />
                </div>
              </template>
              <template v-else-if="index > currentStep && step?.icon">
                <div
                  class="w-[0.48rem] h-[0.48rem] rounded-full flex items-center justify-center border border-dashed border-[#A6A6A6] bg-white"
                >
                  <img
                    :src="step.icon"
                    :alt="step.label"
                    class="w-[0.34rem] h-[0.34rem] flex-shrink-0"
                    referrerpolicy="no-referrer"
                  />
                </div>
              </template>
              <template v-else>
                <img
                  :src="checkCircle"
                  :alt="step.label"
                  class="w-[0.48rem] h-[0.48rem] flex-shrink-0"
                  referrerpolicy="no-referrer"
                />
              </template>
            </div>
            <div
              class="!w-[1.68rem] h-[1rem] text-[0.28rem] leading-[0.32rem] mt-[0.12rem] flex justify-center items-center flex-shrink-0"
              :class="currentStep < index ? 'text-[#4D4D4D]' : 'text-[#E50113]'"
            >
              {{ step.label }}
            </div>
          </div>
        </div>

        <div class="flex flex-shrink-0">
          <div
            class="w-[0.16rem] h-[0.2rem] bg-[#e50113] mt-[0.46rem] mx-[0.04rem] flex-shrink-0"
          ></div>
          <div>
            <img
              src="@/assets/icons/find/mobile-step-line.svg"
              alt="step"
              class="w-[5.2rem] h-[0.16rem] mt-[0.48rem]"
              referrerpolicy="no-referrer"
            />
            <div class="flex mt-[-0.28rem]">
              <div class="flex flex-col gap-[0.24rem] ml-[0.58rem]">
                <div
                  class="w-[0.48rem] h-[0.48rem] rounded-full flex items-center justify-center border border-dashed border-[#E6E6E6] bg-white"
                >
                  <img
                    :alt="authStore.i18n('cm_common.step4')"
                    src="@/assets/icons/find/paid.svg"
                    class="w-[0.34rem] h-[0.34rem] flex-shrink-0"
                    referrerpolicy="no-referrer"
                  />
                </div>
                <div
                  class="w-[1.36rem] h-[1rem] text-[0.28rem] leading-[0.32rem] text-[#A6A6A6] flex justify-center items-center"
                >
                  {{ authStore.i18n("cm_common.step4") }}
                </div>
              </div>
              <div class="flex flex-col gap-[0.4rem] items-center ml-[0.24rem]">
                <div class="flex gap-[0.08rem]">
                  <div
                    class="w-[0.48rem] h-[0.48rem] rounded-full flex items-center justify-center border border-dashed border-[#E6E6E6] bg-white"
                  >
                    <img
                      :alt="authStore.i18n('cm_common.step5')"
                      src="@/assets/icons/order/purchasing.svg"
                      class="w-[0.34rem] h-[0.34rem] flex-shrink-0"
                      referrerpolicy="no-referrer"
                    />
                  </div>
                  <div
                    class="w-[0.48rem] h-[0.48rem] rounded-full flex items-center justify-center border border-dashed border-[#E6E6E6] bg-white"
                  >
                    <img
                      :alt="authStore.i18n('cm_common.step5')"
                      src="@/assets/icons/find/check.svg"
                      class="w-[0.34rem] h-[0.34rem] flex-shrink-0"
                      referrerpolicy="no-referrer"
                    />
                  </div>
                </div>

                <div
                  class="w-[1.36rem] text-[0.28rem] leading-[0.32rem] text-[#A6A6A6] text-center"
                >
                  {{ authStore.i18n("cm_common.step5") }}
                </div>
              </div>
              <div class="flex flex-col gap-[0.4rem] items-center ml-[0.24rem]">
                <div
                  class="w-[0.48rem] h-[0.48rem] rounded-full flex items-center justify-center bg-[#E50113]"
                >
                  <img
                    :alt="authStore.i18n('cm_common.step6')"
                    src="@/assets/icons/order/deliveringAc.svg"
                    class="w-[0.34rem] h-[0.34rem] flex-shrink-0"
                    referrerpolicy="no-referrer"
                  />
                </div>
                <div
                  class="w-[1.36rem] text-[0.28rem] leading-[0.32rem] text-[#A6A6A6] text-center"
                >
                  {{ authStore.i18n("cm_common.step6") }}
                </div>
              </div>
              <div
                class="w-[0.36rem] h-[0.36rem] relative circle-container ml-[-0.14rem] mt-[0]"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="showRightArrow"
        @click="onScrollToEnd"
        class="absolute right-0 top-0 bottom-[0.24rem] w-[0.8rem] flex items-center justify-end bg-gradient-to-l from-white via-white/80 to-transparent z-20"
      >
        <img
          src="@/assets/icons/common/arrow-right-thin.svg"
          alt="step"
          class="w-[0.14rem] mr-[0.16rem]"
          referrerpolicy="no-referrer"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useAuthStore } from "@/stores/authStore";
import checkCircle from "@/assets/icons/find/check-circle.svg";
import unpaid from "@/assets/icons/find/unpaid.svg";
import quote from "@/assets/icons/find/quote.svg";
import quoteActive from "@/assets/icons/find/quote-active.svg";

const authStore = useAuthStore();

const props = defineProps({
  currentStep: {
    type: Number,
    default: 0,
  },
});

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

const stepProgressRef = ref<HTMLElement>();
const containerRef = ref<HTMLElement>();

const firstPartWidth = 5.56 * resetFontSize.value;
const lastStepWidth = 1.3 * resetFontSize.value;
const stepWidth = 2.12 * resetFontSize.value;
const hasManuallyScrolled = ref(false);
const showRightArrow = ref(false);

const steps = [
  {
    icon: checkCircle,
    label: authStore.i18n("cm_common.step1"),
  },
  {
    iconActive: unpaid,
    label: authStore.i18n("cm_common.step2"),
  },
  {
    icon: quote,
    iconActive: quoteActive,
    label: authStore.i18n("cm_common.step3"),
  },
];

const activeLineWidth = computed(() => {
  if (props.currentStep === 0) return "0";

  const stepCount = steps.length;
  const averageStepWidth = (firstPartWidth - lastStepWidth) / (stepCount - 1);

  const width = props.currentStep * averageStepWidth;
  return `${width > firstPartWidth ? firstPartWidth : width}px`;
});

const handleScroll = () => {
  if (!containerRef.value) return;

  hasManuallyScrolled.value = true;
  updateRightArrow();
};

const scrollToCurrentStep = () => {
  if (!containerRef.value || hasManuallyScrolled.value) return;

  const currentStepPosition = props.currentStep * stepWidth;
  const targetScrollLeft = Math.max(0, currentStepPosition - 118);

  containerRef.value.scrollTo({
    left: targetScrollLeft,
    behavior: "smooth",
  });

  updateRightArrow();
};

const updateRightArrow = () => {
  if (!containerRef.value) return;

  const { scrollLeft, scrollWidth, clientWidth } = containerRef.value;
  showRightArrow.value = scrollLeft + clientWidth < scrollWidth - 5; // 5px 容差
};

const onScrollToEnd = () => {
  if (!containerRef.value) return;

  containerRef.value.scrollTo({
    left: containerRef.value.scrollWidth,
    behavior: "smooth",
  });

  hasManuallyScrolled.value = true;
  updateRightArrow();
};

// 初始化
onMounted(() => {
  nextTick(() => {
    scrollToCurrentStep();
    updateRightArrow();
  });
});

// 监听步骤变化
watch(
  () => props.currentStep,
  () => {
    hasManuallyScrolled.value = false;
    nextTick(() => {
      scrollToCurrentStep();
    });
  }
);
</script>

<style scoped lang="scss">
.step-progress-container {
  width: 100%;

  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.step-progress-wrapper {
  display: flex;
  align-items: flex-start;
  width: max-content;
}

.step-progress {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 5.56rem;
}

.step-progress-line {
  position: absolute;
  top: 0.54rem;
  left: 0;
  right: 0;
  height: 0.04rem;
  background-color: #e0e0e0;
  z-index: 0;
}

.step-progress-line.completed {
  background-color: #e50113;
  height: 0.04rem;
  z-index: 1;
  transition: width 0.4s ease;
}

.step-item {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.circle-container {
  border-radius: 50%;
  background-color: rgba(229, 1, 19, 0.1);
  &::after {
    content: "";
    display: block;
    width: 0.2rem;
    height: 0.2rem;
    border-radius: 50%;
    background-color: #e50113;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
