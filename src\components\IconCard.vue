<template>
  <span>
    <span v-if="!props.multiple">
      <Icon :size="props.size" :name="props.name" :color="props.color" />
    </span>
    <span v-else>
      <template v-if="!isMobile">
        <n-space
          vertical
          class="flex-center hover:cursor-pointer"
          :style="{
            padding: '8px 6px',
            borderRadius: '8px',
            backgroundColor: 'rgba(255,255,255,1)',
            border: props.border ? '1px solid #F5F5F5' : 'none',
            gap: '6px 6px',
          }"
        >
          <div class="text-center">
            <Icon :size="props.size" :name="props.name" :color="props.color" />
          </div>
          <div v-show="props.title" class="text-center">
            <span class="text-[14px]">{{ props.title }}</span>
          </div>
        </n-space>
      </template>
      <template v-else>
        <n-space
          vertical
          class="flex-center hover:cursor-pointer"
          :style="{
            padding: '0.15rem 0.08rem',
            borderRadius: '0.16rem',
            backgroundColor: 'rgba(255,255,255,1)',
            border: props.border ? '1px solid #F5F5F5' : 'none',
            gap: '0.08rem 0.08rem',
          }"
        >
          <div class="text-center">
            <Icon :size="props.size" :name="props.name" :color="props.color" />
          </div>
          <div v-show="props.title" class="text-center">
            <span class="text-[0.26rem]">{{ props.title }}</span>
          </div>
        </n-space>
      </template>
    </span>
  </span>
</template>

<script setup lang="ts" name="IconCard">
const props = defineProps({
  name: {
    type: String,
    default: "uil:search",
  },
  title: {
    type: String,
    default: "",
  },
  color: {
    type: String,
    default: "white",
  },
  size: {
    type: String,
    default: "24px",
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  border: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});
</script>
