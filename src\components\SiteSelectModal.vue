<template>
  <!-- 站点选择弹窗 -->
  <country-select
    v-if="!isMobile"
    mode="modal"
    v-model:show="showModal"
    @save="onSaveCountry"
    spm="select_site_from_pop"
  />
  <mobile-country-select
    v-else
    mode="modal"
    v-model:show="showModal"
    @save="onSaveCountry"
    spm="select_site_from_pop"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useAuthStore } from "@/stores/authStore";
import { useRoute } from "vue-router";
import { getSiteInfo, getSiteList } from "@/utils/siteUtils";

const route = useRoute();
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const authStore = useAuthStore();
const showModal = ref(false);

// 获取站点信息（从window.siteData）
const siteInfo = computed(() => getSiteInfo());
const siteList = computed(() => getSiteList());

// 监听站点信息变化
watch(
  () => siteInfo.value.id,
  (newId) => {
    // 当站点ID为空且站点列表不为空时显示弹窗
    if (!newId && siteList.value.length > 0) {
      showModal.value = true;
    } else {
      showModal.value = false;
    }
  },
  { immediate: true }
);

// 保存选择的国家
const onSaveCountry = () => {
  // 可以根据需要决定是否重新加载页面
  window.location.reload();
};
</script>

<style scoped></style>
