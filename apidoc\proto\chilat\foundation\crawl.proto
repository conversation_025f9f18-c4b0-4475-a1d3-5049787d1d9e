syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/foundation/param/crawl_param.proto";
import "chilat/foundation/model/crawl_model.proto";
import "common.proto";

// 爬虫管理
service Crawl {
  // 抓取提交
  rpc submit (CrawlSubmitParam) returns (common.StringResult);
  // 商品列表
  rpc list (CrawlGoodsQueryParam) returns (CrawlGoodsListResp);
  // 商品分页列表
  rpc pageList (CrawlGoodsPageQueryParam) returns (CrawlGoodsPageResp);
  // 商品详情
  rpc detail (common.IdParam) returns (CrawlGoodsDetailResp);
  // 删除商品
  rpc remove (common.IdParam) returns (common.ApiResult);
  // 保存货源
  rpc saveGoodsSupply (CrawlGoodsSupplyParam) returns (common.ApiResult);
  // 货源列表
  rpc goodsSupplyList (CrawlGoodsSupplyQueryParam) returns (CrawlGoodsSupplyListResp);
  // 货源详情
  rpc goodsSupplyDetail (common.IdParam) returns (CrawlSupplyDetailResp);
  // 删除货源
  rpc removeGoodsSupply (CrawlGoodsSupplyRemoveParam) returns (common.ApiResult);
  // 检查更新
  rpc checkUpdate (common.StringParam) returns (CrawlCheckUpdateResp);
  // 发布商品
  rpc publish (CrawlPublishParam) returns (common.IdResult);
  // 错误上报
  rpc errorReport (CrawlErrorReportParam) returns (common.ApiResult);
  // 错误列表
  rpc errorList (common.EmptyParam) returns (CrawlErrorListResp);
  // 日志
  rpc log (common.IdParam) returns (common.LogResult);
  // 重新更新
  rpc reSync (common.IdParam) returns (common.ApiResult);
  // 更新并发布
  rpc reSyncAndPublish (common.IdParam) returns (common.ApiResult);
  // 抓取1688商品
  rpc crawl1688 (Crawl1688Param) returns (common.ApiResult);
  // 1688图搜
  rpc pageListByImage (CrawlImagePageQueryParam) returns (CrawlGoodsPageResp) {
    option (common.webapi).upload = true;
  };
  // 获取1688子账号列表（下单用）
  rpc getSubAccountListForOrder(common.EmptyParam) returns (CrawlSubAccountListResp);

  // 批量抓取1688商品
  rpc batchCrawl1688 (BatchCrawl1688Param) returns (common.ApiResult);

  // 异步抓取1688商品
  rpc asyncCrawl1688 (Crawl1688Param) returns (common.ApiResult);

  // 异步抓取任务列表
  rpc asyncCrawTaskList (CrawlTaskListPageQueryParam) returns (CrawlTaskListResp);
}