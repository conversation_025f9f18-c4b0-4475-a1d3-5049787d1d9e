syntax = "proto3";
package chilat.logistics;

option java_package = "com.chilat.rpc.logistics.model";

import "common.proto";

message RoutePageListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated RouteModel data = 3;
}

message RouteModel {
  string id = 1;
  string routeName = 2;
  string routeCode = 3;
  string routeAlias = 4;
  string remark = 5;
  bool supportGoodsFreight = 20; //是否支持商品运费配置（true为支持，默认不支持）
  bool enabled = 6;
  repeated RouteTransportMethodModel transportMethods = 7;
  repeated RouteTransportDaysModel transportDays = 8;
  string coperator = 9;
  int64 cdate = 10;
}

message RouteTransportMethodModel {
  string id = 1;
  string transportMethodCode = 2;
  string transportMethodName = 3;
}

message RouteTransportDaysModel {
  string countryId = 10; //国家id
  string countryName = 20; //国家名称
  string minDays = 30; //最快时效
  string maxDays = 44; //最慢时效
  double m3FreightUsd = 50; //1立方米预估运费，单位：美元
  double kgFreightUsd = 60; //1公斤（KG）预估运费，单位：美元
}

message RouteLogListResp {
  common.Result result = 1;
  repeated common.LogModel data = 2;
}

message RouteListResp {
  common.Result result = 1;
  repeated RouteModel data = 2;
}

message CountryRouteListResp {
  common.Result result = 1;
  repeated CountryRouteModel data = 2;
}

message CountryRouteModel {
  string id = 1;
  string routeName = 2;
  string routeCode = 3;
  string routeAlias = 4;
  string remark = 5;
  bool enabled = 6;
  RouteTransportDaysModel transportDays = 8;
  string coperator = 9;
  int64 cdate = 10;
}

//列出支持商品运费配置的国家与线路
message ListGoodsFreightCountryRoutesResp {
  common.Result result = 1;
  repeated GoodsFreightCountryModel data = 2;
}

//支持商品运费配置的国家（内含线路）
message GoodsFreightCountryModel {
  string countryId = 10; //国家ID
  string countryName = 20; //国家名称
  string countryNameCn = 30; //国家中文名称
  repeated GoodsFreightRouteModel routes = 40; //可选线路列表
}

//支持商品运费配置的线路（先选国家）
message GoodsFreightRouteModel {
  string routeId = 10; //线路ID
  string routeName = 20; //线路名称
  string routeNameCn = 30; //线路中文名称
}
