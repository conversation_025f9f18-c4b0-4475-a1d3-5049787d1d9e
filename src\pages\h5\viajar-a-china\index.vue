<template>
  <div class="page-container">
    <div class="page-header">
      <div class="w-full px-[0.4rem] py-[0.44rem] mx-auto">
        <div class="relative z-2">
          <div class="w-[2.44rem] h-[0.56rem]">
            <img
              loading="lazy"
              :src="configStore.pageTheme.chilatWhiteLogo"
              alt="logo"
              class="w-[2.44rem]"
            />
          </div>
          <div
            class="w-[6.8rem] text-[0.76rem] leading-[1rem] font-medium mt-[1.16rem]"
            v-html="authStore.i18n('cm_visit.excellenceCenterMobile')"
          ></div>

          <div
            class="w-[6.8rem] text-[0.4rem] leading-[0.68rem] mt-[0.72rem]"
            v-html="authStore.i18n('cm_visit.transformJourneyMobile')"
          ></div>
        </div>
      </div>
    </div>
    <div class="w-full text-[#333] px-[0.4rem] pb-[3.08rem]">
      <div class="w-full mt-[0.8rem] text-center">
        <div class="text-[0.56rem] leading-[0.68rem]">
          {{ authStore.i18n("cm_visit.travelToChina") }}
        </div>
        <div
          class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] mt-[0.32rem]"
        >
          <div>
            {{ authStore.i18n("cm_visit.faceToFaceMeetings") }}
          </div>
          <div>
            {{ authStore.i18n("cm_visit.tripToChina") }}
          </div>
        </div>
        <div
          class="w-[1rem] h-[0.06rem] bg-[#E50113] mt-[0.6rem] mx-auto"
        ></div>
        <div class="w-full mt-[0.52rem] flex flex-col">
          <div
            v-for="(item, index) in visitChinaData"
            :key="index"
            class="w-full flex flex-col items-center mt-[0.68rem]"
          >
            <img
              loading="lazy"
              :src="item.imgUrl"
              :alt="item.title"
              class="w-[0.8rem]"
            />
            <div class="text-[0.36rem] leading-[0.36rem] mt-[0.28rem]">
              {{ item.title }}
            </div>
            <div
              class="text-[0.28rem] leading-[0.36rem] text-[#7F7F7F] mt-[0.2rem]"
            >
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
      <div class="w-full mt-[1.6rem]">
        <div class="text-[0.56rem] leading-[0.68rem] text-center">
          {{ authStore.i18n("cm_visit.takeCareOf") }}
        </div>
        <div
          class="w-full text-[0.32rem] leading-[0.4rem] text-[#7F7F7F] text-center mt-[0.32rem]"
        >
          {{ authStore.i18n("cm_visit.comprehensiveService") }}
        </div>
        <div
          class="w-[1rem] h-[0.06rem] bg-[#E50113] mt-[0.6rem] mx-auto"
        ></div>
        <div class="flex flex-col mt-[0.96rem]">
          <div
            v-for="(item, index) in visitProcData"
            :key="index"
            class="relative w-full h-[7.64rem] bg-white border border-[#333] rounded-[0.4rem] pt-[4.4rem] pb-[0.32rem] px-[0.24rem] mt-[0.24rem]"
          >
            <img
              loading="lazy"
              :src="item.imgUrl"
              alt="visitChina"
              class="absolute top-0 left-0"
            />
            <div class="text-[0.4rem] leading-[0.48rem] text-center">
              {{ item.title }}
            </div>
            <n-space
              vertical
              :style="{ gap: '0.08rem 0' }"
              class="text-[0.28rem] leading-[0.34rem] text-[#7F7F7F] mt-[0.24rem]"
            >
              <div v-for="desc in item.descData" :key="desc">
                {{ desc }}
              </div>
            </n-space>
          </div>
        </div>
      </div>
      <div class="w-full mt-[1.6rem]">
        <div class="flex items-center justify-center">
          <img
            loading="lazy"
            :src="rightArrow"
            alt="arrow"
            class="mr-[0.16rem] w-[0.16rem]"
          />
          <span class="text-[0.32rem] leading-[0.32rem] text-[#7F7F7F]">{{
            authStore.i18n("cm_visit.comeToChina")
          }}</span>
        </div>
        <div class="text-[0.56rem] leading-[0.68rem] mt-[0.32rem] text-center">
          {{ authStore.i18n("cm_visit.howWeWork") }}
        </div>
        <div class="mt-[1.2rem]">
          <div class="w-[6.7rem] h-[3.76rem] !overflow-hidden">
            <video-you-tube
              :width="6.7 * resetFontSize"
              :height="3.76 * resetFontSize"
              :key="pageData.activatedWorkVideo?.id"
              :poster="pageData.activatedWorkVideo.poster"
              :youtubeId="pageData.activatedWorkVideo?.id"
              :title="pageData.activatedWorkVideo?.title"
              :titleCh="pageData.activatedWorkVideo?.titleCh"
            ></video-you-tube>
          </div>
          <n-space vertical :style="{ gap: '0.44rem 0' }" class="mt-[0.6rem]">
            <div
              v-for="item in workVideoData"
              :key="item.id"
              class="flex items-center cursor-pointer"
              @click="onPlayVideo(item)"
            >
              <img
                loading="lazy"
                :src="
                  item.id === pageData.activatedWorkVideo?.id
                    ? mobileVideoing
                    : mobileVideo
                "
                alt="video"
                class="mr-[0.16rem]"
              />
              <div
                :class="
                  item.id === pageData.activatedWorkVideo?.id
                    ? '!text-[#e50113]'
                    : ''
                "
                class="text-[0.3rem] leading-[0.32rem] text-medium"
              >
                {{ item.title }}
              </div>
            </div>
          </n-space>
        </div>
      </div>
    </div>
    <div class="bg-[#D5141B] text-[#FFF] relative z-1">
      <div class="w-full relative px-[0.4rem] pt-[3.16rem] pb-[1.4rem]">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          class="w-[6.7rem] absolute right-[0.4rem] top-[-1rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/06/aaf4cf98-ef26-4a1f-a873-7823ee8bac88.png"
        />
        <div class="text-[0.56rem] leading-[0.68rem] text-center">
          {{ authStore.i18n("cm_visit.whyUs") }}
        </div>
        <div class="w-[1rem] h-[0.06rem] bg-[#fff] mx-auto mt-[0.32rem]"></div>
        <div class="flex flex-col mt-[0.48rem]">
          <div
            v-for="(item, index) in whyUsData"
            :key="index"
            class="w-full pb-[0.52rem] border-b-1 border-[#FFF] mt-[0.52rem]"
          >
            <div class="flex text-[0.44rem] leading-[0.44rem] font-medium">
              <img
                loading="lazy"
                :src="rightArrowWhite"
                :alt="item.title"
                class="mr-[0.2rem] w-[0.16rem]"
              />
              0{{ index + 1 }}
            </div>
            <div
              class="text-[0.36rem] leading-[0.44rem] font-medium mt-[0.4rem]"
              v-html="item.title"
            ></div>
            <div
              class="text-[0.28rem] leading-[0.36rem] text-[#F2F2F2] mt-[0.24rem]"
            >
              {{ item.desc }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="bg-[#F7F7F7]">
      <div class="w-full relative flex flex-col">
        <div
          class="w-[7.84rem] h-[7.84rem] rounded-full bg-white absolute bottom-[-3.88rem] right-[0.92rem]"
        ></div>
        <div class="w-full overflow-hidden relative z-1">
          <div
            class="text-[0.56rem] leading-[0.68rem] text-center mt-[1.6rem] px-[0.4rem]"
          >
            {{ authStore.i18n("cm_visit.knowPeople") }}
          </div>
          <div
            class="w-[1rem] h-[0.06rem] bg-[#E50113] mt-[0.32rem] mx-auto"
          ></div>
          <div class="w-full flex my-[1.2rem] pb-[3.1rem]">
            <n-carousel
              :space-between="10"
              :loop="false"
              draggable
              slides-per-view="auto"
              centered-slides
              show-arrow
              :on-update:current-index="onUpdateCurrentIndex"
            >
              <n-carousel-item
                class="!w-[5.76rem] !h-[3.24rem] rounded-[0.4rem] relative overflow-hidden flex-shrink-0"
                v-for="(video, index) in userVideoData"
                :key="video.id"
                @click="onOpenVideo(video)"
              >
                <div class="w-[5.76rem] h-[3.24rem]">
                  <n-image
                    lazy
                    preview-disabled
                    :src="video.poster"
                    class="img"
                    object-fit="cover"
                  />
                  <img
                    loading="lazy"
                    :src="videoPlay"
                    alt="video"
                    class="w-[1rem] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
                  />
                </div>
              </n-carousel-item>
              <template #arrow="{ prev, next }">
                <div class="custom-arrow">
                  <icon-card
                    @click="prev"
                    name="fe:arrow-left"
                    size="26"
                    :color="
                      pageData.currentCarouselIndex === 0
                        ? '#D9D9D9'
                        : '#e50113'
                    "
                    class="mr-[0.72rem]"
                  >
                  </icon-card>
                  <icon-card
                    @click="next"
                    name="fe:arrow-right"
                    size="26"
                    :color="
                      pageData.currentCarouselIndex === userVideoData.length - 1
                        ? '#D9D9D9'
                        : '#e50113'
                    "
                  >
                  </icon-card>
                </div>
              </template>
              <template #dots="{ total, currentIndex, to }">
                <ul class="custom-dots">
                  <li
                    v-for="index of total"
                    :key="index"
                    :class="{ ['is-active']: index - 1 <= currentIndex }"
                  ></li>
                </ul>
              </template>
            </n-carousel>
          </div>
        </div>
      </div>
    </div>
    <div class="whatsapp-wrapper">
      <div
        class="w-[6rem] text-[0.44rem] leading-[0.76rem] font-semibold text-[#333] pt-[0.32rem] ml-[0.4rem] relative"
      >
        <span>Haga clic en</span>
        <span class="relative">
          <span class="text-[#25D366]"> WhatsApp</span>
          <img
            loading="lazy"
            src="@/assets/icons/open/greenLine.svg"
            alt="line"
            class="w-[3.2rem] absolute top-[0.51rem] left-[0.22rem]"
          />
        </span>
        <br />
        <span
          >para contactarnos y<br />
          obtener más información</span
        >
      </div>
      <img
        loading="lazy"
        alt="click"
        class="icon"
        @click="onHandleWhatsAppClick('bottom')"
        src="@/assets/icons/open/mobileWhatsappClick.png"
      />
      <n-button
        class="button"
        color="#E50113"
        @click="onHandleWhatsAppClick('bottom')"
        data-spm-box="potential_user_note_submit"
      >
        <div
          class="flex items-center justify-center text-[0.36rem] leading-[0.36rem] font-semibold"
          v-html="authStore.i18n('cm_nota.submitWhatsappMobile')"
        ></div>
      </n-button>
    </div>
    <div class="side-affix">
      <icon-card
        name="logos:whatsapp-icon"
        size="0.44rem"
        title="W/app"
        :border="true"
        :multiple="true"
        @click="onHandleWhatsAppClick()"
      ></icon-card>
    </div>
  </div>
  <video-modal ref="videoModalRef"></video-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";

import rightArrow from "@/assets/icons/open/rightArrow.svg";
import rightArrowWhite from "@/assets/icons/open/rightArrowWhite.svg";
import mobileVideo from "@/assets/icons/open/mobileVideo.svg";
import mobileVideoing from "@/assets/icons/open/mobileVideoing.svg";
import videoPlay from "@/assets/icons/open/videoPlay.svg";
import money from "@/assets/icons/open/money.svg";
import shopping from "@/assets/icons/open/shopping.svg";
import find from "@/assets/icons/open/find.svg";
import cooperation from "@/assets/icons/open/cooperation.svg";
import quotation from "@/assets/icons/open/quotation.svg";
import travelTips from "@/assets/icons/open/travelTips.png";
import accommodation from "@/assets/icons/open/accommodation.png";
import expertAdvice from "@/assets/icons/open/expertAdvice.png";
import interpreters from "@/assets/icons/open/interpreters.png";
import orderQualityControl from "@/assets/icons/open/orderQualityControl.png";
import logisticsAssistance from "@/assets/icons/open/logisticsAssistance.png";
import workVideoPoster1 from "@/assets/icons/open/workVideoPoster1.png";
import workVideoPoster2 from "@/assets/icons/open/workVideoPoster2.png";
import workVideoPoster3 from "@/assets/icons/open/workVideoPoster3.png";
import workVideoPoster4 from "@/assets/icons/open/workVideoPoster4.png";
import workVideoPoster5 from "@/assets/icons/open/workVideoPoster5.png";

import userVideoPoster1 from "@/assets/icons/open/userVideoPoster1.png";
import userVideoPoster2 from "@/assets/icons/open/userVideoPoster2.png";
import userVideoPoster3 from "@/assets/icons/open/userVideoPoster3.png";
import userVideoPoster4 from "@/assets/icons/open/userVideoPoster4.png";

const authStore = useAuthStore();
const configStore = useConfigStore();
const videoModalRef = ref<any>(null);

const visitChinaData = [
  {
    imgUrl: money,
    title: authStore.i18n("cm_visit.competitivePrice"),
    desc: authStore.i18n("cm_visit.competitivePriceDesc"),
  },
  {
    imgUrl: shopping,
    title: authStore.i18n("cm_visit.wideRange"),
    desc: authStore.i18n("cm_visit.wideRangeDesc"),
  },
  {
    imgUrl: find,
    title: authStore.i18n("cm_visit.qualityAssurance"),
    desc: authStore.i18n("cm_visit.qualityAssuranceDesc"),
  },
  {
    imgUrl: cooperation,
    title: authStore.i18n("cm_visit.buildRelations"),
    desc: authStore.i18n("cm_visit.buildRelationsDesc"),
  },
];

const visitProcData = [
  {
    imgUrl: travelTips,
    title: authStore.i18n("cm_visit.travelTips"),
    descData: [
      authStore.i18n("cm_visit.researchMarkets"),
      authStore.i18n("cm_visit.prioritizePlaces"),
    ],
  },
  {
    imgUrl: accommodation,
    title: authStore.i18n("cm_visit.accommodation"),
    descData: [authStore.i18n("cm_visit.hotelArrange")],
  },
  {
    imgUrl: expertAdvice,
    title: authStore.i18n("cm_visit.expertAdvice"),
    descData: [authStore.i18n("cm_visit.professionalAdvisors")],
  },
  {
    imgUrl: interpreters,
    title: authStore.i18n("cm_visit.interpreters"),
    descData: [authStore.i18n("cm_visit.languageBarriers")],
  },
  {
    imgUrl: orderQualityControl,
    title: authStore.i18n("cm_visit.orderQualityControl"),
    descData: [authStore.i18n("cm_visit.trackProdProc")],
  },
  {
    imgUrl: logisticsAssistance,
    title: authStore.i18n("cm_visit.logisticsAssistance"),
    descData: [
      authStore.i18n("cm_visit.shipmentAdvice"),
      authStore.i18n("cm_visit.logisticalSupport"),
    ],
  },
];

const workVideoData = [
  {
    id: "b7X9D3BLvMc",
    poster: workVideoPoster1,
    title: authStore.i18n("cm_visit.hotelReservation"),
    titleCh: "1.发出邀请信/酒店预订",
  },
  {
    id: "VCgjAWzHB1A",
    poster: workVideoPoster2,
    title: authStore.i18n("cm_visit.marketVisit"),
    titleCh: "2.在机场接待并参观市场",
  },
  {
    id: "wWtOhkPDg5A",
    poster: workVideoPoster3,
    title: authStore.i18n("cm_visit.productionTracking"),
    titleCh: "3.下订单并跟踪生产",
  },
  {
    id: "GN6F6oLssUw",
    poster: workVideoPoster4,
    title: authStore.i18n("cm_visit.qualityInspection"),
    titleCh: "4.接收产品并进行质量检查",
  },
  {
    id: "KbpxdDskNsQ",
    poster: workVideoPoster5,
    title: authStore.i18n("cm_visit.loadContainer"),
    titleCh: "5.装载集装箱并组织运输",
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    poster: userVideoPoster1,
    titleCh: "1.我们的客户对我们的看法7",
  },
  {
    id: "Tj0nrnhxgXw",
    poster: userVideoPoster2,
    titleCh: "2.我们的客户对我们的看法2",
  },
  {
    id: "_omi5a-pHkA",
    poster: userVideoPoster3,
    titleCh: "3.我们的客户对我们的看法6",
  },
  {
    id: "4FVIz0PvEcE",
    poster: userVideoPoster4,
    titleCh: "4.我们的客户对我们的看法5",
  },
];

const whyUsData = [
  {
    title: authStore.i18n("cm_visit.famousBrand"),
    desc: authStore.i18n("cm_visit.famousBrandDesc"),
  },
  {
    title: authStore.i18n("cm_visit.experiencedTeam"),
    desc: authStore.i18n("cm_visit.experiencedTeamDesc"),
  },
  {
    title: authStore.i18n("cm_visit.factoryResources"),
    desc: authStore.i18n("cm_visit.factoryResourcesDesc"),
  },
  {
    title: authStore.i18n("cm_visit.alliesTeam"),
    desc: authStore.i18n("cm_visit.alliesTeamDesc"),
  },
];

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

const pageData = reactive(<any>{
  activatedWorkVideo: workVideoData[0],
  currentCarouselIndex: 0,
});

function onPlayVideo(val: any) {
  pageData.activatedWorkVideo = val;
}

function onOpenVideo(video: any) {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(video);
  }
}

function onUpdateCurrentIndex(index: any) {
  pageData.currentCarouselIndex = index;
}

async function onHandleWhatsAppClick(val?: any) {
  let clickSource = "viajar-a-china页面悬浮按钮点击WhatsApp";
  if (val) {
    clickSource = "viajar-a-china页面底部按钮点击WhatsApp";
  }
  window?.MyStat?.addPageEvent(
    "potential_user_click_whatsapp",
    clickSource,
    true
  );
  let paramsObj = {
    clickSource,
  };
  const res: any = await useClickWhatsapp(paramsObj);
  if (res?.result?.code === 200) {
    onWhatsAppClick();
  } else {
    showToast(res?.result?.message);
  }
}
</script>

<style scoped lang="scss">
.page-container {
  height: auto;
  min-height: 100vh;
  color: #333;
}

.page-header {
  position: relative;
  width: 100%;
  height: 13.16rem;
  background-size: cover;
  color: #fff;
  background-image: url("@/assets/icons/open/svipVisitMobileBg.png");
  background-repeat: no-repeat;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(21, 27, 35, 0.5);
    z-index: 1;
  }
}

.scrollable-container {
  overflow-y: auto;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
.n-carousel {
  overflow: visible;
}
.custom-arrow {
  display: flex;
  position: absolute;
  bottom: -1.6rem;
  right: 0.2rem;
  z-index: 10;
}

.custom-dots {
  display: flex;
  margin: 0;
  padding: 0;
  position: absolute;
  bottom: -1.4rem;
  left: 0.4rem;
  border-radius: 0.08rem;
  background-color: #d9d9d9;
}

.custom-dots li {
  display: inline-block;
  width: 1.12rem;
  max-width: none;
  height: 0.04rem;
  background-color: #d9d9d9;
  border-radius: 0;
  cursor: pointer;
  margin: 0;
}

.custom-dots li.is-active {
  height: 0.06rem;
  background: #e50113;
}
.vjs-poster img {
  object-fit: none;
  border-radius: 0.24rem !important;
}

.side-affix {
  position: fixed;
  right: 0.12rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
}
.whatsapp-wrapper {
  position: relative;
  width: 100%;
  height: 8.68rem;
  background-size: 100%100%;
  color: #fff;
  background-image: url("@/assets/icons/open/mobileWhatsappBg.png");
  .icon {
    position: absolute;
    top: 2.8rem;
    width: 2.8rem;
    right: 0.32rem;
    z-index: 1;
  }
  .button {
    position: absolute;
    top: 4.64rem;
    right: 0.4rem;
    height: fit-content;
    display: inline-flex;
    padding: 0.32rem 0.68rem;
    align-items: center;
    gap: 0.08rem;
    border-radius: 12.96rem;
    border: 0.06rem solid var(---100, #fff);
    background: #25d366;
    box-shadow: 0 0.02rem 0.04rem 0 rgba(0, 0, 0, 0.5);
  }
}
</style>
