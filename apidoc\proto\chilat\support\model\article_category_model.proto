syntax = "proto3";
package chilat.support;

option java_package = "com.chilat.rpc.support.model";

import "common.proto";
import "chilat/support/support_common.proto";

message ArticleCategoryTreeResp {
  common.Result result = 1;
  repeated ArticleCategoryTreeModel data = 2;
}

message ArticleCategoryTreeModel {
  string id = 1;
  string name = 2; //类目名称
  string icon = 3; //类目图标
  int32 idx = 4; //排序
  string parentId = 5; //上级类目
  bool isDefault = 6; //是否默认显示
  repeated ArticleCategoryTreeModel children = 7;
}

message ArticlePageListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated ArticleModel data = 3;
}

message ArticleModel {
  string id = 1;
  string title = 2; //标题
  string content = 3; //内容
  string logo = 4; //logo
  string author = 5; //作者
  string introduce = 6; //简介
  bool isDefault = 7; //是否默认显示
  int32 idx = 8; //排序
  repeated ArticleCategoryTreeModel articleCategories = 9; //文章分类
  PageType pageType = 10; //显示在pc上还是h5上
  string pageTypeDesc = 11;
  int64 udate = 12;
  string articleCode = 13; //文章代码
}