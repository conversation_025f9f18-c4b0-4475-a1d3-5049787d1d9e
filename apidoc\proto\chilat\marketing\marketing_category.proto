syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing";

import "chilat/marketing/param/marketing_param.proto";
import "chilat/marketing/model/marketing_model.proto";
import "common.proto";

service MarketingCategory {
  // 查询详情
  rpc getDetail (common.IdParam) returns (MarketingCategoryDetailResp);
  // 删除营销分类
  rpc remove (common.IdParam) returns (common.ApiResult);
  // 保存营销分类信息（id不存在，则为新增）
  rpc saveMarketingCategory (MarketingCategorySaveParam) returns (common.ApiResult);
  // 获取营销分类树
  rpc getMarketingCategoryTree (common.EmptyParam) returns (MarketingCategoryTreeResp);
  // 更新营销分类的商品数量（参数为营销分类ID，若传空值，则更新全部）
  rpc updateMarketingCategoryGoodsCount (common.IdsParam) returns (common.MapResult);
  // 禁用/启用
  rpc enable (common.EnabledParam) returns (common.ApiResult);
  // 查询日志
  rpc listLog (common.IdParam) returns (common.LogResult);
}
