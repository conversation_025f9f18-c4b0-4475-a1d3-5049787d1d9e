syntax = "proto3";
package chilat.report;

import "chilat/report/model/mall_traffice_model.proto";
import "chilat/report/param/mall_traffice_param.proto";
import "common.proto";

option java_package = "com.chilat.rpc.report";

// 商城流量查询
service MallTraffic {
    // 查询访客列表（最早可查询日期：2024-05-23）
    rpc queryVisitorList(QueryVisitorListParam) returns (QueryVisitorListResp);
    // 查询访客PV
    rpc queryPageVisit(QueryPageVisitParam) returns (QueryPageVisitResp);
    // 查询页面内事件
    rpc queryPageEvent(QueryPageEventParam) returns (QueryPageEventResp);
    // 导出访客列表
    rpc exportVisitorList(QueryVisitorListParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };

    // 查询访客列表（最早可查询日期：2024-05-23）
    rpc queryVisitSessionList(QueryVisitSessionListParam) returns (QueryVisitSessionListResp);
    // 查询访客PV
    rpc queryPageVisitBySession(QueryPageVisitBySessionParam) returns (QueryPageVisitBySessionResp);
    // 导出访客列表
    rpc exportVisitSessionList(QueryVisitSessionListParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };

    // 导出美客多与义乌货盘数据（新货盘数据表）
    rpc exportTagGoodsMY(QueryVisitorListParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };
    // 导出询盘信息
    rpc exportLookingInfo(QueryVisitorListParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };
    rpc updateDailyReport(UpdateDailyReportParam) returns (common.StringResult);
    rpc sendYesterdayReport(common.EmptyParam) returns (common.ApiResult);

}