syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "common.proto";

import "mall/pages/param/mall_goods_find_pages_param.proto";
import "mall/pages/model/mall_goods_find_pages_model.proto";

service MallGoodsFindPage {

  // 根据登录账号获取对应的联系人信息
  rpc getContactInfo(common.EmptyParam) returns (GetContactInfoResp);

  // 提交找货信息
  rpc submitGoodsFind(SubmitGoodsFindParam) returns (SubmitGoodsFindResp);

  rpc goodsFindDetail(common.IdParam) returns (GoodsFindDetailResp);
}