<template>
  <n-modal :show="visible" :show-icon="false" @update:show="onClose">
    <n-card
      :bordered="false"
      style="
        width: 500px;
        color: #000;
        padding: 26px 0 !important;
        text-align: center;
      "
    >
      <div>
        <icon-card
          size="24"
          name="mingcute:warning-line"
          class="add-btn-check"
          color="#E50113"
        ></icon-card>
        {{ message }}
      </div>
      <a href="/">
        <n-button
          round
          class="mt-4"
          size="small"
          color="#E50113"
          text-color="#fff"
        >
          {{ authStore.i18n("cm_find_shopAgain") }}
        </n-button>
      </a>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  message: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:visible"]);

function onClose(value: boolean) {
  emit("update:visible", value);
}
</script>

<style scoped>
.add-btn-check {
  margin-bottom: 8px;
}
</style>
