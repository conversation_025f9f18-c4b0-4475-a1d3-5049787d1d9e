syntax = "proto3";
package mall.topic;

option java_package = "com.chilat.rpc.topic.param";


import "common/topic.proto";

message GetFindGoodsInfoParam {
  string keyword = 10; //小组查找关键字（groupCode查找预处理：去除空白字符，西语字母转英语，英文数字大于6个字符时去除标点符号；若不存在，则自动创建；自动创建时，原输入转为groupName）
}

//单次保存找货信息（保存一个找货任务中的问题）
message SaveFindGoodsOnceParam {
  string groupId = 10; //小组ID
  string questionId = 20; //问题ID
  int32 answerVersion = 30; //回答的版本号（若已有回答，则此字段必填）
  repeated FindGoodsAnswerFoundGoodsParam foundGoodsList = 50; //找到的商品信息列表
}

//找货任务回答中的找到的商品信息
message FindGoodsAnswerFoundGoodsParam {
  int32 answerSeq = 10; //回答次序号（按位置算，第1个商品则为1，第2个商品则为2，依序类推；必填）
  repeated string goodsImages = 20; //商品图片
  double goodsPrice = 30; //商品价格
  int32 packingCount = 40; //装箱数
  double goodsVolume = 50; //商品体积（单位：立方米，长*宽*高）
  double goodsWeight = 60; //商品重量（单位：kg；保留字段）
  int32 minBuyQuantity = 70; //最小购买数量（起订量）
  string recommendReason = 80; //推荐理由
  common.FindGoodsPurchaseSource findSource = 90; //采购来源
  repeated string proofImages = 100; //证明图片URL
}

