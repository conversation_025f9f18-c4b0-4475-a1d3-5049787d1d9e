/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { CouponAvailableModelResp, CouponUsableModelResp } from "../../chilat/basis/model/coupon_usable_model";
import { MyCouponDetailModelResp } from "../../chilat/basis/model/my_coupon_detail_model";
import {
  MallCouponCheckParam,
  MallCouponUsableParam,
  MallMyCouponDetailParam,
} from "./param/mall_my_coupon_detail_param";

export const protobufPackage = "mall.marketing";

/** 卡券管理 */
export interface CouponDetail {
  /** 我的优惠券列表查询 */
  getMyCouponDetailList(request: MallMyCouponDetailParam): Promise<MyCouponDetailModelResp>;
  /** 可用产品券/佣金券/列表 */
  getCouponUsableList(request: MallCouponUsableParam): Promise<CouponUsableModelResp>;
  /** checkbox优惠券校验 */
  checkAvailableList(request: MallCouponCheckParam): Promise<CouponAvailableModelResp>;
}

export const CouponDetailServiceName = "mall.marketing.CouponDetail";
export class CouponDetailClientImpl implements CouponDetail {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || CouponDetailServiceName;
    this.rpc = rpc;
    this.getMyCouponDetailList = this.getMyCouponDetailList.bind(this);
    this.getCouponUsableList = this.getCouponUsableList.bind(this);
    this.checkAvailableList = this.checkAvailableList.bind(this);
  }
  getMyCouponDetailList(request: MallMyCouponDetailParam): Promise<MyCouponDetailModelResp> {
    const data = MallMyCouponDetailParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getMyCouponDetailList", data);
    return promise.then((data) => MyCouponDetailModelResp.decode(_m0.Reader.create(data)));
  }

  getCouponUsableList(request: MallCouponUsableParam): Promise<CouponUsableModelResp> {
    const data = MallCouponUsableParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getCouponUsableList", data);
    return promise.then((data) => CouponUsableModelResp.decode(_m0.Reader.create(data)));
  }

  checkAvailableList(request: MallCouponCheckParam): Promise<CouponAvailableModelResp> {
    const data = MallCouponCheckParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "checkAvailableList", data);
    return promise.then((data) => CouponAvailableModelResp.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
