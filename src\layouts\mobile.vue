<template>
  <n-layout>
    <n-layout-content>
      <!-- 页面禁止缩放 -->
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      />
      <!-- 悬浮按钮-->
      <mobile-floating-buttons />
      <!-- 内容 -->
      <slot />
    </n-layout-content>
  </n-layout>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "mobile",
});
</script>

<style scoped>
.n-layout {
  height: 100%;
  width: 100%;
  background: #fff;
}

.n-layout-content {
  background-color: #fff;
}
</style>
