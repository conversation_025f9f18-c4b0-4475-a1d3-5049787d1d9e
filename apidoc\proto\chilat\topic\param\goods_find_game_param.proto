syntax = "proto3";
package chilat.topic;

option java_package = "com.chilat.rpc.topic.param";

import "common.proto";
import "common/business.proto";

message GoodsFindGameSaveParam {
  string id = 10; //groupId,新建时不传,编辑保存时传
  repeated GoodsQuestionModel goodsList = 30; // 列表
}

message GoodsQuestionModel {
  string id = 10; //新建时不传,编辑时传
  string imageUrl = 20; // 图片url
  string specDesc = 30; // 参数-规格描述
}