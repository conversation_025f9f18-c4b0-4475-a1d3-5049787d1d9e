syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basic.param";

import "common.proto";
import "common/business.proto";

message MidGetPromotionGoodsIndexParam {
    string promotionId = 10; //优惠活动ID
    string promotionCode = 20; //优惠活动代码
    int32 siteId = 120; //站点ID
    string visitorId = 130; //商城访客ID
    string userId = 140; //商城用户ID
    common.CurrencyType defaultCurrency = 601; //请求参数或返回中的默认货币单位
    common.CurrencyType requestCurrency = 602; //请求参数中的货币单位（若defaultCurrency留空，则必填）
    common.CurrencyType responseCurrency = 603; //响应结果中的货币单位（若defaultCurrency留空，则必填）
}