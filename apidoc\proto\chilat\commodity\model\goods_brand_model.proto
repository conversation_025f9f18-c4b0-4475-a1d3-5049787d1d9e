syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";

message GoodsBrandPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsBrandModel data = 3;
}

message GoodsBrandDetailResp {
    common.Result result = 1;
    GoodsBrandModel data = 2;
}

// 品牌信息
message GoodsBrandModel {
    string id = 1; //品牌ID
    int32 idx = 2; //顺序
    string brandName = 3; //品牌名称
    string firstLettler = 4; //首字母大写
    string spellLettler = 5; //字母简拼
    string groupLettler = 6; //所属字母分组
    string brandAlias = 7; //品牌别名
    string brandLogo = 8; //品牌logo
    string brandRemark = 9; //品牌描述
    string brandSiteUrl = 10; //品牌官网URL
    string brandStory = 11; //品牌故事
    repeated string categoryIds = 12; //类目id列表
    repeated string categoryPaths = 13; //多选类目树列表
    int32 relatedGoodsCount = 14; //关联的商品数量（详情接口返回）
    bool enabled = 15; //是否启用
}
