syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";

import "chilat/foundation/foundation_common.proto";
import "common.proto";
import "common/business.proto";

// 查询WhatsApp列表的结果
message PageWhatsAppPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated PageWhatsAppDetailModel data = 3;
}

// WhatsApp详细信息返回
message PageWhatsAppDetailResp {
    common.Result result = 1;
    PageWhatsAppDetailModel data = 2;
}

// WhatsApp详细信息
message PageWhatsAppDetailModel {
    string id = 5; //ID
    string pageName = 10; //页面名称
    string pageCode = 20; //页面代码
    common.VisitSiteSelectEnum visitSite = 30; //网站ID（10：主站，20：Shop）
    string pagePath = 40; //页面路径
    string pageUrl = 50; //页面链接
    string whatsapp = 60; //WhatsApp号码
    string preFilledText = 70; //WhatsApp预填文本
    string preFilledTextDemo = 75; //WhatsApp预填文本的例子（仅在详情接口输出）
    repeated string preFilledTextErrors = 76; //WhatsApp预填文本解析错误
    int32 idx = 80; //排序（小的在前）
}

// 查询日志返回
message PageWhatsAppLogResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated PageWhatsAppLogModel data = 3;
}

// 日志信息
message PageWhatsAppLogModel {
    string id = 1; //ID
    string operation = 2; //操作类型（新增、修改、删除或更详细的操作说明）
    string content = 3; //操作内容（html格式）
    string operator = 4; //操作人
    int64 operateTime = 5; //操作时间
}

