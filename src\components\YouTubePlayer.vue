/** * YouTube 视频播放 * * 技术: YouTube IFrame Player API * 功能: * - 支持
YouTube 链接 * - 用户交互后可自动播放 * - 不支持封面图 */

<template>
  <div
    ref="playerContainer"
    :style="{ width: props.width + 'px', height: props.height + 'px' }"
  ></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from "vue";

const props = defineProps({
  youtubeId: {
    type: String,
    required: true,
  },
  width: {
    type: Number,
    default: 360,
  },
  height: {
    type: Number,
    default: 180,
  },
  serial: {
    type: Number,
    default: 1,
  },
  title: {
    type: String,
    default: "",
  },
  titleCh: {
    type: String,
    default: "",
  },
  autoplay: {
    type: Boolean,
    default: false,
  },
});

const playerContainer = ref<HTMLElement | null>(null);
const player = ref<any>(null);
const isPlaying = ref(false);

// 确保 YouTube API 已加载
const loadYouTubeAPI = () => {
  return new Promise((resolve) => {
    if (window.YT) {
      resolve(window.YT);
      return;
    }

    const tag = document.createElement("script");
    tag.src = "https://www.youtube.com/iframe_api";
    const firstScriptTag = document.getElementsByTagName("script")[0];
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

    window.onYouTubeIframeAPIReady = () => {
      resolve(window.YT);
    };
  });
};

onMounted(async () => {
  try {
    await loadYouTubeAPI();

    // 创建播放器
    player.value = new window.YT.Player(playerContainer.value, {
      host: "https://www.youtube.com",
      width: props.width,
      height: props.height,
      videoId: props.youtubeId,
      playerVars: {
        playsinline: 1,
        controls: 1,
        autoplay: props.autoplay ? 1 : 0,
        enablejsapi: 1,
        modestbranding: 1,
        rel: 0,
        showinfo: 0,
        fs: 0,
        iv_load_policy: 3,
        origin: window.location.protocol + "//" + window.location.hostname,
        widget_referrer:
          window.location.protocol + "//" + window.location.hostname,
        wmode: "opaque",
        html5: 1,
      },
      events: {
        onReady: (event: any) => {
          if (props.autoplay) {
            event.target.playVideo();
          }
        },
        onStateChange: (event: any) => {
          if (event.data === window.YT.PlayerState.PLAYING) {
            if (!isPlaying.value) {
              isPlaying.value = true;
              if (props.titleCh || props.title) {
                window?.MyStat?.addPageEvent(
                  "video_play_start",
                  `${props.titleCh ?? props.title}播放开始`
                );
              }
            }
          } else if (event.data === window.YT.PlayerState.ENDED) {
            isPlaying.value = false;
            if (props.titleCh || props.title) {
              window?.MyStat?.addPageEvent(
                "video_play_finish",
                `${props.titleCh ?? props.title}播放结束`
              );
            }
          }
        },
        onError: (event: any) => {
          const errorMessage = event.data.toString();
          if (props.titleCh || props.title) {
            window?.MyStat?.addPageEvent(
              "video_play_error",
              `${props.titleCh ?? props.title}播放失败：${errorMessage}`
            );
          }
        },
      },
    });
  } catch (error) {
    console.error("Error initializing YouTube player:", error);
  }
});

onBeforeUnmount(() => {
  if (player.value?.destroy) {
    player.value.destroy();
  }
});

function onYouPlay() {
  if (player.value?.playVideo) {
    player.value.playVideo();
  }
}

defineExpose({ onYouPlay });
</script>

<style scoped>
:deep(.video-js) {
  width: 100%;
  height: 100%;
}
</style>
