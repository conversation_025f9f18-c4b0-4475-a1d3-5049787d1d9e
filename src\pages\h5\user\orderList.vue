<template>
  <div class="bg-[#F2F2F2] min-h-[100vh] py-[1rem]">
    <div
      class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] items-center justify-center bg-white fixed top-0 z-10"
    >
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        data-spm-box="navigation-back-icon"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_order.myOrder") }}
      </div>
    </div>
    <n-tabs
      type="line"
      animated
      v-model:value="pageData.mallOrderStatus"
      :on-update:value="onUpdateTab"
    >
      <n-tab-pane
        v-for="tab in orderTabData"
        :key="tab.value"
        :tab="tab.label"
        :name="tab.value"
      >
        <div>
          <div
            class="px-[0.2rem] text-[0.28rem] leading-[0.42rem]"
            v-if="pageData.orderList?.length"
          >
            <n-space vertical :style="{ gap: '0.2rem 0' }">
              <div
                v-for="order in pageData.orderList"
                :key="order.orderNo"
                class="bg-white rounded-[0.1rem]"
              >
                <div
                  class="flex justify-between px-[0.2rem] py-[0.2rem] border-b !border-emerald-400 bg-green-50 rounded-t-[0.1rem] mb-[0.2rem] text-[0.26rem]"
                >
                  <a
                    data-spm-box="order-list-go-detail"
                    :href="`/h5/order/details?orderNo=${order.orderNo}`"
                    >{{ order.orderNo }}</a
                  >
                  <div
                    class="text-[#E50113] text-[0.24rem] w-[3.6rem] break-all text-right"
                  >
                    {{ order.statusDesc }}
                  </div>
                </div>

                <n-space
                  vertical
                  :style="{ gap: '0.24rem 0' }"
                  class="px-[0.3rem]"
                >
                  <div
                    v-for="(sku, index) in order?.skuList"
                    :key="index"
                    class="flex text-[0.26rem] text-[#000]"
                  >
                    <n-image
                      lazy
                      :src="sku.picUrl"
                      class="w-[1.24rem] h-[1.24rem] flex-shrink-0 mr-[0.2rem]"
                      :img-props="{ referrerpolicy: 'no-referrer' }"
                    />
                    <div>
                      <a
                        :href="`/h5/goods/${sku.goodsId}${
                          sku.padc ? `?padc=${sku.padc}` : ''
                        }`"
                        class="goodsName-ellipsis"
                        :data-spm-index="index + 1"
                        data-spm-box="order-list-goods"
                      >
                        {{ sku.goodsName }}
                      </a>
                      <div
                        class="text-[0.24rem] text-[#7F7F7F] my-[0.05rem] leading-[0.34rem]"
                      >
                        {{ getSkuName(sku.specList) }}
                      </div>
                      <div class="flex justify-between">
                        <!-- 销售单价 -->
                        <span class="text-[#E50013]">{{
                          setUnit(sku.unitPrice)
                        }}</span>
                        <!-- 数量 -->
                        <span>x {{ sku.count }}</span>
                      </div>
                    </div>
                  </div>
                </n-space>

                <div
                  class="border-t border-[#D7D7D7] flex justify-between p-[0.24rem] mt-[0.2rem]"
                >
                  <div class="flex ml-auto">
                    <n-space>
                      <!-- [支付订单费用] [支付产品成本] 显示取消按钮 -->
                      <n-button
                        color="#F6D2D4"
                        text-color="#E50013"
                        @click="onOrderCancel(order)"
                        v-if="
                          order.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE' ||
                          order.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT'
                        "
                        class="rounded-[0.32rem] w-[1.44rem] h-[0.48rem] text-[0.26rem] px-0"
                      >
                        {{ authStore.i18n("cm_order.orderCancel") }}
                      </n-button>
                      <!-- [支付订单费用] [支付产品成本] [支付国际费用] 显示付款按钮 -->
                      <a
                        data-spm-box="order-list-go-pay"
                        :href="`/h5/order/details?orderNo=${order.orderNo}`"
                      >
                        <n-button
                          color="#E50113"
                          text-color="#fff"
                          class="rounded-[0.32rem] w-[1.44rem] h-[0.48rem] text-[0.26rem]"
                          v-if="
                            order.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE' ||
                            order.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT' ||
                            order.mallOrderStatus === 'MALL_WAIT_PAY_INTER_FEE'
                          "
                        >
                          {{ authStore.i18n("cm_order.orderPay") }}
                        </n-button>
                      </a>
                      <a
                        data-spm-box="order-list-go-detail"
                        :href="`/h5/order/details?orderNo=${order.orderNo}`"
                      >
                        <n-button
                          color="#E50113"
                          text-color="#fff"
                          class="rounded-[0.32rem] w-[1.44rem] h-[0.48rem] text-[0.26rem]"
                        >
                          {{ authStore.i18n("cm_order.orderDetail") }}
                        </n-button>
                      </a>
                    </n-space>
                  </div>
                </div>
              </div>
            </n-space>
            <div>
              <img
                loading="lazy"
                v-if="pageData.isLoading"
                src="@/assets/icons/loading.svg"
                class="w-[1rem] h-[1rem] mx-auto"
                referrerpolicy="no-referrer"
              />
            </div>
          </div>
          <div v-else>
            <n-empty
              :description="authStore.i18n('cm_order.noData')"
              class="mt-[1.28rem]"
            >
            </n-empty>
          </div>
        </div>
      </n-tab-pane>
    </n-tabs>
    <order-cancel
      ref="orderCancelRef"
      @updateOrderState="onUpdateOrderState"
    ></order-cancel>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import OrderCancel from "@/pages/h5/order/components/OrderCancel.vue";
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const orderCancelRef = ref<any>(null);

const orderTabData = reactive<any>([
  {
    // 全部状态
    value: "MALL_STATUS_ALL",
    label: authStore.i18n("cm_order.statusALL"),
  },
  {
    // 待支付 [待支付产品成本、待支付订单费用]
    value: "MALL_WAIT_PAY",
    label: authStore.i18n("cm_order.waitPayMoney"),
  },
  {
    // 采购
    value: "MALL_PURCHASING",
    label: authStore.i18n("cm_order.purchasing"),
  },
  {
    // 国际费用 [待计算国际费用、待支付国际费用]
    value: "MALL_INTERNATIONAL_FEE",
    label: authStore.i18n("cm_order.internationalFee"),
  },
  {
    // 待发货
    value: "MALL_WAIT_SEND_OUT",
    label: authStore.i18n("cm_order.waitSendOut"),
  },
  // {
  //   // 国际运输
  //   value: "MALL_TRANSPORTING",
  //   label: authStore.i18n("cm_order.transporting"),
  // },
  // {
  //   // 清关中
  //   value: "MALL_CUSTOMS_CLEARING",
  //   label: authStore.i18n("cm_order.customsClearing"),
  // },
  {
    // 派送中
    value: "MALL_DELIVERING",
    label: authStore.i18n("cm_order.delivering"),
  },
  {
    // 已签收
    value: "MALL_USER_SIGNED",
    label: authStore.i18n("cm_order.userSigned"),
  },
  {
    // 已取消
    value: "MALL_CANCELED",
    label: authStore.i18n("cm_order.canceled"),
  },
]);

const pageData = reactive(<any>{
  mallOrderStatus: route?.query?.mallOrderStatus || "MALL_STATUS_ALL",
  orderList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 5,
  },
  isLoading: false,
  noOrderData: false,
});

onMounted(() => {
  window.addEventListener("scroll", onScrollBottom);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScrollBottom);
});

onGetOrderList();
async function onGetOrderList(scroll?: any) {
  const statusMap = <any>{
    MALL_WAIT_PAY: ["MALL_WAIT_PAY_PRODUCT", "MALL_WAIT_PAY_ALL_FEE"],
    MALL_INTERNATIONAL_FEE: [
      "MALL_WAIT_CAL_INTER_FEE",
      "MALL_WAIT_PAY_INTER_FEE",
    ],
    MALL_DELIVERING: [
      "MALL_TRANSPORTING",
      "MALL_CUSTOMS_CLEARING",
      "MALL_DELIVERING",
    ],
  };
  const res: any = await useGetOrderList({
    page: pageData.pageInfo,
    mallOrderStatusList: statusMap[pageData.mallOrderStatus] || [
      pageData.mallOrderStatus,
    ],
  });
  pageData.isLoading = false;
  if (res?.result?.code === 200) {
    if (scroll && !res?.data?.orderList?.length) {
      pageData.noOrderData = true;
      pageData.isLoading = false;
      return;
    }
    if (scroll) {
      pageData.orderList = pageData.orderList.concat(res?.data?.orderList);
    } else {
      pageData.orderList = res?.data.orderList;
      pageData.pageInfo = res?.data.page;
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    --pageData.pageInfo.current;
    showToast(res.result?.message);
  }
}

// 更新订单状态
function onUpdateOrderState() {
  onGetOrderList();
}

// 加载下一页
async function onScrollBottom() {
  if (pageData.isLoading || pageData.noOrderData) return;
  if (window.innerHeight + window.scrollY < document.body.scrollHeight) return; // 判断是否滚动到底部
  pageData.isLoading = true;
  pageData.pageInfo.current++;
  onGetOrderList("scroll");
}

function getSkuName(specs: any) {
  const names = specs?.map((spec: any) => {
    return `${spec.specName}: ${spec.itemName}`;
  });
  return names?.join("; ");
}

function onUpdateTab(val: any) {
  pageData.pageInfo.current = 1;
  const url = new URL(window.location.href);
  url.searchParams.set("mallOrderStatus", val);
  window.history.replaceState(null, "", url.toString());
  pageData.mallOrderStatus = val;
  onGetOrderList();
}

// 订单取消
function onOrderCancel(row: any) {
  orderCancelRef.value?.onOpenDialog(row.orderNo);
}

// 返回
function onBackClick() {
  router.go(-1);
}
</script>
<style scoped lang="scss">
.goodsName-ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
}

:deep(.n-tabs .n-tabs-nav-scroll-wrapper) {
  background: #fff;
  margin: 0.12rem 0.2rem;
  border-radius: 0.1rem;
  padding: 0 0.2rem;
}
:deep(.n-tabs .n-tab-pane) {
  padding: 0;
}
</style>
