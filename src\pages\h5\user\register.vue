<template>
  <div class="bg-[#8f0000] min-h-100vh pb-[1rem]">
    <div
      class="flex h-[0.96rem] border-b-1 border-solid border-gray-200 flex justify-center items-center bg-white"
    >
      <a
        class="flex justify-center items-center"
        href="/h5"
        data-spm-box="navigation-logo-icon"
      >
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          :src="pageTheme.logo"
          class="w-[3rem] mx-auto"
        />
      </a>
    </div>
    <div class="login-wrapper" :style="`background-image: url(${loginBg})`">
      <div class="bg-white py-[0.3rem] rounded-[0.16rem]">
        <div
          class="text-center font-medium my-[0.3rem] text-[0.38rem] leading-[0.56rem]"
        >
          {{ authStore.i18n("cm_common_register") }}
        </div>
        <div>
          <!-- 注册 -->
          <n-form
            :rules="rules"
            ref="regFromRef"
            class="px-[0.4rem]"
            :model="pageData.regForm"
          >
            <n-form-item
              path="username"
              :label="authStore.i18n('cm_common.username')"
            >
              <n-input
                v-trim
                clearable
                class="h-[0.96rem]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.regForm.username, 'email')"
                v-model:value="pageData.regForm.username"
                :placeholder="authStore.i18n('cm_common.inputEmail')"
              />
            </n-form-item>
            <n-form-item
              path="password"
              :label="authStore.i18n('cm_common.password')"
            >
              <n-input
                v-trim
                clearable
                class="h-[0.96rem]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.regForm.password, 'password')"
                :type="pageData.showPwd ? 'text' : 'password'"
                v-model:value="pageData.regForm.password"
                :placeholder="authStore.i18n('cm_common.inputPassword')"
              >
                <template #suffix>
                  <icon-card
                    size="24"
                    color="#7F7F7F"
                    class="cursor-pointer"
                    :name="
                      pageData.showPwd
                        ? 'weui:eyes-on-outlined'
                        : 'weui:eyes-off-outlined'
                    "
                    @click="pageData.showPwd = !pageData.showPwd"
                  /> </template
              ></n-input>
            </n-form-item>
            <!-- <n-form-item
              path="fromInviteCode"
              :label="authStore.i18n('cm_common.fromInviteCode')"
            >
              <n-input
                v-trim
                clearable
                class="h-[0.96rem]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.regForm.fromInviteCode, 'invite')"
                v-model:value="pageData.regForm.fromInviteCode"
                :placeholder="authStore.i18n('cm_common.inputFromInviteCode')"
              />
            </n-form-item> -->
            <div
              class="text-[0.28rem] leading-[0.4rem] h-[0.6rem]"
              :class="{ shake: pageData.shouldShake }"
              @animationend="pageData.shouldShake = false"
            >
              <n-checkbox v-model:checked="pageData.regForm.acceptTerms">
              </n-checkbox
              ><span class="ml-[0.16rem]"
                >{{ authStore.i18n("cm_common.readAgree") }}
                <span
                  class="text-[#034AA6] hover:underline cursor-pointer"
                  @click="onOpenAgree"
                  >{{ authStore.i18n("cm_common.viewAgree") }}</span
                ></span
              >
            </div>
            <n-form-item>
              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                class="rounded-[0.16rem] px-2 w-full h-[0.96rem] text-[0.32rem] leading-[0.4rem]"
                @click="onRegister"
              >
                {{ authStore.i18n("cm_common.regLogin") }}
              </n-button>
            </n-form-item>
            <div class="flex justify-end">
              <div
                class="text-[#034AA6] cursor-pointer text-[0.32rem] leading-[0.4rem]"
                @click="onGoLogin"
              >
                {{ authStore.i18n("cm_common_login") }}
              </div>
            </div>
          </n-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

import loginBg from "@/assets/icons/loginBg.jpg";

const route = useRoute();
const authStore = useAuthStore();
const regFromRef = ref<FormInst | null>(null);
const pageTheme = computed(() => useConfigStore().getPageTheme);
const pageData = reactive({
  showPwd: false,
  regForm: <any>{
    username: "",
    password: "",
    fromInviteCode: "",
    acceptTerms: false,
  },
  addCartGoods: {},
  shouldShake: false,
});

onMounted(() => {
  // 记录未登录首页是否打开过注册登录弹框
  if (route.query.pageSource === "/h5" || route.query.pageSource === "/h5/") {
    window?.MyStat.setSessionValue("isClickLoginModal", "true");
  }
  let loginInfo = sessionStorage.getItem("loginInfo");
  if (loginInfo) {
    const info = JSON.parse(loginInfo);
    pageData.regForm.username = info.username;
    pageData.regForm.password = info.password;
    pageData.regForm.fromInviteCode = info.fromInviteCode;
    pageData.regForm.acceptTerms = info.acceptTerms;
    sessionStorage.removeItem("loginInfo");
  }
  if (!pageData.regForm.fromInviteCode) {
    pageData.regForm.fromInviteCode = authStore.$state.fromInviteCode || "";
    if (pageData.regForm.fromInviteCode) {
      window?.MyStat?.addPageEvent(
        "passport_input_invite",
        "在账号窗口，输入了邀请码"
      );
    }
  }
});

const rules: FormRules = {
  username: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const pattern = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputEmailTips"));
      } else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_email_format_error",
          `邮箱格式错误，邮箱：${value}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.emailTips"));
      }
      return true;
    },
  },
  password: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const specialCharPattern = /[^A-Za-z\d]/; // 特殊字符匹配
      const pattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/; // 8-16位包含数字及字母
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputPwdTips"));
      }
      // 校验是否包含特殊字符
      else if (specialCharPattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdFormatTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdFormatTips"));
      }
      // 校验8-16位包含数字及字母
      else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdLengthTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdLengthTips"));
      }
      return true;
    },
  },
};

// 去登录
function onGoLogin() {
  const loginInfo = {
    username: pageData.regForm.username,
    password: pageData.regForm.password,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  window?.MyStat?.addPageEvent("passport_switch_login", "切换到登录TAB");
  navigateToPage("/h5/user/login", route.query, false);
}

// 注册
async function onRegister() {
  await regFromRef.value?.validate((errors: any) => {
    errors?.length &&
      window?.MyStat?.addPageEvent(
        "passport_register_verify_fail",
        `注册表单校验不通过：${errors.map((item: any) =>
          item.map((it: any) => it.message)
        )}`
      );
  });

  if (!pageData.regForm.acceptTerms) {
    window?.MyStat?.addPageEvent(
      "passport_unchoice_term",
      "用户注册，未勾选用户协议错误"
    );
    setTimeout(() => {
      pageData.shouldShake = true;
    }, 3100);
    return showToast(authStore.i18n("cm_common_needAgree"));
  }
  const res: any = await useRegister(pageData.regForm);
  if (res?.result?.code === 200) {
    if (!!window?.gtag) {
      window?.gtag("event", "RegisterSuccess");
    }
    // 密码修改成功之后登录
    onLogin({
      username: pageData.regForm.username,
      password: pageData.regForm.password,
    });
    authStore.setUserInfo(res?.result?.data);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_register_submit_error",
      `注册提交错误：${res.result?.message}`
    );
    showToast(res.result?.message);
  }
}

// 登录
async function onLogin(params: any) {
  try {
    const res: any = await useLogin(params);
    if (res?.result?.code === 200) {
      await authStore.setUserInfo(res?.data);
      await onAddCart();
      if (!!window?.gtag) {
        window?.gtag("event", "LoginSuccess");
      }
      window.location.replace("/h5/user/register-success");
    } else {
      showToast(res?.result?.message);
      window?.MyStat?.addPageEvent(
        "passport_login_submit_error",
        `登录提交错误：${res.result?.message}`
      );
    }
  } catch (error) {
    showToast(error);
  }
}

// 加购
async function onAddCart() {
  if (!route.query.goods) return;
  const res: any = await useAddCart(JSON.parse(route.query.goods));
  if (res.result?.code === 200) {
    showToast(
      authStore.i18n("cm_goods.addToList"),
      1500,
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/11/df87d5e1-099d-48ca-97a4-239bb000748a.png"
    );
    authStore.getCartList();
  } else {
    showToast(res.result?.message);
  }
}

function onOpenAgree() {
  const loginInfo = {
    username: pageData.regForm.username,
    password: pageData.regForm.password,
    fromInviteCode: pageData.regForm.fromInviteCode,
    acceptTerms: pageData.regForm.acceptTerms,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  navigateToPage("/h5/user/terms", route.query, false);
}

function onBlurEvent(val: any, type: string) {
  if (!val) return;
  const eventMap: { [key: string]: { event: string; message: string } } = {
    email: {
      event: "passport_input_email",
      message: "在账号窗口，输入了邮箱",
    },
    password: {
      event: "passport_input_password",
      message: "在账号窗口，输入了密码",
    },
    invite: {
      event: "passport_input_invite",
      message: "在账号窗口，输入了邀请码",
    },
    captcha: {
      event: "passport_input_verify_code",
      message: "在账号窗口，输入了验证码",
    },
  };

  const eventInfo = eventMap[type];
  if (eventInfo) {
    window?.MyStat?.addPageEvent(eventInfo.event, eventInfo.message);
  }
}
</script>

<style scoped lang="scss">
:deep(.n-input__input-el) {
  height: 0.96rem;
}
:deep(.n-form-item-label) {
  font-size: 0.28rem;
  line-height: 0.4rem;
}

.login-wrapper {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 100% 11rem;
  padding: 2.8rem 0.4rem 0;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  15% {
    transform: translateX(-0.1rem);
  }
  30% {
    transform: translateX(0.1rem);
  }
  45% {
    transform: translateX(-0.1rem);
  }
  60% {
    transform: translateX(0.1rem);
  }
  75% {
    transform: translateX(-0.1rem);
  }
  90% {
    transform: translateX(0.1rem);
  }
}

.shake {
  animation: shake 1.2s ease-in-out;
  will-change: transform;
}
:deep(.n-input__placeholder) {
  font-size: 0.28rem;
}
</style>
