<template>
  <ClientOnly>
    <div
      ref="floatingRef"
      class="floating-buttons"
      :style="{ left: `${position.left}px`, top: `${position.top}px` }"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
      v-if="
        showVideoPlaying ||
        showWhatsApp ||
        showCopyShortLink ||
        (showBackTopPage && pageData.showBackTop)
      "
    >
      <div
        v-if="showVideoPlaying"
        @click="onOpenVideo"
        class="video-playing inline-flex flex-col items-center justify-center gap-[0.04rem] px-[0.08rem] pt-[0.2rem] pb-[0.16rem] bg-white text-center"
      >
        <img
          class="w-[0.76rem]"
          src="@/assets/icons/common/playing.gif"
          :alt="authStore.i18n('cm_common.videoPlaying')"
        />
        <div class="w-[0.76rem] h-[0.4rem] overflow-hidden">
          <div
            class="text-[#4D4D4D] text-[0.24rem] origin-left scale-67 leading-[0.24rem] w-[1.12rem]"
          >
            {{ authStore.i18n("cm_common.videoPlaying") }}
          </div>
        </div>
      </div>
      <div class="buttons-wrapper">
        <div
          class="w-[0.68rem] h-[0.68rem] flex items-center justify-center"
          v-if="showWhatsApp"
        >
          <img
            alt="whatsapp"
            class="whatsapp-icon w-[0.48rem] h-[0.48rem]"
            @click="onWhatsAppClick"
            src="@/assets/icons/common/whatsapp.svg"
          />
        </div>

        <div
          class="w-[0.48rem] h-[0.48rem] flex items-center justify-center rounded-full"
          v-if="showCopyShortLink"
        >
          <img
            alt="copy"
            @click="onCopyShortLink"
            src="@/assets/icons/common/copy.svg"
          />
        </div>

        <!-- <div
          v-if="
            ($pwa?.showInstallPrompt || $pwa?.offlineReady) && showAddToDesktop
          "
          class="w-[0.48rem] h-[0.48rem] flex items-center justify-center rounded-full"
        >
          <img
            alt="download"
            @click="() => onAddToDesktop($pwa)"
            src="@/assets/icons/common/download.svg"
          />
        </div> -->

        <div
          v-show="pageData.showBackTop && showBackTopPage"
          class="w-[0.68rem] h-[0.68rem] flex items-center justify-center"
        >
          <img
            alt="backtop"
            @click="onBackTop"
            src="@/assets/icons/common/back-top.svg"
            class="hover:scale-141 transition-all duration-300"
          />
        </div>
      </div>
    </div>
    <video-modal ref="videoModalRef"></video-modal>
  </ClientOnly>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import _ from "lodash";

const route = useRoute();
const authStore = useAuthStore();
const videoModalRef = ref<any>(null);
const floatingRef = ref<HTMLElement | null>(null);

const videoShortsData = [
  {
    id: "0YCpG_CqNAE",
    title: "Sobre confianza con Chilat",
    titleCh: "关于与Chilat的信任",
    isVertical: true, // 竖屏视频
  },
  {
    id: "xW1j7rwWOso",
    title: "Los beneficios de colaborar con Chilat",
    titleCh: "与Chilat合作的好处",
    isVertical: true,
  },
  {
    id: "4j14aCzhZxk",
    title: "¿Quieres desarrollar tu propia marca?",
    titleCh: "想打造自己的品牌吗？",
    isVertical: true,
  },
  {
    id: "-XZkMkY__vn8",
    title: "¡Feliz colaboración! ¡Éxito en nuestra asociación!",
    titleCh: "合作愉快！共创成功！",
    isVertical: true,
  },
  {
    id: "9fqSJGC4ZlE",
    title: "Directo exclusivo de Chilatshop",
    titleCh: "Chilatshop独家直播",
    isVertical: true,
  },
];

const pageData = reactive({
  originLink: "",
  shortLink: "",
  showBackTop: false,
});

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

// 缓存当前路径，避免重复计算
const currentPath = computed(() => normalizePath(route.path));

// 计算组件宽度：首页显示video小窗时为46px，其他页面为40px
const componentWidth = computed(() =>
  currentPath.value === "/h5"
    ? 0.92 * resetFontSize.value
    : 0.8 * resetFontSize.value
);

const sideMargin = computed(() => 0.08 * resetFontSize.value);

const showVideoPlaying = computed(() => currentPath.value === "/h5");

const showWhatsApp = computed(
  () => !HIDDEN_PAGES.whatsApp.includes(currentPath.value)
);
const showCopyShortLink = computed(
  () =>
    !HIDDEN_PAGES.copyShortLink.includes(currentPath.value) &&
    route.query?.preview !== "true"
);
// const showAddToDesktop = computed(
//   () => !HIDDEN_PAGES.addToDesktop.includes(currentPath.value)
// );
const showBackTopPage = computed(
  () => !HIDDEN_PAGES.backTop.includes(currentPath.value)
);

const HIDDEN_PAGES = {
  whatsApp: [
    "/h5/notas",
    "/h5/open/notas",
    "/h5/vip",
    "/h5/viajar-a-china",
    "/inquiry",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
  copyShortLink: [
    "/open",
    "/h5/notas",
    "/h5/open/notas",
    "/h5/vip",
    "/h5/viajar-a-china",
    "/inquiry",
    "/h5/user/terms",
    "/h5/user/login",
    "/h5/user/register",
    "/h5/user/modifyPwd",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
  addToDesktop: [
    "/open",
    "/h5/notas",
    "/h5/open/notas",
    "/h5/vip",
    "/h5/viajar-a-china",
    "/inquiry",
    "/h5/user/terms",
    "/h5/user/login",
    "/h5/user/register",
    "/h5/user/modifyPwd",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
  backTop: [
    "/h5/user/terms",
    "/h5/user/login",
    "/h5/user/register",
    "/h5/user/modifyPwd",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
};

// 获取短链接，IOS系统不支持事件中的拷贝
function onCopyShortLink() {
  if (pageData.originLink === window.location.href && !!pageData.shortLink) {
    onCopyText(pageData.shortLink);
  } else {
    pageData.originLink = window.location.href;
    useShortLink({ url: window.location.href }).then((res: any) => {
      if (res?.result?.code === 200) {
        pageData.shortLink = res?.data?.shortUrl;
        onCopyText(pageData.shortLink);
      } else {
        showToast(
          res?.result?.message || authStore.i18n("cm_find.errorMessage")
        );
      }
    });
  }
}

function onAddToDesktop(pwa: any) {
  const onHandleResult = (result: any) => {
    const eventName =
      result.outcome === "accepted"
        ? "shortcut_desktop_install_success"
        : "shortcut_desktop_install_cancel";
    window?.MyStat?.addPageEvent(
      eventName,
      result.outcome === "accepted" ? "安装成功" : "安装取消"
    );
  };

  const onHandleError = () => {
    window?.MyStat?.addPageEvent("shortcut_desktop_install_fail", "安装失败");
  };

  if (pwa?.offlineReady) {
    pwa.cancelPrompt();
    setTimeout(
      () => pwa.install().then(onHandleResult).catch(onHandleError),
      2000
    );
  } else {
    pwa.install().then(onHandleResult).catch(onHandleError);
  }
}

function onBackTop() {
  window.scrollTo({ top: 0, behavior: "smooth" });
}

// 节流处理滚动事件，提升性能
const throttledScroll = _.throttle((e: any) => {
  let scrollHeight =
    document.documentElement.scrollTop ||
    document.body.scrollTop ||
    e.target.scrollTop;
  pageData.showBackTop = scrollHeight > window.innerHeight / 2;
}, 100);

onMounted(() => {
  // 使用动态宽度计算初始位置
  position.left = window.innerWidth - componentWidth.value - sideMargin.value;
  position.top = window.innerHeight / 2;

  window.addEventListener("scroll", throttledScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", throttledScroll);
});

// 监听组件宽度变化，调整位置
watch(componentWidth, (newWidth, oldWidth) => {
  if (newWidth !== oldWidth) {
    // 如果当前在右侧，需要调整位置
    const margin = sideMargin.value;
    const middle = window.innerWidth / 2;
    if (position.left > middle) {
      position.left = window.innerWidth - newWidth - margin;
    }
  }
});

const position = reactive({
  left: 0,
  top: 0,
});

let startX = 0,
  startY = 0,
  startLeft = 0,
  startTop = 0;

function onTouchStart(e: TouchEvent) {
  const touch = e.touches[0];
  startX = touch.clientX;
  startY = touch.clientY;
  startLeft = position.left;
  startTop = position.top;
}

function onTouchMove(e: TouchEvent) {
  // 仅当拖动悬浮按钮时，阻止页面滚动
  e.preventDefault();

  const touch = e.touches[0];
  const deltaX = touch.clientX - startX;
  const deltaY = touch.clientY - startY;

  // 使用动态宽度计算边界
  const maxLeft = window.innerWidth - componentWidth.value;
  const newLeft = startLeft + deltaX;
  position.left = Math.min(Math.max(newLeft, 0), maxLeft);

  const floatHeight = floatingRef.value?.offsetHeight || 120; // fallback
  const maxTop = window.innerHeight - floatHeight;
  const newTop = startTop + deltaY;
  position.top = Math.min(Math.max(newTop, 0), maxTop);
}

function onTouchEnd() {
  const middle = window.innerWidth / 2;
  const width = componentWidth.value;
  position.left =
    position.left < middle
      ? sideMargin.value
      : window.innerWidth - width - sideMargin.value;
}

function onOpenVideo() {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(videoShortsData[0]);
  }
}
</script>

<style scoped lang="scss">
.floating-buttons {
  position: fixed;
  z-index: 999;
  transition: left 0.3s ease, top 0.3s ease;
  touch-action: none; // 防止按钮拖拽时页面滚动
  display: flex;
  flex-direction: column;
  align-items: center;
}

.buttons-wrapper {
  width: 0.8rem;
  height: auto;
  padding: 0.1rem 0.06rem;
  display: flex;
  flex-direction: column;
  gap: 0.08rem;
  align-items: center;
  background: #fff;
  border-radius: 0.4rem;
  box-shadow: 0 0 0.1rem rgba(0, 0, 0, 0.1);
}

.whatsapp-icon {
  width: 0.48rem;
  height: 0.48rem;
  &:hover {
    width: 0.68rem;
    height: 0.68rem;
    content: url("@/assets/icons/common/whatsapp-active.svg");
  }
}

.video-playing {
  width: 0.92rem;
  border-radius: 10rem;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 0.1rem 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(0.5rem);
  margin-bottom: 0.28rem;
}
</style>
