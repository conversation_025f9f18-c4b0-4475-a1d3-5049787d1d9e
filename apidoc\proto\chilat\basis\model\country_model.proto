syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.model";
import "common.proto";

message CountryResp {
  common.Result result = 1;
  repeated CountryModel data = 2; //国家
}

message CountryModel {
  string id = 1;
  string countryName = 11; //国家名称（使用商城前台语言）
  string countryCnName = 2; //国家中文名
  string countryEnName = 3; //国家英文名
  string countryEsName = 4; //国家西文名
  string countryCodeTwo = 5; //国家二字代码
  string countryCodeThree = 6; //国家三字代码
  string countryLogo = 120; //国家LOGO图片地址
  string areaCode = 7; //区号
  repeated string phoneRegexes = 9; //电话格式
  string phoneCount = 10; //电话位数
}

message CountryInfoResp {
  common.Result result = 1;
  CountryModel data = 2; //国家
}

message ListRegionResp {
  common.Result result = 1;
  repeated RegionModel data = 2;
}

message RegionModel {
  string id = 1;
  string parentCode = 2; //父级编号
  string name = 3; //名称
  string code = 4; //编号
  string level = 5; //层级
  int32 sort = 6; //排序
  repeated RegionModel children = 7;
  repeated string communityAddress = 8;
}

message RegionResp {
  common.Result result = 1;
  RegionModel data = 2;
}