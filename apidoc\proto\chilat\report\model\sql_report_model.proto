syntax = "proto3";
package chilat.report;

option java_package = "com.chilat.rpc.report.model";

import "common.proto";
message SqlReportPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated SqlReportListModel data = 3;
}

// SQL报表列表数据
message SqlReportListModel {
  string id = 10; //报表ID
  string reportName = 20; //报表名称
  string pagePath = 21; //页面路径（“页面链接”字段，显示的名称）
  string pageUrl = 22; //页面链接（鼠标移到“页面路径”上时提示；点击时，以新窗口打开的链接）
  string description = 30; //说明（Text格式，前端显示时，类似于<PRE>标签格式显示）
  int32 queryCount = 40; //查询次数
  int32 exportCount = 50; //导出次数
  int64 createTime = 110; //创建时间
  int64 modifyTime = 120; //修改时间
}

message SqlReportDataTableResp {
  common.Result result = 1;
  SqlReportDataTableModel data = 2;
}

message SqlReportDataTableModel {
  string id = 10; //报表ID
  string reportName = 20; //报表名称
  string htmlTable = 30; //HTML格式的数据表格
  SqlReportChartData chartData = 40; //图表数据（非空，表示可制图）
}

// SQL报表筛选参数列表
message SqlReportFilterOptionsResp {
  common.Result result = 1;
  SqlReportFilterInfoModel data = 2;
}

// SQL报表筛选参数列表数据
message SqlReportFilterInfoModel {
  string reportId = 10; //报表ID
  string reportName = 20; //报表名称
  repeated SqlReportFilterItemModel filterOptions = 30; //过滤参数选项
}

// SQL报表筛选参数列表数据
message SqlReportFilterItemModel {
  int32 rowNo = 10; //显示行号（从1开始）
  string displayName = 20; //显示字段名（在页面上显示的名称）
  string fieldName = 30; //提交字段名（查询报表时的名称）
  string fieldType = 40; //字段类型（可选值：date 日期，input 输入框，select 下拉菜单）
  string defaultValue = 50; //默认值
  repeated common.NameValueModel options = 60; //select类型的选项（生成option标签的例子：<option value='{value}' 'IF(value=defaultValue) selected'>{name}</option>）
}

// 图表数据对象
message SqlReportChartData {
  string xFieldName = 10; // X轴字段名
  repeated string xFieldData = 20; // X轴字段值列表
  repeated SqlReportChartField yFieldList = 30; // Y轴字段数据列表
}

// 图表Y轴字段对象
message SqlReportChartField {
  string name = 10; // Y轴指标名称
  repeated double data = 20; // Y轴指标数据
  string formatter = 30; // Y轴指标格式方法
}

