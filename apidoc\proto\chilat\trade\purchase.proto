syntax = "proto3";
package chilat.trade;

option java_package = "com.chilat.rpc.trade";

import "chilat/trade/param/purchase_param.proto";
import "chilat/trade/model/purchase_model.proto";
import "common.proto";

// 采购管理
service Purchase {
  // 采购单列表
  rpc pageList (PurchasePageQueryParam) returns (PurchasePageResp);
  // 保存采购单
  rpc save (PurchaseSaveParam) returns (common.ApiResult);
  // 取消采购单
  rpc cancel (PurchaseOrderParam) returns (PurchaseCancelResp);
  // 下单详情
  rpc previewOrder (PurchaseOrderParam) returns (PurchaseOrderResp);
  // 1688下单
  rpc createOrder (PurchaseSaveParam) returns (common.ApiResult);
  // 支付链接
  rpc payLink (PurchaseOrderParam) returns (PurchasePayLinkResp);
  // 内采订单列表
  rpc orderPageList (PurchaseOrderPageQueryParam) returns (PurchaseOrderPageResp);
  // 更新订单
  rpc updateOrderView (PurchaseOrderParam) returns (common.ApiResult);
  // 采购单日志
  rpc log (common.IdParam) returns (common.LogResult);
  // 内采单日志
  rpc innerLog (common.IdParam) returns (common.LogResult);

  //导出采购单，传入采购单号
  rpc exportPurchase(common.IdParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };

  // 采购线下下单
  rpc offlineCreateOrder (PurchaseOfflineOrderParam) returns (common.ApiResult);

  // 采购线下下单订单支付
  rpc offlineOrderPay (PurchaseOfflineOrderPayParam) returns (common.ApiResult);

  //  采购线下下单订单发货
  rpc offlineOrderDelivery (PurchaseOfflineOrderDeliveryParam) returns (common.ApiResult);

  // 采购线下下单确认收货
  rpc offlineOrderReceived (PurchaseOfflineOrderReceivedParam) returns (common.ApiResult);
}