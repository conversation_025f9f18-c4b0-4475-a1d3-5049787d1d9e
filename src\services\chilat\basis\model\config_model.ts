/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Page, Result } from "../../../common";

export const protobufPackage = "chilat.basis";

export interface ConfigResp {
  result: Result | undefined;
  data: ConfigModel | undefined;
}

export interface LanguageResp {
  result: Result | undefined;
  data: LanguageModel | undefined;
}

/** 全局配置 */
export interface ConfigModel {
  /** 当前语言 */
  lang: string;
  /** 所有语言 */
  langs: { [key: string]: string };
  /** 语言翻译 */
  trans: { [key: string]: string };
}

export interface ConfigModel_LangsEntry {
  key: string;
  value: string;
}

export interface ConfigModel_TransEntry {
  key: string;
  value: string;
}

/** 语言包 */
export interface LanguageModel {
  /** 语言翻译 */
  trans: { [key: string]: string };
}

export interface LanguageModel_TransEntry {
  key: string;
  value: string;
}

/** 翻译字典表 */
export interface SysTranslateModel {
  id: string;
  systemId: number;
  moduleCode: string;
  tranCode: string;
  termZH: string;
  termEN: string;
  termES: string;
  moduleDesc: string;
}

export interface TranslatePageResp {
  result: Result | undefined;
  page: Page | undefined;
  data: SysTranslateModel[];
}

function createBaseConfigResp(): ConfigResp {
  return { result: undefined, data: undefined };
}

export const ConfigResp = {
  encode(message: ConfigResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      ConfigModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ConfigResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfigResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = ConfigModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfigResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? ConfigModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: ConfigResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = ConfigModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfigResp>, I>>(base?: I): ConfigResp {
    return ConfigResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigResp>, I>>(object: I): ConfigResp {
    const message = createBaseConfigResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? ConfigModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseLanguageResp(): LanguageResp {
  return { result: undefined, data: undefined };
}

export const LanguageResp = {
  encode(message: LanguageResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      LanguageModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LanguageResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLanguageResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = LanguageModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LanguageResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? LanguageModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: LanguageResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = LanguageModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LanguageResp>, I>>(base?: I): LanguageResp {
    return LanguageResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LanguageResp>, I>>(object: I): LanguageResp {
    const message = createBaseLanguageResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? LanguageModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseConfigModel(): ConfigModel {
  return { lang: "", langs: {}, trans: {} };
}

export const ConfigModel = {
  encode(message: ConfigModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.lang !== "") {
      writer.uint32(10).string(message.lang);
    }
    Object.entries(message.langs).forEach(([key, value]) => {
      ConfigModel_LangsEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).ldelim();
    });
    Object.entries(message.trans).forEach(([key, value]) => {
      ConfigModel_TransEntry.encode({ key: key as any, value }, writer.uint32(26).fork()).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ConfigModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfigModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.lang = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          const entry2 = ConfigModel_LangsEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.langs[entry2.key] = entry2.value;
          }
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          const entry3 = ConfigModel_TransEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.trans[entry3.key] = entry3.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfigModel {
    return {
      lang: isSet(object.lang) ? globalThis.String(object.lang) : "",
      langs: isObject(object.langs)
        ? Object.entries(object.langs).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
      trans: isObject(object.trans)
        ? Object.entries(object.trans).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: ConfigModel): unknown {
    const obj: any = {};
    if (message.lang !== "") {
      obj.lang = message.lang;
    }
    if (message.langs) {
      const entries = Object.entries(message.langs);
      if (entries.length > 0) {
        obj.langs = {};
        entries.forEach(([k, v]) => {
          obj.langs[k] = v;
        });
      }
    }
    if (message.trans) {
      const entries = Object.entries(message.trans);
      if (entries.length > 0) {
        obj.trans = {};
        entries.forEach(([k, v]) => {
          obj.trans[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfigModel>, I>>(base?: I): ConfigModel {
    return ConfigModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigModel>, I>>(object: I): ConfigModel {
    const message = createBaseConfigModel();
    message.lang = object.lang ?? "";
    message.langs = Object.entries(object.langs ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.trans = Object.entries(object.trans ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseConfigModel_LangsEntry(): ConfigModel_LangsEntry {
  return { key: "", value: "" };
}

export const ConfigModel_LangsEntry = {
  encode(message: ConfigModel_LangsEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ConfigModel_LangsEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfigModel_LangsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfigModel_LangsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: ConfigModel_LangsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfigModel_LangsEntry>, I>>(base?: I): ConfigModel_LangsEntry {
    return ConfigModel_LangsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigModel_LangsEntry>, I>>(object: I): ConfigModel_LangsEntry {
    const message = createBaseConfigModel_LangsEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseConfigModel_TransEntry(): ConfigModel_TransEntry {
  return { key: "", value: "" };
}

export const ConfigModel_TransEntry = {
  encode(message: ConfigModel_TransEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ConfigModel_TransEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfigModel_TransEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfigModel_TransEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: ConfigModel_TransEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfigModel_TransEntry>, I>>(base?: I): ConfigModel_TransEntry {
    return ConfigModel_TransEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigModel_TransEntry>, I>>(object: I): ConfigModel_TransEntry {
    const message = createBaseConfigModel_TransEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseLanguageModel(): LanguageModel {
  return { trans: {} };
}

export const LanguageModel = {
  encode(message: LanguageModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    Object.entries(message.trans).forEach(([key, value]) => {
      LanguageModel_TransEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LanguageModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLanguageModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          const entry1 = LanguageModel_TransEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.trans[entry1.key] = entry1.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LanguageModel {
    return {
      trans: isObject(object.trans)
        ? Object.entries(object.trans).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: LanguageModel): unknown {
    const obj: any = {};
    if (message.trans) {
      const entries = Object.entries(message.trans);
      if (entries.length > 0) {
        obj.trans = {};
        entries.forEach(([k, v]) => {
          obj.trans[k] = v;
        });
      }
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LanguageModel>, I>>(base?: I): LanguageModel {
    return LanguageModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LanguageModel>, I>>(object: I): LanguageModel {
    const message = createBaseLanguageModel();
    message.trans = Object.entries(object.trans ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseLanguageModel_TransEntry(): LanguageModel_TransEntry {
  return { key: "", value: "" };
}

export const LanguageModel_TransEntry = {
  encode(message: LanguageModel_TransEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LanguageModel_TransEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLanguageModel_TransEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LanguageModel_TransEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: LanguageModel_TransEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LanguageModel_TransEntry>, I>>(base?: I): LanguageModel_TransEntry {
    return LanguageModel_TransEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LanguageModel_TransEntry>, I>>(object: I): LanguageModel_TransEntry {
    const message = createBaseLanguageModel_TransEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseSysTranslateModel(): SysTranslateModel {
  return { id: "", systemId: 0, moduleCode: "", tranCode: "", termZH: "", termEN: "", termES: "", moduleDesc: "" };
}

export const SysTranslateModel = {
  encode(message: SysTranslateModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.systemId !== 0) {
      writer.uint32(16).int32(message.systemId);
    }
    if (message.moduleCode !== "") {
      writer.uint32(26).string(message.moduleCode);
    }
    if (message.tranCode !== "") {
      writer.uint32(34).string(message.tranCode);
    }
    if (message.termZH !== "") {
      writer.uint32(42).string(message.termZH);
    }
    if (message.termEN !== "") {
      writer.uint32(50).string(message.termEN);
    }
    if (message.termES !== "") {
      writer.uint32(58).string(message.termES);
    }
    if (message.moduleDesc !== "") {
      writer.uint32(66).string(message.moduleDesc);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SysTranslateModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSysTranslateModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.systemId = reader.int32();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.moduleCode = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.tranCode = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.termZH = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.termEN = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.termES = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.moduleDesc = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SysTranslateModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      systemId: isSet(object.systemId) ? globalThis.Number(object.systemId) : 0,
      moduleCode: isSet(object.moduleCode) ? globalThis.String(object.moduleCode) : "",
      tranCode: isSet(object.tranCode) ? globalThis.String(object.tranCode) : "",
      termZH: isSet(object.termZH) ? globalThis.String(object.termZH) : "",
      termEN: isSet(object.termEN) ? globalThis.String(object.termEN) : "",
      termES: isSet(object.termES) ? globalThis.String(object.termES) : "",
      moduleDesc: isSet(object.moduleDesc) ? globalThis.String(object.moduleDesc) : "",
    };
  },

  toJSON(message: SysTranslateModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.systemId !== 0) {
      obj.systemId = Math.round(message.systemId);
    }
    if (message.moduleCode !== "") {
      obj.moduleCode = message.moduleCode;
    }
    if (message.tranCode !== "") {
      obj.tranCode = message.tranCode;
    }
    if (message.termZH !== "") {
      obj.termZH = message.termZH;
    }
    if (message.termEN !== "") {
      obj.termEN = message.termEN;
    }
    if (message.termES !== "") {
      obj.termES = message.termES;
    }
    if (message.moduleDesc !== "") {
      obj.moduleDesc = message.moduleDesc;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SysTranslateModel>, I>>(base?: I): SysTranslateModel {
    return SysTranslateModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SysTranslateModel>, I>>(object: I): SysTranslateModel {
    const message = createBaseSysTranslateModel();
    message.id = object.id ?? "";
    message.systemId = object.systemId ?? 0;
    message.moduleCode = object.moduleCode ?? "";
    message.tranCode = object.tranCode ?? "";
    message.termZH = object.termZH ?? "";
    message.termEN = object.termEN ?? "";
    message.termES = object.termES ?? "";
    message.moduleDesc = object.moduleDesc ?? "";
    return message;
  },
};

function createBaseTranslatePageResp(): TranslatePageResp {
  return { result: undefined, page: undefined, data: [] };
}

export const TranslatePageResp = {
  encode(message: TranslatePageResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.page !== undefined) {
      Page.encode(message.page, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.data) {
      SysTranslateModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): TranslatePageResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTranslatePageResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.page = Page.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(SysTranslateModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TranslatePageResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => SysTranslateModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: TranslatePageResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.page !== undefined) {
      obj.page = Page.toJSON(message.page);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => SysTranslateModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TranslatePageResp>, I>>(base?: I): TranslatePageResp {
    return TranslatePageResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TranslatePageResp>, I>>(object: I): TranslatePageResp {
    const message = createBaseTranslatePageResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.page = (object.page !== undefined && object.page !== null) ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map((e) => SysTranslateModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
