syntax = "proto3";
package chilat.coupon;

import "chilat/coupon/model/coupon_info_model.proto";
import "chilat/coupon/model/coupon_info_page_list_model.proto";
import "chilat/coupon/param/coupon_info_param.proto";
import "common.proto";

option java_package = "com.chilat.rpc.coupon";

// 卡券管理
service CouponInfoService {
  // 卡券保存
  rpc saveCouponInfo(SaveCouponInfoParam) returns (CouponSaveUpdateResp);
  // 卡券删除
  rpc removeCouponInfo(common.IdParam) returns (common.ApiResult);
  // 失效/生效
  rpc couponActiveEnable (common.EnabledParam) returns (common.ApiResult);
  // 优惠券列表查询
  rpc getCouponInfoList (CouponInfoPageQueryParam) returns (CouponInfoPageResp);

  // 优惠券详情查询
  rpc getCouponInfoDetail (common.IdParam) returns (CouponDetailModelResp);

  //获取优惠券活动代码列表（仅取可用）
  rpc getCouponActivityCodes(common.EmptyParam) returns (GetCouponActivityCodesResp);

  //发送优惠券给指定用户
  rpc sendCouponToUser(SendCouponToUserParam) returns (common.ApiResult);

}