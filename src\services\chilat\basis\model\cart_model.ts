/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Result } from "../../../common";
import { SkuStepPrice } from "../../../common/business";
import { GoodsExtendItem } from "../../commodity/commodity_common";
import { InquiryModel } from "./goods_looking_model";

export const protobufPackage = "chilat.basis";

/** 购物车统计数据 */
export interface MidCartStatResp {
  result: Result | undefined;
  data: MidCartStatModel | undefined;
}

/** 购物车统计数据 */
export interface MidCartStatModel {
  /** 上架SKU数量 */
  onlineSkuCount: number;
  /** 选择的SKU行数 */
  selectSkuCount: number;
  /** 商品种类数 */
  goodsCount: number;
  /** sku种类数 */
  skuCount: number;
  /** 总销售额 */
  totalSalePrice: number;
  /** 选择的SKU总数 */
  selectSkuTotalQuantity: number;
  /** 总选择销售额 */
  selectTotalSalePrice: number;
}

export interface MidCartResp {
  result: Result | undefined;
  data: MidCartModel | undefined;
}

export interface MidCartModel {
  stat: MidCartStatModel | undefined;
  goodsList: MidCartGoodsModel[];
  /** 上次询盘信息 */
  lastInquiry: InquiryModel | undefined;
}

export interface MidCartGoodsModel {
  /** 商品ID */
  goodsId: string;
  /** 商品编号-主数据 */
  goodsNo: string;
  /** 商品名称-主数据 */
  goodsName: string;
  /** 商品标题-主数据 */
  goodsTitle: string;
  /** 最小购买数量（起订量） */
  minBuyQuantity: number;
  /** 商品计价单位名称 */
  goodsPriceUnitName: string;
  /** 最小加购数量 */
  minIncreaseQuantity: number;
  /** 商品主图 */
  mainImageUrl: string;
  /** 商品SKU列表 */
  skuList: MidCartSkuModel[];
  /** 规格参数 */
  specItemList: GoodsExtendItem[];
}

/** 商品SKU信息 */
export interface MidCartSkuModel {
  /** SKU ID（必填） */
  skuId: string;
  /** SKU商品料号（必填） */
  skuNo: string;
  specItemList: GoodsExtendItem[];
  /** 库存数量 */
  stockQty: number;
  /** 是否已选中 */
  selected: boolean;
  /** 销售价 */
  salePrice: number;
  /** 加购数量 */
  buyQty: number;
  /** 销售价小计 */
  subtotalSalePrice: number;
  /** 商品图片 */
  skuImage: string;
  /** 商品ID */
  goodsId: string;
  /** 阶梯价 */
  stepPrices: SkuStepPrice[];
  /** SPM跟踪码 */
  spm: string;
}

function createBaseMidCartStatResp(): MidCartStatResp {
  return { result: undefined, data: undefined };
}

export const MidCartStatResp = {
  encode(message: MidCartStatResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      MidCartStatModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCartStatResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCartStatResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = MidCartStatModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCartStatResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? MidCartStatModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: MidCartStatResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = MidCartStatModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCartStatResp>, I>>(base?: I): MidCartStatResp {
    return MidCartStatResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCartStatResp>, I>>(object: I): MidCartStatResp {
    const message = createBaseMidCartStatResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? MidCartStatModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseMidCartStatModel(): MidCartStatModel {
  return {
    onlineSkuCount: 0,
    selectSkuCount: 0,
    goodsCount: 0,
    skuCount: 0,
    totalSalePrice: 0,
    selectSkuTotalQuantity: 0,
    selectTotalSalePrice: 0,
  };
}

export const MidCartStatModel = {
  encode(message: MidCartStatModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.onlineSkuCount !== 0) {
      writer.uint32(8).int32(message.onlineSkuCount);
    }
    if (message.selectSkuCount !== 0) {
      writer.uint32(16).int32(message.selectSkuCount);
    }
    if (message.goodsCount !== 0) {
      writer.uint32(24).int32(message.goodsCount);
    }
    if (message.skuCount !== 0) {
      writer.uint32(32).int32(message.skuCount);
    }
    if (message.totalSalePrice !== 0) {
      writer.uint32(41).double(message.totalSalePrice);
    }
    if (message.selectSkuTotalQuantity !== 0) {
      writer.uint32(48).int32(message.selectSkuTotalQuantity);
    }
    if (message.selectTotalSalePrice !== 0) {
      writer.uint32(57).double(message.selectTotalSalePrice);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCartStatModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCartStatModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.onlineSkuCount = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.selectSkuCount = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.goodsCount = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.skuCount = reader.int32();
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }

          message.totalSalePrice = reader.double();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.selectSkuTotalQuantity = reader.int32();
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }

          message.selectTotalSalePrice = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCartStatModel {
    return {
      onlineSkuCount: isSet(object.onlineSkuCount) ? globalThis.Number(object.onlineSkuCount) : 0,
      selectSkuCount: isSet(object.selectSkuCount) ? globalThis.Number(object.selectSkuCount) : 0,
      goodsCount: isSet(object.goodsCount) ? globalThis.Number(object.goodsCount) : 0,
      skuCount: isSet(object.skuCount) ? globalThis.Number(object.skuCount) : 0,
      totalSalePrice: isSet(object.totalSalePrice) ? globalThis.Number(object.totalSalePrice) : 0,
      selectSkuTotalQuantity: isSet(object.selectSkuTotalQuantity)
        ? globalThis.Number(object.selectSkuTotalQuantity)
        : 0,
      selectTotalSalePrice: isSet(object.selectTotalSalePrice) ? globalThis.Number(object.selectTotalSalePrice) : 0,
    };
  },

  toJSON(message: MidCartStatModel): unknown {
    const obj: any = {};
    if (message.onlineSkuCount !== 0) {
      obj.onlineSkuCount = Math.round(message.onlineSkuCount);
    }
    if (message.selectSkuCount !== 0) {
      obj.selectSkuCount = Math.round(message.selectSkuCount);
    }
    if (message.goodsCount !== 0) {
      obj.goodsCount = Math.round(message.goodsCount);
    }
    if (message.skuCount !== 0) {
      obj.skuCount = Math.round(message.skuCount);
    }
    if (message.totalSalePrice !== 0) {
      obj.totalSalePrice = message.totalSalePrice;
    }
    if (message.selectSkuTotalQuantity !== 0) {
      obj.selectSkuTotalQuantity = Math.round(message.selectSkuTotalQuantity);
    }
    if (message.selectTotalSalePrice !== 0) {
      obj.selectTotalSalePrice = message.selectTotalSalePrice;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCartStatModel>, I>>(base?: I): MidCartStatModel {
    return MidCartStatModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCartStatModel>, I>>(object: I): MidCartStatModel {
    const message = createBaseMidCartStatModel();
    message.onlineSkuCount = object.onlineSkuCount ?? 0;
    message.selectSkuCount = object.selectSkuCount ?? 0;
    message.goodsCount = object.goodsCount ?? 0;
    message.skuCount = object.skuCount ?? 0;
    message.totalSalePrice = object.totalSalePrice ?? 0;
    message.selectSkuTotalQuantity = object.selectSkuTotalQuantity ?? 0;
    message.selectTotalSalePrice = object.selectTotalSalePrice ?? 0;
    return message;
  },
};

function createBaseMidCartResp(): MidCartResp {
  return { result: undefined, data: undefined };
}

export const MidCartResp = {
  encode(message: MidCartResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      MidCartModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCartResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCartResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = MidCartModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCartResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? MidCartModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: MidCartResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = MidCartModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCartResp>, I>>(base?: I): MidCartResp {
    return MidCartResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCartResp>, I>>(object: I): MidCartResp {
    const message = createBaseMidCartResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? MidCartModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseMidCartModel(): MidCartModel {
  return { stat: undefined, goodsList: [], lastInquiry: undefined };
}

export const MidCartModel = {
  encode(message: MidCartModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.stat !== undefined) {
      MidCartStatModel.encode(message.stat, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.goodsList) {
      MidCartGoodsModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    if (message.lastInquiry !== undefined) {
      InquiryModel.encode(message.lastInquiry, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCartModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCartModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.stat = MidCartStatModel.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.goodsList.push(MidCartGoodsModel.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.lastInquiry = InquiryModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCartModel {
    return {
      stat: isSet(object.stat) ? MidCartStatModel.fromJSON(object.stat) : undefined,
      goodsList: globalThis.Array.isArray(object?.goodsList)
        ? object.goodsList.map((e: any) => MidCartGoodsModel.fromJSON(e))
        : [],
      lastInquiry: isSet(object.lastInquiry) ? InquiryModel.fromJSON(object.lastInquiry) : undefined,
    };
  },

  toJSON(message: MidCartModel): unknown {
    const obj: any = {};
    if (message.stat !== undefined) {
      obj.stat = MidCartStatModel.toJSON(message.stat);
    }
    if (message.goodsList?.length) {
      obj.goodsList = message.goodsList.map((e) => MidCartGoodsModel.toJSON(e));
    }
    if (message.lastInquiry !== undefined) {
      obj.lastInquiry = InquiryModel.toJSON(message.lastInquiry);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCartModel>, I>>(base?: I): MidCartModel {
    return MidCartModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCartModel>, I>>(object: I): MidCartModel {
    const message = createBaseMidCartModel();
    message.stat = (object.stat !== undefined && object.stat !== null)
      ? MidCartStatModel.fromPartial(object.stat)
      : undefined;
    message.goodsList = object.goodsList?.map((e) => MidCartGoodsModel.fromPartial(e)) || [];
    message.lastInquiry = (object.lastInquiry !== undefined && object.lastInquiry !== null)
      ? InquiryModel.fromPartial(object.lastInquiry)
      : undefined;
    return message;
  },
};

function createBaseMidCartGoodsModel(): MidCartGoodsModel {
  return {
    goodsId: "",
    goodsNo: "",
    goodsName: "",
    goodsTitle: "",
    minBuyQuantity: 0,
    goodsPriceUnitName: "",
    minIncreaseQuantity: 0,
    mainImageUrl: "",
    skuList: [],
    specItemList: [],
  };
}

export const MidCartGoodsModel = {
  encode(message: MidCartGoodsModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.goodsId !== "") {
      writer.uint32(10).string(message.goodsId);
    }
    if (message.goodsNo !== "") {
      writer.uint32(18).string(message.goodsNo);
    }
    if (message.goodsName !== "") {
      writer.uint32(26).string(message.goodsName);
    }
    if (message.goodsTitle !== "") {
      writer.uint32(34).string(message.goodsTitle);
    }
    if (message.minBuyQuantity !== 0) {
      writer.uint32(40).int32(message.minBuyQuantity);
    }
    if (message.goodsPriceUnitName !== "") {
      writer.uint32(50).string(message.goodsPriceUnitName);
    }
    if (message.minIncreaseQuantity !== 0) {
      writer.uint32(56).int32(message.minIncreaseQuantity);
    }
    if (message.mainImageUrl !== "") {
      writer.uint32(66).string(message.mainImageUrl);
    }
    for (const v of message.skuList) {
      MidCartSkuModel.encode(v!, writer.uint32(74).fork()).ldelim();
    }
    for (const v of message.specItemList) {
      GoodsExtendItem.encode(v!, writer.uint32(82).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCartGoodsModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCartGoodsModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.goodsNo = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.goodsName = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.goodsTitle = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.minBuyQuantity = reader.int32();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.goodsPriceUnitName = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.minIncreaseQuantity = reader.int32();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.mainImageUrl = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.skuList.push(MidCartSkuModel.decode(reader, reader.uint32()));
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.specItemList.push(GoodsExtendItem.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCartGoodsModel {
    return {
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      goodsNo: isSet(object.goodsNo) ? globalThis.String(object.goodsNo) : "",
      goodsName: isSet(object.goodsName) ? globalThis.String(object.goodsName) : "",
      goodsTitle: isSet(object.goodsTitle) ? globalThis.String(object.goodsTitle) : "",
      minBuyQuantity: isSet(object.minBuyQuantity) ? globalThis.Number(object.minBuyQuantity) : 0,
      goodsPriceUnitName: isSet(object.goodsPriceUnitName) ? globalThis.String(object.goodsPriceUnitName) : "",
      minIncreaseQuantity: isSet(object.minIncreaseQuantity) ? globalThis.Number(object.minIncreaseQuantity) : 0,
      mainImageUrl: isSet(object.mainImageUrl) ? globalThis.String(object.mainImageUrl) : "",
      skuList: globalThis.Array.isArray(object?.skuList)
        ? object.skuList.map((e: any) => MidCartSkuModel.fromJSON(e))
        : [],
      specItemList: globalThis.Array.isArray(object?.specItemList)
        ? object.specItemList.map((e: any) => GoodsExtendItem.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidCartGoodsModel): unknown {
    const obj: any = {};
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.goodsNo !== "") {
      obj.goodsNo = message.goodsNo;
    }
    if (message.goodsName !== "") {
      obj.goodsName = message.goodsName;
    }
    if (message.goodsTitle !== "") {
      obj.goodsTitle = message.goodsTitle;
    }
    if (message.minBuyQuantity !== 0) {
      obj.minBuyQuantity = Math.round(message.minBuyQuantity);
    }
    if (message.goodsPriceUnitName !== "") {
      obj.goodsPriceUnitName = message.goodsPriceUnitName;
    }
    if (message.minIncreaseQuantity !== 0) {
      obj.minIncreaseQuantity = Math.round(message.minIncreaseQuantity);
    }
    if (message.mainImageUrl !== "") {
      obj.mainImageUrl = message.mainImageUrl;
    }
    if (message.skuList?.length) {
      obj.skuList = message.skuList.map((e) => MidCartSkuModel.toJSON(e));
    }
    if (message.specItemList?.length) {
      obj.specItemList = message.specItemList.map((e) => GoodsExtendItem.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCartGoodsModel>, I>>(base?: I): MidCartGoodsModel {
    return MidCartGoodsModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCartGoodsModel>, I>>(object: I): MidCartGoodsModel {
    const message = createBaseMidCartGoodsModel();
    message.goodsId = object.goodsId ?? "";
    message.goodsNo = object.goodsNo ?? "";
    message.goodsName = object.goodsName ?? "";
    message.goodsTitle = object.goodsTitle ?? "";
    message.minBuyQuantity = object.minBuyQuantity ?? 0;
    message.goodsPriceUnitName = object.goodsPriceUnitName ?? "";
    message.minIncreaseQuantity = object.minIncreaseQuantity ?? 0;
    message.mainImageUrl = object.mainImageUrl ?? "";
    message.skuList = object.skuList?.map((e) => MidCartSkuModel.fromPartial(e)) || [];
    message.specItemList = object.specItemList?.map((e) => GoodsExtendItem.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidCartSkuModel(): MidCartSkuModel {
  return {
    skuId: "",
    skuNo: "",
    specItemList: [],
    stockQty: 0,
    selected: false,
    salePrice: 0,
    buyQty: 0,
    subtotalSalePrice: 0,
    skuImage: "",
    goodsId: "",
    stepPrices: [],
    spm: "",
  };
}

export const MidCartSkuModel = {
  encode(message: MidCartSkuModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.skuId !== "") {
      writer.uint32(10).string(message.skuId);
    }
    if (message.skuNo !== "") {
      writer.uint32(18).string(message.skuNo);
    }
    for (const v of message.specItemList) {
      GoodsExtendItem.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    if (message.stockQty !== 0) {
      writer.uint32(32).int32(message.stockQty);
    }
    if (message.selected !== false) {
      writer.uint32(40).bool(message.selected);
    }
    if (message.salePrice !== 0) {
      writer.uint32(49).double(message.salePrice);
    }
    if (message.buyQty !== 0) {
      writer.uint32(56).int32(message.buyQty);
    }
    if (message.subtotalSalePrice !== 0) {
      writer.uint32(73).double(message.subtotalSalePrice);
    }
    if (message.skuImage !== "") {
      writer.uint32(82).string(message.skuImage);
    }
    if (message.goodsId !== "") {
      writer.uint32(90).string(message.goodsId);
    }
    for (const v of message.stepPrices) {
      SkuStepPrice.encode(v!, writer.uint32(98).fork()).ldelim();
    }
    if (message.spm !== "") {
      writer.uint32(106).string(message.spm);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidCartSkuModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidCartSkuModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.skuId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.skuNo = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.specItemList.push(GoodsExtendItem.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.stockQty = reader.int32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.selected = reader.bool();
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }

          message.salePrice = reader.double();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.buyQty = reader.int32();
          continue;
        case 9:
          if (tag !== 73) {
            break;
          }

          message.subtotalSalePrice = reader.double();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.skuImage = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.goodsId = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.stepPrices.push(SkuStepPrice.decode(reader, reader.uint32()));
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.spm = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidCartSkuModel {
    return {
      skuId: isSet(object.skuId) ? globalThis.String(object.skuId) : "",
      skuNo: isSet(object.skuNo) ? globalThis.String(object.skuNo) : "",
      specItemList: globalThis.Array.isArray(object?.specItemList)
        ? object.specItemList.map((e: any) => GoodsExtendItem.fromJSON(e))
        : [],
      stockQty: isSet(object.stockQty) ? globalThis.Number(object.stockQty) : 0,
      selected: isSet(object.selected) ? globalThis.Boolean(object.selected) : false,
      salePrice: isSet(object.salePrice) ? globalThis.Number(object.salePrice) : 0,
      buyQty: isSet(object.buyQty) ? globalThis.Number(object.buyQty) : 0,
      subtotalSalePrice: isSet(object.subtotalSalePrice) ? globalThis.Number(object.subtotalSalePrice) : 0,
      skuImage: isSet(object.skuImage) ? globalThis.String(object.skuImage) : "",
      goodsId: isSet(object.goodsId) ? globalThis.String(object.goodsId) : "",
      stepPrices: globalThis.Array.isArray(object?.stepPrices)
        ? object.stepPrices.map((e: any) => SkuStepPrice.fromJSON(e))
        : [],
      spm: isSet(object.spm) ? globalThis.String(object.spm) : "",
    };
  },

  toJSON(message: MidCartSkuModel): unknown {
    const obj: any = {};
    if (message.skuId !== "") {
      obj.skuId = message.skuId;
    }
    if (message.skuNo !== "") {
      obj.skuNo = message.skuNo;
    }
    if (message.specItemList?.length) {
      obj.specItemList = message.specItemList.map((e) => GoodsExtendItem.toJSON(e));
    }
    if (message.stockQty !== 0) {
      obj.stockQty = Math.round(message.stockQty);
    }
    if (message.selected !== false) {
      obj.selected = message.selected;
    }
    if (message.salePrice !== 0) {
      obj.salePrice = message.salePrice;
    }
    if (message.buyQty !== 0) {
      obj.buyQty = Math.round(message.buyQty);
    }
    if (message.subtotalSalePrice !== 0) {
      obj.subtotalSalePrice = message.subtotalSalePrice;
    }
    if (message.skuImage !== "") {
      obj.skuImage = message.skuImage;
    }
    if (message.goodsId !== "") {
      obj.goodsId = message.goodsId;
    }
    if (message.stepPrices?.length) {
      obj.stepPrices = message.stepPrices.map((e) => SkuStepPrice.toJSON(e));
    }
    if (message.spm !== "") {
      obj.spm = message.spm;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidCartSkuModel>, I>>(base?: I): MidCartSkuModel {
    return MidCartSkuModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidCartSkuModel>, I>>(object: I): MidCartSkuModel {
    const message = createBaseMidCartSkuModel();
    message.skuId = object.skuId ?? "";
    message.skuNo = object.skuNo ?? "";
    message.specItemList = object.specItemList?.map((e) => GoodsExtendItem.fromPartial(e)) || [];
    message.stockQty = object.stockQty ?? 0;
    message.selected = object.selected ?? false;
    message.salePrice = object.salePrice ?? 0;
    message.buyQty = object.buyQty ?? 0;
    message.subtotalSalePrice = object.subtotalSalePrice ?? 0;
    message.skuImage = object.skuImage ?? "";
    message.goodsId = object.goodsId ?? "";
    message.stepPrices = object.stepPrices?.map((e) => SkuStepPrice.fromPartial(e)) || [];
    message.spm = object.spm ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
