syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/sales_order_model.proto";
import "chilat/logistics/model/route_model.proto";
import "common.proto";
import "common/business.proto";





//订单创建
message SalesOrderCreateParam {
//    string checkoutId = 10; //结算ID
    string userId = 20; //用户id
//    string userName = 2; // 用户名
//    string orderNo = 3; //订单号
//    common.OrderStatus orderStatus = 4; //订单状态
//    int64 cdate = 5; //开单时间
    string countryId = 30; //支付国家ID（订单归属国家；若不传值，默认取收货地址中的国家）
    repeated SalesOrderRouteFeeParam routeFeeList = 40; //线路总运费（仅当 isRouteFeeInBox=false 才可能需要传值）
//    string routeDesc = 8; //线路描述（运输方式）
    repeated string goodsLookingNoList = 50; //关联询盘单号
    string buyerRemark = 60; //客户备注（最大1000字）
    string sellerRemark = 70; //卖家备注（最大1000字）
    common.QuotationMode priceModel = 80; //报价模式
    common.PayMode paymentModel = 90; //支付模式:0.一次性支付 100.首次支付只支付国内费用，二次支付待仓库收货之后再支付
//    string paymentModelDesc = 13; //支付模式描述
    OrderAddressParam orderAddress = 100; //配送信息
    repeated SalesOrderBoxParam salesOrderBoxList= 110; //装箱信息
    repeated SalesOrderSundryFeeParam salesOrderFeeList = 120; //费用信息
//    repeated SalesOrderBillParam soBillList = 17; //账单信息
//    repeated SalesOrderBillPaymentParam soBillPaymentList = 18; //支付流水
//    repeated SalesOrderLogParam soLogList = 19; //订单日志
    repeated SalesOrderLineParam soLineList = 130; //订单行信息
    double commission = 140; //佣金
    double goodsDiscountAmount = 150; //商品优惠金额（默认0，大于0表示享受了商品优惠）
    bool isRouteFeeInBox = 160; //装箱信息中，是否包含线路运费（必填）
    string salesManUserId = 170; //业务员Id
}

// 订单地址信息
message OrderAddressParam {
//    string id = 1;
    string phone = 2; //手机号
    string email = 3; //邮箱
    string countryName = 4; //国家名称
    string countryId = 5; //国家id
    string provinceCode = 6; //省code
    string province = 7; //省
    string cityCode = 8; //市code
    string city = 9; //市/县
    string regionCode = 10; //区县code
    string region = 11; //区域
    string postcode = 12; //邮政编码
    string address = 13; //详细地址
    string houseNo = 14; //房好
    string referLandmark = 15; //参考地标
    string contactName = 16; // 联系人
    common.AddressLabel addressLabel = 17; //地址标签：10：家，20公司
    string street = 18; //街道地址
    string addressId = 19; //用户地址id
//    string fullAddress = 20; //完整的地址描述
}

//订单行信息
message SalesOrderLineParam {
//    string id = 1; //订单行id
    string skuId = 1; // SKU ID
    string skuNo = 2; // SKU货号
    string spm = 3; //SPM跟踪码
    int32 goodsQty = 4; //下单数量
    double buyPrice = 5; //结算单价（TODO：业务员或客服改价，须检查权限）
//    double unitPrice = 5; //商品单价
//    double totalPrice = 6; //商品总价格
//    string skuImage = 7; //sku图片
//    string skuNo = 8; //sku号
//    repeated SpecInfoModel specsInfo = 9; //商品规格
//    string goodsName = 10; //商品名称
//    string goodsPriceUnitName = 11; //价格单位
//    int32 purchaseSendNum = 12; //采购发货数量
    repeated SalesOrderLineLookingParam lineLookingList = 13; //询盘单订单行绑定信息
//    bool deleted = 14; //是否删除（修改用）
//    string goodsNo = 15; //商品编码
//    string goodsId = 16; //商品id
    string padc = 88; //促销活动动态代码
}
//询盘行信息
message SalesOrderLineLookingParam {
//    string id = 1; //id
    int32 goodsQty = 2; //商品总数
    string goodsLookingNo = 3; //询盘单号
    string lookingGoodsLineId = 4; //询盘单商品id
    bool deleted = 5; //是否删除
}

//装箱信息
message SalesOrderBoxParam {
//    string id = 1; // 装箱id
    string spm = 2; //SPM跟踪码
    int32 goodsQty = 3; //商品总数
    int32 boxInsideCount = 4; //装箱数
    double boxQty = 5; // 箱数
    double boxUnitWeight = 6; // 单件重量
    double totalWeight = 7; //总重量
    double boxUnitVolume = 8; //单箱体积
    double totalVolume = 9; //总体积
    repeated SalesOrderBoxLineParam boxLineList = 10; //装箱明细
    repeated SalesOrderBoxRouteFeeParam boxRouteFeeList = 11; //运输费
    bool deleted = 13; //是否删除（修改用）
}

//装箱行
message SalesOrderBoxLineParam {
//    string id = 1; // 装箱行id
    string skuId = 2; //SKU ID
    int32 goodsQty = 3; //商品总数
    double unitPrice = 4; //商品单价
    double totalPrice = 5; //商品总价格
    string skuImage = 6; //sku图片
    string skuNo = 7; //sku号
    repeated SpecInfoModel specInfo = 8; //商品规格
    bool deleted = 9; //是否删除（修改用）
    string goodsNo = 10; //商品编码
    string goodsId = 11; //商品id
    string goodsName = 12; //商品名称
    string goodsPriceUnitName = 13; //价格单位
    string sourceShopId = 14; //源店铺id
    string sourceGoodsId = 15; //源商品id
    string goodsPriceUnitNameCn = 16; //商品计价单位名称中文
    double supplierPrice = 17;// 采购价
    string padc = 88; //促销活动动态代码
}

message SalesOrderBoxRouteFeeParam {
//    string id = 1; //id
    string routeId = 2; //线路id
//    string routeName = 3; //线路名称
    double transportFee = 4; //运费
//    bool deleted = 8; //是否删除（修改用）
}


//订单杂费（暂不支持二级费用）
message SalesOrderSundryFeeParam {
    string feeId = 1; //费用ID
    double feeAmount = 2; //费用金额
    //repeated SalesOrderFeeLineParam soFeeLineList = 3; //费用明细列表
}

//订单费用明细
message SalesOrderFeeLineParam {
//    string id = 1; //id
    string feeAlias = 2; // 费用明细类型： EXW CIF
    string feeItemId = 3; //费用项id
    string feeItemName = 4; //费用项名称
    double fee = 5;// 费用
    repeated SalesOrderFeeLineParam soFeeLineList = 6; //明细
    bool deleted = 7; //是否删除
}

//订单账单
message SalesOrderBillParam {
//    string id = 1; //账单号
    common.SalesOrderBillType bilType = 2; //账单类型：100.国内费用 200.国际费用
    common.SalesOrderBillStatus billStatus = 3; //账单状态： -100.取消 0.待支付 100.部分支付 200.已支付
    string billStatusDesc = 4; //账单状态描述
    string remark = 5; //账单备注
    int64 finishTime = 6; //完成时间
    double billAmount = 7; //金额
}

//销售单线路
message SalesOrderRouteFeeParam {
//    string id = 1;
    string routeId = 10; //线路id
    double totalTransportFee = 20; //线路总运费
    //    string routeName = 3; //线路名称
    //    string routeCode = 4; //线路code
    //    string routeAlias = 5; //线路别名
    //    string remark = 6; //备注
    //    logistics.RouteTransportDaysModel transportDays = 7;
    //    bool deleted = 8; //是否删除（修改用）
}

//支付流水
message SalesOrderBillPaymentParam {
//    string id = 1; //账单号
    double paymentAmount = 2; //支付金额
    string paymentType = 3; //支付方式
    repeated string billId = 4; //账单号
    string remark = 5; //备注备注
    int64 cdate = 6; //支付时间
    string coperator = 7; //操作人
}

//销售单查询
message SalesOrderQueryParam {
    string userId = 1; //用户id
    string userName = 2; // 用户名
    string orderNo = 3; //订单号
    common.OrderStatus orderStatus = 4; //订单状态
    int64 startTime = 5; //开始时间
    int64 endTime = 6; //结束时间
    string country = 7; //国家（待废弃，转用countryId字段）
    string countryId = 20; //国家ID
    string goodsLookingNo = 8; //询盘单号
    string goodsNo = 9; //商品编号
    string skuNo = 10; //sku编号
    bool needLine = 11; //是否需要订单行
    repeated common.OrderStatus orderStatusList = 12; //订单状态列表
    string contactName = 13; //收件人
    string phone = 14; //手机号-whatsapp
    string saleManUserId = 15; //业务员id
    bool isQueryFromMall = 30; //是否商城端查询
    common.OrderSourcePlatform sourcePlatform = 40; //订单来源平台
}

// 获取订单详情的参数
message GetSoDetailParam {
    string id = 10; //订单ID
    bool isQueryFromMall = 20; //是否商城端查询
}

message SalesOrderPageParam {
    common.PageParam page = 1;
    SalesOrderQueryParam query = 2;
}


message SalesOrderCancelParam {
    string orderNo = 1; //订单号
    common.SoHandleUserType handleUserType = 2; //更改类型
    string userId = 3; //操作人id （前台用户）
    string cancelReason = 4; //取消原因
    string cancelId = 5; //取消原因id
    string cancelRemark = 6; //取消备注

}

message QueryOffLinePayInfoParam {
    string orderNo = 1; //订单号
}

message SubmitOffLinePayParam {
    string orderNo = 10; //订单号
    double amount = 30; //支付的金额
    string payMethod = 40; //支付方式
    string routeId = 50; //线路id,没有线路的支付模式不用填
    string remark = 60; //备注
    repeated string payOrderPicList = 70; //支付凭证url列表
    bool isFinish = 80; //是否本单支付完成
    bool isPurchase = 90; //是否采购下单
}

message OrderNoParam {
    string orderNo = 1; //订单号
}

message UpdateOrderLineStatusParam {
    repeated OrderLineQueryParam orderLineList = 1;
    common.OrderStatus orderStatus = 2; //订单状态
    string remark = 3;
    string operator = 4;
    common.SalesOrderOperationType operationType = 5;
}

message OrderLineQueryParam {
    string orderNo = 1; //订单号
    string skuId = 2; //sku id
    string padc = 88; //促销活动动态代码（非null，表示按padc查询）
}

message UpdateOrderStatusParam {
    string orderNo = 1;
    common.OrderStatus orderStatus = 2;
    string remark = 3;
}

message CreatePurchaseBySalesOrderParam {
    string salesOrderNo = 1; // 2.0订单号
    string purchaserId = 2; // 采购员id
    string purchaserName = 3; //采购员用户名
}