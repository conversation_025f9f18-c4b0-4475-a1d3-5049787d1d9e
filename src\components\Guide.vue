<template>
  <n-modal
    preset="dialog"
    :block-scroll="true"
    :closable="false"
    :show-icon="false"
    v-model:show="showGuide"
    :on-close="onCloseGuide"
    :on-esc="onCloseGuide"
    :on-mask-click="onCloseGuide"
    class="guide-dialog"
    :style="
      isMobile
        ? { width: '100vw', padding: 0 }
        : {
            width: '1280px',
            maxWidth: '1280px',
            padding: 0,
          }
    "
  >
    <n-image
      object-fit="fill"
      preview-disabled
      :src="isMobile ? pageTheme.mobileGuide : pageTheme.Guide"
      class="w-[100%] block"
      :img-props="{ referrerpolicy: 'no-referrer' }"
    />
    <icon-card
      :size="isMobile ? '18' : '26'"
      color="#fff"
      class="close-icon cursor-pointer"
      :class="isMobile ? 'mobile-icon' : ''"
      name="vaadin:close"
      @click="onCloseGuide"
      v-show="pageTheme.guideLogo"
    ></icon-card>
  </n-modal>
</template>

<script setup lang="ts" name="GuideModal">
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
const route = useRoute();
const authStore = useAuthStore();
const showGuide = ref(false);

const pageTheme = computed(() => useConfigStore().getPageTheme);
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

onMounted(() => {
  if (authStore.getShowedGuideStatus() && route.query.guide) {
    showGuide.value = false;
  } else {
    showGuide.value = true;
  }
  authStore.setShowedGuideStatus(true);
});

function onCloseGuide() {
  showGuide.value = false;
}
</script>
<style scoped lang="scss">
.close-icon {
  position: absolute;
  right: 0.2rem;
  top: 0.3rem;
  z-index: 1000;
}
.mobile-icon {
  right: 0.06rem;
  top: 0.04rem;
}
</style>
<style>
.guide-dialog.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0px !important;
}
</style>
