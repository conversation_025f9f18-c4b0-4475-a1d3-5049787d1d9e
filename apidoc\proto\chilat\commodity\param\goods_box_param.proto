syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "chilat/commodity/model/goods_box_model.proto";
import "chilat/foundation/foundation_common.proto";
import "common.proto";


// 导入批次分页查询
message ImportBoxBatchPageParam {
  common.PageParam page = 1;
  ImportBoxBatchQueryParam query = 2;
}

// 导入批次分页查询参数
message ImportBoxBatchQueryParam {
  string id = 10; // 商品ID
  string salesmanId = 20; // 导入人ID（业务员ID）
  int64 startImportTime = 30; // 开始导入时间
  int64 endImportTime = 40; // 结束导入时间
  foundation.CrawlTaskState taskState = 50; //导入状态
}

// 导入批次内的商品分页查询
message ImportBoxBatchGoodsPageParam {
  common.PageParam page = 1;
  ImportBoxBatchGoodsQueryParam query = 2;
}

// 导入批次内的商品分页查询参数
message ImportBoxBatchGoodsQueryParam {
  string batchId = 10; // 导入批次ID
  string goodsNo = 20; // shop商品编码
  string goodsIdWith1688 = 30; //1688商品ID
}

// 确认shop商品维度装箱信息（确认导入，selected置为true）
message ConfirmShopBoxInfoParam {
  repeated ImportShopBoxInfoModel data = 10;
}
