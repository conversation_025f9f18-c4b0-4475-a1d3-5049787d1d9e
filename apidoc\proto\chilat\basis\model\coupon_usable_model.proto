syntax = "proto3";

package chilat.basis;

import "chilat/coupon/coupon_common.proto";
import "chilat/coupon/coupon_detail_common.proto";
import "chilat/coupon/model/coupon_info_model.proto";
import "common.proto";

option java_package = "com.chilat.rpc.basis.model";

// 我的优惠券列表
message CouponUsableModelResp {
    common.Result result = 1;
    CouponUsableModel data = 2; //优惠券基本规则参数
}

message CouponUsableModel{
    repeated CouponUsableDetailModel commissionCouponList = 1;//佣金券集合
    repeated CouponUsableDetailModel productCouponList = 2;//产品券集合
}

//可用优惠券组装Model
message CouponUsableDetailModel {
    string id = 1;//主键id
    string couponId = 2;//优惠券id
    string couponName = 3;//优惠券名称
    string userInstructions = 4; //用户使用说明
    coupon.CouponTypeStatus couponType = 5; //优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券
    coupon.TicketStatus ticketStatus = 6; //优惠券状态: ticketNotUse 未使用 ticketUse 已使用 ticketLoseEfficacy 已失效
    coupon.CouponWayStatus couponWay = 7; //优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券
    double useConditionsAmount = 8; //使用条件(0不限制，达到多少金额可以使用，每满xxx可用)
    double preferentialAmount = 9; //优惠额度
    double discount = 10; //折扣（券类型为折扣券使用）
    string activeId = 11;//活动编码
    int64 ticketStartExpirationDate = 12;//优惠券生效日期
    int64 ticketEndExpirationDate = 13;//优惠券结束日期
    repeated coupon.CouponInfoUseRuleModel couponInfoUseRuleModelList = 14;//使用规则
    string exchangeCode = 15; //兑换码
    coupon.CouponUseConditionsTypestatus couponUseConditionsType = 16;//使用条件类型
    double ticketActualPrice = 17;//单张券实际金额（根据订单金额 以及优惠方式，使用条件计算）
    int32 ruleCount = 18;//使用张数
    bool sameType = 19;//是否同类型
    bool availableFlag = 20;//是否可用
    bool check = 21;//是否选中
    string notAvailableReason = 22;//不可用原因
}

message CouponAvailableModelResp {
    common.Result result = 1;
    repeated CouponUsableDetailModel data = 3; //优惠券基本规则参数
}

//发券优惠券组装Model
message CouponDistributionDetailModel {
    string id = 1;//主键id
    string couponId = 2;//优惠券id
    string couponName = 3;//优惠券名称
    string userInstructions = 4; //用户使用说明
    string activeId = 5;//活动编码
}

//批量发券优惠券组装Model
message CouponDistributionBatchModel {
    common.Result result = 1;
    CouponDistributionListModel data = 2; //优惠券基本规则参数
}

message CouponDistributionListModel{
    repeated CouponDistributionDetailModel successCouponList = 1;//发放成功券集合
    repeated CouponDistributionDetailModel failCouponList = 2;//发放失败券集合
}



