syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";

import "common.proto";
import "common/business.proto";

message GetContactInfoResp {
  common.Result result = 1; //错误码
  GetContactInfoModel data = 2;
}

message GetContactInfoModel {
  string email = 1; // 邮箱
  string countryId = 2; // 国家ID
  string country = 3; // 国家
  string whatsapp = 4; // WhatsApp
  string areaCode = 5; // whatsapp区号
  string name = 6; // 姓名
}

message SubmitGoodsFindResp {
  common.Result result = 1;
  SubmitGoodsFindModel data = 2;
}

message SubmitGoodsFindModel {
  string goodsFindNo = 1; // 找货单号
}

message GoodsFindDetailResp {
  common.Result result = 1;
  GoodsFindDetailModel data = 2;
}

message GoodsFindDetailModel {

  string goodsFindNo = 10; // 找货单号
  string remark = 20; // 备注
  int64 createTime = 30; // 找货时间, 时间戳，前端根据时间戳进行格式化
  string country = 40; // 国家
  string whatsapp = 50; // WhatsApp
  string email = 60; // 邮箱
  string name = 61; // 姓名
  bool isAcceptSimilarProduct = 70; // 是否接受相似商品
  int32 goodsLineCount = 80; // 商品款数
  repeated GoodsLineModel goodsLines = 90; // 商品列表
}

message GoodsLineModel {
  string id = 1;
  repeated string imageList = 10; // 图片列表
  string goodsName = 20; // 商品名称
  int32 count = 30; // 数量
  bool isPriceLimited = 40;  // 是否限制价格
  double minPrice = 50; // 价格下限
  double maxPrice = 60; // 价格上限
  int32 lineNo = 70; // 行号
}