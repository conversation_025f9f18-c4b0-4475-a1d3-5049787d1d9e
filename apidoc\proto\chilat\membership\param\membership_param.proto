syntax = "proto3";
package chilat.membership;

option java_package = "com.chilat.rpc.membership.param";

import "common.proto";
import "common/business.proto";

message MembershipPageQueryParam {
  common.PageParam page = 1;
  MembershipQueryParam query = 2;
}

message MembershipQueryParam {
  string username = 1; // 用户名
  int64 startRegisterTime = 2; // 开始注册时间
  int64 endRegisterTime = 3; // 结束注册时间
}


message SaveUserAddressParam {
  string id = 1;
  string countryId = 2; //国家
  string provinceCode = 3; //省
  string cityCode = 4; //市
  string regionCode = 5; //区
  string address = 6; //详细地址
  string houseNo = 7; //门牌号
  string postcode = 8; //邮编
  string contactName = 9; //联系人
  string phone = 10; //手机号
  bool isDefault = 11; //是否是默认地址
  string referLandmark = 12; //参考地标
  common.AddressLabel addressLabel = 13; //地址标签
  string street = 14;
  string province = 15; //省名称
  string city = 16; //市名称
  string region = 17; //县名称
  string userId = 18;//用户id
}

message ListByPrivilegeParam {
  string requestId = 10; //请求ID（前端赋值的请求ID，在查询结果中返回）
  string keyword = 20; //用户名模糊匹配的查询关键字
}