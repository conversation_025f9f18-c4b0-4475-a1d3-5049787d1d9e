syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.param";

import "common.proto";

message MemberPageQueryParam {
  common.PageParam page = 1;
  MemberQueryParam query = 2;
}

message MemberQueryParam {
  string userId = 1; // 用户ID
  string memberName = 2; // 客户名
  string whatsapp = 3; // whatsapp
  string countryId = 4; // 国家
}

message MemberSaveParam {
  string id = 1;
  string userId = 2; // 用户ID
  string username = 3; // 用户名
  string memberName = 4; // 客户名
  string whatsapp = 5; // whatsapp
  string countryId = 6; // 国家
  string email = 7; // 邮箱
  string memberId = 8; // 客户userID
}