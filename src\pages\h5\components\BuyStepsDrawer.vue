<template>
  <!-- 福利明细抽屉 -->
  <n-drawer
    v-model:show="pageData.dialogVisible"
    width="100%"
    placement="bottom"
    :trap-focus="false"
    default-height="11.44rem"
  >
    <n-drawer-content closable>
      <template #header>
        <div
          class="w-[4.24rem] mx-auto text-[0.4rem] leading-[0.44rem] text-center font-normal"
        >
          {{ authStore.i18n("cm_home_3stepPurchasing") }}
        </div>
      </template>

      <div class="pt-[0.7rem] pb-[0.44rem] px-[0.4rem]">
        <div
          v-for="(step, stepIndex) in props.buySteps"
          :key="stepIndex"
          class="mb-[0.62rem] last:mb-0"
        >
          <div class="flex gap-[0.32rem]">
            <div
              class="flex flex-col items-center bg-[#FAFAFA] rounded-[0.16rem] w-[2.16rem] pb-[0.16rem]"
              :class="
                stepIndex === props.buySteps.length - 1
                  ? 'gap-[0.7rem]'
                  : 'gap-[0.1rem]'
              "
            >
              <!-- 圆形数字 -->
              <div
                class="w-[0.56rem] h-[0.56rem] rounded-full border-1 border-[#E50113] bg-white flex items-center justify-center mb-[0.14rem] text-[0.4rem] leading-[0.4rem] text-[#e50113] mt-[-0.26rem]"
              >
                {{ stepIndex + 1 }}
              </div>

              <div
                class="w-[1.92rem] h-[1.08rem] flex items-center text-[0.32rem] leading-[0.36rem] text-[#333] mt-[0.04rem] text-center"
              >
                {{ step.title }}
              </div>

              <!-- 步骤图片 -->
              <img
                :src="step.image"
                :alt="step.title"
                loading="lazy"
                class="w-[1.92rem] h-[0.76rem] rounded-[0.08rem] overflow-hidden"
              />
            </div>

            <div class="flex-1 pt-[0.16rem]">
              <div
                v-for="(desc, descIndex) in step.desc"
                :key="descIndex"
                class="flex items-start gap-[0.24rem] last:mb-0"
                :class="
                  stepIndex === props.buySteps.length - 1
                    ? 'mb-[0.16rem]'
                    : 'mb-[0.32rem]'
                "
              >
                <img
                  src="/assets/icons/home/<USER>"
                  alt="check"
                  loading="lazy"
                  class="w-[0.24rem] h-[0.24rem] flex-shrink-0 mt-[0.08rem]"
                />
                <span
                  class="text-[0.32rem] leading-[0.36rem] text-[#7F7F7F] flex-1"
                >
                  {{ desc }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const props = defineProps({
  buySteps: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive({
  dialogVisible: false,
});

const openDrawer = () => {
  pageData.dialogVisible = true;
};

const closeDrawer = () => {
  pageData.dialogVisible = false;
};

defineExpose({
  openDrawer,
  closeDrawer,
});
</script>

<style lang="scss" scoped>
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
}
:deep(.n-drawer-header) {
  padding: 0.24rem 0.26rem !important;
  border-bottom: none !important;
}
:deep(.n-base-icon svg) {
  width: 0.32rem !important;
  height: 0.32rem !important;
}
:deep(.n-base-close) {
  color: #333 !important;
}
</style>
