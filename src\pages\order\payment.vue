<template>
  <div class="page-wrapper">
    <div class="bg-white">
      <search-card></search-card>
    </div>
    <div class="page-content" v-if="pageData.payAmount?.amount">
      <div class="text-xl font-medium">
        {{ authStore.i18n("cm_order.payMethod") }}
      </div>
      <div class="flex mt-6">
        <div class="flex-1 mr-8" v-if="pageData.payMethodList.length > 0">
          <n-radio-group
            v-model:value="pageData.payMethodId"
            :on-update:value="onUpdatePayMethod"
            class="w-full"
          >
            <n-radio
              v-for="pay in pageData.payMethodList"
              :key="pay.id"
              :value="pay.id"
              class="pay-item"
              :class="
                pageData.payMethodId === pay.id ? 'selected-pay-item' : ''
              "
              size="large"
              :disabled="pay.max < pageData.payAmount?.amount"
            >
              <div class="flex justify-between items-center">
                <div>
                  <div class="text-base font-medium">
                    {{ pay.name }}
                  </div>
                  <div
                    v-if="pay.max < pageData.payAmount?.amount"
                    class="text-[14px] text-[#E15A47]"
                  >
                    {{ authStore.i18n("cm_order.orderPayLimit") }}
                    {{ setUnit(pay.max) }}
                  </div>
                </div>
                <div
                  class="min-w-[106px] h-[50px] bg-[#F2F2F2] px-[8px] flex justify-center items-center"
                >
                  <img
                    loading="lazy"
                    :src="pay.iconUrl"
                    class="max-h-[40px]"
                    referrerpolicy="no-referrer"
                  />
                </div>
              </div>
            </n-radio>
          </n-radio-group>
        </div>
        <div class="w-[450px]">
          <n-affix :trigger-top="0">
            <n-card class="w-[450px] bg-white">
              <div class="w-[440px] text-left">
                <a
                  :href="`/order/details?orderNo=${pageData.orderNo}`"
                  class="px-4 pb-5 flex border-b border-[#797979]"
                >
                  <img
                    loading="lazy"
                    :src="pageData.picUrl"
                    class="w-[50px] h-[50px] mr-4"
                    referrerpolicy="no-referrer"
                  />
                  <n-space vertical :style="{ gap: '0 0' }">
                    <span
                      class="text-[14px] font-medium underline underline-offset-2"
                      >{{ pageData.totalCount }}
                      {{ authStore.i18n("cm_order.orderItems") }}</span
                    >
                    <div
                      class="w-[340px] text-[12px] text-[#767676] break-all whitespace-nowrap overflow-hidden text-ellipsis"
                    >
                      {{ pageData.goodsName }}
                    </div>
                    <div class="text-[14px] text-[#767676]">
                      <span>{{ authStore.i18n("cm_order.orderNo") }}</span>
                      <span>{{ pageData.orderNo }}</span>
                    </div>
                  </n-space>
                </a>
                <div class="px-4 py-6 text-[#222]">
                  <n-space vertical :style="{ gap: '6px 0' }">
                    <div
                      class="flex justify-between"
                      v-for="(fee, index) in pageData.payAmount.feeList"
                      :key="index"
                    >
                      <div class="w-[60%]">{{ fee.feeName }}</div>
                      <span class="text-base" v-if="fee.feeAmount"
                        >{{ setUnit(fee.feeAmount) }}
                      </span>
                    </div>
                  </n-space>

                  <div class="flex justify-between mt-5">
                    <span>{{ authStore.i18n("cm_order.totalMoney") }}</span>
                    <span
                      class="font-medium text-lg"
                      v-if="pageData.payAmount?.amount"
                      >{{ setUnit(pageData.payAmount.amount) }}
                    </span>
                  </div>
                </div>
                <div class="flex items-center mt-2 mb-4">
                  <n-button
                    size="large"
                    color="#E50113"
                    text-color="#fff"
                    @click="onOrderPay"
                    :loading="pageData.submitLoading"
                    :disabled="!pageData.payMethodId"
                    class="rounded-[8px] w-[94%] mx-auto h-12"
                  >
                    <span class="text-base">{{
                      authStore.i18n("cm_order.payNow")
                    }}</span>
                  </n-button>
                </div>
              </div>
            </n-card>
          </n-affix>
        </div>
      </div>
    </div>
    <!-- 是否支付成功 -->
    <n-modal
      preset="dialog"
      :closable="false"
      :show-icon="false"
      :closeOnEsc="false"
      :maskClosable="false"
      :block-scroll="true"
      v-model:show="pageData.showPaySuccess"
    >
      <div class="py-2">
        <div class="text-xl font-medium mb-4 text-center">
          {{ authStore.i18n("cm_order.isPaySuccess") }}
        </div>
        <div class="w-full flex justify-end">
          <n-button class="px-8 mr-4" @click="onGetQueryPayResult">
            {{ authStore.i18n("cm_order.orderCancelNo") }}
          </n-button>
          <n-button color="#E50113" class="px-8" @click="onGetQueryPayResult">
            {{ authStore.i18n("cm_order.orderCancelYes") }}
          </n-button>
        </div>
      </div>
    </n-modal>
    <!-- 点击付款 付款信息错误提示 -->
    <n-modal
      preset="dialog"
      :show-icon="false"
      :closable="false"
      :closeOnEsc="false"
      :maskClosable="false"
      :block-scroll="true"
      v-model:show="pageData.showPayInfoError"
    >
      <div class="text-base">
        <icon-card
          size="22"
          name="mingcute:warning-line"
          color="#e50113"
          class="mr-1"
        ></icon-card>
        <span>
          <!-- 优惠券信息报错 -->
          <span v-if="pageData.couponInfoError">{{
            authStore.i18n("cm_order.chooseCouponError")
          }}</span>
          <span v-else>{{ authStore.i18n("cm_order.orderChanged") }}</span>
        </span>
        <div v-if="pageData.timeLeft" class="border-t-1 mt-[12px] pt-[12px]">
          <span
            class="text-[20px] mr-[0.12rem] text-[#e50113] w-[14px] inline-block"
            >{{ pageData.timeLeft }}</span
          >
          {{ authStore.i18n("cm_order.orderRedirectDetails") }}
        </div>
      </div>
    </n-modal>
    <!-- 显示付款按钮 -->
    <n-modal
      preset="dialog"
      :show-icon="false"
      :closable="false"
      :auto-focus="false"
      :closeOnEsc="false"
      :maskClosable="false"
      :block-scroll="true"
      v-model:show="pageData.showPayButton"
    >
      <a
        target="_blank"
        @click="onOpenPaySuccess"
        :href="pageData.payUrl"
        class="flex items-center justify-center h-15"
      >
        <n-button
          color="#E50113"
          text-color="#fff"
          class="rounded-[8px] w-[200px] mx-auto h-11 px-[20px]"
        >
          {{ authStore.i18n("cm_order.orderPaySmile") }}
        </n-button>
      </a>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive(<any>{
  orderNo: route?.query?.orderNo || "",
  paymentId: route?.query?.paymentId || "",
  totalCount: "", // 商品总数
  picUrl: "", // 商品图片
  goodsName: "", // 商品名称
  payMethodList: <any>[], // 支付方式列表
  payAmount: <any>{}, // 支付金额 实际支付金额 + 费用明细
  payResultModel: <any>{}, //支付结果
  payMethodId: "", //选中的支付方式id
  submitLoading: false, // 提交支付按钮loading
  showPayButton: false, // 展示付款按钮
  showPaySuccess: false, //展示确认支付成功
  showPayInfoError: false, // 是否展示付款信息错误提示
  timeLeft: 3,
  payUrl: "", //付款链接
  couponInfoError: false, // 优惠券信息报错
});

onMounted(() => {
  document.documentElement.style.backgroundColor = "#f5f3f3"; // fix bug
});

// 获取收银台信息
onGetCashDeskInfo();
async function onGetCashDeskInfo() {
  const res: any = await useGetCashDeskInfo({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    pageData.payMethodList.some((pay: any) => {
      if (pay.max >= pageData.payAmount?.amount) {
        pageData.payMethodId = pay.id;
        return true;
      }
      return false;
    });
    // 校验支付状态 支付成功 跳转支付结果页
    if (pageData.payResultModel.payStatus === "PAID_SUCCESS") {
      navigateTo({
        path: "/order/pay-results",
        query: {
          orderNo: pageData.orderNo,
          paymentId: pageData.paymentId,
        },
      });
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "payment_gopay_fail",
      `支付页去支付失败：${res?.result?.message}`
    );
    pageData.showPayInfoError = true;
    if (res?.result?.code === 70116) {
      pageData.couponInfoError = true;
    }
    pageData.timeLeft = 3;
    const countdown = setInterval(() => {
      if (pageData.timeLeft === 1) {
        navigateTo(`/order/details?orderNo=${pageData.orderNo}`);
        clearInterval(countdown);
        pageData.showPayInfoError = false;
      } else {
        pageData.timeLeft--;
      }
    }, 1000);
  }
}

function onUpdatePayMethod(val: any) {
  pageData.payMethodId = val;
  const payMethod = pageData.payMethodList.find(
    (pay: any) => pay.id === pageData.payMethodId
  );
  window?.MyStat?.addPageEvent(
    "payment_choice_paytype",
    `选择支付方式：${payMethod.name}`
  );
}

// 提交支付 打开pagsmile付款页面
/**
 * 浏览器检测到非用户操作产生的新弹出窗口，则会对其进行阻止。因为浏览器认为这可能是一个广告，不是一个用户希望看到的页面，
 * 所以改用弹窗显示付款按钮，点击付款按钮后，再打开付款页面。
 */
async function onOrderPay() {
  pageData.submitLoading = true;
  const payMeth = pageData.payMethodList.find(
    (item: any) => item.id === pageData.payMethodId
  );
  const res: any = await useSubmitPayment({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
    payMethodId: pageData.payMethodId,
    payMethod: payMeth?.code,
    amount: pageData.payAmount.amount,
  });
  pageData.submitLoading = false;
  if (res?.result?.code === 200) {
    pageData.showPayButton = true;
    pageData.payUrl = res.data.payUrl;
  } else {
    window?.MyStat?.addPageEvent(
      "payment_gopay_fail",
      `支付页去支付失败：${res?.result?.message}`
    );
    showToast(res?.result?.message);
    // pageData.showPayInfoError = true;
    // if (res?.result?.code === 70116) {
    //   pageData.couponInfoError = true;
    // }
    // pageData.timeLeft = 3;
    // const countdown = setInterval(() => {
    //   if (pageData.timeLeft === 1) {
    //     navigateToPage(`/order/details`, { orderNo: pageData.orderNo }, false);
    //     pageData.showPayInfoError = false;
    //     clearInterval(countdown);
    //   } else {
    //     pageData.timeLeft--;
    //   }
    // }, 1000);
  }
}

// 弹框显示付款的按钮
function onOpenPaySuccess() {
  setTimeout(() => {
    pageData.showPayButton = false;
    pageData.showPaySuccess = true;
  }, 600);
  window?.MyStat?.addPageEvent("payment_goto_payurl", "跳转至支付链接");
}

// 查询支付结果
async function onGetQueryPayResult() {
  const res: any = await useQueryPayResult({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
  });
  if (res?.result?.code === 200) {
    // 未支付，请继续支付
    if (res?.data.payResult.payStatus === "INIT") {
      pageData.showPaySuccess = false;
      setTimeout(() => {
        showToast(authStore.i18n("cm_order.continuePayment"));
      }, 100);
    } else {
      // 其他状态 跳转支付结果页
      window.location.replace(
        `/order/pay-results?orderNo=${pageData.orderNo}&paymentId=${pageData.paymentId}`
      );
      pageData.showPaySuccess = false;
    }
  }
}
</script>

<style scoped lang="scss">
.page-wrapper {
  width: 100%;
  color: #222;
  min-height: 100vh;
  background-color: #f2f2f2;
  padding-bottom: 100px;
}
.page-content {
  width: 1280px;
  margin: 0 auto;
  padding: 30px 20px 0;
}
.pay-item {
  width: 100%;
  min-height: 84px;
  background: #fff;
  margin-bottom: 16px;
  border-radius: 4px;
  padding: 16px;
  align-items: center;
}
.selected-pay-item {
  background-color: #f9e5e6;
  border: 1px solid #e50113;
}
:deep(.n-radio .n-radio__label) {
  width: 100%;
}

:deep(.n-card .n-card__content) {
  padding: 20px 5px;
}
</style>
