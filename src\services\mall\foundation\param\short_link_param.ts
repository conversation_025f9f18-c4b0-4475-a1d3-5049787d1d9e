/* eslint-disable */
import * as _m0 from "protobufjs/minimal";

export const protobufPackage = "mall.foundation";

/** 获取短链接请求参数 */
export interface GetShortLinkParam {
  /** 需要转换为短链的完整URL（当前页面URL） */
  fullUrl: string;
}

/** 获取完整链接请求参数 */
export interface GetFullLinkParam {
  /** 链接代码 */
  linkCode: string;
}

function createBaseGetShortLinkParam(): GetShortLinkParam {
  return { fullUrl: "" };
}

export const GetShortLinkParam = {
  encode(message: GetShortLinkParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.fullUrl !== "") {
      writer.uint32(82).string(message.fullUrl);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetShortLinkParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetShortLinkParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.fullUrl = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetShortLinkParam {
    return { fullUrl: isSet(object.fullUrl) ? globalThis.String(object.fullUrl) : "" };
  },

  toJSON(message: GetShortLinkParam): unknown {
    const obj: any = {};
    if (message.fullUrl !== "") {
      obj.fullUrl = message.fullUrl;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetShortLinkParam>, I>>(base?: I): GetShortLinkParam {
    return GetShortLinkParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetShortLinkParam>, I>>(object: I): GetShortLinkParam {
    const message = createBaseGetShortLinkParam();
    message.fullUrl = object.fullUrl ?? "";
    return message;
  },
};

function createBaseGetFullLinkParam(): GetFullLinkParam {
  return { linkCode: "" };
}

export const GetFullLinkParam = {
  encode(message: GetFullLinkParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.linkCode !== "") {
      writer.uint32(82).string(message.linkCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetFullLinkParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetFullLinkParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.linkCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetFullLinkParam {
    return { linkCode: isSet(object.linkCode) ? globalThis.String(object.linkCode) : "" };
  },

  toJSON(message: GetFullLinkParam): unknown {
    const obj: any = {};
    if (message.linkCode !== "") {
      obj.linkCode = message.linkCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetFullLinkParam>, I>>(base?: I): GetFullLinkParam {
    return GetFullLinkParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFullLinkParam>, I>>(object: I): GetFullLinkParam {
    const message = createBaseGetFullLinkParam();
    message.linkCode = object.linkCode ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
