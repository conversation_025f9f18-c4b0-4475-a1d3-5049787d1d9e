<template>
  <div class="goods-item">
    <a
      target="_blank"
      v-bind:href="`/goods/${goods.goodsId}`"
      class="flex flex-col items-center"
    >
      <n-image
        lazy
        preview-disabled
        :src="goods.mainImageUrl"
        class="goods-img"
        :style="{ width: props.imageWidth, height: props.imageHeight }"
      />
      <p class="goods-title">
        {{ goods.goodsName }}
      </p>
      <p class="goods-price">
        <!-- 规格单价的最小值和最大值展示；若相同则只展示一个价格值 -->
        <span v-if="goods.minPrice != goods.maxPrice"
          >{{ setUnit(goods.minPrice) }} - {{ goods.maxPrice }}</span
        >
        <span v-else>{{ setUnit(goods.maxPrice) }}</span>
      </p>
    </a>
  </div>
</template>
<script setup lang="ts" name="GoodsCard">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();
const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  imageWidth: {
    type: String,
    default: "186px",
  },
  imageHeight: {
    type: String,
    default: "186px",
  },
});
</script>

<style scoped lang="scss">
.goods-item {
  padding: 16px 16px 24px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  height: fit-content;
  width: fit-content;

  .goods-img {
    display: inline-block;
    margin-bottom: 18px;
  }
  :deep(.n-image img) {
    width: 100%;
    height: 100%;
  }
  .goods-title {
    width: 100%;
    color: #212121;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    margin-bottom: 8px;
  }
  .goods-price {
    width: 100%;
    font-weight: 600;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}
</style>
