syntax = "proto3";
package chilat.report;

option java_package = "com.chilat.rpc.report.param";

import "common.proto";
import "common/business.proto";

message InquiryReportPageParam {
    common.PageParam page = 1;
    InquiryReportParam param = 2;
}

message InquiryReportParam {
    common.TimeInterval timeInterval = 1;
    int64 startTime = 2;
    int64 endTime = 3;
}

message ProcessIndicatorParam {
    common.VisitSiteSelectEnum visitSite = 10; //网站选择
    int64 startTime = 20;
    int64 endTime = 30;
}