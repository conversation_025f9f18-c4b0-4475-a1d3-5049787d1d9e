syntax = "proto3";
package mall.main;

option java_package = "com.chilat.rpc.main.param";


import "chilat/basis/param/cart_param.proto";
import "common.proto";
import "common/business.proto";

message GetConfigReq {
  string language = 1; // 语言类型（必填）
}

message GetSiteOptionParam {
  string optionCodeGroup = 1; //分组设置
  string optionCode = 2; //配置编码
  string optionName = 3; //配置名
  common.VisitDeviceType deviceType = 4;
}

message GetThemePageParam {
  int64 pageId = 1; //页面id
  int32 type = 2; //类型
  common.VisitDeviceType deviceType = 5;
}

message LanguageSetParam {
  string language = 1; // 语言类型（必填）
  string prefix = 2; // 前缀
}

message ConfigGetParam {
  string language = 1; // 语言类型（必填）
  string prefix = 2; // 前缀
}

message CalculateEstimateFreightParam {
  int32 siteId = 10; // 当前站点ID（即页面选中的配送国家ID；必填）
  string routeId = 20; //当前页面选中的线路（不传，则取默认值）
  bool ignoreRouteIdError = 30; //忽略线路ID错误（从购物车中取到的线路，作为默认传参时，将此参数置为true）
  repeated chilat.basis.MidCartSkuAddParam skuList = 40; //sku列表
}

