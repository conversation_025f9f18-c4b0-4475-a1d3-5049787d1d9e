syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "chilat/marketing/marketing_common.proto";
import "common.proto";
import "common/business.proto";
import "chilat/commodity/commodity_common.proto";

// 商品列表参数
message GoodsInfoPageQueryParam {
  common.PageParam page = 1;
  GoodsInfoQueryParam query = 2;
}

// 商品列表查询参数
message GoodsInfoQueryParam {
  string id = 10; // 商品ID
  string goodsNo = 20; // 商品编码
  string skuNo = 30; //SKU货号
  string categoryId = 40; //分类ID
  string goodsName = 50; // 商品名称
  int32 minTotalStockQty = 61; //最低总库存数（可用库存数，包含）
  int32 maxTotalStockQty = 62; //最高总库存数（可用库存数，包含）
  GoodsOnlineState onlineState = 70; //上架状态（必填）
  GoodsSourceType sourceType = 75; //商品来源（1人工抓取，2 1688抓取，3人工创建等）
  SourceGoodsState sourceGoodsState = 80; //源商品状态
  int64 startPublishTime = 91; // 开始发布时间
  int64 endPublishTime = 92; // 结束发布时间
  int64 startCdate = 93; // 开始创建时间
  int64 endCdate = 94; // 结束创建时间
  int64 startUdate = 95; // 开始更新时间
  int64 endUdate = 96; // 结束更新时间
  repeated common.SortField sortFields = 100; //排序字段数组（可选排序：goodsNo 商品编码；totalStockQty 总库存；publishTime 发布时间；udate 更新时间）
  bool isAudit = 111; // 是否校验
  bool isIllegal = 112; // 是否侵权
  repeated string goodsTagIds = 120; // 商品标签ID列表
  repeated string goodsNos = 130; // 商品编码列表
  repeated string sourceShopIds = 140; //1688店铺ID列表
  string sourceGoodsId = 141; //1688商品ID
  string goodsNoLike  = 142; // 商品编码模糊搜索
}

// SKU信息查询（生成）参数
message SkuItemQueryParam {
  string id = 1; // 商品ID（必填）
  repeated GoodsExtendItem specItemList = 3; // 规格参数-附属属性
}

message ChangeGoodsOnlineStateParam {
  bool isOnline = 1; // 是否上架（必填）
  repeated string goodsIds = 2; // 商品ID列表
  bool notNeedConfirm = 3; // 不需要再次确认（false表示需要弹出确认框）
}

message GoodsInfoSaveParam {
  string id = 10; //商品ID（空值为新增）
  string goodsNo = 20; // 商品编号
//  string brandId = 4; // 品牌id
  string categoryId = 30; // 类目id
  string goodsName = 40; // 商品名称
  string goodsNameCn = 41; // 商品名称中文
//  string goodsTitle = 7; // 商品标题
  string goodsPriceUnitId = 50; // 商品价格单位ID，原goodsPriceUnit
  string goodsPriceUnitName = 60; // 商品价格单位名称（goodsPriceUnitId与goodsPriceUnitName必填其一；以goodsPriceUnitId优先；通过goodsPriceUnitName找ID，找不到自动创建）
  string goodsPriceUnitNameCn = 61; // 商品价格单位名称中文
  string videoCoverImage = 70; // 视频封面图片（原coverImage）
  string videoUrl = 80; // 视频URL（原video）
  repeated string goodsImageList = 90; // 商品图片组
  string goodsRemark = 100; // 商品备注
  int32 minBuyQuantity = 110; //最小购买数量（起订量）
  int32 minIncreaseQuantity = 120; //最小加购数量（一次加购数量）
  double priceFactor = 130; //价格系数
  double goodsLength = 140; // 商品长（单位：cm）
  double goodsWidth = 150; // 商品宽（单位：cm）
  double goodsHeight = 160; // 商品高（单位：cm）
  double goodsWeight = 170; // 商品重量（单位：kg）
  int32 xporPackageInsideCount = 171; // 发布到 XPorMayor 用的 包装含量/装箱数量
  int32 packageInsideCount = 175; // 包装内含货品的数量（包装含量/装箱数量）
  string packageInsideUnit = 176; // 包装内含货品的数量单位（包装规格）
//  int32 purchaseLimitation = 30; // 限购数量，大于等于0的整数,0表示不限购
//  string templateId = 31; // 运费模板id-附属属性
  repeated GoodsExtendItem specItemList = 190; // 规格参数
  repeated common.SkuStepRange skuStepRanges = 180; // SKU阶梯价的起始范围（在 goods 维度保存，每个SKU的阶梯范围，必须与此相同）
  repeated SkuItem skuList = 200; // sku数据
  repeated GoodsExtendItem attrItemList = 210; // 属性参数
  string goodsDesc = 220; // 商品描述（源goodsPcDesc）
  string crawlGoodsId = 300; //采集商品ID
  GoodsSourceType sourceType = 310; //商品来源类型（人工抓取、1688 同步、人工创建）
  string sourceGoodsId = 320; //源商品ID
  SourceGoodsState sourceGoodsState = 330; //源商品状态
  GoodsUpdateType goodsUpdateType = 340; //商品更新规则
  int32 sourceSalesQty = 350; //源平台商品销量
  string sourceShopId = 360; //源平台店铺ID
  SourceShopScoreInfoParam sourceShopScoreInfo = 370; //源平台店铺评分信息
  repeated string goodsTagIds = 400;
  repeated PackingSchemeItem packingSchemes = 410; //装箱方案数组
  double m3FreightUsd = 420; //1立方米预估运费，单位：美元；-1表示删除
  string accountId = 430; // 提交的账号ID
  bool syncTagToCrawlBox = 440; //是否同步标签数据到采集箱
}

// 更新价格系数的参数
message UpdatePriceFactorParam {
  string id = 10; //商品ID
  double priceFactor = 130; //价格系数
}

// 源平台卖家评分信息
message SourceShopScoreInfoParam {
  int32 tradeMedalLevel = 10; //卖家交易勋章(例子：5）
  double compositeServiceScore = 20; //综合服务分(例子：3.5）
  double logisticsExperienceScore = 30; //物流体验分(例子：4.5）
  double disputeComplaintScore = 40; //纠纷解决分(例子：3.0）
  double offerExperienceScore = 50; //商品体验分(例子：4.0）
  double consultingExperienceScore = 60; //咨询体验分(例子：5.0）
  double repeatPurchasePercent = 70; //卖家回头率(例子：0.4666）
  double afterSalesExperienceScore = 80; //退换体验分(例子：3.0）
}

// 翻译商品
message GoodsInfoTranslateParam {
  int32 type = 1; // 翻译类型，1、文字 2、图片 3、视频
  repeated string contents = 3; // 翻译内容
}

message GoodsImportParam {
  GoodsImportType importType = 1; // 导入类型
}

// 商品导入类型
enum GoodsImportType {
    GOODS_IMPORT_TYPE_IMPORT = 0;
    GOODS_IMPORT_TYPE_NAME = 1;
    GOODS_IMPORT_TYPE_CATEGORY = 2;
    GOODS_IMPORT_TYPE_IMAGE = 3;
    GOODS_IMPORT_TYPE_SINGLE_PRICE = 4;
    GOODS_IMPORT_TYPE_MULTI_PRICE = 5;
    GOODS_IMPORT_TYPE_STOCK = 6;
    GOODS_IMPORT_TYPE_BOX = 7; //装箱信息
    GOODS_IMPORT_TYPE_BOX_WITH_1688 = 8; //1688装箱信息
}

message SyncToXParam {
  repeated string goodsIds = 1; //商品id
  bool force = 2; //是否强制同步
}

//商城商品（营销规则）搜索参数
message MallGoodsListQueryParam {
  repeated string categoryIds = 10; //类目ID列表（admin后台分类ID）
  repeated string brandIds = 20; //品牌ID，多个用下划线（_）分隔
  string keyword = 30; //关键字
  double minPrice = 45; //最低价限制
  double maxPrice = 60; //最高价限制
  int32 leMinBuyQuantity = 61; //小于等于“最小购买数量”（起订量）
  int32 geMinBuyQuantity = 62; //大于等于“最小购买数量”（起订量）
  string marketingRuleId = 64; //营销规则ID
  string marketingRuleCode = 65; //营销规则代码
  marketing.GoodsOnlineDays goodsOnlineDays = 67; //商品上架时间
  int32 displayCount = 68; //展示数量
  int32 sortField = 70; //排序字段（11:人气升序；12:人气倒序；21:销量升序；22:销量倒序；31:价格升序；32:价格倒序）
  int32 pageNo = 80; //页号（从1开始，默认1）
  int32 pageSize = 90; //每页显示条数（后端自动匹配到最接近的页面条数）
  repeated string goodsTagIds = 100; //标签id
  string promotionId = 110; //促销活动ID
}

// 商品打标
message TagGoodsParam {
  repeated string goodsIds = 1; //商品ID列表
  repeated string goodsTagIds = 2; //商品打标
  bool updateTagWithAddMode = 6; //是否以“添加模式”更新商品标签（传true，不删除已有标签）
}

message GoodsListForTagPageQueryParam {
  common.PageParam page = 1;
  GoodsListForTagQueryParam query = 2;
}

message GoodsListForTagQueryParam {
  string shopName = 10; // 1688店铺名称
  string sourceGoodsId = 20; // 1688商品ID
  string sourceGoodsName = 30; // 1688商品名称
  string goodsNo = 40; //商品编号
  repeated string goodsTagIds = 50; // 商品标签ID列表
  SourceGoodsState sourceGoodsState = 60; //源商品状态
  int64 startPublishTime = 71; // 开始发布时间
  int64 endPublishTime = 72; // 结束发布时间
  int64 startUdate = 73; // 开始更新时间
  int64 endUdate = 74; // 结束更新时间
  GoodsOnlineState onlineState = 80;
}

message RemoveGoodsTagParam {

  repeated GoodsTagModel goodsTagList = 1;

}

message GoodsTagModel {
  string goodsId = 1;
  repeated string tagIdList = 2;
}

message PublishAndPutOnGoodsParam {
  repeated string sourceGoodsIdList = 1; //1688商品ID列表
}