<template>
  <div class="email-validation">
    <div class="validation-content">
      <div class="validation-title">
        {{ authStore.i18n("cm_common_verifyEmail") }}
      </div>
      <div class="validation-subtitle">
        {{ authStore.i18n("cm_common_unlockBundle") }}
      </div>
      <div class="validation-desc">
        {{ authStore.i18n("cm_common_verifyEmailPrompt") }}
      </div>
      <div
        class="validation-coupon px-[0.08rem] text-[0.3rem] leading-[0.3rem]"
      >
        <n-grid
          :cols="3"
          item-responsive
          :class="{ '!flex !justify-center': props.couponList.length < 3 }"
        >
          <n-gi v-for="coupon in props.couponList" :key="coupon?.id">
            <div
              class="flex items-center text-[#e50113] mb-[0.2rem]"
              :class="{
                'comm-coupon': coupon?.couponType === 'COUPON_TYPE_COMMISSION',
              }"
            >
              <div class="coupon-card mr-[0.04rem] text-[#fff]">
                <div
                  class="w-full h-[0.3rem] flex items-center justify-center mb-[0.04rem]"
                >
                  <template v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
                    <div>
                      <span
                        class="text-[0.28rem] leading-[0.28rem] font-semibold"
                      >
                        {{ discountToPercentage(coupon?.discount) }}
                      </span>
                      <span class="coupon-discount font-medium">
                        {{ authStore.i18n("cm_coupon.discount") }}
                      </span>
                    </div>
                  </template>
                  <template v-else>
                    <span class="coupon-unit font-medium">
                      <span>{{ currencyUnit }}</span>
                    </span>
                    <span class="text-[0.28rem] leading-[0.28rem] font-semibold"
                      >$
                      {{
                        setNewUnit(coupon?.preferentialAmount, "noUnit")
                      }}</span
                    >
                  </template>
                </div>
                <div class="coupon-type">
                  <span v-if="coupon?.couponType === 'COUPON_TYPE_PRODUCT'">
                    {{ authStore.i18n("cm_coupon.productCoupon") }}
                  </span>
                  <span v-if="coupon?.couponType === 'COUPON_TYPE_COMMISSION'">
                    {{ authStore.i18n("cm_coupon.commissionCoupon") }}
                  </span>
                </div>
              </div>
              <div class="flex w-[0.5rem] tracking-[-0.03rem]">
                <span class="mr-[0.04rem]">x</span>
                {{ coupon?.count }}
              </div>
            </div>
          </n-gi>
        </n-grid>
      </div>
      <n-button
        color="#E50113"
        text-color="#fff"
        :loading="pageData.loading"
        @click="resendVerification"
        class="rounded-[1rem] w-[4rem] h-[0.76rem] text-[0.32rem] mr-[0.24rem]"
      >
        <div>{{ authStore.i18n("cm_common_emailActivateNow") }}</div>
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts" name="emailValidation">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();

const props = defineProps({
  couponList: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive(<any>{
  loading: false,
});

// 发送验证邮箱的邮件
async function resendVerification() {
  pageData.loading = true;
  const res: any = await useSendVerifyMail({
    verifyMailScene: "MY_COUPON_LIST",
  });
  pageData.loading = false;
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.reload();
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      // 跳转邮箱
      navigateToEmail();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>

<style scoped lang="scss">
.email-validation {
  margin-top: 0.36rem;
  .validation-content {
    padding: 0.36rem 0.14rem;
    border-radius: 0.08rem;
    text-align: center;
    .validation-title {
      color: #333;
      font-size: 0.4rem;
      font-style: normal;
      font-weight: 500;
      line-height: 0.4rem;
    }
    .validation-subtitle {
      padding: 0 0.8rem;
      margin: 0.34rem 0;
      color: #e50113;
      text-align: center;
      font-family: Roboto;
      font-size: 0.32rem;
      font-style: normal;
      font-weight: 500;
      line-height: 0.4rem;
    }
    .validation-desc {
      padding: 0 0.4rem;
      color: #4d4d4d;
      font-size: 0.28rem;
      font-style: normal;
      font-weight: 400;
      line-height: 0.4rem;
    }
    .validation-coupon {
      margin: 0.4rem 0 0.68rem;
    }
  }
}

.coupon-card {
  width: 1.8rem;
  height: 0.88rem;
  background: url("@/assets/icons/marketing/productCoupon.png");
  background-size: 100%100%;
  padding: 0.2rem 0.08rem 0.08rem 0.48rem;
}
.comm-coupon {
  color: #dd4f12;
  .coupon-card {
    background: url("@/assets/icons/marketing/commissionCoupon.png");
    background-size: 100%100%;
  }
}
.coupon-type {
  width: 100%;
  text-transform: uppercase;
  letter-spacing: -0.02rem;
  text-align: center;
  text-wrap: nowrap;

  span {
    width: 150%;
    display: inline-block;
    font-size: 0.2rem;
    line-height: 0.2rem;
    transform: scale(0.7);
    transform-origin: left top;
  }
}
.coupon-unit {
  display: inline-block;
  transform: rotate(-90deg);
  span {
    display: inline-block;
    font-size: 0.2rem;
    transform: scale(0.9);
    transform-origin: right bottom;
  }
}
.coupon-discount {
  display: inline-block;
  font-size: 0.2rem;
  transform: scale(0.84);
  transform-origin: right top;
  margin-left: -0.08rem;
}
</style>
