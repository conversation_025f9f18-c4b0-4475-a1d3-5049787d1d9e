<template>
  <div>
    <div
      class="flex justify-between items-center py-[0.36rem] px-[0.24rem] border-b-1 border-[#F2F2F2]"
    >
      <div
        class="text-[#1EA62A] text-[0.36rem] leading-[0.36rem] font-medium"
        :class="props.showTabs ? '!text-[#1EA62A]' : 'text-[#333]'"
      >
        {{ authStore.i18n("cm_find.directOrderList") }}
        <span class="text-[0.28rem] leading-[0.28rem] ml-[0.08rem]">
          ({{ cartData.stat?.goodsCount || 0 }})
        </span>
      </div>
      <div class="flex items-center">
        <img
          loading="lazy"
          alt="back"
          class="w-[0.24rem] mr-[0.08rem]"
          src="@/assets/icons/common/address.svg"
          referrerpolicy="no-referrer"
        />
        <mobile-country-select
          @save="onSaveCountry"
          spm="select_site_from_cart"
        />
      </div>
    </div>

    <!-- 地址选择区域 -->
    <div class="bg-white pt-[0.24rem] pl-[0.18rem] pr-[0.32rem]">
      <div class="flex items-center">
        <icon-card name="mdi:address-marker" size="24" color="#e50113">
        </icon-card>
        <div class="text-[0.32rem] leading-[0.32rem]">
          {{ authStore.i18n("cm_addr.shippingAddress") }}
        </div>
        <span
          class="text-[#e50113] ml-[0.04rem]"
          v-if="isEmptyObject(pageData?.orderAddress)"
          >*</span
        >
      </div>
      <!-- 已选择地址 -->
      <div
        v-if="!isEmptyObject(pageData?.orderAddress)"
        class="border-b-1 border-[#F2F2F2] flex items-start ml-[0.5rem] pb-[0.28rem]"
        @click="onShowSelectAddress"
      >
        <div class="flex-1">
          <div class="text-[0.28rem] font-medium mb-[0.08rem]">
            {{ pageData?.orderAddress.contactName }}
            <span class="whitespace-nowrap">{{
              formatPhone(pageData?.orderAddress)
            }}</span>
          </div>
          <div class="text-[0.28rem] text-[#666] leading-[0.32rem]">
            {{ pageData?.orderAddress.fullAddress }}
          </div>
        </div>
        <img
          alt="edit"
          @click="onShowSelectAddress"
          class="w-[0.32rem] ml-[0.44rem] cursor-pointer"
          src="@/assets/icons/find/edit.svg"
        />
      </div>

      <!-- 未选择地址 -->
      <div
        v-else
        @click="onOpenAddAddr"
        class="border border-dashed border-[#E50113] rounded-[0.12rem] py-[0.24rem] h-[0.8rem] text-center flex items-center justify-center gap-[0.08rem] mt-[0.08rem] ml-[0.4rem] mr-[0.28rem]"
      >
        <img
          class="w-[0.32rem] leading-[0.32rem]"
          src="@/assets/icons/common/add-primary.svg"
          :alt="authStore.i18n('cm_addr.shippingAddress')"
        />
        <div class="text-[0.32rem] leading-[0.32rem] text-[#E50113]">
          {{ authStore.i18n("cm_addr.createNewAddress") }}
        </div>
      </div>
    </div>

    <!-- 商品列表区域 -->
    <div
      class="goods-section bg-white rounded-[0.16rem] p-[0.16rem] mb-[0.16rem]"
    >
      <div v-if="cartData.goodsList.length">
        <!-- 二级 -->
        <div
          v-for="(goods, index) in cartData.goodsList"
          :key="goods.goodsId"
          class="mb-[0.5rem]"
        >
          <div class="flex items-start w-full">
            <n-checkbox
              class="mr-[0.16rem] mt-[0.24rem]"
              v-model:checked="goods.selected"
              @update:checked="
                (value) => $emit('onGoodsSelection', value, goods)
              "
            >
            </n-checkbox>
            <a
              :href="`/h5/goods/${goods.goodsId}${
                goods.padc ? `?padc=${goods.padc}` : ''
              }`"
              data-spm-box="cart-goods-list"
              :data-spm-index="index + 1"
            >
              <mobile-goods-card
                :goods="goods"
                class="flex-1"
                spmCode="cart-goods-list"
                ><icon-card
                  name="uil:trash-alt"
                  color="#797979"
                  size="0.4rem"
                  class="mx-[0.16rem]"
                  @click.stop.prevent="$emit('onDeleteGoods', goods)"
                ></icon-card
              ></mobile-goods-card>
            </a>
          </div>
          <!-- 三级分类 -->
          <div
            v-for="sku in goods.skuList"
            :key="sku.skuId"
            class="mb-[0.24rem]"
          >
            <div class="flex items-center">
              <n-checkbox
                class="mr-[0.16rem]"
                v-model:checked="sku.selected"
                @update:checked="
                  (value) => $emit('onSkuSelection', value, sku, goods)
                "
              >
              </n-checkbox>
              <mobile-sku-card
                :sku="sku"
                :goods="goods"
                @onCartQtyUpdate="
                  (value) => $emit('onCartQtyUpdate', value, sku, goods)
                "
                :step="sku.minIncreaseQuantity"
                ><template v-slot:spec>
                  <icon-card
                    name="iconamoon:arrow-right-2"
                    color="#797979"
                    size="0.45rem"
                    @click.stop="$emit('onOpenSkuDialog', sku, goods)"
                  ></icon-card>
                </template>
                <template v-slot:delete>
                  <icon-card
                    name="uil:trash-alt"
                    color="#797979"
                    size="0.4rem"
                    @click="$emit('onDeleteSku', sku, goods)"
                  >
                  </icon-card>
                </template>
              </mobile-sku-card>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else>
        <n-empty
          :description="authStore.i18n('cm_order.noData')"
          class="mt-[1.92rem] text-[0.28rem]"
        >
          <template #extra>
            <a href="/h5">
              <n-button
                size="small"
                color="#E50113"
                text-color="#fff"
                class="text-[0.28rem] h-[0.56rem] px-[0.2rem]"
              >
                {{ authStore.i18n("cm_find.goHome") }}
              </n-button>
            </a>
          </template>
        </n-empty>
      </div>
    </div>

    <!-- 底部信息 -->
    <div
      class="w-full fixed bottom-[1.32rem] bg-white pt-[0.24rem] pb-[0.32rem] px-[0.16rem] z-[99]"
    >
      <div>
        <div class="flex items-start justify-between">
          <!-- 一级 -->
          <n-checkbox
            v-model:checked="selectAll"
            @update:checked="onAllSelection"
            ><span class="text-[0.32rem] leading-[0.32rem]">{{
              authStore.i18n("cm_find_selectAll")
            }}</span></n-checkbox
          >
          <div
            class="flex gap-[0.08rem] items-center text-[#E50113] cursor-pointer text-[0.28rem] leading-[0.28rem]"
            @click="openCostDetailsDrawer"
          >
            <div>{{ authStore.i18n("cm_find.viewDetails") }}</div>
            <img
              src="@/assets/icons/common/arrow-top-medium-primary.svg"
              :alt="authStore.i18n('cm_find.viewDetails')"
              class="arrow-icon transition-transform duration-300"
              :class="{ 'rotate-180': pageData.costDetailsDrawerVisible }"
            />
          </div>
        </div>
        <div
          class="flex text-[0.28rem] leading-[0.28rem] items-center justify-end mt-[0.12rem]"
        >
          <div
            class="flex justify-between items-center text-[0.36rem] leading-[0.36rem]"
          >
            <span class="text-[0.28rem] leading-[0.28rem]"
              >{{ authStore.i18n("cm_find.totalPayment") }}:</span
            >
            <span
              class="text-[0.36rem] leading-[0.36rem] font-medium text-[#e50113]"
            >
              <span
                class="text-[0.32rem] leading-[0.32rem] ml-[0.08rem] mr-[0.04rem]"
                >{{ monetaryUnit }}</span
              >{{ setNewUnit(cartData.stat?.selectTotalActualPrice, true) }}
            </span>
          </div>
        </div>
      </div>
      <n-button
        block
        round
        size="large"
        color="#E50113"
        text-color="#fff"
        class="h-[0.88rem] mt-[0.32rem]"
        @click="onGoDirectPayment($event)"
        data-spm-box="cart-fast-create-order"
      >
        <div class="text-[0.32rem] leading-[0.32rem] font-medium">
          {{ authStore.i18n("cm_find.confirmOrder") }}
        </div>
      </n-button>
    </div>

    <!-- 抽屉容器 -->
    <div id="drawer-container" class="drawer-container"></div>

    <!-- 费用明细抽屉 -->
    <n-drawer
      v-model:show="pageData.costDetailsDrawerVisible"
      height="3.6rem"
      placement="bottom"
      :mask-closable="false"
      :show-mask="true"
      :close-on-esc="true"
      :auto-focus="false"
      to="#drawer-container"
      :trap-focus="false"
      :block-scroll="false"
    >
      <n-drawer-content :closable="true">
        <template #header>
          <div
            class="text-[0.36rem] leading-[0.36rem] font-medium text-center pt-[0.28rem] pb-[0.16rem]"
          >
            {{ authStore.i18n("cm_find.orderSummary") }}
          </div>
        </template>
        <div
          class="text-[0.32rem] leading-[0.32rem] text-[#1A1A1A] flex flex-col gap-[0.32rem]"
        >
          <!-- 商品数量 -->
          <div class="flex justify-between">
            <span>{{ authStore.i18n("cm_find_quantityOfUnits") }}</span>
            <span>
              <span class="font-medium">{{
                cartData.stat?.selectSkuTotalQuantity || 0
              }}</span>
              <span class="text-[0.28rem] leading-[0.28rem] ml-[0.08rem]">{{
                cartData.stat?.selectSkuTotalQuantity > 1
                  ? authStore.i18n("cm_find_totalSkuUnits")
                  : authStore.i18n("cm_find_totalSkuUnit")
              }}</span>
            </span>
          </div>

          <!-- 费用明细 -->
          <div class="flex justify-between">
            <span>{{ authStore.i18n("cm_find_itemsCost") }}:</span>
            <span class="text-[#333333] font-medium">
              {{ setUnit(cartData.stat?.selectGoodsSalePrice || 0) }}
            </span>
          </div>

          <div class="flex justify-between">
            <span>{{ authStore.i18n("cm_news.commission") }}:</span>
            <span class="font-medium">
              {{ setUnit(cartData.stat?.selectCommission || 0) }}
            </span>
          </div>

          <div class="flex justify-between">
            <span>{{ authStore.i18n("cm_find.finalShipping") }}:</span>
            <span class="font-medium">
              {{ setUnit(cartData.stat?.selectRouteFee || 0) }}
            </span>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>

    <!-- 地址选择抽屉 -->
    <SelectAddressDrawer
      ref="selectAddressDrawerRef"
      :addressList="pageData.addressList"
      @addAddress="onOpenAddAddr"
      @editAddress="onOpenEditAddr"
      @confirmAddress="onConfirmSelectAddress"
      @refresh="onUpdateListUserAddress"
    />

    <!-- 添加/编辑地址抽屉 -->
    <AddAddressDrawer
      ref="addAddressDrawerRef"
      :country-list="pageData.countryList"
      :country-regexes="pageData.countryRegexes"
      @onUpdateListUserAddress="onUpdateListUserAddress"
    />

    <!-- 错误弹窗 -->
    <ErrorModal
      :message="pageData.errorMessage"
      v-model:visible="pageData.errorDialogVisible"
    />

    <SubmitLoadingModal :show="pageData.submitLoading" />
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import MobileGoodsCard from "./MobileGoodsCard.vue";
import MobileSkuCard from "./MobileSkuCard.vue";
import ErrorModal from "./ErrorModal.vue";
import AddAddressDrawer from "./AddAddressDrawer.vue";
import SelectAddressDrawer from "./SelectAddressDrawer.vue";
import SubmitLoadingModal from "@/pages/find/components/SubmitLoadingModal.vue";

const authStore = useAuthStore();
const config = useRuntimeConfig();
const selectAddressDrawerRef = ref<any>(null);
const addAddressDrawerRef = ref<any>(null);

const props = defineProps({
  cartData: {
    type: Object,
    default: () => {},
  },
  showTabs: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "onAllSelection",
  "onGoodsSelection",
  "onSkuSelection",
  "onCartQtyUpdate",
  "onDeleteGoods",
  "onDeleteSku",
  "onOpenSkuDialog",
]);

// 响应式数据
const pageData = reactive<any>({
  errorMessage: "",
  errorDialogVisible: false,
  orderAddress: <any>{},
  countryList: <any>[],
  addressList: <any>[],
  countryRegexes: <any>{},
  costDetailsDrawerVisible: false,
});

// 计算全选状态
const selectAll = computed(() => {
  return (
    props.cartData?.goodsList?.every((goods: any) => goods.selected) || false
  );
});

onListUserAddress("init");
onGetCountry();

// 地址列表加载函数
async function onListUserAddress(type?: string) {
  const res: any = await useListUserAddress({});
  if (res?.result?.code === 200) {
    // 只保留秘鲁国家的地址
    const peruAddresses =
      res?.data?.filter(
        (item: any) => item.countryId === config.public.peruCountryId
      ) || [];

    pageData.addressList = peruAddresses;

    // 地址选择逻辑：优先选择默认地址，如果没有默认地址则选择第一条
    if (type === "init" && peruAddresses.length > 0) {
      // 查找默认地址
      const defaultAddress = peruAddresses.find((item: any) => item.isDefault);

      if (defaultAddress) {
        // 有默认地址，选择默认地址
        pageData.orderAddress = defaultAddress;
      } else {
        // 没有默认地址，选择第一条秘鲁地址
        pageData.orderAddress = peruAddresses[0];
      }
    }
  } else if (res?.result?.code === 403) {
    navigateToPage(
      `/h5/user/login`,
      { pageSource: window.location.href },
      false
    );
  }
}

// 获取国家列表
async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    // 设置秘鲁国家的默认数据
    const country = pageData.countryList.find(
      (item: any) => item.id === config.public.peruCountryId
    );
    if (country) {
      pageData.countryRegexes = country;
    }
  }
}

const formatPhone = (address: any) => {
  return `${address.areaCode || ""} ${address.phone || ""}`.trim();
};

const onAllSelection = (value: boolean) => {
  emit("onAllSelection", value, true);
};

function openCostDetailsDrawer() {
  pageData.costDetailsDrawerVisible = !pageData.costDetailsDrawerVisible;
}

function onShowSelectAddress() {
  selectAddressDrawerRef.value?.onShowDrawer(pageData.orderAddress);
}

function onConfirmSelectAddress(address: any) {
  pageData.orderAddress = address;
}

// 新增地址
function onOpenAddAddr(type: any = "modal") {
  addAddressDrawerRef.value?.onShowDrawer(type);
}

// 编辑地址
function onOpenEditAddr(type: any = "modal", address: any) {
  addAddressDrawerRef.value?.onShowDrawer(type, address);
}

function onUpdateListUserAddress(type?: any) {
  if (type === "inline") {
    onListUserAddress("init");
  } else {
    onListUserAddress();
  }
}

// 直接下单处理
async function onGoDirectPayment(event: any) {
  if (isEmptyObject(pageData?.orderAddress)) {
    showToast(authStore.i18n("cm_addr.pleaseSelectAddress"));
    return;
  }
  pageData.submitLoading = true;
  const selectedSkuList = <any>[];
  const currentGoodsList = props.cartData?.goodsList || [];
  currentGoodsList.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      if (sku.selected) {
        selectedSkuList.push({
          quantity: sku.buyQty,
          skuId: sku.skuId,
          spm: sku.spm,
          routeId: goods.routeId,
          padc: sku.padc,
        });
      }
    });
  });

  const res: any = await useFastCreateOrder({
    skuList: selectedSkuList,
    siteId: window.siteData.siteInfo.id,
    orderAddress: pageData.orderAddress,
  });
  if (res?.result?.code === 200) {
    // 埋点：订单购物车快速创建订单成功
    window?.MyStat?.addPageEvent(
      "order_cart_create_order_success",
      `创建订单成功，订单号：${res.data.orderNo}`,
      true
    );

    sessionStorage.setItem("fromCartToOrderDetail", "true");
    navigateToPage(
      `/h5/order/details`,
      { orderNo: res.data.orderNo },
      false,
      event
    );
  } else if (res?.result?.code === 403) {
    navigateToPage(
      `/h5/user/login`,
      { pageSource: window.location.href },
      false
    );
  } else {
    // 埋点：订单购物车快速创建订单失败
    window?.MyStat?.addPageEvent(
      "order_cart_create_order_fail",
      `创建订单失败，原因：${res?.result?.message}`
    );

    pageData.errorDialogVisible = true;
    setTimeout(() => {
      pageData.errorDialogVisible = false;
    }, 3000);
    pageData.errorMessage =
      res?.result?.message || authStore.i18n("cm_find.errorMessage");
  }
  pageData.submitLoading = false;
}

const onSaveCountry = () => {
  window.location.reload();
};
</script>

<style scoped lang="scss">
:deep(.n-checkbox.n-checkbox--checked .n-checkbox-box) {
  background: #e50113;
  border-color: #e50113;
}

:deep(.n-drawer-body-content-wrapper) {
  padding: 0.08rem 0.16rem !important;
}

:deep(.n-base-icon svg) {
  width: 0.32rem !important;
  height: 0.32rem !important;
}
:deep(.n-base-close) {
  color: #000 !important;
}

.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 3.9rem;
  pointer-events: none;
  z-index: 3;
}

:deep(.n-drawer) {
  box-shadow: none !important;
}
</style>
