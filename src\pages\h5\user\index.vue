<template>
  <div class="mobile-container">
    <div class="user-header px-[0.4rem]" v-if="userInfo?.username">
      <icon-card
        size="1.32rem"
        color="#fff"
        class="mr-[0.16rem]"
        name="iconamoon:profile-circle-fill"
      ></icon-card>
      <div
        class="text-[0.32rem] leading-[0.48rem] text-white break-all w-[70%]"
      >
        {{ pageData.userInfo.email }}
      </div>
      <a href="/h5/user/setting" data-spm-box="myhome-account-main">
        <icon-card
          size="0.44rem"
          color="#fff"
          class="absolute top-[0.3rem] right-[0.24rem]"
          name="carbon:settings"
        ></icon-card>
      </a>
    </div>
    <div
      v-else
      class="header"
      data-spm-box="myhome-unlogin-body"
      :style="`background-image: url(${unloginUser})`"
    >
      <a href="/h5/user/login?pageSource=/h5/user">
        <n-button
          round
          color="#000"
          text-color="#fff"
          class="w-[2.12rem] mr-[0.16rem] p-0 text-[0.28rem] h-[0.68rem]"
        >
          {{ authStore.i18n("cm_common_login") }}
        </n-button>
      </a>
      <a href="/h5/user/register?pageSource=/h5/user">
        <n-button
          color="#fff"
          text-color="#e50113"
          round
          class="w-[2.12rem] text-[0.28rem] h-[0.68rem]"
        >
          {{ authStore.i18n("cm_common_register") }}
        </n-button>
      </a>
    </div>

    <div
      class="w-full px-[0.28rem]"
      :data-spm-box="
        userInfo?.username ? 'myhome-account-main' : 'myhome-unlogin-body'
      "
    >
      <div
        v-if="userInfo?.username"
        class="text-center bg-white rounded-[0.08rem] py-[0.3rem] px-[0.1rem] mb-[0.2rem] min-h-[1.8rem]"
      >
        <a href="/h5/user/inquiry" class="w-[2.5rem]">
          <div class="w-[2rem] text-[0.5rem] leading-[0.72rem] font-medium">
            {{ pageData.userInfo.inquiryCount }}
          </div>
          <div
            class="w-[2rem] text-[0.32rem] leading-[0.48rem]"
            v-if="
              pageData.userInfo.inquiryCount ||
              pageData.userInfo.inquiryCount === 0
            "
          >
            {{ authStore.i18n("cm_user.consultas") }}
          </div>
        </a>
      </div>
      <div
        class="text-center bg-white rounded-[0.08rem] py-[0.3rem] px-[0.3rem] mb-[0.2rem]"
      >
        <a
          :href="
            userInfo?.username
              ? '/h5/user/orderList'
              : `/h5/user/login?pageSource=${encodeURIComponent(
                  '/h5/user/orderList'
                )}`
          "
          class="flex items-center justify-between"
        >
          <span class="text-[0.32rem] font-medium">{{
            authStore.i18n("cm_order.myOrder")
          }}</span>
          <icon-card
            color="#333"
            name="iconamoon:arrow-right-2-light"
            size="0.6rem"
          />
        </a>
      </div>
      <n-space vertical :style="{ gap: '0.2rem 0' }">
        <a
          v-for="(jump, index) in jumpData"
          :key="index"
          :href="
            userInfo?.username
              ? jump.path
              : `/h5/user/login?pageSource=${encodeURIComponent(jump.path)}`
          "
        >
          <div
            class="bg-white rounded-[0.08rem] py-[0.3rem] pl-[0.3rem] pr-[0.16rem] flex items-center"
          >
            <span
              ><icon-card
                :size="jump.iconSize"
                color="#E50113"
                class="mr-[0.16rem]"
                :name="jump.icon"
              ></icon-card
            ></span>
            <span class="text-[0.32rem] leading-[0.48rem]">
              {{ jump.title }}
            </span>
            <span
              v-if="!!jump.subtitle"
              class="text-[0.24rem] pl-[0.04rem] leading-[0.32rem] text-gray-500"
              >{{ "(" + jump.subtitle + ")" }}</span
            >
          </div>
        </a>
      </n-space>
      <div
        class="bg-white rounded-[0.08rem] py-[0.3rem] px-[0.3rem] flex items-center mt-[0.2rem]"
      >
        <icon-card
          size="0.46rem"
          color="#E50113"
          class="mr-[0.16rem]"
          name="tdesign:location"
        ></icon-card>
        <mobile-country-select />
      </div>

      <n-button
        size="large"
        color="#fff"
        text-color="#000"
        v-if="userInfo?.username"
        @click="onLogoutClick($event)"
        data-spm-box="myhome-logout-botton"
        class="rounded-[0.08rem] w-full h-[1rem] mt-[0.5rem] border border-[#c7c7c7]"
      >
        <span class="text-[0.32rem] leading-[0.48rem]">
          {{ authStore.i18n("cm_common_loginOut") }}
        </span>
      </n-button>
    </div>
    <!-- 底部信息 -->
    <mobile-tab-bar :naiveBar="4" />
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import unloginUser from "@/assets/icons/unlogin-user.png";
import { getSiteInfo } from "@/utils/siteUtils";

const siteInfo = computed(() => getSiteInfo());
const authStore = useAuthStore();
const config = useRuntimeConfig();
const pageData = reactive(<any>{
  userInfo: <any>{},
  orderState: null,
});

const userInfo = ref<object>({});
userInfo.value = config.public.userInfo as object;

const jumpData = ref([
  {
    iconSize: "0.44rem",
    icon: "pepicons-pop:share-android-circle",
    path: "/h5/user/invite",
    title: authStore.i18n("cm_user.invite"),
    subtitle: authStore.i18n("cm_user.inviteSubtitle"),
  },
  {
    iconSize: "0.46rem",
    icon: "ic:outline-email",
    path: "/h5/user/inquiry",
    title: authStore.i18n("cm_user.inquiry"),
  },
  {
    iconSize: "0.46rem",
    icon: "ri:coupon-2-line",
    path: "/h5/user/coupon",
    title: authStore.i18n("cm_bar.myCoupon"),
  },
  {
    iconSize: "0.48rem",
    icon: "tabler:address-book",
    path: "/h5/user/address",
    title: authStore.i18n("cm_user.direction"),
  },
]);

onBeforeMount(() => {
  window.addEventListener("popstate", () => {
    // 用户点击了后退或前进按钮
    !pageData.userInfo.email && location.reload();
  });
});

onBeforeUnmount(() => {
  // 页面离开时，清除监听事件
  window.removeEventListener("popstate", () => {});
});

onUserDetail();
async function onUserDetail() {
  const res: any = await useUserDetail({});
  if (res?.result?.code === 200) {
    pageData.userInfo = res?.data;
  }
}

async function onLogoutClick(event: any) {
  try {
    const res: any = await useLogout({});
    if (res?.result?.code === 200) {
      authStore.setUserInfo({});
      navigateToPage("/h5/user", {}, false, event);
    } else {
      showToast(res?.result?.message);
    }
  } catch (error) {
    showToast(error);
  }
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  padding-bottom: 1.72rem;
  overflow-y: auto;
  background: #f2f2f2;
  min-height: 100vh;
}
.header {
  height: 3.94rem;
  margin-bottom: -0.26rem;
  background-size: 100%100%;
  padding: 2.48rem 0.28rem 0 0.28rem;
}
.user-header {
  display: flex;
  align-items: center;
  height: 2.4rem;
  background: radial-gradient(circle at left, #e72528, #f13d33, #fc573f);
  margin-bottom: -0.4rem;
}

:deep(.country-delivery) {
  font-size: 0.32rem;
  margin-right: 0.24rem;
}
</style>
