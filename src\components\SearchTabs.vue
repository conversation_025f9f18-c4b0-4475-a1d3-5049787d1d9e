<template>
  <div class="w-full relative my-2 px-6 relative">
    <!-- 轮播图 -->
    <n-carousel
      v-if="!noCarouselCate"
      class="w-full py-4 static"
      show-arrow
      :show-dots="false"
      :slides-per-view="9"
      :space-between="10"
      :loop="false"
    >
      <n-carousel-item v-for="cate in allCates" :key="cate.id">
        <n-button
          text
          :text-color="textColor"
          :type="getButtonType(cate)"
          @click="onMouseEnter(cate)"
          @mouseleave="onMouseLeave"
        >
          <div>{{ cate.cateName }}</div>
        </n-button>
      </n-carousel-item>

      <template #arrow="{ prev, next }">
        <div class="custom-arrow">
          <div class="custom-arrow-icon custom-arrow--left">
            <icon-card
              name="iconamoon:arrow-left-2-light"
              :color="textColor"
              size="30"
              @click="prev"
            />
          </div>
          <div class="custom-arrow-icon custom-arrow--right">
            <icon-card
              name="iconamoon:arrow-right-2-light"
              :color="textColor"
              size="30"
              @click="next"
            />
          </div>
        </div>
      </template>
    </n-carousel>

    <div v-else>
      <slot
        name="default"
        :onMouseEnter="onMouseEnter"
        :onMouseLeave="onMouseLeave"
      ></slot>
    </div>

    <transition
      appear
      enter-active-class="animated fadeIn"
      leave-active-class="animated fadeOut"
      :duration="300"
    >
      <div
        ref="categoryRef"
        v-if="pageData.visible"
        class="bg-white category-container w-full"
        @mouseleave="onMouseLeaveCategory"
      >
        <n-split :default-size="0.3">
          <template #1>
            <n-scrollbar style="max-height: 320px" trigger="hover">
              <div class="flex flex-col pr-2">
                <a
                  v-for="(cate, index) in pageData.categoryTree"
                  :key="cate.id"
                  :href="`${goodsListPath}/${cate.id}?cateName=${cate.cateName}`"
                  target="_blank"
                  data-spm-box="navigation-category"
                  :data-spm-index="index + 1"
                  class="w-full block border-b-1 border-[#F2F2F2]"
                >
                  <div
                    class="px-4 py-3 text-left text-[#333]"
                    @click="onCategoryClick(cate, 1)"
                    @mouseenter="(e) => onMouseEnterCategory(e, cate)"
                    :class="{
                      'font-medium bg-[#F2F2F2]': pageData.cateId === cate.id,
                    }"
                  >
                    {{ cate.cateName }}
                  </div>
                </a>
              </div>
            </n-scrollbar>
          </template>
          <template #2>
            <n-split>
              <template #1>
                <n-scrollbar style="max-height: 320px" trigger="hover">
                  <div class="flex flex-col px-2">
                    <a
                      v-for="(item, iIndex) in secondaryCates"
                      :key="item.id"
                      :href="`${goodsListPath}/${item.id}?cateName=${item.cateName}`"
                      target="_blank"
                      data-spm-box="navigation-subcategory"
                      :data-spm-index="iIndex + 1"
                      class="w-full block border-b-1 border-[#F2F2F2]"
                    >
                      <div
                        class="px-4 py-3 text-left text-[#333]"
                        @click="onCategoryClick(item, 2)"
                        @mouseenter="(e) => onMouseEnterSecondary(e, item)"
                        :class="{
                          'font-medium bg-[#F2F2F2]':
                            pageData.secondaryCid === item.id,
                        }"
                      >
                        {{ item.cateName }}
                      </div>
                    </a>
                  </div>
                </n-scrollbar>
              </template>
              <template #2>
                <n-scrollbar style="max-height: 320px" trigger="hover">
                  <div class="flex flex-col px-2">
                    <a
                      v-for="(subItem, sIndex) in tertiaryCates"
                      :key="subItem.id"
                      :href="`${goodsListPath}/${subItem.id}?cateName=${subItem.cateName}`"
                      target="_blank"
                      data-spm-box="navigation-tertiarycategory"
                      :data-spm-index="sIndex + 1"
                      class="w-full block border-b-1 border-[#F2F2F2]"
                    >
                      <div
                        class="px-4 py-3 text-left text-[#333]"
                        @click="onCategoryClick(subItem, 3)"
                        @mouseenter="(e) => onMouseEnterTertiary(e, subItem)"
                        :class="{
                          'font-medium bg-[#F2F2F2]':
                            pageData.tertiaryCid === subItem.id,
                        }"
                      >
                        {{ subItem.cateName }}
                      </div>
                    </a>
                  </div>
                </n-scrollbar>
              </template>
            </n-split>
          </template>
        </n-split>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/authStore";
import { goodsListPath } from "@/utils/constant";

interface Category {
  id: string;
  cateName: string;
  children?: Category[];
}

const props = defineProps({
  textColor: {
    type: String,
    default: "",
  },
  categories: {
    type: Array as () => Category[],
    default: () => [],
  },
  cleareCate: {
    type: Boolean,
    default: false,
  },
  noCarouselCate: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const categoryRef = ref<HTMLElement | null>(null);

const pageData = reactive({
  cateId: route?.query?.primaryCid || route?.params?.id || null,
  secondaryCid: route?.query?.secondaryCid || null,
  tertiaryCid: route?.query?.tertiaryCid || null,
  categoryTree: [] as Category[],
  visible: false,
  moveOutFlag: false,
});

const allCates = computed(() => [
  { id: "0", cateName: "Catálogo" },
  ...pageData.categoryTree,
]);

const secondaryCates = computed(() => {
  const target = pageData.categoryTree.find(
    (item) => item.id === pageData.cateId
  ) ||
    pageData.categoryTree[0] || { children: [] };
  return target.children || [];
});

const tertiaryCates = computed(() => {
  if (!pageData.secondaryCid) return [];
  const target = secondaryCates.value.find(
    (item) => item.id === pageData.secondaryCid
  );
  return target?.children || [];
});

watch(
  () => props.cleareCate,
  (newVal) => {
    if (newVal) {
      pageData.cateId = null;
      pageData.secondaryCid = null;
      pageData.tertiaryCid = null;
    }
  }
);

watch(
  () => props.categories,
  (newVal) => {
    if (newVal && newVal.length) {
      pageData.categoryTree = newVal as Category[];
      // 设置默认选中项
      if (!pageData.cateId && pageData.categoryTree.length > 0) {
        pageData.cateId = pageData.categoryTree[0].id;
        if (pageData.categoryTree[0].children?.length) {
          pageData.secondaryCid = pageData.categoryTree[0].children[0].id;
        }
      }
    }
  },
  { immediate: true, deep: true }
);

onMounted(async () => {
  if (!props.categories?.length) {
    const res: any = await useCategoryTree({});
    if (res?.result?.code === 200) {
      pageData.categoryTree = res?.data;
      // 设置默认选中项
      if (pageData.categoryTree.length > 0 && !pageData.cateId) {
        pageData.cateId = pageData.categoryTree[0].id;
        if (pageData.categoryTree[0].children?.length) {
          pageData.secondaryCid = pageData.categoryTree[0].children[0].id;
        }
      }
    }
  }

  window.addEventListener("mousemove", onMousemove);
});

onBeforeUnmount(() => {
  window.removeEventListener("mousemove", onMousemove);
});

function getButtonType(cate: Category) {
  if (props.textColor) return "tertiary";
  return route?.query?.primaryCid || route?.params?.id === cate.id
    ? "primary"
    : "tertiary";
}

function onCategoryClick(cate: Category, level: number) {
  window?.MyStat?.addPageEvent(`search_category`, `分类名称：${cate.cateName}`);

  if (level === 1) {
    pageData.cateId = cate.id;
    // 选中一级分类时，默认选中其第一个二级分类
    const target = pageData.categoryTree.find((item) => item.id === cate.id);
    if (target?.children?.length) {
      pageData.secondaryCid = target.children[0].id;
    } else {
      pageData.secondaryCid = null;
    }
    pageData.tertiaryCid = null;
  } else if (level === 2) {
    pageData.secondaryCid = cate.id;
    pageData.tertiaryCid = null;
  } else {
    pageData.tertiaryCid = cate.id;
  }

  closeCategoryMenu();
}

function onMouseEnter(cate: Category) {
  if (pageData.visible) {
    closeCategoryMenu();
    return;
  }

  if (cate.id === "all") {
    pageData.visible = false;
    return;
  }

  setTimeout(() => {
    pageData.visible = true;
    const target =
      pageData.categoryTree.find((item) => item.id === cate.id) ||
      pageData.categoryTree[0];
    pageData.cateId = target.id;
    // 鼠标悬停时也保持默认选中第一个二级分类
    if (target.children?.length) {
      pageData.secondaryCid = target.children[0].id;
    } else {
      pageData.secondaryCid = null;
    }
    pageData.tertiaryCid = null;
  }, 200);
}

function onMouseEnterCategory(e: Event, cate: Category) {
  pageData.moveOutFlag = false;
  pageData.cateId = cate.id;
  // 鼠标进入一级分类时，默认选中其第一个二级分类
  const target = pageData.categoryTree.find((item) => item.id === cate.id);
  if (target?.children?.length) {
    pageData.secondaryCid = target.children[0].id;
  } else {
    pageData.secondaryCid = null;
  }
  pageData.tertiaryCid = null;
}

function onMouseEnterSecondary(e: Event, item: Category) {
  pageData.secondaryCid = item.id;
  // 不自动选中三级分类
  pageData.tertiaryCid = null;
}

function onMouseEnterTertiary(e: Event, item: Category) {
  pageData.tertiaryCid = item.id;
}

function onMouseLeave(e: MouseEvent) {
  const clientY = e.clientY + 6;
  const bottom = (e.target as HTMLElement).getBoundingClientRect().bottom;

  if (bottom > clientY) {
    closeCategoryMenu();
  }
}

function onMouseLeaveCategory(e: MouseEvent) {
  const rect = (e.target as HTMLElement).getBoundingClientRect();
  if (
    e.clientY > rect.bottom ||
    e.clientX < rect.left ||
    e.clientX > rect.right
  ) {
    closeCategoryMenu();
  }
}

function onMousemove(e: MouseEvent) {
  const categoryEl = categoryRef.value;
  if (!categoryEl) return;

  const rect = categoryEl.getBoundingClientRect();
  if (
    e.clientY > rect.bottom ||
    e.clientX < rect.left ||
    e.clientX > rect.right
  ) {
    closeCategoryMenu();
  }
}

function closeCategoryMenu() {
  pageData.moveOutFlag = true;
  setTimeout(() => {
    pageData.visible = !pageData.moveOutFlag;
  }, 200);
}
</script>

<style scoped lang="scss">
.category-container {
  position: absolute;
  top: 46px;
  left: 0px;
  padding: 1rem;
  z-index: 98;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.custom-arrow {
  .custom-arrow-icon {
    width: 28px;
    height: 28px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .custom-arrow--left {
    left: 0;
  }
  .custom-arrow--right {
    right: 0;
  }
}

:deep(.n-carousel__slide) {
  width: auto !important;
  padding: 0 16px;
}
</style>
