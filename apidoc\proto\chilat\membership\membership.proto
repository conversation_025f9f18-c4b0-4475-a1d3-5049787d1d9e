syntax = "proto3";
package chilat.membership;

option java_package = "com.chilat.rpc.membership";

import "chilat/membership/param/membership_param.proto";
import "chilat/membership/model/membership_model.proto";
import "common.proto";

// 会员管理
service Membership {
  // 会员列表
  rpc pageList(MembershipPageQueryParam) returns (MembershipPageQueryResp);
  // 会员导出
  rpc exportMember(MembershipQueryParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };
  // 查询所有地址
  rpc listMembershipAddress(common.IdParam) returns (MembershipAddressResp);
  // 保存地址
  rpc saveUserAddress(SaveUserAddressParam) returns (common.ApiResult);
  // 设为默认
  rpc addressToDefault(common.IdParam) returns (common.ApiResult);

  // 待废弃（改用 listByPrivilegeV2）
  rpc listByPrivilege(common.EmptyParam) returns (MembershipListResp);

  // 查询有权限的用户列表（最大返回100行）
  rpc listByPrivilegeV2(ListByPrivilegeParam) returns (ListByPrivilegeResp);

  rpc initUserMember(common.EmptyParam) returns (common.ApiResult);
}
