/**
 *  nuxt项目目录/composables/http.ts
 */

import { useNuxtApp } from "#app";
import { useAuthStore } from "@/stores/authStore";

//定义ts变量类型接口
interface HttpParms {
  baseURL?: string; //请求的基本URL，即后台服务器地址，（若服务器请求地址只有一个，可不填）
  url: string; //请求api接口地址
  method?: any; //请求方法
  query?: any; //添加查询搜索参数到URL
  body?: any; //请求体
}

function getHttpHeaders() {
  let srcHeaders = useRequestHeaders();
  let dstHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    "Accept-Language": "es",
  };
  for (let key in srcHeaders) {
    let key2 = key.toLowerCase();
    let value = srcHeaders[key];
    if (key2 == "content-type") {
      continue;
    }
    if (key2 == "host") {
      dstHeaders["Origin-Host"] = value;
    } else {
      dstHeaders[key] = value;
    }
  }
  let visitCode = null;
  if (window && window.MyStat) {
    visitCode = window.MyStat.getVisitCode();
  } else {
    const nuxtApp = useNuxtApp();
    if (nuxtApp) {
      let globalData = nuxtApp.$getGlobalData();
      if (globalData && globalData.visitCode) {
        visitCode = globalData.visitCode;
        dstHeaders["X-Forwarded-Path"] = "/api/";
        dstHeaders["Access-Key"] = "dfa06007af4110c2ca30ac9fb006df0f";
      }
      const originalUrl = nuxtApp.ssrContext?.url;
      if (originalUrl) {
        dstHeaders["X-Browser-Uri"] = originalUrl;
      }
    }
  }
  if (visitCode) {
    dstHeaders["Visit-Code"] = visitCode;
  }
  return dstHeaders;
}

/**
 * 网络请求方法
 * @param obj 请求参数
 * @returns 响应结果
 */
export const httpGet = (url: any, param: any) => {
  const BASEURL: string =
    typeof window == "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;
  const res = new Promise<void>((resolve, reject) => {
    $fetch(`${BASEURL}` + url, {
      method: "GET",
      mode: "cors",
      cache: "no-cache",
      query: param,
      onRequest({ request, options }) {
        // 设置请求报头
        // options.headers = options.headers || {};
        options.headers = getHttpHeaders();
        /**如果接口需求携带token请求，则可先自行使用官方的useCookie()方法设置Cookie存储后，再使用useCookie()方法，取出token使用。如下例子：*/
        // const token = useCookie("token");
        //@ts-ignore
        // options.headers.Authorization = token.value || null;
      },
      onRequestError({ request, options, error }) {
        // 处理请求错误
        console.log(
          "服务器链接失败!",
          "失败的url:",
          BASEURL + url,
          "错误消息:",
          error
        );
        reject(error);
      },
      onResponse({ request, response, options }) {
        // 处理响应数据
        resolve(response._data);
      },
      onResponseError({ request, response, options }) {
        // 处理响应错误
      },
    });
  });
  return res;
};

/**
 * 网络请求方法
 * @param obj 请求参数
 * @returns 响应结果
 */
export const httpPost = (url: any, param: any) => {
  const BASEURL: string =
    typeof window == "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;

  // 记录开始时间
  // let startTime: number;

  const res = new Promise<void>((resolve, reject) => {
    $fetch(`${BASEURL}` + url, {
      method: "POST",
      mode: "cors",
      cache: "no-cache",
      body: param,
      onRequest({ request, options }) {
        // // 记录发送请求的时间
        // startTime = Date.now();

        // 设置请求报头
        options.headers = getHttpHeaders();
        // const token = useCookie("token");
        // options.headers.Authorization = token.value || null;
      },
      onRequestError({ request, options, error }) {
        // 处理请求错误
        console.log(
          "服务器链接失败!",
          "失败的url:",
          BASEURL + url,
          "错误消息:",
          error
        );
        reject(error);
      },
      onResponse({ request, response, options }) {
        // // 计算响应时间
        // const endTime = Date.now();
        // const timeTaken = endTime - startTime;
        // console.log(url, `请求花费的时间为: ${timeTaken}ms`);

        if (typeof window == "object") {
          if (response?._data?.result?.code === 403) {
            const authStore = useAuthStore();
            authStore.setUserInfo({});
          }
        }
        // 处理响应数据
        resolve(response?._data);
      },
      onResponseError({ request, response, options }) {
        // 处理响应错误
      },
    }).catch((err) => {
      reject(err);
    });
  });
  return res;
};

export const newHttpPost = async (url: string, param: any) => {
  const BASEURL =
    typeof window === "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;

  // // 记录开始时间
  // let startTime: number;

  try {
    const { data, error } = await useAsyncData(url, () =>
      $fetch(`${BASEURL}${url}`, {
        method: "POST",
        mode: "cors",
        cache: "no-cache",
        body: param,
        onRequest({ request, options }) {
          // // 记录发送请求的时间
          // startTime = Date.now();

          // 设置请求报头
          options.headers = getHttpHeaders();
          /**如果接口需求携带token请求，则可先自行使用官方的useCookie()方法设置Cookie存储后，再使用useCookie()方法，取出token使用。如下例子：*/
          // const token = useCookie("token");
          //@ts-ignore
          // options.headers.Authorization = token.value || null;
        },
        onRequestError({ request, options, error }) {
          // 处理请求错误
          console.log(
            "服务器链接失败!",
            "失败的url:",
            BASEURL + url,
            "错误消息:",
            error
          );
          throw error;
        },
        onResponse({ request, response, options }) {
          // // 记录响应的时间
          // const endTime = Date.now();
          // const timeTaken = endTime - startTime;
          // console.log("---->", url, `请求花费的时间为: ${timeTaken}ms`);

          // 处理响应数据
          if (typeof window === "object") {
            if (response?._data?.result?.code === 403) {
              const authStore = useAuthStore();
              authStore.setUserInfo({});
            }
          }
          return response?._data;
        },
        onResponseError({ request, response, options }) {
          // 处理响应错误
          console.log("响应错误:", response);
          throw response._data;
        },
      })
    );

    if (error.value) {
      throw new Error(`Request failed: ${error.value.message}`);
    }

    return data.value;
  } catch (err) {
    console.error("Error in httpPost:", err);
    throw err;
  }
};

export const httpDownload = (url: string, param: any) => {
  const BASEURL: string =
    typeof window == "object"
      ? useRuntimeConfig().public.clientURL
      : useRuntimeConfig().public.baseURL;
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open("POST", `${BASEURL}` + url, true);
    xhr.responseType = "blob";
    const headers = <any>{
      ...getHttpHeaders(),
      Accept: "application/json, text/plain, */*",
    };
    Object.keys(headers).forEach((header) => {
      xhr.setRequestHeader(header, headers[header]);
    });

    // 设置请求体
    if (param instanceof FormData) {
      xhr.send(param);
    } else {
      xhr.send(JSON.stringify(param));
    }

    xhr.onload = function () {
      if (xhr.status >= 200 && xhr.status < 300) {
        const data = xhr.response;
        const headers = xhr.getAllResponseHeaders();
        const result = {
          data,
          headers: headers ? parseResponseHeaders(headers) : {},
        };
        resolve(result);
      } else {
        reject();
      }
    };
    xhr.onerror = function () {
      reject();
    };
  });
};

// 响应头字符串解析为对象
function parseResponseHeaders(headersString: string) {
  const headers: Record<string, string> = {};
  if (!headersString) {
    return headers;
  }
  const lines = headersString.trim().split(/[\r\n]+/);
  lines.forEach((line) => {
    const parts = line.split(": ");
    const header = parts.shift();
    if (header) {
      headers[header] = parts.join(": ");
    }
  });
  return headers;
}

// 获取nuxt配置
export const useGetNuxtConfig = (data: any) => {
  return httpPost("/mystat/incoming", data);
};

// 获取配置
export const useGetConfig = (data: any) => {
  return httpPost("/main/MallConfig/getConfig", data);
};

// 设置语言
export const useSetLanguage = (data: any) => {
  return httpPost("/main/MallConfig/setLanguage", data);
};

// 首页数据查询
export const useHomePageData = (data: any) => {
  return newHttpPost("/pages/HomePage/getPageData", data);
};

// 推荐商品
export const useRecommendGoods = (data: any) => {
  return httpPost("/pages/HomePage/recommendGoods", data);
};

// 推荐商品
export const useRecommendGoodsV2 = (data: any) => {
  return httpPost("/pages/HomePage/recommendGoodsV2", data);
};

// pc首页-已登录
export const usePcHomePageGoods = (data: any) => {
  return newHttpPost("/pages/HomePage/pcHomePageGoods", data);
};

// pc首页分类-未登录
export const useHomePageCategory = (data: any) => {
  return newHttpPost("/pages/HomePage/homePageCategory", data);
};

// 首页商品
export const useHomePageGoods = (data: any) => {
  return newHttpPost("/pages/HomePage/homePageGoods", data);
};

// 查询商品分类树
export const useCategoryTree = (data: any) => {
  return httpPost(
    "/marketing/MarketingCategory/getMarketingCategoryTree",
    data
  );
};

// 获取首页最新商品分页数据 & 获取商品列表分页数据
export const useGoodsPageListData = (data: any) => {
  return httpPost("/pages/GoodsListPage/searchGoods", data);
};

// 商品查询列表
export const useGoodsListPage = (data: any) => {
  return httpPost("/pages/GoodsListPage/getPageData", data);
};

// 商品详情页-商品详情
export const useGoodsDetail = (data: any) => {
  return httpPost("/pages/GoodsDetailPage/getPageData", data);
};

// 商品列表页-商品详情数据
export const useGoodsInfo = (data: any) => {
  return httpPost("/pages/GoodsDetailPage/getGoodsInfo", data);
};

// 获取商品分类信息
export const useGetCategoryInfo = (data: any) => {
  return httpPost("/pages/CategoryPage/getCategoryInfo", data);
};

// 获取购物车信息
export const useGetCart = (data: any) => {
  return httpPost("/inquiry/Cart/getCart", data);
};

// 获取购物车信息（按Tab分割）
export const useGetCartByTab = (data: any) => {
  return httpPost("/inquiry/Cart/getCartByTab", data);
};

// 更新全部选择或全部不选中
export const useUpdateSelectedAll = (data: any) => {
  return httpPost("/inquiry/Cart/updateSelectedAll", data);
};

// 获取购物车统计信息
export const useGetCartStat = (data: any) => {
  return httpPost("/inquiry/Cart/getCartStat", data);
};

// 添加到购物车
export const useAddCart = (data: any) => {
  return httpPost("/inquiry/Cart/addCart", data);
};

// 修改购物车商品
export const useUpdateCart = (data: any) => {
  return httpPost("/inquiry/Cart/updateCart", data);
};

// 删除购物车商品
export const useRemoveCart = (data: any) => {
  return httpPost("/inquiry/Cart/removeCart", data);
};

// 获取询盘信息
export const useGetInquiry = (data: any) => {
  return httpPost("/inquiry/GoodsLooking/inquiry", data);
};

// 提交询盘
export const useSubmitInquiry = (data: any) => {
  return httpPost("/inquiry/GoodsLooking/submit", data);
};
// 暂存询盘数据
export const useSubmitTemporary = (data: any) => {
  return httpPost("/inquiry/GoodsLooking/submitTemporary", data);
};

// 查询询盘数据
export const useGetQueryInquiry = (data: any) => {
  return httpPost("/inquiry/GoodsLooking/queryInquiry", data);
};
// 获取国家
export const useGetCountry = (data: any) => {
  return httpPost("/basis/Country/listAll", data);
};
//  暂存求购信息
export const useGoodsLookingTemSave = (data: any) => {
  return httpPost("/commodity/GoodsLooking/saveTemporary", data);
};

// 保存求购信息
export const useGoodsLookingSave = (data: any) => {
  return httpPost("/commodity/GoodsLooking/save", data);
};

// 导出给客户
export const useExportToCustomer = (data: any) => {
  return httpDownload("/inquiry/GoodsLooking/exportToCustomer", data);
};

// 导出给采购
export const useExportToPurchase = (data: any) => {
  return httpDownload("/inquiry/GoodsLooking/exportToPurchase", data);
};

// 注册
export const useRegister = (data: any) => {
  return httpPost("/passport/Auth/register", data);
};
// 登录
export const useLogin = (data: any) => {
  return httpPost("/passport/Auth/login", data);
};
// 登出
export const useLogout = (data: any) => {
  return httpPost("/passport/Auth/logout", data);
};
// 修改密码
export const useModifyPassword = (data: any) => {
  return httpPost("/passport/Auth/modifyPassword", data);
};
// 发送验证码
export const useSendCaptcha = (data: any) => {
  return httpPost("/passport/Auth/sendCaptcha", data);
};

// 以图搜图-上传1688图片
export const useUploadImage1688 = (data: any) => {
  return httpDownload("/commodity/GoodsSearch/uploadImage1688", data);
};
//以图搜图-根据1688id获取商品信息
export const useGetGoods = (data: any) => {
  return httpPost("/pages/GoodsListPage/getGoods", data);
};

// 查询栏目
export const useListArticleCategory = (data: any) => {
  return httpPost("/pages/ArticlePage/listArticleCategory", data);
};

// 根据栏目查询文章
export const useListArticleByCategoryId = (data: any) => {
  return httpPost("/pages/ArticlePage/listArticleByCategoryId", data);
};

// 获取BLOG列表
export const useGetBlogList = (data: any) => {
  return httpPost("/pages/ArticlePage/getBlogList", data);
};

// 查询文章详情
export const useArticleDetail = (data: any) => {
  return httpPost("/pages/ArticlePage/articleDetail", data);
};

// 个人中心-获取用户信息
export const useUserDetail = (data: any) => {
  return httpPost("/passport/User/detail", data);
};
// 个人中心-询盘列表
export const useGoodsLookingList = (data: any) => {
  return httpPost("/inquiry/GoodsLookingList/pageList", data);
};
// 个人中心-修改密码
export const useUpdatePassword = (data: any) => {
  return httpPost("/passport/User/updatePassword", data);
};
// 个人中心-查询所有地址
export const useListUserAddress = (data: any) => {
  return newHttpPost("/passport/User/listUserAddress", data);
};
// 个人中心-保存地址
export const useSaveUserAddress = (data: any) => {
  return httpPost("/passport/User/saveUserAddress", data);
};
// 个人中心-删除地址
export const useDeleteUserAddress = (data: any) => {
  return httpPost("/passport/User/deleteUserAddress", data);
};
// 个人中心-设为默认地址
export const useAddressToDefault = (data: any) => {
  return httpPost("/passport/User/addressToDefault", data);
};
// 个人中心-根据国家查询地区
export const useListRegionByCountry = (data: any) => {
  return httpPost("/basis/Country/listRegionByCountry", data);
};
//个人中心-根据邮编查询地区
export const useListRegionByPostcode = (data: any) => {
  return httpPost("/basis/Country/listRegionByPostcode", data);
};

// 订单管理-分页查询订单列表
export const useGetOrderList = (data: any) => {
  return httpPost("/pages/MallOrderPage/getOrderList", data);
};

// 订单管理-取消订单
export const useCancelOrder = (data: any) => {
  return httpPost("/pages/MallOrderPage/cancelOrder", data);
};

//订单管理-获取取消订单理由列表
export const useGetCancelOrderReasonConfig = (data: any) => {
  return httpPost("/pages/MallOrderGetConfig/getCancelOrderReasonConfig", data);
};

// 订单管理-查询订单详情
export const useGetOrderDetail = (data: any) => {
  return httpPost("/pages/MallOrderPage/getOrderDetail", data);
};

// 订单管理-未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
export const useOpenCashDesk = (data: any) => {
  return httpPost("/pages/MallOrderPage/openCashDesk", data);
};

// 查询收银台信息，包括待支付金额、支付方式列表、是否已支付
export const useGetCashDeskInfo = (data: any) => {
  return httpPost("/pages/MallOrderPage/getCashDeskInfo", data);
};

// 订单管理-收银台提交支付
export const useSubmitPayment = (data: any) => {
  return httpPost("/pages/MallOrderPage/submitPayment", data);
};

// 订单管理-查询支付结果
export const useQueryPayResult = (data: any) => {
  return httpPost("/pages/MallOrderPage/queryPayResult", data);
};

// 订单管理-确认收货
export const useConfirmReceipt = (data: any) => {
  return httpPost("/pages/MallOrderPage/confirmReceipt", data);
};

// 保存潜客信息
export const useSaveUserInfo = (data: any) => {
  return httpPost("/marketing/PotentialUser/saveUserInfo", data);
};

// 保存潜客信息
export const useShortLink = (data: any) => {
  return httpPost("/foundation/ShortLink/getShortLink", data);
};

// 保存潜客信息
export const useFullLink = (data: any) => {
  return httpPost("/foundation/ShortLink/getFullLink", data);
};

// 优惠券-我的优惠券列表查询
export const useGetMyCouponDetailList = (data: any) => {
  return httpPost("/marketing/CouponDetail/getMyCouponDetailList", data);
};

// 可用产品券/佣金券/列表
export const useGetCouponUsableList = (data: any) => {
  return httpPost("/marketing/CouponDetail/getCouponUsableList", data);
};

// checkbox优惠券校验
export const useCheckAvailableList = (data: any) => {
  return httpPost("/marketing/CouponDetail/checkAvailableList", data);
};

// 发送验证邮箱的邮件
export const useSendVerifyMail = (data: any) => {
  return httpPost("/passport/User/sendVerifyMail", data);
};

// 查询邮箱是否已验证
export const useQueryVerifyMailResult = (data: any) => {
  return httpPost("/passport/User/queryVerifyMailResult", data);
};

// 点击激活链接 ---- 入参为激活链接上的code参数
export const useVerifyMail = (data: any) => {
  return httpPost("/passport/User/verifyMail", data);
};

// 查询被邀请好友用户的邮箱验证情况
export const useInvitedUserMailStatus = (data: any) => {
  return httpPost("/passport/User/invitedUserMailStatus", data);
};

// 网站工具箱
// 获取验证码的图形数据
export const useGetCaptchaImage = (data: any) => {
  return httpPost("/main/Toolkit/getCaptchaImage", data);
};

// 检查图形验证码（验证码错误的错误码：952401
export const useCheckCaptchaImage = (data: any) => {
  return httpPost("/main/Toolkit/checkCaptchaImage", data);
};

// 直接点击whatsapp
export const useClickWhatsapp = (data: any) => {
  return httpPost("/marketing/PotentialUser/clickWhatsapp", data);
};

// 计算预估运费
export const useCalculateEstimateFreight = (data: any) => {
  return httpPost("/main/MallConfig/calculateEstimateFreight", data);
};

// 保存问卷调查的表单
export const useSaveUserSurvey = (data: any) => {
  return httpPost("/main/Survey/saveSurveyForm", data);
};

// 设置当前站点的选择（传参为siteId，即配送国家ID）
export const useSetCurrentSite = (data: any) => {
  return httpPost("/main/MallConfig/setCurrentSite", data);
};

// 根据“促销活动代码”查询促销活动商品索引页信息
export const useGetPromotionGoodsIndexByCode = (data: any) => {
  return httpPost(
    "/marketing/PromotionActivity/getPromotionGoodsIndexByCode",
    data
  );
};

// 查询360栏目
export const useListWordPressCategory = (data: any) => {
  return httpPost("/pages/WordPressPage/listWordPressCategory", data);
};

// 查询360文章详情
export const useWordPressDetail = (data: any) => {
  return httpPost("/pages/WordPressPage/wordPressDetail", data);
};

// 根据筛选条件查询360文章列表
export const useSearchWordPressList = (data: any) => {
  return httpPost("/pages/WordPressPage/searchWordPressList", data);
};

// 根据页面路径查询WhatsApp配置
export const useGetPageWhatsAppConfig = (data: any) => {
  return httpPost("/foundation/PageTool/getWhatsAppLink", data);
};

// 根据登录账号获取对应的联系人信息
export const useGetContactInfo = (data: any) => {
  return httpPost("/pages/MallGoodsFindPage/getContactInfo", data);
};

// 提交找货信息
export const useSubmitGoodsFind = (data: any) => {
  return httpPost("/pages/MallGoodsFindPage/submitGoodsFind", data);
};

export const useGoodsFindDetail = (data: any) => {
  return httpPost("/pages/MallGoodsFindPage/goodsFindDetail", data);
};

// 找货活动
// 获取找货任务信息（若不存在，则自动创建；读取问题与已保存的回答数据，进入新增或编辑模式）
export const useGetFindGoodsInfo = (data: any) => {
  return httpPost("/topic/FindGoodsGame/getFindGoodsInfo", data);
};

// 根据小组ID，查询详情
export const useGetDetailByGroupId = (data: any) => {
  return httpPost("/topic/FindGoodsGame/getDetailByGroupId", data);
};

// 单次保存找货信息（保存一个找货任务中的问题）
export const useSaveFindGoodsOnce = (data: any) => {
  return httpPost("/topic/FindGoodsGame/saveFindGoodsOnce", data);
};

// 暂存找货信息（每完成一个字段的变更，即刻调用暂存接口）
export const useTempStoreFindGoods = (data: any) => {
  return httpPost("/topic/FindGoodsGame/tempStoreFindGoods", data);
};

// 暂存找货信息（每完成一个字段的变更，即刻调用暂存接口）
export const useFastCreateOrder = (data: any) => {
  return httpPost("/trade/Checkout/fastCreateOrder", data);
};
