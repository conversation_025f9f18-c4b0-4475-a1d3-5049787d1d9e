syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.model";

import "chilat/user/user_common.proto";
import "common.proto";

message PermissionListResp {
  common.Result result = 1;
  repeated PermissionModel data = 2;
}

// 菜单
message PermissionModel {
  string id = 1; // 菜单ID
  string name = 2; // 菜单名称
  string icon = 3; // 菜单图标
  string path = 4; // 菜单路径
  string code = 5; // 菜单编码
  string tranCode = 6; // 翻译代码
  PermissionType type = 7; // 菜单类型
  bool hidden = 8; // 是否隐藏
  string redirect = 9; // 跳转地址
  repeated PermissionModel children = 10; // 子菜单
}
