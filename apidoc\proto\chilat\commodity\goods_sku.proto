syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/commodity_common.proto";
import "chilat/commodity/param/goods_sku_param.proto";
import "chilat/commodity/model/goods_sku_model.proto";
import "common.proto";

// 商品信息管理
service GoodsSku {
//  // 查询商品列表
//  rpc listPage (GoodsSkuPageQueryParam) returns (GoodsSkuPageResp);
  // SKU上下架
  rpc changeSkuOnline(ChangeSkuOnlineStateParam) returns (ChangeSkuOnlineStateResp);
//  // 查询日志（根据SKU ID查询）
//  rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
  //admin 开单搜sku（用户下单，价格单位：美元）
  rpc querySkuList(OrderSkuQueryParam) returns (OrderSkuQueryResp);
}