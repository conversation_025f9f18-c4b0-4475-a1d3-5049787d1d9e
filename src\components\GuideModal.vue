<template>
  <n-modal
    preset="dialog"
    :block-scroll="true"
    :closable="false"
    :show-icon="false"
    v-model:show="showGuide"
    :on-close="onCloseGuide"
    :on-esc="onCloseGuide"
    :on-mask-click="onCloseGuide"
    class="guide-dialog"
    :style="
      isMobile
        ? { width: '100vw', padding: 0 }
        : {
            width: '1280px',
            maxWidth: '1280px',
            padding: 0,
          }
    "
  >
    <img
      loading="lazy"
      :src="isMobile ? pageTheme.mobileGuideLogo : pageTheme.guideLogo"
      class="w-[100%] block object-fill"
      referrerpolicy="no-referrer"
    />
    <icon-card
      :size="isMobile ? '18' : '26'"
      color="#E50113"
      class="close-icon cursor-pointer"
      :class="isMobile ? 'mobile-icon' : ''"
      name="vaadin:close"
      @click="onCloseGuide"
      v-show="pageTheme.guideLogo"
    ></icon-card>
  </n-modal>
</template>

<script setup lang="ts" name="GuideModal">
import { useConfigStore } from "@/stores/configStore";
const route = useRoute();
const showGuide = ref(false);

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});
const pageTheme = computed(() => useConfigStore().getPageTheme);

function onOpenGuide() {
  showGuide.value = true;
}
function onCloseGuide() {
  showGuide.value = false;
}

defineExpose({
  onOpenGuide,
});
</script>
<style scoped lang="scss">
.close-icon {
  position: absolute;
  right: 1rem;
  top: 1rem;
  z-index: 1000;
}
.mobile-icon {
  right: 0.16rem;
  top: 0.16rem;
}
</style>
<style>
.guide-dialog.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0px !important;
}
</style>
