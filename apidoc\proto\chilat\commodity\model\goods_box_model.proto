syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "chilat/foundation/foundation_common.proto";
import "common.proto";
import "chilat/commodity/param/goods_info_param.proto";

// 导入批次分页查询
message GoodsBoxBatchPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsBoxBatchListModel data = 3;
}

// 导入批次分页查询返回对象
message GoodsBoxBatchListModel {
    string id = 3; // 导入批次ID
    string batchNo = 4; // 导入批次号
    GoodsImportType importType = 30; // 导入维度
    int32 importCount = 40; // 导入总数
    int32 successCount = 50; // 导入成功数
    int32 failCount = 60; // 导入失败数
    foundation.CrawlTaskState taskState = 70; // 导入状态
    string salesmanId = 80; // 操作用户ID（业务员ID）
    string salesmanName = 90; // 操作用户名（业务员姓名）
    int64 importTime = 100; // 导入时间
}

// 导入批次的商品分页查询
message GoodsBoxBatchGoodsPageResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsBoxBatchGoodsListModel data = 3;
}

// 导入批次的商品分页查询返回对象
message GoodsBoxBatchGoodsListModel {
    string id = 5; // 导入批次商品ID
    string goodsIdWith1688 = 10; //1688商品ID
    string goodsNo = 20; // shop商品编码
    string packingCount = 30; // 装箱数（表示每个包装箱中包含的商品件数，同一个商品或SKU中唯一）
    string boxLength = 21; // 包装箱的长（单位：cm）
    string boxWidth = 22; // 包装箱的宽（单位：cm）
    string boxHeight = 23; // 包装箱的高（单位：cm）
    string boxVolume = 40; // 单箱体积（单位：立方米，长*宽*高）
    string boxWeight = 50; // 单箱重量（单位：kg）
    bool waitImport = 60; // 是否为待导入
    bool importSuccess = 70; // 导入是否成功（true成功, false失败）
    string errorMessage = 80; // 导入错误原因
}

// 批量导入shop商品维度装箱信息（shop商品维度导入1/2）
message ImportShopBoxInfoResp {
    common.Result result = 1;
    repeated ImportShopBoxInfoModel data = 2;
}

// 批量导入shop商品维度装箱信息（shop商品维度导入1/2）返回对象
message ImportShopBoxInfoModel {
    string goodsIdWith1688 = 10; //1688商品ID
    string goodsNo = 15; // shop商品编码
    string packingCount = 20; // 装箱数（表示每个包装箱中包含的商品件数，同一个商品或SKU中唯一）
    string boxLength = 21; // 包装箱的长（单位：cm）
    string boxWidth = 22; // 包装箱的宽（单位：cm）
    string boxHeight = 23; // 包装箱的高（单位：cm）
    string boxVolume = 30; // 单箱体积（单位：立方米，长*宽*高）
    string boxWeight = 40; // 单箱重量（单位：kg）
    string message = 50; // 提示信息（含错误原因）
    bool forbidImport = 60; // 是否禁止导入（若有错误，如商品编码不存在，后端会设置禁止导入）
    bool selected = 70; // 是否选中（若forbidImport为true，则不可选中）
}

// 提交商品导入
message SubmitImportBoxInfoResp {
    common.Result result = 1;
    SubmitImportBoxInfoModel data = 2;
}

// 提交商品导入返回对象
message SubmitImportBoxInfoModel {
    string batchNo = 10; // 导入批次号
}

