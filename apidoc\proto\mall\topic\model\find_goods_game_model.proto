syntax = "proto3";
package mall.topic;

option java_package = "com.chilat.rpc.topic.model";


import "common.proto";
import "common/topic.proto";

//获取找货任务信息返回
message GetFindGoodsInfoResp {
    common.Result result = 1;
    FindGoodsTaskInfoModel data = 2;
}

//找货任务信息（包含问题与回答）
message FindGoodsTaskInfoModel {
    string groupId = 10; //小组ID
    string groupCode = 20; //小组代码
    string groupName = 30; //小组名称
    int32 questionCount = 40; //问题数量
    int32 answerCount = 50; //回答数量（保存了回答就算数，不管是否提交完整）
    int32 finishedAnswerCount = 60; //已完成的回答数量（所有必填项都已输入，才算完成1个问题）
    bool isFinished = 70; //是否已完成
    repeated FindGoodsQuestionAnswerModel questionAnswers = 80;
}

//找货任务的问题与回答
message FindGoodsQuestionAnswerModel {
    string questionId = 10; //问题ID
    string questionImageUrl = 20; //待找货的商品图片URL
    string questionSpecDesc = 30; //待找货的商品图片URL
    int32 answerVersion = 40; //回答的版本号（若已有回答，则此字段必填）
    bool isFinished = 50; //是否已完成（待找货商品，已找到规定的数量，由后端标记完成）
    repeated FindGoodsAnswerFoundGoodsModel foundGoodsList = 60; //找到的商品信息列表
}

//找货任务回答中的找到的商品信息
message FindGoodsAnswerFoundGoodsModel {
    int32 answerSeq = 10; //回答次序号（找到第1个商品则为1，第2个商品则2，依序类推）
    repeated string goodsImages = 20; //商品图片
    double goodsPrice = 30; //商品价格
    int32 packingCount = 40; //装箱数
    double goodsVolume = 50; //商品体积（单位：立方米，长*宽*高）
    double goodsWeight = 60; //商品重量（单位：kg）
    int32 minBuyQuantity = 70; //最小购买数量（起订量）
    string recommendReason = 80; //推荐理由
    common.FindGoodsPurchaseSource findSource = 90; //采购来源
    repeated string proofImages = 100; //证明图片URL
    bool fromSnapshot = 110; //是否来自快照（未正式保存）
    bool isFinished = 120; //是否已完成（此商品的数据输入完整，就算完成）
}

