syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.model";

import "chilat/user/model/permission_model.proto";
import "common.proto";
import "chilat/user/user_common.proto";

// 登录返回信息
message AuthLoginResp {
  common.Result result = 1;
  AuthLoginModel data = 2;
}

// 登录结果
message AuthLoginModel {
  string userId = 1; // 用户ID
  string username = 2; // 用户名
  string token = 3; // 登录令牌
  repeated PermissionModel menus = 4; // 菜单
  bool isManager = 5; //是否管理员
  repeated JobDuty duties = 6; // 岗位职责
}
