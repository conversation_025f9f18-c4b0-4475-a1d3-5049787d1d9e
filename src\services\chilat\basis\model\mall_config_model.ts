/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { IdNameModel, Result } from "../../../common";
import { MidCartModel, MidCartStatModel } from "./cart_model";
import { ConfigModel } from "./config_model";
import { MallCategoryPathItemModel, MallCategoryTreeModel } from "./goods_model";

export const protobufPackage = "chilat.basis";

export interface MallConfigMultiQueryResp {
  result: Result | undefined;
  data: MallConfigMultiQueryModel | undefined;
}

export interface MallConfigMultiQueryModel {
  globalConfigModel: ConfigModel | undefined;
  categoryRelatedBrandsModel: CategoryRelatedBrandsModel | undefined;
  cartStatModel:
    | MidCartStatModel
    | undefined;
  /** 购物车数据 */
  cartInfoModel: MidCartModel | undefined;
  categoryTreeModel:
    | MallCategoryTreeModel
    | undefined;
  /** 商品分类路径 */
  categoryPathModel: MallCategoryPathItemModel[];
}

export interface CategoryRelatedBrandsModel {
  /** 可选择的品牌（id为品牌ID，name为品牌名称） */
  optionalBrands: IdNameModel[];
}

function createBaseMallConfigMultiQueryResp(): MallConfigMultiQueryResp {
  return { result: undefined, data: undefined };
}

export const MallConfigMultiQueryResp = {
  encode(message: MallConfigMultiQueryResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      MallConfigMultiQueryModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MallConfigMultiQueryResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMallConfigMultiQueryResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = MallConfigMultiQueryModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MallConfigMultiQueryResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? MallConfigMultiQueryModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: MallConfigMultiQueryResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = MallConfigMultiQueryModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MallConfigMultiQueryResp>, I>>(base?: I): MallConfigMultiQueryResp {
    return MallConfigMultiQueryResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallConfigMultiQueryResp>, I>>(object: I): MallConfigMultiQueryResp {
    const message = createBaseMallConfigMultiQueryResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? MallConfigMultiQueryModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseMallConfigMultiQueryModel(): MallConfigMultiQueryModel {
  return {
    globalConfigModel: undefined,
    categoryRelatedBrandsModel: undefined,
    cartStatModel: undefined,
    cartInfoModel: undefined,
    categoryTreeModel: undefined,
    categoryPathModel: [],
  };
}

export const MallConfigMultiQueryModel = {
  encode(message: MallConfigMultiQueryModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.globalConfigModel !== undefined) {
      ConfigModel.encode(message.globalConfigModel, writer.uint32(82).fork()).ldelim();
    }
    if (message.categoryRelatedBrandsModel !== undefined) {
      CategoryRelatedBrandsModel.encode(message.categoryRelatedBrandsModel, writer.uint32(162).fork()).ldelim();
    }
    if (message.cartStatModel !== undefined) {
      MidCartStatModel.encode(message.cartStatModel, writer.uint32(242).fork()).ldelim();
    }
    if (message.cartInfoModel !== undefined) {
      MidCartModel.encode(message.cartInfoModel, writer.uint32(282).fork()).ldelim();
    }
    if (message.categoryTreeModel !== undefined) {
      MallCategoryTreeModel.encode(message.categoryTreeModel, writer.uint32(322).fork()).ldelim();
    }
    for (const v of message.categoryPathModel) {
      MallCategoryPathItemModel.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MallConfigMultiQueryModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMallConfigMultiQueryModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.globalConfigModel = ConfigModel.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.categoryRelatedBrandsModel = CategoryRelatedBrandsModel.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.cartStatModel = MidCartStatModel.decode(reader, reader.uint32());
          continue;
        case 35:
          if (tag !== 282) {
            break;
          }

          message.cartInfoModel = MidCartModel.decode(reader, reader.uint32());
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.categoryTreeModel = MallCategoryTreeModel.decode(reader, reader.uint32());
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.categoryPathModel.push(MallCategoryPathItemModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MallConfigMultiQueryModel {
    return {
      globalConfigModel: isSet(object.globalConfigModel) ? ConfigModel.fromJSON(object.globalConfigModel) : undefined,
      categoryRelatedBrandsModel: isSet(object.categoryRelatedBrandsModel)
        ? CategoryRelatedBrandsModel.fromJSON(object.categoryRelatedBrandsModel)
        : undefined,
      cartStatModel: isSet(object.cartStatModel) ? MidCartStatModel.fromJSON(object.cartStatModel) : undefined,
      cartInfoModel: isSet(object.cartInfoModel) ? MidCartModel.fromJSON(object.cartInfoModel) : undefined,
      categoryTreeModel: isSet(object.categoryTreeModel)
        ? MallCategoryTreeModel.fromJSON(object.categoryTreeModel)
        : undefined,
      categoryPathModel: globalThis.Array.isArray(object?.categoryPathModel)
        ? object.categoryPathModel.map((e: any) => MallCategoryPathItemModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MallConfigMultiQueryModel): unknown {
    const obj: any = {};
    if (message.globalConfigModel !== undefined) {
      obj.globalConfigModel = ConfigModel.toJSON(message.globalConfigModel);
    }
    if (message.categoryRelatedBrandsModel !== undefined) {
      obj.categoryRelatedBrandsModel = CategoryRelatedBrandsModel.toJSON(message.categoryRelatedBrandsModel);
    }
    if (message.cartStatModel !== undefined) {
      obj.cartStatModel = MidCartStatModel.toJSON(message.cartStatModel);
    }
    if (message.cartInfoModel !== undefined) {
      obj.cartInfoModel = MidCartModel.toJSON(message.cartInfoModel);
    }
    if (message.categoryTreeModel !== undefined) {
      obj.categoryTreeModel = MallCategoryTreeModel.toJSON(message.categoryTreeModel);
    }
    if (message.categoryPathModel?.length) {
      obj.categoryPathModel = message.categoryPathModel.map((e) => MallCategoryPathItemModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MallConfigMultiQueryModel>, I>>(base?: I): MallConfigMultiQueryModel {
    return MallConfigMultiQueryModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallConfigMultiQueryModel>, I>>(object: I): MallConfigMultiQueryModel {
    const message = createBaseMallConfigMultiQueryModel();
    message.globalConfigModel = (object.globalConfigModel !== undefined && object.globalConfigModel !== null)
      ? ConfigModel.fromPartial(object.globalConfigModel)
      : undefined;
    message.categoryRelatedBrandsModel =
      (object.categoryRelatedBrandsModel !== undefined && object.categoryRelatedBrandsModel !== null)
        ? CategoryRelatedBrandsModel.fromPartial(object.categoryRelatedBrandsModel)
        : undefined;
    message.cartStatModel = (object.cartStatModel !== undefined && object.cartStatModel !== null)
      ? MidCartStatModel.fromPartial(object.cartStatModel)
      : undefined;
    message.cartInfoModel = (object.cartInfoModel !== undefined && object.cartInfoModel !== null)
      ? MidCartModel.fromPartial(object.cartInfoModel)
      : undefined;
    message.categoryTreeModel = (object.categoryTreeModel !== undefined && object.categoryTreeModel !== null)
      ? MallCategoryTreeModel.fromPartial(object.categoryTreeModel)
      : undefined;
    message.categoryPathModel = object.categoryPathModel?.map((e) => MallCategoryPathItemModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCategoryRelatedBrandsModel(): CategoryRelatedBrandsModel {
  return { optionalBrands: [] };
}

export const CategoryRelatedBrandsModel = {
  encode(message: CategoryRelatedBrandsModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.optionalBrands) {
      IdNameModel.encode(v!, writer.uint32(82).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CategoryRelatedBrandsModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCategoryRelatedBrandsModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.optionalBrands.push(IdNameModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CategoryRelatedBrandsModel {
    return {
      optionalBrands: globalThis.Array.isArray(object?.optionalBrands)
        ? object.optionalBrands.map((e: any) => IdNameModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CategoryRelatedBrandsModel): unknown {
    const obj: any = {};
    if (message.optionalBrands?.length) {
      obj.optionalBrands = message.optionalBrands.map((e) => IdNameModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CategoryRelatedBrandsModel>, I>>(base?: I): CategoryRelatedBrandsModel {
    return CategoryRelatedBrandsModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CategoryRelatedBrandsModel>, I>>(object: I): CategoryRelatedBrandsModel {
    const message = createBaseCategoryRelatedBrandsModel();
    message.optionalBrands = object.optionalBrands?.map((e) => IdNameModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
