syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/model/goods_category_model.proto";
import "chilat/commodity/param/goods_category_param.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

// 分类管理
service GoodsCategory {
    // 查询列表
    rpc listPage (GoodsCategoryPageQueryParam) returns (GoodsCategoryPageResp);
    // 查询详情
    rpc getDetail (common.IdParam) returns (GoodsCategoryDetailResp);
    // 删除商品分类
    rpc removeIt (common.IdParam) returns (common.ApiResult);
    // 保存商品分类信息（id不存在，则为新增）
    rpc saveCategory (GoodsCategorySaveParam) returns (common.ApiResult);
    // 获取商品分类树
    rpc getCategoryTree (common.EnabledParam) returns (GoodsCategoryTreeResp);
    // 更新搜索权重分
    rpc updateCategoryAdjustRank (UpdateCategoryAdjustRankParam) returns (common.ApiResult);
    // 全量更新商品分类包含的商品数量
    rpc updateCategoryGoodsCount (common.EmptyParam) returns (UpdateCategoryGoodsCountResp);
    // 查询日志
    rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
    // 禁用/启用
    rpc enable (common.EnabledParam) returns (GoodsCategoryEnableResp);
}