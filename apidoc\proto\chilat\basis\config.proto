syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";
import "chilat/basis/model/config_model.proto";
import "chilat/basis/param/config_param.proto";

// 全局配置
service Config {
  // 获取配置
  rpc getConfig (common.StringParam) returns (ConfigResp);
  // 设置语言
  rpc setLanguage (common.StringParam) returns (LanguageResp);
  // 保存翻译
  rpc saveSysTranslate(SysTranslateParam) returns (common.ApiResult);
  // 删除翻译
  rpc removeSysTranslate(common.IdParam) returns (common.ApiResult);
  // 分页查询翻译
  rpc translatePageList(TranslatePageQueryParam) returns (TranslatePageResp);
  // 更新机器翻译
  rpc updateAutoSysTranslate(common.EmptyParam) returns (common.ApiResult);
}