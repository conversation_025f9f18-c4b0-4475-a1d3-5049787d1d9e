<template>
    <div class="!bg-[#f5f3f3]">
        <seo-data :pageData="pageData"></seo-data>
        <div class="wrapper">
            <div class="bg-white">
                <search-card></search-card>
            </div>
            <div class="cwidth mx-auto pb-4 px-4">
                <div class="mt-4 flex h-[76vh]">
                    <div class="w-55 mr-4 rounded bg-white py-5 px-3 text-base flex-shrink-0">
                        <SidebarItem v-for="(item, index) in pageData.sideData" :key="index" :item="item" :isActive="isActive" :onGoPage="onGoPage" />
                    </div>
                    <div class="flex-1 h-full">
                        <NuxtPage :page-key="(route) => route.fullPath" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import SidebarItem from './user/components/SidebarItem.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const pageData = reactive({
    sideData: [
        {
            name: authStore.i18n('cm_bar.account'),
            icon: 'lucide:home',
            iconSize: '20',
            path: '/user/account',
        },
        {
            name: authStore.i18n('cm_bar.inquiry'),
            icon: "iconamoon:email-light",
            iconSize: '24',
            path: '/user/inquiry',
        },
        {
            name: authStore.i18n('cm_bar.order'),
            icon: 'f7:square-list',
            iconSize: '25',
            path: '/user/orderList',
        },
        {
      name: authStore.i18n("cm_bar.myCoupon"),
      icon: "ri:coupon-2-line",
      iconSize: "23",
      path: "/user/coupon",
    },
        {
            name: authStore.i18n('cm_user.invite'),
            subtitle: authStore.i18n('cm_user.inviteSubtitle'),
            icon: 'mdi:gift-outline',
            iconSize: '25',
            path: '/user/invite',
        },
        {
            name: authStore.i18n('cm_bar.configuration'),
            icon: 'carbon:settings',
            iconSize: '21',
            children: [
                {
                    path: '/user/updatePwd',
                    name: authStore.i18n('cm_bar.updatePwd'),
                },
                {
                    path: '/user/address',
                    name: authStore.i18n('cm_bar.addresses'),
                },
            ],
        },
    ],
})

authStore.getCartList()
onMounted(() => {
    document.documentElement.style.backgroundColor = '#f5f3f3' // fix bug
})

function isActive(val: any) {
    return route.path.indexOf(val.path) !== -1
}

function onGoPage(val: any, event: any) {
    router.push({
        path: val.path,
        query: {
            spm: window.MyStat.getPageSPM(event),
        },
    })
}
</script>

<style scoped lang="scss">
.wrapper {
    min-height: 70vh;
    width: 100%;
}
</style>
