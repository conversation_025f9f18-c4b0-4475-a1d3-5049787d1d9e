<template>
  <div class="page-wrapper">
    <div
      class="w-[1280px] mx-auto flex justify-end pr-[40px] pt-[126px] relative"
    >
      <div class="page-title">
        {{ authStore.i18n("cm_common.inviteNewClients") }}
      </div>
      <img
        loading="lazy"
        src="@/assets/icons/loginPart.png"
        alt="login"
        class="w-[840px] absolute top-[194px] right-[432px]"
      />
      <div
        class="bg-white w-[530px] min-h-[512px] rounded-[8px] px-[80px] py-[32px] flex-shrink-0 relative z-1"
      >
        <div
          class="text-center font-medium text-[30px] leading-[30px] mb-[36px]"
        >
          {{ authStore.i18n("cm_common_register") }}
        </div>
        <div>
          <!-- 登录 -->
          <n-form :rules="rules" ref="regFromRef" :model="pageData.regForm">
            <n-form-item
              path="username"
              :label="authStore.i18n('cm_common.username')"
            >
              <n-input
                v-trim
                clearable
                class="h-[52px]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.regForm.username, 'email')"
                v-model:value="pageData.regForm.username"
                :placeholder="authStore.i18n('cm_common.inputEmail')"
              >
                <template #prefix>
                  <img
                    loading="lazy"
                    alt="email"
                    class="mr-1.5 w-[22px]"
                    src="@/assets/icons/email.svg"
                  />
                </template>
              </n-input>
            </n-form-item>
            <n-form-item
              path="password"
              :label="authStore.i18n('cm_common.password')"
            >
              <n-input
                v-trim
                clearable
                class="h-[52px]"
                @keydown.enter.prevent
                @blur="onBlurEvent(pageData.regForm.password, 'password')"
                :type="pageData.showPwd ? 'text' : 'password'"
                v-model:value="pageData.regForm.password"
                :placeholder="authStore.i18n('cm_common.inputPassword')"
              >
                <template #prefix>
                  <img
                    loading="lazy"
                    alt="lock"
                    class="mr-1.5 w-[22px]"
                    src="@/assets/icons/lock.svg"
                  />
                </template>
                <template #suffix>
                  <icon-card
                    size="24"
                    color="#7F7F7F"
                    class="cursor-pointer"
                    :name="
                      pageData.showPwd
                        ? 'weui:eyes-on-outlined'
                        : 'weui:eyes-off-outlined'
                    "
                    @click="pageData.showPwd = !pageData.showPwd"
                  /> </template
              ></n-input>
            </n-form-item>
            <div
              class="mt-[12px]"
              :class="{ shake: pageData.shouldShake }"
              @animationend="pageData.shouldShake = false"
            >
              <n-checkbox v-model:checked="pageData.regForm.acceptTerms">
              </n-checkbox>
              <span class="text-[15px] text-[#666] mx-[8px]">{{
                authStore.i18n("cm_common.readAgree")
              }}</span>
              <span
                @click="onOpenAgree"
                class="text-[16px] text-[#FF6B81] hover:underline cursor-pointer"
                >{{ authStore.i18n("cm_common.viewAgree") }}
              </span>
            </div>
            <n-form-item>
              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                class="rounded-[8px] px-[0.16rem] w-full h-[52px] text-[16px] leading-[16px]"
                @click="onRegister"
              >
                {{ authStore.i18n("cm_common.regLogin") }}
              </n-button>
            </n-form-item>
            <div class="flex justify-end">
              <div
                class="text-[#e50113] cursor-pointer text-[16px] leading-[16px]"
                @click="onGoLogin"
              >
                {{ authStore.i18n("cm_common_login") }}
              </div>
            </div>
          </n-form>
        </div>
      </div>
    </div>
  </div>
  <n-modal
    preset="dialog"
    :block-scroll="true"
    :autoFocus="false"
    :closable="true"
    :show-icon="false"
    v-model:show="pageData.showAgree"
    :on-close="onCloseAgree"
    :on-esc="onCloseAgree"
    :on-mask-click="onCloseAgree"
    :style="{
      width: '1280px',
    }"
  >
    <p class="text-3xl font-medium text-center pt-[30px]">
      Política de privacidad
    </p>
    <n-scrollbar class="h-[90vh]" style="padding: 30px 40px" trigger="none">
      <div>
        <p class="my-4">
          De conformidad con la legislación en Protección de Datos y el artículo
          10 de la Ley 34/2002 de Servicios de la Sociedad de la Información y
          Comercio Electrónico, el propietario del sitio y responsable del
          tratamiento de los datos es:
        </p>
        <p class="my-4">Ming Zhan Import & Export Co., Limited</p>
        <p class="my-4">Datos de contacto de la empresa:</p>
        <p class="my-4">
          2001 Edificio A, Mansión Liandu, Distrito Financiero de Negocios,
          Yiwu, Zhejiang, China, 322000
        </p>
        <a
          target="_blank"
          class="my-2 text-[#4D85FF]"
          href="https://shop.chilat.com/"
          >shop.chilat.com</a
        >
      </div>
      <div>
        <p class="text-xl font-medium my-4">
          Uso y finalidad de los datos obtenidos
        </p>
        <p class="my-4">
          Los datos que solicitamos en nuestra página web son los adecuados,
          pertinentes y estrictamente necesarios para la finalidad de gestionar
          y tramitar la petición que nos ha realizado, poder enviarle
          información de nuestros servicios, así como realizar la compraventa de
          productos y servicios, y en ningún caso está obligado a facilitarlos.
        </p>
        <p class="text-base font-medium my-4">Usos de los datos obtenidos:</p>
        <ul>
          <li class="my-2">
            Gestionar la petición realizada en nuestros respectivos formularios
            de contacto.
          </li>
          <li class="my-2">
            Enviar comunicaciones comerciales vía SMS, WhatsApp y correo
            electrónico.
          </li>
          <li class="my-2">
            Gestionar la compra de productos por parte del titular de los datos.
          </li>
        </ul>
        <p class="my-4">
          Los datos de cumplimentación obligatoria se especifican en el propio
          formulario donde se recaban los datos, y su negativa a suministrarlos
          implicará no poder gestionar su petición. Asimismo, nos asegura que
          todos los datos facilitados son ciertos, veraces y pertinentes para la
          finalidad por la que los solicitamos.
        </p>
        <p class="my-4">
          Los datos personales proporcionados se conservarán mientras se
          mantenga la relación comercial, y una vez finalice la citada relación
          se mantendrán bloqueados el tiempo legalmente establecido, antes de su
          destrucción.
        </p>
        <p class="my-4">
          La base de legitimación para el tratamiento de sus datos:
        </p>
        <ul>
          <li class="my-2">
            El consentimiento del interesado para la gestión y tramitación de
            peticiones, así como para el envío de comunicaciones comerciales.
          </li>
          <li class="my-2">
            Relación contractual para la formalización y gestión de la
            compraventa de productos.
          </li>
        </ul>
        <p class="my-4">
          El envío de los mismos implica su autorización expresa a incorporarlos
          a nuestros ficheros correspondientes, siempre y cuando Ming Zhan
          Import & Export Co., Limited lo considere conveniente para la gestión
          de la petición que solicite.
        </p>
        <p class="my-4">
          Sus datos no serán cedidos a terceros sin su consentimiento expreso, a
          excepción de aquellas cesiones necesarias para dar cumplimiento a una
          obligación legal.
        </p>
        <p class="my-4">
          Le comunicamos que sus datos serán transferidos a un tercer país fuera
          del espacio económico latinoamericano dado que la compra de productos
          se realiza a empresas residentes en la República Popular de China y
          los datos recabados en la página web son conservados en un servidor
          instalado en el mismo país. Dicha transferencia es necesaria para la
          realización de la compraventa y la ejecución del contrato.
        </p>
        <p class="my-4">
          Mediante la aceptación de la casilla del formulario, usted consiente
          el envío de información de nuestros servicios que puedan resultar de
          su interés.
        </p>
        <p class="my-4">
          Es importante que para que podamos mantener sus datos personales
          actualizados, nos informe siempre que haya alguna modificación en
          ellos. En caso contrario, no respondemos de la veracidad de los
          mismos. Consideramos que, si no cancela sus datos personales
          expresamente de nuestros ficheros, continúa interesado en seguir
          incorporado a los mismos hasta que el Responsable lo considere
          oportuno y mientras sea adecuado a la finalidad por la que se
          obtuvieron.
        </p>
      </div>
      <div>
        <p class="text-xl font-medium my-4">Derechos de los interesados</p>
        <p class="my-4">El titular de los datos personales tiene derecho a:</p>
        <ul>
          <li class="my-2">
            Obtener confirmación sobre si el Responsable está tratando los datos
            personales que les conciernan, o no.
          </li>
          <li class="my-2">
            Acceder a sus datos personales, así como a solicitar la
            rectificación de los datos inexactos o, en su caso, solicitar su
            supresión cuando, entre otros motivos, los datos ya no sean
            necesarios para los fines que fueron recogidos.
          </li>
          <li class="my-2">
            En determinadas circunstancias, los interesados podrán solicitar la
            limitación del tratamiento de sus datos, en cuyo caso únicamente el
            Responsable los conservará para el ejercicio o la defensa de
            reclamaciones.
          </li>
          <li class="my-2">
            En determinadas circunstancias y por motivos relacionados con su
            situación particular, los interesados podrán oponerse al tratamiento
            de sus datos.
          </li>
          <li class="my-2">
            En determinadas circunstancias, en virtud del derecho de
            portabilidad, los interesados tendrán derecho a obtener sus datos
            personales en un formato estructurado de uso común y lectura
            mecánica y transmitirlo a otro responsable.
          </li>
        </ul>
        <p class="my-4">El titular puede ejercer sus derechos:</p>
        <ul>
          <li class="my-2">
            Mediante escrito dirigido a Ming Zhan Import & Export Co., Limited,
            2001 Edificio A, Mansión Liandu, Distrito Financiero de Negocios,
            Yiwu, Zhejiang, China, 322000, referencia “Protección de Datos”.
          </li>
          <li class="my-2">
            Mediante correo electrónico a la dirección <EMAIL> indicando
            en el asunto “Protección de Datos”.
          </li>
        </ul>
        <p class="my-4">
          Si considera que sus derechos no se han atendido adecuadamente, tiene
          derecho a presentar una reclamación ante la Agencia China de
          Protección de Datos.
        </p>
      </div>
      <div>
        <p class="text-xl font-medium my-4">
          Consentimiento para el uso de cookies.
        </p>
        <p class="my-4">
          Nuestros servicios utilizan cookies para recopilar y almacenar cierta
          información. Por lo general, implican piezas de información o código
          que un sitio web transfiere o accede desde el disco duro de su
          computadora o dispositivo móvil para almacenar y, a veces, rastrear
          información sobre usted. Las cookies y tecnologías similares le
          permiten ser recordado cuando usa esa computadora o dispositivo para
          interactuar con sitios web y servicios en línea y se pueden usar para
          administrar una variedad de funciones y contenido, así como para
          almacenar búsquedas y presentar contenido personalizado.
        </p>

        <p class="my-4">
          Nuestros servicios utilizan cookies y tecnologías similares para
          distinguirlo de otros usuarios de nuestros servicios. Esto nos ayuda a
          brindarle una buena experiencia cuando navega por nuestro sitio/usa
          nuestra aplicación y también nos permite mejorar nuestros servicios.
        </p>
        <p class="my-4">
          La mayoría de los navegadores web aceptan automáticamente cookies y
          tecnologías similares, pero si lo prefiere, puede cambiar su navegador
          para evitarlo y su pantalla de ayuda o manual le indicará cómo
          hacerlo.
        </p>
        <p class="my-4">
          Varias cookies y tecnologías similares que utilizamos duran solo la
          duración de su sesión web o aplicación y caducan cuando cierra su
          navegador o sale de la aplicación. Otros se usan para recordarlo
          cuando regresa a los servicios y durarán más.
        </p>
        <p class="my-4">
          Utilizamos estas cookies y otras tecnologías sobre la base de que son
          necesarias para la ejecución de un contrato con usted, o porque su uso
          es en nuestro interés legítimo (donde hemos considerado que estos no
          son anulados por sus derechos), y, en algunos casos, cuando así lo
          exija la ley, en los que haya dado su consentimiento para su uso.
        </p>
      </div>
      <div>
        <p class="text-xl font-medium my-4">
          Usamos cookies para crear una identificación única.
        </p>
        <p class="my-4">Utilizamos los siguientes tipos de cookies:</p>
        <ul>
          <li>
            <p class="text-base font-medium my-4">
              Cookies estrictamente necesarias.
            </p>
            <p class="my-4">
              Estas son cookies que se requieren para el funcionamiento de
              nuestro sitio web y según nuestros términos con usted. Incluyen,
              por ejemplo, cookies que le permiten iniciar sesión en áreas
              seguras de nuestro sitio web, usar un carrito de compras o hacer
              uso de los servicios de facturación electrónica.
            </p>
          </li>
          <li>
            <p class="text-base font-medium my-4">
              Cookies analíticas/de rendimiento.
            </p>
            <p class="my-4">
              Nos permiten reconocer y contar el número de visitantes y ver cómo
              se mueven los visitantes por nuestro sitio web cuando lo están
              utilizando. Esto nos ayuda a nuestros intereses legítimos de
              mejorar la forma en que funciona nuestro sitio web, por ejemplo,
              al garantizar que los usuarios encuentren lo que buscan
              fácilmente.
            </p>
          </li>
          <li>
            <p class="text-base font-medium my-4">Cookies de funcionalidad.</p>
            <p class="my-4">
              Estas se utilizan para reconocerlo cuando regresa a nuestro sitio
              web. Esto nos permite, sujeto a sus elecciones y preferencias,
              personalizar nuestro contenido, saludarlo por su nombre y recordar
              sus preferencias (por ejemplo, su elección de idioma o región).
            </p>
          </li>
        </ul>
      </div>
      <div>
        <p class="text-xl font-medium my-4">CAMBIOS AL AVISO DE PRIVACIDAD</p>
        <p class="my-4">
          EL RESPONSABLE, se reserva el derecho de modificar el presente Aviso
          de Privacidad como estime conveniente, de conformidad al cumplimiento
          de la legislación sobre la protección de datos y los intereses de EL
          RESPONSABLE, se le informa que el Aviso de Privacidad actualizado
          estará disponible en la siguiente página de internet:
          <a
            target="_blank"
            class="my-2 text-[#4D85FF]"
            href="https://shop.chilat.com/"
            >shop.chilat.com</a
          >.
        </p>
      </div>
      <div>
        <p class="text-xl font-medium my-4">CONTACTO</p>
        <p class="my-4">
          Cualquier duda al presente aviso de privacidad, nos puede escribir al
          siguiente correo electrónico <a
            target="_blank"
            class="my-2 text-[#4D85FF]"
            href="https://shop.chilat.com/"
            >shop.chilat.com</a
          >, número telefónico +86 15924262117 o visitarnos en nuestro domicilio
          ubicado en 2001 Edificio A, Mansión Liandu, Distrito Financiero de
          Negocios, Yiwu, Zhejiang, China, 322000.
        </p>
      </div>
    </n-scrollbar>
  </n-modal>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

import loginBg from "@/assets/icons/loginBg.jpg";

const route = useRoute();
const authStore = useAuthStore();
const regFromRef = ref<FormInst | null>(null);
const loginFromRef = ref<FormInst | null>(null);
const pageTheme = computed(() => useAuthStore().getPageTheme);
const pageData = reactive({
  showPwd: false,
  regForm: <any>{
    username: "",
    password: "",
    fromInviteCode: "",
    acceptTerms: false,
  },
  addCartGoods: {},
  showAgree: false,
  shouldShake: false,
});

onMounted(() => {
  // 记录未登录首页是否打开过注册登录弹框
  if (route.query.pageSource === "/h5" || route.query.pageSource === "/h5/") {
    window?.MyStat.setSessionValue("isClickLoginModal", "true");
  }
  let loginInfo = sessionStorage.getItem("loginInfo");
  if (loginInfo) {
    const info = JSON.parse(loginInfo);
    pageData.regForm.username = info.username;
    pageData.regForm.password = info.password;
    pageData.regForm.fromInviteCode = info.fromInviteCode;
    pageData.regForm.acceptTerms = info.acceptTerms;
    sessionStorage.removeItem("loginInfo");
  }
  if (!pageData.regForm.fromInviteCode) {
    pageData.regForm.fromInviteCode = authStore.$state.fromInviteCode || "";
    if (pageData.regForm.fromInviteCode) {
      window?.MyStat?.addPageEvent(
        "passport_input_invite",
        "在账号窗口，输入了邀请码"
      );
    }
  }
});

const rules: FormRules = {
  username: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const pattern = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/;
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputEmailTips"));
      } else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_email_format_error",
          `邮箱格式错误，邮箱：${value}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.emailTips"));
      }
      return true;
    },
  },
  password: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const specialCharPattern = /[^A-Za-z\d]/; // 特殊字符匹配
      const pattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/; // 8-16位包含数字及字母
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputPwdTips"));
      }
      // 校验是否包含特殊字符
      else if (specialCharPattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdFormatTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdFormatTips"));
      }
      // 校验8-16位包含数字及字母
      else if (!pattern.test(value)) {
        window?.MyStat.addPageEvent(
          "passport_password_format_error",
          `密码格式错误：${authStore.i18n("cm_common.pwdLengthTips")}`
        ); // 埋点
        return new Error(authStore.i18n("cm_common.pwdLengthTips"));
      }
      return true;
    },
  },
};

// 去登录
function onGoLogin() {
  const loginInfo = {
    username: pageData.regForm.username,
    password: pageData.regForm.password,
  };
  sessionStorage.setItem("loginInfo", JSON.stringify(loginInfo));
  window?.MyStat?.addPageEvent("passport_switch_login", "切换到登录TAB");
  navigateToPage("/h5/user/login", route.query, false);
}

// 注册
async function onRegister() {
  await regFromRef.value?.validate((errors: any) => {
    errors?.length &&
      window?.MyStat?.addPageEvent(
        "passport_register_verify_fail",
        `注册表单校验不通过：${errors.map((item: any) =>
          item.map((it: any) => it.message)
        )}`
      );
  });

  if (!pageData.regForm.acceptTerms) {
    window?.MyStat?.addPageEvent(
      "passport_unchoice_term",
      "用户注册，未勾选用户协议错误"
    );
    setTimeout(() => {
      pageData.shouldShake = true;
    }, 3100);
    return showToast(authStore.i18n("cm_common_needAgree"));
  }
  const res: any = await useRegister(pageData.regForm);
  if (res?.result?.code === 200) {
    if (!!window?.gtag) {
      window?.gtag("event", "RegisterSuccess");
    }
    // 密码修改成功之后登录
    onLogin({
      username: pageData.regForm.username,
      password: pageData.regForm.password,
    });
    authStore.setUserInfo(res?.result?.data);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_register_submit_error",
      `注册提交错误：${res.result?.message}`
    );
    showToast(res.result?.message);
  }
}

// 登录
async function onLogin(params: any) {
  try {
    const res: any = await useLogin(params);
    if (res?.result?.code === 200) {
      await authStore.setUserInfo(res?.data);
      await onAddCart();
      if (!!window?.gtag) {
        window?.gtag("event", "LoginSuccess");
      }
      window.location.replace("/register/success");
    } else {
      showToast(res?.result?.message);
      window?.MyStat?.addPageEvent(
        "passport_login_submit_error",
        `登录提交错误：${res.result?.message}`
      );
    }
  } catch (error) {
    showToast(error);
  }
}

// 加购
async function onAddCart() {
  if (!route.query.goods) return;
  const res: any = await useAddCart(JSON.parse(route.query.goods));
  if (res.result?.code === 200) {
    showToast(
      authStore.i18n("cm_goods.addToList"),
      1500,
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/11/df87d5e1-099d-48ca-97a4-239bb000748a.png"
    );
    authStore.getCartList();
  } else {
    showToast(res.result?.message);
  }
}

function onBlurEvent(val: any, type: string) {
  if (!val) return;
  const eventMap: { [key: string]: { event: string; message: string } } = {
    email: {
      event: "passport_input_email",
      message: "在账号窗口，输入了邮箱",
    },
    password: {
      event: "passport_input_password",
      message: "在账号窗口，输入了密码",
    },
    invite: {
      event: "passport_input_invite",
      message: "在账号窗口，输入了邀请码",
    },
    captcha: {
      event: "passport_input_verify_code",
      message: "在账号窗口，输入了验证码",
    },
  };

  const eventInfo = eventMap[type];
  if (eventInfo) {
    window?.MyStat?.addPageEvent(eventInfo.event, eventInfo.message);
  }
}

function onOpenAgree() {
  pageData.showAgree = true;
}

function onCloseAgree() {
  pageData.showAgree = false;
}
</script>

<style scoped lang="scss">
:deep(.n-input__input-el) {
  height: 52px;
}
:deep(.n-form-item-label) {
  font-size: 16px;
  line-height: 16px;
}
.page-wrapper {
  width: 100%;
  height: 100vh;
  min-width: 1280px;
  min-height: 650px;
  background-repeat: no-repeat;
  background-image: url("@/assets/icons/newLoginBg.png");
  background-size: cover;
  overflow: hidden;
}
.page-title {
  width: 540px;
  position: relative;
  text-align: center;
  z-index: 1;
  margin-right: 64px;
  margin-top: 10px;
  font-size: 48px;
  font-style: italic;
  font-weight: 600;
  line-height: 48px;
  text-shadow: 2px 4px 8px rgba(229, 1, 19, 0.9);
  color: #fff4d5;
}
:deep(.n-input, .n-input--focus) {
  --n-box-shadow-focus: none;
  --n-box-shadow-focus-error: none;
  --n-border-warning: none;
  --n-border-focus-warning: none;
  --n-border-hover-warning: none;
  --n-border: none;
  --n-border-disabled: none;
  --n-border-hover: none;
  --n-border-focus: none;
  background-color: #f2f2f2 !important;
  border-radius: 8px;
}
:deep(.n-input--focus) {
  background-color: #f2f2f2;
}
:deep(.n-input__placeholder) {
  font-size: 16px;
  line-height: 16px;
}
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  15% {
    transform: translateX(-5px);
  }
  30% {
    transform: translateX(5px);
  }
  45% {
    transform: translateX(-5px);
  }
  60% {
    transform: translateX(5px);
  }
  75% {
    transform: translateX(-5px);
  }
  90% {
    transform: translateX(5px);
  }
}

.shake {
  animation: shake 1.2s ease-in-out;
  will-change: transform;
}
</style>
