<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from "vue";

const props = defineProps({
  images: Array, // 图片数组
  direction: {
    type: String,
    default: "left", // 滚动方向，可选 "left" 或 "right"
  },
  speed: {
    type: Number,
    default: 50, // 滚动速度，数值越小速度越快
  },
  autoplay: {
    type: Boolean,
    default: false, // 默认不自动播放
  },
  gap: {
    type: Number,
    default: 20, // 图片之间的间距
  },
  imageWidth: {
    type: String,
    default: "auto", // 图片宽度，可以是具体像素值或百分比
  },
  imageHeight: {
    type: String,
    default: "238px", // 默认图片高度
  },
  autoPlayWhenVisible: {
    type: Boolean,
    default: false, // 默认不启用视口检测自动播放
  },
});

const container = ref(null);
const scrollContent = ref(null);
const isDragging = ref(false);
const startX = ref(0);
const scrollLeft = ref(0);
const scrollSpeed = ref(1); // 滚动速度
// 添加内部状态跟踪是否应该自动滚动
const isAutoScrolling = ref(props.autoplay);
// 添加状态跟踪组件是否在视口内
const isInViewport = ref(false);

// 修改计算方向的逻辑，确保正确设置滚动方向
const actualDirection = computed(() => (props.direction === "left" ? 1 : -1));

// 处理拖拽滚动
const startDrag = (e) => {
  isDragging.value = true;
  startX.value = e.type.includes("touch")
    ? e.touches[0].pageX - container.value.offsetLeft
    : e.pageX - container.value.offsetLeft;
  scrollLeft.value = container.value.scrollLeft;
  container.value.classList.add("is-dragging"); // 添加拖拽中的类名
};

const onDrag = (e) => {
  if (!isDragging.value) return;
  e.preventDefault();
  const x = e.type.includes("touch")
    ? e.touches[0].pageX - container.value.offsetLeft
    : e.pageX - container.value.offsetLeft;
  const walk = (x - startX.value) * 2;
  container.value.scrollLeft = scrollLeft.value - walk;
  handleInfiniteScroll();
};

const stopDrag = () => {
  isDragging.value = false;
  if (container.value) {
    container.value.classList.remove("is-dragging"); // 移除拖拽中的类名
  }
};

// 优化无缝滚动逻辑
const handleInfiniteScroll = () => {
  if (!container.value || !scrollContent.value) return;

  const scrollWidth = scrollContent.value.scrollWidth / 2; // 复制的内容宽度
  const threshold = 20; // 增加阈值，提高边界检测的容错性

  if (props.direction === "left") {
    // 向左滚动：当滚动接近或超过一半内容宽度时重置位置
    if (container.value.scrollLeft >= scrollWidth - threshold) {
      // 无动画地重置到开始位置
      container.value.classList.remove("smooth-scroll");
      container.value.scrollLeft = 0;
      setTimeout(() => {
        container.value.classList.add("smooth-scroll");
      }, 50);
    }
  } else {
    // 向右滚动：当滚动接近或到达开始位置时重置到一半内容宽度
    if (container.value.scrollLeft <= threshold) {
      // 无动画地重置到接近末尾位置
      container.value.classList.remove("smooth-scroll");
      container.value.scrollLeft = scrollWidth - threshold;
      setTimeout(() => {
        container.value.classList.add("smooth-scroll");
      }, 50);
    }
  }
};

let animationFrame;

// 优化自动滚动逻辑
const startScroll = () => {
  cancelAnimationFrame(animationFrame);

  const step = () => {
    if (!container.value) return;
    // 使用内部状态而不是直接依赖props.autoplay
    if (!isDragging.value && isAutoScrolling.value) {
      // 使用更平滑的滚动增量
      container.value.scrollLeft += actualDirection.value * scrollSpeed.value;
      handleInfiniteScroll();
    }
    animationFrame = requestAnimationFrame(step);
  };

  step();
};

// 添加停止滚动方法
const stopScroll = () => {
  cancelAnimationFrame(animationFrame);
  animationFrame = null;
};

// 添加控制滚动的方法
const setAutoplay = (value) => {
  isAutoScrolling.value = value;

  if (value) {
    if (!animationFrame) {
      startScroll();
    }
  } else {
    stopScroll();
  }
};

// 添加视口检测逻辑
const setupIntersectionObserver = () => {
  if (!props.autoPlayWhenVisible || !container.value) return null;

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        isInViewport.value = entry.isIntersecting;

        // 根据视口状态自动控制轮播
        if (props.autoPlayWhenVisible) {
          if (entry.isIntersecting) {
            // 进入视口，开始轮播
            if (props.autoplay) {
              setAutoplay(true);
            }
          } else {
            // 离开视口，暂停轮播
            setAutoplay(false);
          }
        }
      });
    },
    { threshold: 0.1 } // 当10%的元素可见时触发
  );

  observer.observe(container.value);
  return observer;
};

// 暴露控制方法给父组件
defineExpose({
  startScroll,
  stopScroll,
  setAutoplay,
});

// 优化速度计算逻辑
watch(
  () => props.speed,
  (newSpeed) => {
    // 确保速度值合理且平滑
    scrollSpeed.value = Math.max(0.5, Math.min(2, 100 / newSpeed));
  },
  { immediate: true }
);

// 监听props.autoplay变化
watch(
  () => props.autoplay,
  (newValue) => {
    // 只有当不启用视口检测或者组件在视口内时才应用自动播放设置
    if (!props.autoPlayWhenVisible || isInViewport.value) {
      isAutoScrolling.value = newValue;
      if (newValue) {
        if (!animationFrame) {
          startScroll();
        }
      } else {
        stopScroll();
      }
    }
  }
);

// 监听autoPlayWhenVisible属性变化
watch(
  () => props.autoPlayWhenVisible,
  () => {
    // 重新设置IntersectionObserver
    if (intersectionObserver) {
      intersectionObserver.disconnect();
    }
    intersectionObserver = setupIntersectionObserver();
  }
);

let intersectionObserver = null;

onMounted(() => {
  if (container.value && scrollContent.value) {
    // 根据滚动方向设置初始位置
    if (props.direction === "right") {
      // 向右滚动时，初始位置设在中间位置
      const scrollWidth = scrollContent.value.scrollWidth / 2;
      container.value.scrollLeft = scrollWidth - 20;
    } else {
      // 向左滚动时，初始位置设在开始位置
      container.value.scrollLeft = 0;
    }

    // 添加平滑滚动类
    container.value.classList.add("smooth-scroll");
  }

  // 设置IntersectionObserver
  intersectionObserver = setupIntersectionObserver();

  // 只有在autoplay为true且不启用视口检测或者组件在视口内时才启动自动滚动
  if (props.autoplay && (!props.autoPlayWhenVisible || isInViewport.value)) {
    isAutoScrolling.value = true;
    startScroll();
  } else {
    isAutoScrolling.value = false;
  }

  // 添加鼠标事件监听
  container.value.addEventListener("mousedown", startDrag);
  container.value.addEventListener("mousemove", onDrag);
  container.value.addEventListener("mouseup", stopDrag);
  container.value.addEventListener("mouseleave", stopDrag);

  // 添加触摸事件监听
  container.value.addEventListener("touchstart", startDrag);
  container.value.addEventListener("touchmove", onDrag, { passive: false });
  container.value.addEventListener("touchend", stopDrag);

  container.value.addEventListener("scroll", handleInfiniteScroll);
});

onUnmounted(() => {
  cancelAnimationFrame(animationFrame);
  if (intersectionObserver) {
    intersectionObserver.disconnect();
  }
  if (container.value) {
    container.value.removeEventListener("mousedown", startDrag);
    container.value.removeEventListener("mousemove", onDrag);
    container.value.removeEventListener("mouseup", stopDrag);
    container.value.removeEventListener("mouseleave", stopDrag);

    // 移除触摸事件监听
    container.value.removeEventListener("touchstart", startDrag);
    container.value.removeEventListener("touchmove", onDrag);
    container.value.removeEventListener("touchend", stopDrag);

    container.value.removeEventListener("scroll", handleInfiniteScroll);
  }
});
</script>

<template>
  <div ref="container" class="carousel-container">
    <div
      ref="scrollContent"
      class="carousel-content"
      :style="{ gap: `${gap}px` }"
    >
      <img
        v-for="(image, index) in images"
        :key="index"
        :src="image"
        alt="carousel-image"
        class="carousel-image"
        :style="{ width: imageWidth, height: imageHeight }"
        draggable="false"
        loading="lazy"
      />
      <img
        v-for="(image, index) in images"
        :key="'clone-' + index"
        :src="image"
        alt="carousel-image"
        class="carousel-image"
        :style="{ width: imageWidth, height: imageHeight }"
        draggable="false"
        loading="lazy"
      />
    </div>
  </div>
</template>

<style scoped>
.carousel-container {
  display: flex;
  overflow-x: hidden;
  position: relative;
  width: 100%;
  -webkit-overflow-scrolling: touch;
}

/* 添加平滑滚动类，用于控制滚动行为 */
.carousel-container.smooth-scroll {
  scroll-behavior: smooth;
}

.carousel-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
  flex-wrap: nowrap;
}

.carousel-image {
  flex-shrink: 0;
  user-select: none;
  border-radius: 6px;
}

.carousel-container.is-dragging {
  scroll-behavior: auto; /* 拖拽时禁用平滑滚动 */
}

.carousel-container::-webkit-scrollbar {
  display: none;
}
.carousel-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
