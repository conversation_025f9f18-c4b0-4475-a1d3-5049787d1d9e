<template>
  <div class="flex-1 bg-white h-full rounded py-4 px-5 flex flex-col">
    <div class="text-[28px] leading-[28px] font-medium mb-[20px]">
      {{ authStore.i18n("cm_coupon.myCoupon") }}
    </div>
    <n-tabs
      animated
      class="flex-1 h-full"
      v-model:value="pageData.ticketStatus"
      :on-update:value="onUpdateTab"
      v-if="!pageData.isNeedMailVerify"
    >
      <n-tab-pane
        v-for="tab in couponTabData"
        :key="tab.value"
        :tab="tab.label"
        :name="tab.value"
        class="h-full"
      >
        <template #tab>
          <span
            class="text-[18px] leading-[18px] text-[#333]"
            :class="{ 'font-medium': pageData.ticketStatus === tab.value }"
          >
            {{ tab.label }}
          </span>
        </template>
        <div class="flex flex-col mt-[28px] page-content">
          <!-- 未使用的优惠券展示tab -->
          <n-tabs
            class="custom-tabs"
            v-model:value="pageData.couponType"
            :on-update:value="onUpdateTypeTab"
            v-if="pageData.ticketStatus === 'TICKET_NOT_USE'"
          >
            <n-tab-pane
              v-for="tab in couponTypeData"
              :key="tab.value"
              :tab="tab.label"
              :name="tab.value"
              ><template #tab>
                <span
                  class="coupon-type-tab"
                  :class="{
                    '!font-medium !text-[#333] !border-[2px] !border-[#333]':
                      pageData.couponType === tab.value,
                  }"
                >
                  {{ tab.label }}
                </span>
              </template>
            </n-tab-pane>
          </n-tabs>
          <div
            v-if="pageData.couponList.length && pageData.isMailVerified"
            class="flex-1 flex flex-col overflow-y-auto"
          >
            <n-scrollbar class="flex-1 flex flex-col">
              <n-space :style="{ gap: '18px 18px' }">
                <coupon-card
                  v-for="coupon in pageData.couponList"
                  :key="coupon.id"
                  :coupon="coupon"
                  pageSource="modal"
                  couponWidth="454px"
                ></coupon-card>
                <!-- 已使用的优惠券 -->
                <template v-if="pageData.ticketStatus === 'TICKET_USE'">
                  <div class="coupon-desc">
                    <icon-card
                      name="mingcute:warning-fill"
                      size="18"
                      color="#999"
                    ></icon-card>
                    {{ authStore.i18n("cm_coupon.usedCouponDesc") }}
                  </div>
                </template>
                <!-- 已过期的优惠券 -->
                <template
                  v-if="pageData.ticketStatus === 'TICKET_LOSE_EFFICACY'"
                >
                  <div class="coupon-desc">
                    <icon-card
                      name="mingcute:warning-fill"
                      size="18"
                      color="#999"
                    ></icon-card>
                    {{ authStore.i18n("cm_coupon.invalidCouponDesc") }}
                  </div>
                </template>
              </n-space>
            </n-scrollbar>
            <n-pagination
              show-size-picker
              show-quick-jumper
              :page-sizes="[10, 20, 30]"
              :item-count="pageData.pageInfo.total"
              v-model:page="pageData.pageInfo.current"
              v-model:page-size="pageData.pageInfo.size"
              :on-update:page="onUpdatePageNo"
              :on-update:page-size="onUpdatePageSize"
              class="mt-3 text-center flex justify-center"
            >
              <template #prefix="{ itemCount }">
                {{ authStore.i18n("cm_inquiry.total") }}
                {{ itemCount }}
              </template>
              <template #goto>{{
                authStore.i18n("cm_inquiry.jumpTo")
              }}</template>
            </n-pagination>
          </div>
          <!-- 没有数据提示 -->
          <div class="mt-[50px] text-center" v-else>
            <img
              loading="lazy"
              alt="noCoupon"
              class="w-[100px] h-[100px] mx-auto"
              src="@/assets/icons/marketing/noCoupon.svg"
              referrerpolicy="no-referrer"
            />
            <!-- 未使用的优惠券 -->
            <div
              v-if="pageData.ticketStatus === 'TICKET_NOT_USE'"
              class="no-coupon-data"
            >
              {{ authStore.i18n("cm_coupon.noCouponData") }}
            </div>
            <!-- 已使用的优惠券 -->
            <div v-if="pageData.ticketStatus === 'TICKET_USE'">
              <div class="no-coupon-data">
                {{ authStore.i18n("cm_coupon.noUsedCouponData") }}
              </div>
              <div class="no-coupon-desc">
                <icon-card
                  name="mingcute:warning-fill"
                  size="18"
                  color="#999"
                ></icon-card>
                {{ authStore.i18n("cm_coupon.usedCouponDesc") }}
              </div>
            </div>
            <!-- 已过期的优惠券 -->
            <div v-if="pageData.ticketStatus === 'TICKET_LOSE_EFFICACY'">
              <div class="no-coupon-data">
                {{ authStore.i18n("cm_coupon.noInvalidCouponData") }}
              </div>
              <div class="no-coupon-desc">
                <icon-card
                  name="mingcute:warning-fill"
                  size="18"
                  color="#999"
                ></icon-card>
                {{ authStore.i18n("cm_coupon.invalidCouponDesc") }}
              </div>
            </div>
          </div>
        </div>
      </n-tab-pane>
    </n-tabs>
    <email-validation
      v-else
      :couponList="pageData.pendingCouponList"
    ></email-validation>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import EmailValidation from "@/pages/user/components/EmailValidation.vue";
const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive(<any>{
  ticketStatus: route?.query?.ticketStatus || "TICKET_NOT_USE", // 优惠券状态
  couponType: route?.query?.couponType || "COUPON_TYPE_ALL", // 优惠券类型
  couponList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 10,
    total: 0,
  },
  isNeedMailVerify: null, //是否需要展示邮箱校验 邮箱校验状态为false且该用户激活后会得到优惠券则展示邮箱校验
  pendingCouponList: <any>[], // 用户激活后的会获取的优惠券列表
});
const couponTabData = reactive<any>([
  {
    // 未使用的优惠券
    value: "TICKET_NOT_USE",
    label: authStore.i18n("cm_coupon.unusedCoupon"),
  },
  {
    // 已使用的优惠券
    value: "TICKET_USE",
    label: authStore.i18n("cm_coupon.usedCoupon"),
  },
  {
    // 过期优惠券
    value: "TICKET_LOSE_EFFICACY",
    label: authStore.i18n("cm_coupon.expiredCoupon"),
  },
]);

const couponTypeData = reactive<any>([
  {
    // 产品+佣金
    value: "COUPON_TYPE_ALL",
    label: authStore.i18n("cm_coupon.allCoupon"),
  },
  {
    // 产品券
    value: "COUPON_TYPE_PRODUCT",
    label: authStore.i18n("cm_coupon.productCoupons"),
  },
  {
    // 佣金券
    value: "COUPON_TYPE_COMMISSION",
    label: authStore.i18n("cm_coupon.commissionCoupons"),
  },
]);

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onQueryVerifyMailResult();

// 查询邮箱是否已验证, 以及验证后可以得到的优惠券
async function onQueryVerifyMailResult() {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo?.value?.username,
    isNeedCoupon: true,
    verifyMailScene: "MY_COUPON_LIST",
  });
  if (res?.result?.code === 200) {
    pageData.isMailVerified = res?.data.isMailVerified;
    if (pageData.isMailVerified) {
      return onGetCouponList();
    }
    pageData.pendingCouponList = res?.data?.couponList || [];
    if (!pageData.isMailVerified && pageData.pendingCouponList?.length) {
      pageData.isNeedMailVerify = true;
    } else {
      pageData.isNeedMailVerify = false;
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

async function onGetCouponList() {
  const params = <any>{
    page: pageData.pageInfo,
    ticketStatus: pageData.ticketStatus,
  };
  if (
    pageData.ticketStatus === "TICKET_NOT_USE" &&
    pageData.couponType !== "COUPON_TYPE_ALL"
  ) {
    params.couponType = pageData.couponType;
  }
  const res: any = await useGetMyCouponDetailList(params);
  if (res?.result?.code === 200) {
    pageData.couponList = res?.data;
    pageData.pageInfo = res?.page;
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res.result?.message);
  }
}

function onUpdatePageNo(page: number) {
  pageData.pageInfo.current = page;
  onGetCouponList();
}

function onUpdatePageSize(pageSize: number) {
  pageData.pageInfo.current = 1;
  pageData.pageInfo.size = pageSize;
  onGetCouponList();
}

function onUpdateTab(val: any) {
  pageData.pageInfo.current = 1;
  const url = new URL(window.location.href);
  url.searchParams.set("ticketStatus", val);
  window.history.replaceState(null, "", url.toString());
  pageData.ticketStatus = val;
  onGetCouponList();
  window?.MyStat?.addPageEvent(
    "coupon_list_select_status",
    `选择优惠券状态：${val}`
  ); // 埋点
}

function onUpdateTypeTab(val: any) {
  pageData.pageInfo.current = 1;
  const url = new URL(window.location.href);
  url.searchParams.set("couponType", val);
  window.history.replaceState(null, "", url.toString());
  pageData.couponType = val;
  onGetCouponList();
  window?.MyStat?.addPageEvent(
    "coupon_list_select_type",
    `选择优惠券类型：${val}`
  ); // 埋点
}
</script>
<style scoped lang="scss">
:deep(.n-tabs .n-tab-pane) {
  padding: 0;
}

.coupon-type-tab {
  display: flex;
  padding: 8px 10px;
  justify-content: center;
  align-items: center;
  border-radius: 100px;
  border-width: 1px;
  border-style: solid;
  border-color: #4d4d4d;
  color: #4d4d4d;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px;
}
.custom-tabs {
  margin-bottom: 28px;
  :deep(.n-tabs-bar) {
    display: none;
  }
  :deep(.n-tabs-tab) {
    padding: 0;
  }
}
.no-coupon-data {
  font-size: 18px;
  font-weight: 500;
  line-height: 18px;
  color: #333;
  margin: 4px 0 14px;
}
.no-coupon-desc {
  font-size: 18px;
  line-height: 18px;
  color: #999;
}
.coupon-desc {
  margin-top: 4px;
  font-size: 14px;
  line-height: 14px;
  color: #999;
}
:deep(.n-tabs .n-tabs-bar) {
  background-color: #333;
}
:deep(.n-tabs-pane-wrapper) {
  height: 100%;
}
.page-content {
  height: calc(100% - 70px);
}
</style>
