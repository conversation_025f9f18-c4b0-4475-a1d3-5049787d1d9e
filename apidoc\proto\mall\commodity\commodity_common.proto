syntax = "proto3";
package mall.commodity;

option java_package = "com.chilat.rpc.commodity.common";

import "common.proto";


enum GoodsListFilterType {
    GOODS_LIST_FILTER_TYPE_UNSPECIFIED = 0;
    GOODS_LIST_FILTER_TYPE_CATEGORY = 10; //商品分类过滤
    GOODS_LIST_FILTER_TYPE_PRICE = 20; //价格范围过滤
    GOODS_LIST_FILTER_TYPE_MIN_BUY = 30; //最少购买过滤
}
/*
// 商品信息选项
message GoodsExtendItem {
    string groupId = 1; // 字典ID
    string groupName = 2; // 字典名称
    string itemId = 3; // item id
    string itemName = 4; // item名称
    string itemAlias = 5; // item别名（可为空）
}
*/