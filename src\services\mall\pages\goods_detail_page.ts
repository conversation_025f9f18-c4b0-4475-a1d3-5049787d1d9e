/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { GoodsDetailDataResp, GoodsDetailPageResp } from "./model/goods_detail_page_model";
import { GoodsDetailQueryParam } from "./param/goods_detail_page_param";

export const protobufPackage = "mall.pages";

/** 商品详情页（暂不使用） */
export interface GoodsDetailPage {
  /** 获取页面数据 */
  getPageData(request: GoodsDetailQueryParam): Promise<GoodsDetailPageResp>;
  /** 获取商品详情数据 */
  getGoodsInfo(request: GoodsDetailQueryParam): Promise<GoodsDetailDataResp>;
}

export const GoodsDetailPageServiceName = "mall.pages.GoodsDetailPage";
export class GoodsDetailPageClientImpl implements GoodsDetailPage {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || GoodsDetailPageServiceName;
    this.rpc = rpc;
    this.getPageData = this.getPageData.bind(this);
    this.getGoodsInfo = this.getGoodsInfo.bind(this);
  }
  getPageData(request: GoodsDetailQueryParam): Promise<GoodsDetailPageResp> {
    const data = GoodsDetailQueryParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getPageData", data);
    return promise.then((data) => GoodsDetailPageResp.decode(_m0.Reader.create(data)));
  }

  getGoodsInfo(request: GoodsDetailQueryParam): Promise<GoodsDetailDataResp> {
    const data = GoodsDetailQueryParam.encode(request).finish();
    const promise = this.rpc.request(this.service, "getGoodsInfo", data);
    return promise.then((data) => GoodsDetailDataResp.decode(_m0.Reader.create(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}
