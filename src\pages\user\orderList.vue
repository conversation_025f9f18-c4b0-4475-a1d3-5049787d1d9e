<template>
  <div class="flex-1 bg-white h-full rounded py-4">
    <div class="text-xl font-medium px-5 mb-2">
      {{ authStore.i18n("cm_order.myOrder") }}
    </div>
    <n-tabs
      animated
      type="line"
      justify-content="space-evenly"
      v-model:value="pageData.mallOrderStatus"
      :on-update:value="onUpdateTab"
    >
      <n-tab-pane
        v-for="tab in orderTabData"
        :key="tab.value"
        :tab="tab.label"
        :name="tab.value"
      >
        <div v-if="pageData.orderList.length" class="h-[64vh] flex flex-col">
          <n-scrollbar class="overflow-auto flex-1">
            <n-table :bordered="false" :single-line="true" class="text-[13px]">
              <thead>
                <tr class="text-[14px]">
                  <!-- 产品信息 -->
                  <th>{{ authStore.i18n("cm_inquiry.product") }}</th>
                  <!-- 商品规格 -->
                  <th class="w-[180px]">
                    {{ authStore.i18n("cm_inquiry.specifications") }}
                  </th>
                  <!-- 数量 -->
                  <th class="w-[80px]">
                    {{ authStore.i18n("cm_inquiry.count") }}
                  </th>
                  <!-- 价格单位 -->
                  <th class="w-[70px]">
                    {{ authStore.i18n("cm_inquiry.unit") }}
                  </th>
                  <!-- 销售单价 -->
                  <th class="w-[90px] break-all !whitespace-normal">
                    {{ authStore.i18n("cm_inquiry.unitPrice")
                    }}<span class="text-[12px]">({{ monetaryUnit }})</span>
                  </th>
                  <!-- 销售总价 -->
                  <th class="w-[100px]">
                    {{ authStore.i18n("cm_inquiry.totalPrice") }}
                    <div class="text-[12px]">({{ monetaryUnit }})</div>
                  </th>
                  <!-- 订单状态 -->
                  <th class="w-[140px] break-all !whitespace-normal">
                    {{ authStore.i18n("cm_inquiry.orderStatus") }}
                  </th>
                  <!-- 操作 -->
                  <th class="w-[80px] break-all !whitespace-normal">
                    {{ authStore.i18n("cm_inquiry.operation") }}
                  </th>
                </tr>
              </thead>
              <tbody v-for="order in pageData.orderList" :key="order.orderNo">
                <tr>
                  <td colspan="8" class="!p-0 !border-none">
                    <div
                      class="border-t-0 border-b h-[50px] leading-[50px] !border-emerald-400 bg-green-50 text-sm px-4"
                    >
                      <div class="flex text-[#333]">
                        <div class="mr-12">
                          <span class="text-[#666]">{{
                            authStore.i18n("cm_order.orderNo")
                          }}</span>
                          <a
                            data-spm-box="order-list-go-detail"
                            :href="`/order/details?orderNo=${order.orderNo}`"
                            class="hover:text-[#e50113]"
                          >
                            <span>{{ order.orderNo }}</span>
                          </a>
                        </div>
                        <div>
                          <span class="text-[#666]">
                            {{ authStore.i18n("cm_order.orderTime") }}</span
                          >{{ timeFormatByZone(order.orderTime) }}
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr v-for="(sku, index) in order?.skuList" :key="sku.skuNo">
                  <!-- 产品信息(商品名称+商品图片) -->
                  <td>
                    <div class="flex items-center flex-nowrap">
                      <n-image
                        lazy
                        :src="sku.picUrl"
                        object-fit="fill"
                        class="w-[62px] h-[62px] flex-shrink-0"
                        :img-props="{ referrerpolicy: 'no-referrer' }"
                      />
                      <a
                        target="_blank"
                        :href="`/goods/${sku.goodsId}${
                          sku.padc ? `?padc=${sku.padc}` : ''
                        }`"
                        :data-spm-index="index + 1"
                        data-spm-box="order-list-goods"
                      >
                        <div
                          class="pl-2 text-current hover:text-[#e50113] hover:cursor-pointer"
                        >
                          <n-ellipsis :line-clamp="3" :tooltip="false">{{
                            sku.goodsName
                          }}</n-ellipsis>
                        </div>
                      </a>
                    </div>
                  </td>
                  <!-- 商品规格 -->
                  <td>
                    <div
                      v-for="(spec, specIndex) in sku.specList"
                      :key="specIndex"
                    >
                      <div>{{ spec.specName }}:{{ spec.itemName }}</div>
                    </div>
                  </td>
                  <!-- 数量 -->
                  <td>{{ sku.count }}</td>
                  <!-- 价格单位 -->
                  <td>{{ sku.goodsPriceUnitName }}</td>
                  <!-- 销售单价 -->
                  <td>{{ sku.unitPrice }}</td>
                  <!-- 销售总价 -->
                  <td>{{ sku.totalAmount }}</td>
                  <!-- 订单状态 -->
                  <td
                    v-if="index === 0"
                    :rowspan="order.skuList.length"
                    class="!border-l !border-[#efeff5ff] break-all text-center"
                  >
                    {{ order.statusDesc }}
                  </td>
                  <!-- 操作 -->
                  <td
                    v-if="index === 0"
                    :rowspan="order.skuList.length"
                    class="!border-l !border-[#efeff5ff] w-[90px]"
                  >
                    <n-space vertical>
                      <!-- [支付订单费用] [支付产品成本] 显示取消按钮 -->
                      <n-button
                        tertiary
                        @click="onOrderCancel(order)"
                        v-if="
                          order.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE' ||
                          order.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT'
                        "
                        class="p-2 w-[90px] break-all !whitespace-normal h-[fit-content]"
                      >
                        {{ authStore.i18n("cm_order.orderCancel") }}
                      </n-button>
                      <!-- [支付订单费用] [支付产品成本] [支付国际费用] 显示付款按钮 -->
                      <a
                        data-spm-box="order-list-go-pay"
                        :href="`/order/details?orderNo=${order.orderNo}`"
                        v-if="
                          order.mallOrderStatus === 'MALL_WAIT_PAY_ALL_FEE' ||
                          order.mallOrderStatus === 'MALL_WAIT_PAY_PRODUCT' ||
                          order.mallOrderStatus === 'MALL_WAIT_PAY_INTER_FEE'
                        "
                      >
                        <n-button
                          color="#E50113"
                          text-color="#fff"
                          class="p-2 w-[90px] break-all !whitespace-normal h-[fit-content]"
                        >
                          {{ authStore.i18n("cm_order.orderPay") }}
                        </n-button>
                      </a>
                      <!-- 所有状态 显示订单详情按钮 -->
                      <a
                        data-spm-box="order-list-go-detail"
                        :href="`/order/details?orderNo=${order.orderNo}`"
                      >
                        <n-button
                          color="#E50113"
                          text-color="#fff"
                          class="p-2 w-[90px] break-all !whitespace-normal h-[fit-content]"
                        >
                          {{ authStore.i18n("cm_order.orderDetail") }}
                        </n-button>
                      </a>
                    </n-space>
                  </td>
                </tr>
              </tbody>
            </n-table>
          </n-scrollbar>
          <n-pagination
            show-size-picker
            show-quick-jumper
            :page-sizes="[10, 20, 30]"
            :item-count="pageData.pageInfo.total"
            v-model:page="pageData.pageInfo.current"
            v-model:page-size="pageData.pageInfo.size"
            :on-update:page="onUpdatePageNo"
            :on-update:page-size="onUpdatePageSize"
            class="mt-3 text-center flex justify-center"
          >
            <template #prefix="{ itemCount }">
              {{ authStore.i18n("cm_inquiry.total") }}
              {{ itemCount }}
            </template>
            <template #goto>{{ authStore.i18n("cm_inquiry.jumpTo") }}</template>
          </n-pagination>
        </div>
        <n-empty
          v-else
          class="mt-[20vh]"
          :description="authStore.i18n('cm_order.noData')"
        >
        </n-empty>
      </n-tab-pane>
    </n-tabs>
    <order-cancel
      ref="orderCancelRef"
      @updateOrderState="onUpdateOrderState"
    ></order-cancel>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import OrderCancel from "@/pages/order/components/OrderCancel.vue";

const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive(<any>{
  mallOrderStatus: route?.query?.mallOrderStatus || "MALL_STATUS_ALL",
  orderList: <any>[],
  pageInfo: <any>{
    current: 1,
    size: 10,
    total: 0,
  },
});
const orderCancelRef = ref<any>(null);
const orderTabData = reactive<any>([
  {
    // 全部状态
    value: "MALL_STATUS_ALL",
    label: authStore.i18n("cm_order.statusALL"),
  },
  {
    // 待支付 [待支付产品成本、待支付订单费用]
    value: "MALL_WAIT_PAY",
    label: authStore.i18n("cm_order.waitPayMoney"),
  },
  {
    // 采购
    value: "MALL_PURCHASING",
    label: authStore.i18n("cm_order.purchasing"),
  },
  {
    // 国际费用 [待计算国际费用、待支付国际费用]
    value: "MALL_INTERNATIONAL_FEE",
    label: authStore.i18n("cm_order.internationalFee"),
  },
  {
    // 待发货
    value: "MALL_WAIT_SEND_OUT",
    label: authStore.i18n("cm_order.waitSendOut"),
  },
  // {
  //   // 国际运输
  //   value: "MALL_TRANSPORTING",
  //   label: authStore.i18n("cm_order.transporting"),
  // },
  // {
  //   // 清关中
  //   value: "MALL_CUSTOMS_CLEARING",
  //   label: authStore.i18n("cm_order.customsClearing"),
  // },
  {
    // 派送中
    value: "MALL_DELIVERING",
    label: authStore.i18n("cm_order.delivering"),
  },
  {
    // 已签收
    value: "MALL_USER_SIGNED",
    label: authStore.i18n("cm_order.userSigned"),
  },
  {
    // 已取消
    value: "MALL_CANCELED",
    label: authStore.i18n("cm_order.canceled"),
  },
]);

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onGetOrderList();
async function onGetOrderList() {
  const statusMap = <any>{
    MALL_WAIT_PAY: ["MALL_WAIT_PAY_PRODUCT", "MALL_WAIT_PAY_ALL_FEE"],
    MALL_INTERNATIONAL_FEE: [
      "MALL_WAIT_CAL_INTER_FEE",
      "MALL_WAIT_PAY_INTER_FEE",
    ],
    MALL_DELIVERING: [
      "MALL_TRANSPORTING",
      "MALL_CUSTOMS_CLEARING",
      "MALL_DELIVERING",
    ],
  };
  const res: any = await useGetOrderList({
    page: pageData.pageInfo,
    mallOrderStatusList: statusMap[pageData.mallOrderStatus] || [
      pageData.mallOrderStatus,
    ],
  });
  if (res?.result?.code === 200) {
    pageData.orderList = res?.data.orderList;
    pageData.pageInfo = res?.data.page;
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    showToast(res.result?.message);
  }
}

function onUpdatePageNo(page: number) {
  pageData.pageInfo.current = page;
  onGetOrderList();
}

function onUpdatePageSize(pageSize: number) {
  pageData.pageInfo.current = 1;
  pageData.pageInfo.size = pageSize;
  onGetOrderList();
}

function onUpdateTab(val: any) {
  pageData.pageInfo.current = 1;
  const url = new URL(window.location.href);
  url.searchParams.set("mallOrderStatus", val);
  window.history.replaceState(null, "", url.toString());
  pageData.mallOrderStatus = val;
  onGetOrderList();
}

function onUpdateOrderState() {
  onGetOrderList();
}

// 订单取消
function onOrderCancel(row: any) {
  orderCancelRef.value?.onOpenDialog(row.orderNo);
}
</script>
<style scoped lang="scss">
thead tr th {
  background: #fff;
  font-weight: 500;
  padding: 8px 10px;
  line-height: 18px;
}
tr td {
  padding: 12px 10px;
}

:deep(.n-tabs .n-tab-pane) {
  padding: 0;
}
:deep(.n-tabs .n-tabs-tab) {
  max-width: 150px;
  display: block;
  white-space: normal;
}
</style>
