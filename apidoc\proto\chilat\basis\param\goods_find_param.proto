syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";
import "common/business.proto";

message GoodsFindListPageQueryParam {
  common.PageParam page = 1;
  GoodsFindListQueryParam param = 2;
}

message GoodsFindListQueryParam {
  int64 startTime = 10; // 开始创建时间
  int64 endTime = 20; // 结束创建时间
  string userId = 30; // 用户ID
  string countryId = 40; // 国家ID
  string salesManId = 50; // 负责人ID
  string goodsFindNo = 60; // 找货单号
  string whatsapp = 70; // WhatsApp
  string email = 80; // 邮箱
  bool isNotifyCustomer = 90; // 是否通知客户
  common.GoodsFindStatusEnum status = 100; // 找货状态
}

message GoodsFindAssignParam {
  repeated string idList = 10; // 找货ID列表
  string salesManId = 20;
  string salesManName = 30;
}

message SyncChargePersonParam {
  repeated string idList = 10; // 找货ID列表
}

message OperateParam {
  string id = 10; // 找货ID
  common.GoodsFindOperationTypeEnum type = 20; // 操作类型: 保存找货备注、开始找货、通知客户、找货完成
  string remark = 30; // 备注
}

message ExportExcelParam {
  repeated string idList = 10; // 找货ID列表
  GoodsFindListQueryParam param = 20; // 查询参数
}