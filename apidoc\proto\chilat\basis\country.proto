syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/country_model.proto";
import "chilat/basis/param/country_param.proto";
import "common.proto";

// 字典服务
service Country {
  // 查询所有国家
  rpc listAll(QueryCountryParam) returns (CountryResp);
  // 查询所有支持开单的国家
  rpc listCountryForOrder(QueryCountryParam) returns (CountryResp);
  // 根据国家查询地区
  rpc listRegionByCountry(common.IdParam) returns (ListRegionResp);
  // 根据邮编查询地区
  rpc listRegionByPostcode(ListRegionByPostcodeParam) returns (ListRegionResp);
  // 获取国家信息
  rpc getCountryInfo(common.IdParam) returns (CountryInfoResp);
}