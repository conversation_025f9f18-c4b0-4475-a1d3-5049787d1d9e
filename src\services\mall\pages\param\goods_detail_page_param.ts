/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { VisitDeviceType, visitDeviceTypeFromJSON, visitDeviceTypeToJSON } from "../../../common/business";

export const protobufPackage = "mall.pages";

/** 商品详情请求参数 */
export interface GoodsDetailQueryParam {
  /** 商品ID（必填） */
  id: string;
  /** 访问失败类型 */
  deviceType: VisitDeviceType;
}

function createBaseGoodsDetailQueryParam(): GoodsDetailQueryParam {
  return { id: "", deviceType: 0 };
}

export const GoodsDetailQueryParam = {
  encode(message: GoodsDetailQueryParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(82).string(message.id);
    }
    if (message.deviceType !== 0) {
      writer.uint32(160).int32(message.deviceType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsDetailQueryParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsDetailQueryParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.id = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.deviceType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsDetailQueryParam {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      deviceType: isSet(object.deviceType) ? visitDeviceTypeFromJSON(object.deviceType) : 0,
    };
  },

  toJSON(message: GoodsDetailQueryParam): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.deviceType !== 0) {
      obj.deviceType = visitDeviceTypeToJSON(message.deviceType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsDetailQueryParam>, I>>(base?: I): GoodsDetailQueryParam {
    return GoodsDetailQueryParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsDetailQueryParam>, I>>(object: I): GoodsDetailQueryParam {
    const message = createBaseGoodsDetailQueryParam();
    message.id = object.id ?? "";
    message.deviceType = object.deviceType ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
