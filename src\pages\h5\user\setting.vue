<template>
  <div class="bg-[#F2F2F2] min-h-[100vh] pt-[1rem]">
    <div
      class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] items-center justify-center bg-white fixed top-0"
    >
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        data-spm-box="navigation-back-icon"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_user.setting") }}
      </div>
    </div>
    <div class="p-[0.2rem]">
      <n-space vertical :style="{ gap: '0.2rem 0' }">
        <a :href="jump.path" v-for="(jump, index) in jumpData" :key="index">
          <div
            class="bg-white rounded-[0.08rem] py-[0.3rem] px-[0.33rem] flex items-center"
          >
            <icon-card
              :size="jump.iconSize"
              color="#E50113"
              class="mr-[0.16rem]"
              :name="jump.icon"
            ></icon-card>
            <div class="text-[0.32rem] leading-[0.48rem]">
              {{ jump.title }}
            </div>
          </div>
        </a>
      </n-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const router = useRouter();
const authStore = useAuthStore();
const pageData = reactive(<any>{});
const jumpData = <any>[
  {
    iconSize: "0.46rem",
    icon: "tabler:lock-cog",
    path: "/h5/user/updatePwd",
    title: authStore.i18n("cm_user.updatePwd"),
  },
];

// 返回
function onBackClick() {
  router.go(-1);
}
</script>

<style scoped lang="scss"></style>
