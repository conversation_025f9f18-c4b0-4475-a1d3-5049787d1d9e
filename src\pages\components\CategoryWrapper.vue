<template>
  <div :data-spm-box="spmCode">
    <div
      v-for="(category, index) in categoryGoods"
      :key="index"
      :data-spm-index="index + 1"
      class="mb-[60px]"
      :data-spm-param="category.tagId || null"
    >
      <div class="flex">
        <a
          :href="
            category.tagId
              ? `/goods/list/all?tagId=${category.tagId}`
              : `/goods/list/${category.categoryId}?cateName=${category.categoryName}`
          "
          target="_blank"
          ><div
            class="category-title flex items-center gap-[10px] px-[30px] justify-center"
            :style="{
              backgroundColor: category?.cateColor,
              color: !!category?.cateColor ? '#FFF' : '#333',
            }"
          >
            <div>
              {{ category.categoryName }}
            </div>
            <img
              loading="lazy"
              alt="arrow"
              class="w-[8px]"
              src="@/assets/icons/home/<USER>"
              referrerpolicy="no-referrer"
            />
          </div>
        </a>

        <div class="category-menus">
          <div
            class="category-menu"
            v-for="(categoryItem, itemIndex) in category.pageGoods"
            :key="categoryItem.categoryId"
            @mouseenter="onMouseEnterCategory(index, itemIndex)"
          >
            <a
              :href="
                category.tagId
                  ? `/goods/list/all?tagId=${category.tagId}&categoryId=${categoryItem.categoryId}&cateName=${categoryItem.categoryName}`
                  : `/goods/list/${categoryItem.categoryId}?cateName=${categoryItem.categoryName}`
              "
              target="_blank"
            >
              <div
                class="menus-name"
                :style="{
                  backgroundColor:
                    pageData.actCategoryData[index].itemIndex === itemIndex
                      ? category?.subCateColor
                      : 'transparent',
                  color:
                    pageData.actCategoryData[index].itemIndex === itemIndex
                      ? category?.cateColor
                      : '#333',
                }"
              >
                {{ categoryItem.categoryName }}
              </div>
              <div
                class="menu-border"
                :style="{
                  backgroundColor:
                    pageData.actCategoryData[index].itemIndex === itemIndex
                      ? category?.cateColor
                      : '#FFF',
                }"
              ></div
            ></a>
          </div>
        </div>
      </div>
      <div class="flex mt-[16px]">
        <a
          :href="`/goods/list/${category.categoryId}?cateName=${category.categoryName}`"
          target="_blank"
        >
          <img
            loading="lazy"
            :src="category.image"
            :alt="category.categoryName"
            class="rounded-[4px] w-[247px] h-[502px]"
            referrerpolicy="no-referrer"
          />
        </a>
        <div class="category-content pl-[20px]">
          <n-grid cols="5 s:4 m:5 l:5 xl:5 2xl:5">
            <n-grid-item
              v-for="goods in category.pageGoods[
                pageData.actCategoryData[index].itemIndex
              ]?.goodsList"
              :key="goods?.goodsId"
            >
              <home-goods-card
                :goods="goods"
                imageWidth="178px"
                imageHeight="178px"
              ></home-goods-card>
            </n-grid-item>
          </n-grid>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import HomeGoodsCard from "./HomeGoodsCard.vue";

interface Category {
  tagId?: string;
  categoryId?: string;
  categoryName: string;
  cateColor?: string;
  subCateColor?: string;
  image: string;
  pageGoods: Array<{
    categoryId: string;
    categoryName: string;
    goodsList: Array<any>;
  }>;
}

const props = defineProps({
  categoryGoods: {
    type: Array as PropType<Category[]>,
    default: () => [],
  },
  spmCode: {
    type: String,
    default: "",
  },
});

const pageData = reactive({
  actCategoryData: [] as { itemIndex: number }[],
});

// 监听 categoryGoods 的变化并更新 actCategoryData
watch(
  () => props.categoryGoods,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      pageData.actCategoryData = newVal.map(() => ({ itemIndex: 0 }));
    }
  },
  { immediate: true }
);

function onMouseEnterCategory(index: number, itemIndex: number) {
  if (pageData.actCategoryData[index]) {
    pageData.actCategoryData[index].itemIndex = itemIndex;
  }
}
</script>

<style scoped lang="scss">
.category-title {
  width: 247px;
  height: 60px;
  color: #fff;
  border-radius: 4px;
  font-size: 16px;
  line-height: 16px;
  font-weight: 500;
}
.category-menus {
  flex: 1;
  margin-left: 20px;
  border-bottom: 2px solid #f2f2f2;
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  .category-menu {
    display: inline-block;
    height: 53px;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
    color: #2f2f2f;
    .menus-name {
      height: 53px;
      line-height: 53px;
      padding: 0 16px;
      border-radius: 4px;
      font-size: 15px;
      background: transparent;
    }
    .menu-border {
      position: absolute;
      left: 0;
      bottom: -6px;
      height: 2px;
      width: 100%;
      background: transparent;
    }
  }
}

.category-content {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}
</style>
