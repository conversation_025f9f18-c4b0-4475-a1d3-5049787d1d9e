<template>
  <div class="bg-[#F2F2F2] min-h-[100vh] pt-[1rem] pb-[0.5rem] text-[0.28rem]">
    <div class="page-header">
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_order.orderPay") }}
      </div>
    </div>
    <div class="page-content px-[0.16rem] pb-[1rem] pt-[0.2rem]">
      <div class="content-card" v-if="pageData.payAmount?.amount">
        <a :href="`/h5/order/details?orderNo=${pageData.orderNo}`" class="flex">
          <img
            loading="lazy"
            :src="pageData.picUrl"
            class="w-[1rem] h-[1rem] mr-[0.24rem]"
            referrerpolicy="no-referrer"
          />
          <n-space vertical :style="{ gap: '0.04rem 0' }" class="flex-1">
            <span class="underline font-medium underline-offset-2"
              >{{ pageData.totalCount }}
              {{ authStore.i18n("cm_order.orderItems") }}</span
            >
            <div
              class="w-[5.4rem] text-[0.24rem] break-all whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {{ pageData.goodsName }}
            </div>
            <div class="text-[0.24rem]">
              <span>{{ authStore.i18n("cm_order.orderNo") }}</span>
              <span>{{ pageData.orderNo }}</span>
            </div>
          </n-space>
        </a>
      </div>
      <div class="content-card" v-if="pageData.payMethodList.length > 0">
        <n-radio-group
          v-model:value="pageData.payMethodId"
          :on-update:value="onUpdatePayMethod"
          class="w-full"
        >
          <n-radio
            v-for="pay in pageData.payMethodList"
            :key="pay.id"
            :value="pay.id"
            class="pay-item"
            :class="pageData.payMethodId === pay.id ? 'selected-pay-item' : ''"
            size="medium"
            :disabled="pay.max < pageData.payAmount?.amount"
          >
            <div class="flex justify-between items-center">
              <div class="mr-[0.04rem]">
                <div class="text-[0.28rem] font-medium min-w-[1.12rem]">
                  {{ pay.name }}
                </div>
                <div
                  v-if="pay.max < pageData.payAmount?.amount"
                  class="text-[0.24rem] text-[#E15A47]"
                >
                  {{ authStore.i18n("cm_order.orderPayLimit")
                  }}{{ setUnit(pay.max) }}
                </div>
              </div>

              <div
                class="min-w-[1rem] h-[0.8rem] bg-[#F2F2F2] px-[0.08rem] flex justify-center items-center"
              >
                <img
                  loading="lazy"
                  :src="pay.iconUrl"
                  class="max-h-[0.4rem]"
                  referrerpolicy="no-referrer"
                />
              </div>
            </div>
          </n-radio>
        </n-radio-group>
      </div>
    </div>
    <div class="page-footer" v-if="pageData.payAmount?.amount">
      <div class="w-full flex justify-between items-center">
        <div class="flex items-center" @click="openAmountDetails">
          <span class="mr-[0.16rem]">{{
            authStore.i18n("cm_order.totalMoney")
          }}</span>
          <div class="font-medium text-[0.32rem]">
            {{ setUnit(pageData.payAmount.amount) }}
          </div>
          <icon-card color="#555" name="ri:arrow-down-s-line" size="26" />
        </div>

        <div class="flex">
          <n-button
            color="#E50113"
            text-color="#fff"
            @click="onOrderPay"
            :loading="pageData.submitLoading"
            :disabled="!pageData.payMethodId"
            class="rounded-[0.16rem] h-[0.6rem] text-[0.26rem]"
          >
            {{ authStore.i18n("cm_order.payNow") }}
          </n-button>
        </div>
      </div>
    </div>
    <!-- 费用明细 -->
    <n-drawer
      v-model:show="pageData.showAmountDetails"
      width="100%"
      placement="bottom"
      :trap-focus="false"
      default-height="8rem"
    >
      <n-drawer-content
        closable
        :title="authStore.i18n('cm_order.feeAmountDetails')"
      >
        <div class="text-[#222] text-[0.28rem]">
          <n-space vertical :style="{ gap: '0.12rem 0' }">
            <div
              class="flex justify-between"
              v-for="(fee, index) in pageData.payAmount.feeList"
              :key="index"
            >
              <div class="w-[60%]">{{ fee.feeName }}</div>
              <span class="text-[0.28rem]" v-if="fee.feeAmount"
                >{{ setUnit(fee.feeAmount) }}
              </span>
            </div>
          </n-space>

          <div class="flex justify-between mt-[0.3rem]">
            <span>{{ authStore.i18n("cm_order.totalMoney") }}</span>
            <span
              class="font-medium text-[0.32rem]"
              v-if="pageData.payAmount?.amount"
              >{{ setUnit(pageData.payAmount.amount) }}
            </span>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
    <!-- 是否支付成功 -->
    <n-modal
      preset="dialog"
      :closable="false"
      :show-icon="false"
      :closeOnEsc="false"
      :maskClosable="false"
      :block-scroll="true"
      v-model:show="pageData.showPaySuccess"
    >
      <div>
        <div class="text-[0.36rem] font-medium mb-[0.3rem]">
          {{ authStore.i18n("cm_order.isPaySuccess") }}
        </div>
        <div class="w-full flex justify-end">
          <n-button
            class="px-[0.4rem] mr-[0.3rem]"
            @click="onGetQueryPayResult"
          >
            {{ authStore.i18n("cm_order.orderCancelNo") }}
          </n-button>
          <n-button
            color="#E50113"
            class="px-[0.4rem]"
            @click="onGetQueryPayResult"
          >
            {{ authStore.i18n("cm_order.orderCancelYes") }}
          </n-button>
        </div>
      </div>
    </n-modal>
    <!-- 点击付款 付款信息错误提示 -->
    <n-modal
      preset="dialog"
      :show-icon="false"
      :closable="false"
      :closeOnEsc="false"
      :maskClosable="false"
      :block-scroll="true"
      v-model:show="pageData.showPayInfoError"
    >
      <div>
        <icon-card
          size="22"
          name="mingcute:warning-line"
          color="#e50113"
          class="mr-[0.06rem]"
        ></icon-card>
        <span class="text-[0.28rem] text-center">
          <!-- 优惠券信息报错 -->
          <span v-if="pageData.couponInfoError">{{
            authStore.i18n("cm_order.chooseCouponError")
          }}</span>
          <span v-else>{{ authStore.i18n("cm_order.orderChanged") }}</span>
        </span>
        <div
          v-if="pageData.timeLeft"
          class="border-t-1 mt-[0.2rem] pt-[0.1rem]"
        >
          <span
            class="text-[0.4rem] mr-[0.12rem] text-[#e50113] w-[0.28rem] inline-block"
            >{{ pageData.timeLeft }}</span
          >
          {{ authStore.i18n("cm_order.orderRedirectDetails") }}
        </div>
      </div>
    </n-modal>
    <!-- 显示付款按钮 -->
    <n-modal
      preset="dialog"
      :show-icon="false"
      :closable="false"
      :auto-focus="false"
      :closeOnEsc="false"
      :maskClosable="false"
      :block-scroll="true"
      v-model:show="pageData.showPayButton"
    >
      <a
        target="_blank"
        @click="onOpenPaySuccess"
        :href="pageData.payUrl"
        class="flex items-center justify-center"
      >
        <n-button
          color="#E50113"
          text-color="#fff"
          class="rounded-[0.16rem] h-[0.7rem] text-[0.28rem] px-[0.5rem]"
        >
          {{ authStore.i18n("cm_order.orderPaySmile") }}
        </n-button>
      </a>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

const pageData = reactive(<any>{
  orderNo: route?.query?.orderNo || "",
  paymentId: route?.query?.paymentId || "",
  totalCount: "", // 商品总数
  picUrl: "", // 商品图片
  goodsName: "", // 商品名称
  payMethodList: <any>[], // 支付方式列表
  payAmount: <any>{}, // 支付金额 实际支付金额 + 费用明细
  payResultModel: <any>{}, //支付结果
  payMethodId: "", //选中的支付方式id
  submitLoading: false, // 提交支付按钮loading
  showPayButton: false, // 展示付款按钮
  showPaySuccess: false, //展示确认支付成功
  showAmountDetails: false, //展示费用明细
  showPayInfoError: false, // 是否展示付款信息错误提示
  timeLeft: 3,
  payUrl: "", //付款链接
  couponInfoError: false, // 优惠券信息报错
});

// 获取收银台信息
onGetCashDeskInfo();
async function onGetCashDeskInfo() {
  const res: any = await useGetCashDeskInfo({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    pageData.payMethodList.some((pay: any) => {
      if (pay.max >= pageData.payAmount?.amount) {
        pageData.payMethodId = pay.id;
        return true;
      }
      return false;
    });
    // 校验支付状态 支付成功 跳转支付结果页
    if (pageData.payResultModel.payStatus === "PAID_SUCCESS") {
      navigateTo({
        path: "/h5/order/pay-results",
        query: {
          orderNo: pageData.orderNo,
          paymentId: pageData.paymentId,
        },
      });
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/h5/user/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "payment_gopay_fail",
      `支付页去支付失败：${res?.result?.message}`
    );
    pageData.showPayInfoError = true;
    if (res?.result?.code === 70116) {
      pageData.couponInfoError = true;
    }
    pageData.timeLeft = 3;
    const countdown = setInterval(() => {
      if (pageData.timeLeft === 1) {
        navigateTo(`/h5/order/details?orderNo=${pageData.orderNo}`);
        clearInterval(countdown);
        pageData.showPayInfoError = false;
      } else {
        pageData.timeLeft--;
      }
    }, 1000);
  }
}

function onUpdatePayMethod(val: any) {
  pageData.payMethodId = val;
  const payMethod = pageData.payMethodList.find(
    (pay: any) => pay.id === pageData.payMethodId
  );
  window?.MyStat?.addPageEvent(
    "payment_choice_paytype",
    `选择支付方式：${payMethod.name}`
  );
}

// 提交支付 打开pagsmile付款页面
/**
 * 浏览器检测到非用户操作产生的新弹出窗口，则会对其进行阻止。因为浏览器认为这可能是一个广告，不是一个用户希望看到的页面，
 * 所以改用弹窗显示付款按钮，点击付款按钮后，再打开付款页面。
 */
async function onOrderPay() {
  pageData.submitLoading = true;
  const payMeth = pageData.payMethodList.find(
    (item: any) => item.id === pageData.payMethodId
  );
  const res: any = await useSubmitPayment({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
    payMethodId: pageData.payMethodId,
    payMethod: payMeth?.code,
    amount: pageData.payAmount.amount,
  });
  pageData.submitLoading = false;
  if (res?.result?.code === 200) {
    pageData.showPayButton = true;
    pageData.payUrl = res.data.payUrl;
  } else {
    window?.MyStat?.addPageEvent(
      "payment_gopay_fail",
      `支付页去支付失败：${res?.result?.message}`
    );
    showToast(res?.result?.message);
    // pageData.showPayInfoError = true;
    // if (res?.result?.code === 70116) {
    //   pageData.couponInfoError = true;
    // }
    // pageData.timeLeft = 3;
    // const countdown = setInterval(() => {
    //   if (pageData.timeLeft === 1) {
    //     navigateToPage(
    //       `/h5/order/details`,
    //       { orderNo: pageData.orderNo },
    //       false
    //     );
    //     pageData.showPayInfoError = false;
    //     clearInterval(countdown);
    //   } else {
    //     pageData.timeLeft--;
    //   }
    // }, 1000);
  }
}

// 弹框显示付款的按钮
function onOpenPaySuccess() {
  setTimeout(() => {
    pageData.showPayButton = false;
    pageData.showPaySuccess = true;
  }, 500);
  window?.MyStat?.addPageEvent("payment_goto_payurl", "跳转至支付链接");
}

// 查询支付结果
async function onGetQueryPayResult() {
  const res: any = await useQueryPayResult({
    orderNo: pageData.orderNo,
    paymentId: pageData.paymentId,
  });
  if (res?.result?.code === 200) {
    // 未支付，请继续支付
    if (res?.data.payResult.payStatus === "INIT") {
      pageData.showPaySuccess = false;
      setTimeout(() => {
        showToast(authStore.i18n("cm_order.continuePayment"));
      }, 100);
    } else {
      // 其他状态 跳转支付结果页
      window.location.replace(
        `/h5/order/pay-results?orderNo=${pageData.orderNo}&paymentId=${pageData.paymentId}`
      );
      pageData.showPaySuccess = false;
    }
  }
}

function openAmountDetails() {
  pageData.showAmountDetails = !pageData.showAmountDetails;
}

// 返回
function onBackClick() {
  router.go(-1);
}
</script>
<style scoped lang="scss">
.page-header {
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 10;
  padding-top: 0.2rem;
  padding-bottom: 0.2rem;
  border-bottom: 0.02rem solid #e5e7eb;
  background: #fff;
}

.page-footer {
  width: 100%;
  min-height: 1.28rem;
  background: #fff;
  border-radius: 0.12rem;
  border: 0.02rem solid #e2e2e2;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.32rem 0.16rem;
  z-index: 9;
}

.content-card {
  width: 100%;
  border-radius: 0.12rem;
  padding: 0.36rem 0.16rem;
  background-color: #fff;
  margin-bottom: 0.24rem;
}

.pay-item {
  width: 100%;
  min-height: 1.2rem;
  background: #fff;
  margin-bottom: 0.32rem;
  border-radius: 0.08rem;
  padding: 0.2rem 0.12rem;
  align-items: center;
  border: 0.02rem solid #d7d7d7;
}
.selected-pay-item {
  background-color: #f9e5e6;
  border: 0.02rem solid #e50113;
}
:deep(.n-radio .n-radio__label) {
  width: 100%;
}
</style>
