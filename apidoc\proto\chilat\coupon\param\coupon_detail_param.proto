syntax = "proto3";
package chilat.coupon;

import "common.proto";

option java_package = "com.chilat.rpc.coupon.param";
// 优惠券领取/明细保存字段
message SaveCouponDetailParam {
    string id = 1;
    string couponId = 2;//优惠券id
    string userId = 3; //用户id
    string userName = 4; //用户名称
    int64 getDate = 6; //卡券领取日期
    int64 coupon_start_expiration_date = 7; //卡券生效日期
    int64 coupon_end_expiration_date = 8; //卡券失效日期
    int64 last_use_date = 9; //最后使用时间
}

message CouponDetailPageQueryParam {
    common.PageParam page = 1;
}



