syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.param";

import "common.proto";
import "common/business.proto";

//分页查询订单列表
message GetOrderListRequestParam {

  common.PageParam page = 10;

  repeated common.MallOrderStatus mallOrderStatusList = 20; //指定订单状态进行过滤
}

//取消订单
message CancelOrderParam {
  string orderNo = 10; //订单号
  int32 cancelReasonId = 20; //取消理由id
  string cancelReason = 30; //取消理由内容
  string cancelRemark = 40; //取消订单备注
}

//查询订单详情
message GetOrderDetailParam {
  string orderNo = 10; //订单号
  string userId = 20; // userId admin请求时必填
  bool isAdminPreview = 30;// 是否来自admin的预览请求
}

//查询收银台信息，包括待支付金额、支付方式列表、是否已支付
message GetCashDeskInfoParam {
  string orderNo = 10; //订单号
  string paymentId = 20; //支付单id
}

//提交支付
message SubmitPaymentParam {
  string orderNo = 10; //订单号
  string paymentId = 20; //跳转收银台时后端返回的支付单id
  double amount = 30; //支付金额
  string payMethod = 40; //支付方式code
  string payMethodId = 50; //支付方式唯一id
}

//查询支付结果
message QueryPayResultParam {
  string orderNo = 10; //订单号
  string paymentId = 20; //支付单id
}

//确认收货
message ConfirmReceiptParam {
  string orderNo = 10; //订单号
}


//未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
message OpenCashDeskParam {
  string orderNo = 10; //订单号
  double amount = 20; //待支付金额
  string orderRemark = 30; //订单备注
  string  transportId = 40; //线路id
  repeated string productIdsList = 41;//产品集合
  repeated string commissionIdsList = 42;//佣金集合
}

