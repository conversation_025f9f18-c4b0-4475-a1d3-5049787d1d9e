<template>
  <div class="validation-coupon pt-[0.36rem]">
    <n-grid
      x-gap="8"
      :cols="2"
      :class="{
        '!flex !justify-center': props.couponList.length < 2,
      }"
    >
      <n-gi v-for="coupon in props.couponList" :key="coupon?.id">
        <div
          class="flex items-center text-[#e50113] mb-[0.2rem]"
          :class="{
            'comm-coupon': coupon?.couponType === 'COUPON_TYPE_COMMISSION',
          }"
        >
          <div class="coupon-card mr-[0.04rem] text-[#fff]">
            <div
              class="w-full h-[0.4rem] mb-[0.12rem] flex items-center justify-center"
            >
              <template v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
                <div>
                  <span class="text-[0.4rem] leading-[0.4rem] font-medium">
                    {{ discountToPercentage(coupon?.discount) }}
                  </span>
                  <span class="coupon-discount">
                    {{ authStore.i18n("cm_coupon.discount") }}
                  </span>
                </div>
              </template>
              <template v-else>
                <span class="coupon-unit">
                  <span>{{ currencyUnit }}</span>
                </span>
                <span class="text-[0.4rem] leading-[0.4rem] font-medium"
                  >$
                  {{ setNewUnit(coupon?.preferentialAmount, "noUnit") }}</span
                >
              </template>
            </div>
            <div class="coupon-type">
              <span v-if="coupon?.couponType === 'COUPON_TYPE_PRODUCT'">
                {{ authStore.i18n("cm_coupon.productCoupon") }}
              </span>
              <span v-if="coupon?.couponType === 'COUPON_TYPE_COMMISSION'">
                {{ authStore.i18n("cm_coupon.commissionCoupon") }}
              </span>
            </div>
          </div>
          <div class="flex w-[0.6rem] text-[0.32rem] tracking-[-0.02rem]">
            <span>x</span>
            {{ coupon?.count }}
          </div>
        </div></n-gi
      >
    </n-grid>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const props = defineProps({
  couponList: {
    type: Array,
    default: () => [],
  },
});
</script>
<style scoped lang="scss">
.coupon-card {
  width: 2.4rem;
  height: 1.16rem;
  background: url("@/assets/icons/marketing/productCoupon.png");
  background-size: 100%100%;
  padding: 0.28rem 0.08rem 0.08rem 0.72rem;
}
.comm-coupon {
  color: #dd4f12;
  .coupon-card {
    background: url("@/assets/icons/marketing/commissionCoupon.png");
    background-size: 100%100%;
  }
}
.coupon-type {
  width: 100%;
  text-transform: uppercase;
  letter-spacing: -0.02rem;
  text-align: center;
  text-wrap: nowrap;

  span {
    width: 100%;
    display: inline-block;
    font-size: 0.24rem;
    line-height: 0.2rem;
    transform: scale(0.74);
    transform-origin: left top;
  }
}
.coupon-unit {
  display: inline-block;
  transform: rotate(-90deg);
  font-weight: 500;
  span {
    display: inline-block;
    font-size: 0.24rem;
  }
}
.coupon-discount {
  display: inline-block;
  font-size: 0.24rem;
  margin-left: 0.04rem;
  font-weight: 500;
}
</style>
