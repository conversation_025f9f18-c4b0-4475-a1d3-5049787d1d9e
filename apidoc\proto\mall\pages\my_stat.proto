syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "common.proto";
import "mall/pages/model/my_stat_model.proto";
import "mall/pages/param/my_stat_param.proto";


// 统计数据
service MyStat {
  // Nuxt接收到页面请求后，共通调用
  rpc incoming (PageClickIncomingParam) returns (PageClickIncomingResp);
  // 获取页面数据
  rpc visitLog (PageClickDataParam) returns (common.StringResult);
}
