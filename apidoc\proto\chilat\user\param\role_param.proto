syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user.param";

import "common.proto";

message RolePageQueryParam {
  common.PageParam page = 1;
  RoleQueryParam query = 2;
}

message RoleQueryParam {
  string name = 1; // 角色名称
  int32 systemId = 2;
}

message RoleSaveParam {
  string id = 1; // 角色ID
  string name = 2; // 角色名称
  string remark = 3; // 备注
  int32 systemId = 4; //系统id
}