<template>
  <div class="px-5 flex justify-between">
    <div class="flex">
      <div class="w-[148px]">{{ title }}:</div>
      <n-popover
        trigger="hover"
        placement="top-end"
        :on-update:show="onUpdateShow"
      >
        <template #trigger>
          <div
            class="border-1 border-[#FF94A8] bg-[#FEF7F8] text-[#FF4056] rounded-[3px] px-[6px] py-[2px] text-[12px] leading-[14px] ml-[8px] cursor-pointer flex items-center"
          >
            <span>{{ authStore.i18n("cm_coupon.viewCoupons") }}</span>
            <img
              loading="lazy"
              :src="pageData.showDetail ? top : down"
              alt="view"
              class="w-[10px] ml-1"
              referrerpolicy="no-referrer"
            />
          </div>
        </template>
        <div>
          <n-scrollbar class="mt-[20px] h-[300px] pr-[16px] w-[380px]">
            <div
              v-for="(coupon, index) in couponList"
              :key="coupon.id"
              class="w-full coupon-card flex items-center pb-[8px]"
              :class="{
                'mb-[8px] border-b border-[#F2F2F2]':
                  index !== couponList.length - 1,
              }"
            >
              <div
                class="coupon-border py-[8px] border-[2px] border-dotted w-[80px] text-center text-[14px] leading-[14px] font-medium"
              >
                <span v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
                  {{ discountToPercentage(coupon.discount) }}
                  {{ authStore.i18n("cm_coupon.discount").toLowerCase() }}
                </span>
                <span v-else>
                  {{ setNewUnit(coupon?.preferentialAmount) }}
                </span>
              </div>
              <div class="flex-1 ml-[13px] mr-[10px]">
                <div class="text-[14px] leading-[14px] font-medium">
                  <!-- 满多少金额使用 -->
                  <template v-if="coupon?.couponUseConditionsType === 'FULL'">
                    <span>
                      {{ authStore.i18n("cm_coupon.minimumRequired") }}
                    </span>
                    {{ setNewUnit(coupon?.useConditionsAmount) }}
                  </template>
                  <!-- 每满多少金额使用 -->
                  <template
                    v-if="coupon?.couponUseConditionsType === 'EVERY_FULL'"
                  >
                    <span>
                      {{ authStore.i18n("cm_coupon.minimumUnmet") }}
                    </span>
                    {{ setNewUnit(coupon?.useConditionsAmount) }}
                    <span>
                      {{ authStore.i18n("cm_coupon.minimumUnmetCost") }}
                    </span>
                  </template>
                  <!-- 不限制金额 -->
                  <div v-if="coupon?.couponUseConditionsType === 'UNLIMITED'">
                    {{ authStore.i18n("cm_coupon.noLimit") }}
                  </div>
                </div>
                <div
                  v-if="
                    coupon?.couponWay === 'COUPON_WAY_DISCOUNT' &&
                    coupon?.preferentialAmount
                  "
                  class="text-[12px] leading-[12px] flex items-center mt-[4px]"
                >
                  {{ authStore.i18n("cm_coupon.upToMoney") }}
                  {{ setNewUnit(coupon?.preferentialAmount) }}
                </div>
              </div>
            </div>
          </n-scrollbar>
        </div>
      </n-popover>
    </div>
    <span>{{ setUnit(amount) }} </span>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

import top from "@/assets/icons/marketing/top.svg";
import down from "@/assets/icons/marketing/down.svg";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  amount: {
    type: Number,
    default: "",
  },
  couponList: {
    type: Array,
    default: () => [],
  },
});

const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive(<any>{ showDetail: false });

function onUpdateShow(val: any) {
  pageData.showDetail = val;
}
</script>
<style scoped lang="scss">
.coupon-card {
  color: #333;
  .coupon-border {
    border-color: #4d4d4d;
  }
}
</style>
