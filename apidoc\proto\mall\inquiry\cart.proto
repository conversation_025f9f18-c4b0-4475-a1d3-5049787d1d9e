syntax = "proto3";
package mall.inquiry;

option java_package = "com.chilat.rpc.inquiry";

import "common.proto";
import "chilat/basis/param/cart_param.proto";
import "chilat/basis/model/cart_model.proto";

// 购物车
service Cart {
    // 获取购物车信息
    rpc getCart(chilat.basis.MidCartQueryParam) returns (chilat.basis.MidCartResp);
    // 获取购物车信息（按Tab分割）
    rpc getCartByTab(chilat.basis.MidCartQueryParam) returns (chilat.basis.MidCartByTabResp);
    // 获取购物车统计信息
    rpc getCartStat (chilat.basis.MidCartQueryParam) returns (chilat.basis.MidCartStatResp);
    // 添加到购物车
    rpc addCart(chilat.basis.MidCartAddParam) returns (chilat.basis.MidCartResp);
    // 修改购物车商品
    rpc updateCart(chilat.basis.MidCartUpdateParam) returns (chilat.basis.MidCartResp);
    // 删除购物车商品
    rpc removeCart(chilat.basis.MidCartRemoveParam) returns (chilat.basis.MidCartResp);
}