/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Result } from "../../../common";
import {
  CouponDistributionStatus,
  couponDistributionStatusFromJSON,
  couponDistributionStatusToJSON,
  CouponEffectiveTypeStatus,
  couponEffectiveTypeStatusFromJSON,
  couponEffectiveTypeStatusToJSON,
  CouponStatus,
  couponStatusFromJSON,
  couponStatusToJSON,
  CouponTypeStatus,
  couponTypeStatusFromJSON,
  couponTypeStatusToJSON,
  CouponUseConditionsTypestatus,
  couponUseConditionsTypestatusFromJSON,
  couponUseConditionsTypestatusToJSON,
  CouponUseRuleStatus,
  couponUseRuleStatusFromJSON,
  couponUseRuleStatusToJSON,
  CouponWayStatus,
  couponWayStatusFromJSON,
  couponWayStatusToJSON,
} from "../coupon_common";
import Long from "long";

export const protobufPackage = "chilat.coupon";

/** 管理系统-优惠券保存/修改返回 */
export interface CouponSaveUpdateResp {
  result: Result | undefined;
}

/** 管理后台-优惠券详请 */
export interface CouponDetailModelResp {
  result:
    | Result
    | undefined;
  /** 优惠券基本规则参数 */
  data: CouponInfoDetailModel | undefined;
}

/** 详情组装Model */
export interface CouponInfoDetailModel {
  id: string;
  /** 优惠券名称 */
  couponName: string;
  /** 优惠券别名 */
  couponAlias: string;
  /** 用户使用说明 */
  userInstructions: string;
  /** 后台备注 */
  backendRemark: string;
  /** 优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券 */
  couponType: CouponTypeStatus;
  /** 优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券 */
  couponWay: CouponWayStatus;
  /** 使用条件(0不限制，达到多少金额可以使用，每满xxx可用) */
  useConditionsAmount: number;
  /** 优惠额度 */
  preferentialAmount: number;
  /** 折扣（券类型为折扣券使用） */
  discount: number;
  /** 最大发放数量 */
  maxNumQuantity: number;
  /** 已发放数量 */
  issuedQuantity: number;
  /** 优惠券发放方式: couponDistributionActive、主动领取 couponDistributionAutomatic、自动发放 couponDistributionExchange、优惠码兑换 */
  couponDistributionMethod: CouponDistributionStatus;
  /** 主体卡券的状态: couponDraft 草稿 couponNotActive 失效 couponActive 生效 couponLock 锁定 couponUnLock 解锁 couponExpire 过期 couponCancel 删除 */
  couponStatus: CouponStatus;
  /** 优惠券发放开始日期 */
  couponIssueStartDate: number;
  /** 优惠券发放结束日期 */
  couponIssueEndDate: number;
  /** 活动编码 */
  activeId: string;
  /** 已领取数量 */
  receivedQuantity: number;
  /** 已使用数量 */
  userQuantity: number;
  /** 基本规则 */
  couponEffectiveType: CouponEffectiveTypeStatus;
  /** 固定开始时间 */
  couponStartExpirationDate: number;
  /** 固定结束时间 */
  couponEndExpirationDate: number;
  /** 有效类型为distributionDateEffectiveDays 时取有效天数,有效类型为distributionDateEffectiveHours 时取、有效小时,fixedTime 固定时间 */
  effectiveNum: number;
  /** 卡券后台操作日志集合 */
  systemCouponModelsList: SystemCouponModel[];
  /** 使用规则 */
  couponInfoUseRuleModelList: CouponInfoUseRuleModel[];
  /** 使用条件类型 */
  couponUseConditionsType: CouponUseConditionsTypestatus;
}

/** 卡券后台操作日志 */
export interface SystemCouponModel {
  /** 操作时间 */
  cdate: number;
  /** 操作人 */
  coperator: string;
  couponStatus: CouponStatus;
  /** 操作内容 */
  remark: string;
}

/** 使用规则 */
export interface CouponInfoUseRuleModel {
  /** 叠加张数,0代表无限 */
  useRuleCount: number;
  /** 使用规则，（COUPON_MAXIMUM_AVAILABLE 本券最多可使用  COUPON_SAME_TYPE_OVERLAY 可与同类型叠加 COUPON_FIRST_USE 仅可首单使用） */
  useRuleType: CouponUseRuleStatus;
  /** 优惠券id */
  couponId: string;
}

function createBaseCouponSaveUpdateResp(): CouponSaveUpdateResp {
  return { result: undefined };
}

export const CouponSaveUpdateResp = {
  encode(message: CouponSaveUpdateResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponSaveUpdateResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponSaveUpdateResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponSaveUpdateResp {
    return { result: isSet(object.result) ? Result.fromJSON(object.result) : undefined };
  },

  toJSON(message: CouponSaveUpdateResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponSaveUpdateResp>, I>>(base?: I): CouponSaveUpdateResp {
    return CouponSaveUpdateResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponSaveUpdateResp>, I>>(object: I): CouponSaveUpdateResp {
    const message = createBaseCouponSaveUpdateResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    return message;
  },
};

function createBaseCouponDetailModelResp(): CouponDetailModelResp {
  return { result: undefined, data: undefined };
}

export const CouponDetailModelResp = {
  encode(message: CouponDetailModelResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      CouponInfoDetailModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponDetailModelResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponDetailModelResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = CouponInfoDetailModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponDetailModelResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? CouponInfoDetailModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: CouponDetailModelResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = CouponInfoDetailModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponDetailModelResp>, I>>(base?: I): CouponDetailModelResp {
    return CouponDetailModelResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponDetailModelResp>, I>>(object: I): CouponDetailModelResp {
    const message = createBaseCouponDetailModelResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? CouponInfoDetailModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseCouponInfoDetailModel(): CouponInfoDetailModel {
  return {
    id: "",
    couponName: "",
    couponAlias: "",
    userInstructions: "",
    backendRemark: "",
    couponType: 0,
    couponWay: 0,
    useConditionsAmount: 0,
    preferentialAmount: 0,
    discount: 0,
    maxNumQuantity: 0,
    issuedQuantity: 0,
    couponDistributionMethod: 0,
    couponStatus: 0,
    couponIssueStartDate: 0,
    couponIssueEndDate: 0,
    activeId: "",
    receivedQuantity: 0,
    userQuantity: 0,
    couponEffectiveType: 0,
    couponStartExpirationDate: 0,
    couponEndExpirationDate: 0,
    effectiveNum: 0,
    systemCouponModelsList: [],
    couponInfoUseRuleModelList: [],
    couponUseConditionsType: 0,
  };
}

export const CouponInfoDetailModel = {
  encode(message: CouponInfoDetailModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.couponName !== "") {
      writer.uint32(18).string(message.couponName);
    }
    if (message.couponAlias !== "") {
      writer.uint32(26).string(message.couponAlias);
    }
    if (message.userInstructions !== "") {
      writer.uint32(34).string(message.userInstructions);
    }
    if (message.backendRemark !== "") {
      writer.uint32(42).string(message.backendRemark);
    }
    if (message.couponType !== 0) {
      writer.uint32(48).int32(message.couponType);
    }
    if (message.couponWay !== 0) {
      writer.uint32(56).int32(message.couponWay);
    }
    if (message.useConditionsAmount !== 0) {
      writer.uint32(65).double(message.useConditionsAmount);
    }
    if (message.preferentialAmount !== 0) {
      writer.uint32(73).double(message.preferentialAmount);
    }
    if (message.discount !== 0) {
      writer.uint32(81).double(message.discount);
    }
    if (message.maxNumQuantity !== 0) {
      writer.uint32(88).int32(message.maxNumQuantity);
    }
    if (message.issuedQuantity !== 0) {
      writer.uint32(96).int32(message.issuedQuantity);
    }
    if (message.couponDistributionMethod !== 0) {
      writer.uint32(104).int32(message.couponDistributionMethod);
    }
    if (message.couponStatus !== 0) {
      writer.uint32(112).int32(message.couponStatus);
    }
    if (message.couponIssueStartDate !== 0) {
      writer.uint32(120).int64(message.couponIssueStartDate);
    }
    if (message.couponIssueEndDate !== 0) {
      writer.uint32(128).int64(message.couponIssueEndDate);
    }
    if (message.activeId !== "") {
      writer.uint32(138).string(message.activeId);
    }
    if (message.receivedQuantity !== 0) {
      writer.uint32(160).int32(message.receivedQuantity);
    }
    if (message.userQuantity !== 0) {
      writer.uint32(168).int32(message.userQuantity);
    }
    if (message.couponEffectiveType !== 0) {
      writer.uint32(176).int32(message.couponEffectiveType);
    }
    if (message.couponStartExpirationDate !== 0) {
      writer.uint32(184).int64(message.couponStartExpirationDate);
    }
    if (message.couponEndExpirationDate !== 0) {
      writer.uint32(192).int64(message.couponEndExpirationDate);
    }
    if (message.effectiveNum !== 0) {
      writer.uint32(216).int32(message.effectiveNum);
    }
    for (const v of message.systemCouponModelsList) {
      SystemCouponModel.encode(v!, writer.uint32(226).fork()).ldelim();
    }
    for (const v of message.couponInfoUseRuleModelList) {
      CouponInfoUseRuleModel.encode(v!, writer.uint32(234).fork()).ldelim();
    }
    if (message.couponUseConditionsType !== 0) {
      writer.uint32(240).int32(message.couponUseConditionsType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponInfoDetailModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponInfoDetailModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.couponName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.couponAlias = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.userInstructions = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.backendRemark = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.couponType = reader.int32() as any;
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.couponWay = reader.int32() as any;
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }

          message.useConditionsAmount = reader.double();
          continue;
        case 9:
          if (tag !== 73) {
            break;
          }

          message.preferentialAmount = reader.double();
          continue;
        case 10:
          if (tag !== 81) {
            break;
          }

          message.discount = reader.double();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.maxNumQuantity = reader.int32();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.issuedQuantity = reader.int32();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.couponDistributionMethod = reader.int32() as any;
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }

          message.couponStatus = reader.int32() as any;
          continue;
        case 15:
          if (tag !== 120) {
            break;
          }

          message.couponIssueStartDate = longToNumber(reader.int64() as Long);
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.couponIssueEndDate = longToNumber(reader.int64() as Long);
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.activeId = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.receivedQuantity = reader.int32();
          continue;
        case 21:
          if (tag !== 168) {
            break;
          }

          message.userQuantity = reader.int32();
          continue;
        case 22:
          if (tag !== 176) {
            break;
          }

          message.couponEffectiveType = reader.int32() as any;
          continue;
        case 23:
          if (tag !== 184) {
            break;
          }

          message.couponStartExpirationDate = longToNumber(reader.int64() as Long);
          continue;
        case 24:
          if (tag !== 192) {
            break;
          }

          message.couponEndExpirationDate = longToNumber(reader.int64() as Long);
          continue;
        case 27:
          if (tag !== 216) {
            break;
          }

          message.effectiveNum = reader.int32();
          continue;
        case 28:
          if (tag !== 226) {
            break;
          }

          message.systemCouponModelsList.push(SystemCouponModel.decode(reader, reader.uint32()));
          continue;
        case 29:
          if (tag !== 234) {
            break;
          }

          message.couponInfoUseRuleModelList.push(CouponInfoUseRuleModel.decode(reader, reader.uint32()));
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.couponUseConditionsType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponInfoDetailModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      couponName: isSet(object.couponName) ? globalThis.String(object.couponName) : "",
      couponAlias: isSet(object.couponAlias) ? globalThis.String(object.couponAlias) : "",
      userInstructions: isSet(object.userInstructions) ? globalThis.String(object.userInstructions) : "",
      backendRemark: isSet(object.backendRemark) ? globalThis.String(object.backendRemark) : "",
      couponType: isSet(object.couponType) ? couponTypeStatusFromJSON(object.couponType) : 0,
      couponWay: isSet(object.couponWay) ? couponWayStatusFromJSON(object.couponWay) : 0,
      useConditionsAmount: isSet(object.useConditionsAmount) ? globalThis.Number(object.useConditionsAmount) : 0,
      preferentialAmount: isSet(object.preferentialAmount) ? globalThis.Number(object.preferentialAmount) : 0,
      discount: isSet(object.discount) ? globalThis.Number(object.discount) : 0,
      maxNumQuantity: isSet(object.maxNumQuantity) ? globalThis.Number(object.maxNumQuantity) : 0,
      issuedQuantity: isSet(object.issuedQuantity) ? globalThis.Number(object.issuedQuantity) : 0,
      couponDistributionMethod: isSet(object.couponDistributionMethod)
        ? couponDistributionStatusFromJSON(object.couponDistributionMethod)
        : 0,
      couponStatus: isSet(object.couponStatus) ? couponStatusFromJSON(object.couponStatus) : 0,
      couponIssueStartDate: isSet(object.couponIssueStartDate) ? globalThis.Number(object.couponIssueStartDate) : 0,
      couponIssueEndDate: isSet(object.couponIssueEndDate) ? globalThis.Number(object.couponIssueEndDate) : 0,
      activeId: isSet(object.activeId) ? globalThis.String(object.activeId) : "",
      receivedQuantity: isSet(object.receivedQuantity) ? globalThis.Number(object.receivedQuantity) : 0,
      userQuantity: isSet(object.userQuantity) ? globalThis.Number(object.userQuantity) : 0,
      couponEffectiveType: isSet(object.couponEffectiveType)
        ? couponEffectiveTypeStatusFromJSON(object.couponEffectiveType)
        : 0,
      couponStartExpirationDate: isSet(object.couponStartExpirationDate)
        ? globalThis.Number(object.couponStartExpirationDate)
        : 0,
      couponEndExpirationDate: isSet(object.couponEndExpirationDate)
        ? globalThis.Number(object.couponEndExpirationDate)
        : 0,
      effectiveNum: isSet(object.effectiveNum) ? globalThis.Number(object.effectiveNum) : 0,
      systemCouponModelsList: globalThis.Array.isArray(object?.systemCouponModelsList)
        ? object.systemCouponModelsList.map((e: any) => SystemCouponModel.fromJSON(e))
        : [],
      couponInfoUseRuleModelList: globalThis.Array.isArray(object?.couponInfoUseRuleModelList)
        ? object.couponInfoUseRuleModelList.map((e: any) => CouponInfoUseRuleModel.fromJSON(e))
        : [],
      couponUseConditionsType: isSet(object.couponUseConditionsType)
        ? couponUseConditionsTypestatusFromJSON(object.couponUseConditionsType)
        : 0,
    };
  },

  toJSON(message: CouponInfoDetailModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.couponName !== "") {
      obj.couponName = message.couponName;
    }
    if (message.couponAlias !== "") {
      obj.couponAlias = message.couponAlias;
    }
    if (message.userInstructions !== "") {
      obj.userInstructions = message.userInstructions;
    }
    if (message.backendRemark !== "") {
      obj.backendRemark = message.backendRemark;
    }
    if (message.couponType !== 0) {
      obj.couponType = couponTypeStatusToJSON(message.couponType);
    }
    if (message.couponWay !== 0) {
      obj.couponWay = couponWayStatusToJSON(message.couponWay);
    }
    if (message.useConditionsAmount !== 0) {
      obj.useConditionsAmount = message.useConditionsAmount;
    }
    if (message.preferentialAmount !== 0) {
      obj.preferentialAmount = message.preferentialAmount;
    }
    if (message.discount !== 0) {
      obj.discount = message.discount;
    }
    if (message.maxNumQuantity !== 0) {
      obj.maxNumQuantity = Math.round(message.maxNumQuantity);
    }
    if (message.issuedQuantity !== 0) {
      obj.issuedQuantity = Math.round(message.issuedQuantity);
    }
    if (message.couponDistributionMethod !== 0) {
      obj.couponDistributionMethod = couponDistributionStatusToJSON(message.couponDistributionMethod);
    }
    if (message.couponStatus !== 0) {
      obj.couponStatus = couponStatusToJSON(message.couponStatus);
    }
    if (message.couponIssueStartDate !== 0) {
      obj.couponIssueStartDate = Math.round(message.couponIssueStartDate);
    }
    if (message.couponIssueEndDate !== 0) {
      obj.couponIssueEndDate = Math.round(message.couponIssueEndDate);
    }
    if (message.activeId !== "") {
      obj.activeId = message.activeId;
    }
    if (message.receivedQuantity !== 0) {
      obj.receivedQuantity = Math.round(message.receivedQuantity);
    }
    if (message.userQuantity !== 0) {
      obj.userQuantity = Math.round(message.userQuantity);
    }
    if (message.couponEffectiveType !== 0) {
      obj.couponEffectiveType = couponEffectiveTypeStatusToJSON(message.couponEffectiveType);
    }
    if (message.couponStartExpirationDate !== 0) {
      obj.couponStartExpirationDate = Math.round(message.couponStartExpirationDate);
    }
    if (message.couponEndExpirationDate !== 0) {
      obj.couponEndExpirationDate = Math.round(message.couponEndExpirationDate);
    }
    if (message.effectiveNum !== 0) {
      obj.effectiveNum = Math.round(message.effectiveNum);
    }
    if (message.systemCouponModelsList?.length) {
      obj.systemCouponModelsList = message.systemCouponModelsList.map((e) => SystemCouponModel.toJSON(e));
    }
    if (message.couponInfoUseRuleModelList?.length) {
      obj.couponInfoUseRuleModelList = message.couponInfoUseRuleModelList.map((e) => CouponInfoUseRuleModel.toJSON(e));
    }
    if (message.couponUseConditionsType !== 0) {
      obj.couponUseConditionsType = couponUseConditionsTypestatusToJSON(message.couponUseConditionsType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponInfoDetailModel>, I>>(base?: I): CouponInfoDetailModel {
    return CouponInfoDetailModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponInfoDetailModel>, I>>(object: I): CouponInfoDetailModel {
    const message = createBaseCouponInfoDetailModel();
    message.id = object.id ?? "";
    message.couponName = object.couponName ?? "";
    message.couponAlias = object.couponAlias ?? "";
    message.userInstructions = object.userInstructions ?? "";
    message.backendRemark = object.backendRemark ?? "";
    message.couponType = object.couponType ?? 0;
    message.couponWay = object.couponWay ?? 0;
    message.useConditionsAmount = object.useConditionsAmount ?? 0;
    message.preferentialAmount = object.preferentialAmount ?? 0;
    message.discount = object.discount ?? 0;
    message.maxNumQuantity = object.maxNumQuantity ?? 0;
    message.issuedQuantity = object.issuedQuantity ?? 0;
    message.couponDistributionMethod = object.couponDistributionMethod ?? 0;
    message.couponStatus = object.couponStatus ?? 0;
    message.couponIssueStartDate = object.couponIssueStartDate ?? 0;
    message.couponIssueEndDate = object.couponIssueEndDate ?? 0;
    message.activeId = object.activeId ?? "";
    message.receivedQuantity = object.receivedQuantity ?? 0;
    message.userQuantity = object.userQuantity ?? 0;
    message.couponEffectiveType = object.couponEffectiveType ?? 0;
    message.couponStartExpirationDate = object.couponStartExpirationDate ?? 0;
    message.couponEndExpirationDate = object.couponEndExpirationDate ?? 0;
    message.effectiveNum = object.effectiveNum ?? 0;
    message.systemCouponModelsList = object.systemCouponModelsList?.map((e) => SystemCouponModel.fromPartial(e)) || [];
    message.couponInfoUseRuleModelList =
      object.couponInfoUseRuleModelList?.map((e) => CouponInfoUseRuleModel.fromPartial(e)) || [];
    message.couponUseConditionsType = object.couponUseConditionsType ?? 0;
    return message;
  },
};

function createBaseSystemCouponModel(): SystemCouponModel {
  return { cdate: 0, coperator: "", couponStatus: 0, remark: "" };
}

export const SystemCouponModel = {
  encode(message: SystemCouponModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.cdate !== 0) {
      writer.uint32(56).int64(message.cdate);
    }
    if (message.coperator !== "") {
      writer.uint32(66).string(message.coperator);
    }
    if (message.couponStatus !== 0) {
      writer.uint32(72).int32(message.couponStatus);
    }
    if (message.remark !== "") {
      writer.uint32(82).string(message.remark);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SystemCouponModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSystemCouponModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 7:
          if (tag !== 56) {
            break;
          }

          message.cdate = longToNumber(reader.int64() as Long);
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.coperator = reader.string();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.couponStatus = reader.int32() as any;
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.remark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SystemCouponModel {
    return {
      cdate: isSet(object.cdate) ? globalThis.Number(object.cdate) : 0,
      coperator: isSet(object.coperator) ? globalThis.String(object.coperator) : "",
      couponStatus: isSet(object.couponStatus) ? couponStatusFromJSON(object.couponStatus) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : "",
    };
  },

  toJSON(message: SystemCouponModel): unknown {
    const obj: any = {};
    if (message.cdate !== 0) {
      obj.cdate = Math.round(message.cdate);
    }
    if (message.coperator !== "") {
      obj.coperator = message.coperator;
    }
    if (message.couponStatus !== 0) {
      obj.couponStatus = couponStatusToJSON(message.couponStatus);
    }
    if (message.remark !== "") {
      obj.remark = message.remark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SystemCouponModel>, I>>(base?: I): SystemCouponModel {
    return SystemCouponModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SystemCouponModel>, I>>(object: I): SystemCouponModel {
    const message = createBaseSystemCouponModel();
    message.cdate = object.cdate ?? 0;
    message.coperator = object.coperator ?? "";
    message.couponStatus = object.couponStatus ?? 0;
    message.remark = object.remark ?? "";
    return message;
  },
};

function createBaseCouponInfoUseRuleModel(): CouponInfoUseRuleModel {
  return { useRuleCount: 0, useRuleType: 0, couponId: "" };
}

export const CouponInfoUseRuleModel = {
  encode(message: CouponInfoUseRuleModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.useRuleCount !== 0) {
      writer.uint32(8).int32(message.useRuleCount);
    }
    if (message.useRuleType !== 0) {
      writer.uint32(16).int32(message.useRuleType);
    }
    if (message.couponId !== "") {
      writer.uint32(26).string(message.couponId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponInfoUseRuleModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponInfoUseRuleModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.useRuleCount = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.useRuleType = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.couponId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponInfoUseRuleModel {
    return {
      useRuleCount: isSet(object.useRuleCount) ? globalThis.Number(object.useRuleCount) : 0,
      useRuleType: isSet(object.useRuleType) ? couponUseRuleStatusFromJSON(object.useRuleType) : 0,
      couponId: isSet(object.couponId) ? globalThis.String(object.couponId) : "",
    };
  },

  toJSON(message: CouponInfoUseRuleModel): unknown {
    const obj: any = {};
    if (message.useRuleCount !== 0) {
      obj.useRuleCount = Math.round(message.useRuleCount);
    }
    if (message.useRuleType !== 0) {
      obj.useRuleType = couponUseRuleStatusToJSON(message.useRuleType);
    }
    if (message.couponId !== "") {
      obj.couponId = message.couponId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponInfoUseRuleModel>, I>>(base?: I): CouponInfoUseRuleModel {
    return CouponInfoUseRuleModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponInfoUseRuleModel>, I>>(object: I): CouponInfoUseRuleModel {
    const message = createBaseCouponInfoUseRuleModel();
    message.useRuleCount = object.useRuleCount ?? 0;
    message.useRuleType = object.useRuleType ?? 0;
    message.couponId = object.couponId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
