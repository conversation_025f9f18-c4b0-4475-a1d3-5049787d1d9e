syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";

import "chilat/foundation/foundation_common.proto";
import "common.proto";

message WarehouseListResp {
  common.Result result = 1;
  repeated WarehouseModel data = 3;
}

message WarehouseResp {
  common.Result result = 1;
  WarehouseModel data = 2;
}

message WarehouseModel {
  string id = 1;
  string code = 2; // 仓库代码
  string name = 3; // 仓库名称
  string contact = 4; // 联系人
  string phone = 5; // 联系电话
  string postcode = 6; // 邮编
  string province = 7; // 省
  string city = 8; // 市
  string district = 9; // 区
  string town = 10; // 镇
  string streetAddress = 11; // 街道地址
  string address = 12; // 完整地址
  bool enabled = 13; // 启用状态
  string coperator = 14; // 创建人
  int64 cdate = 15; // 创建时间
}