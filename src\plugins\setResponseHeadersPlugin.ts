/**
 * 向浏览器发送自定义的 header
 * 参数名：req.responseHeaders
 * 参数类型：[{name: header名称, value: header值}]
 */

export default defineNuxtPlugin((nuxtApp) => {
  const globalData = {};
  return {
    provide: {
      setResponseHeaders: (headers:any) => {
        if (!headers) {
          return;
        }
        if (!Array.isArray(headers)) {
          console.warn("responseHeaders is not array - " + headers)
          return;
        }
        for (let i = 0; i < headers.length; i++) {          
          nuxtApp.ssrContext?.event.node.res.appendHeader(headers[i].name, headers[i].value)
        }
      },
      getGlobalData: () => {
        return globalData;
      }
    }
  }
})
  
