/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Result } from "../../../common";
import {
  CouponTypeStatus,
  couponTypeStatusFromJSON,
  couponTypeStatusToJSON,
  CouponUseConditionsTypestatus,
  couponUseConditionsTypestatusFromJSON,
  couponUseConditionsTypestatusToJSON,
  CouponWayStatus,
  couponWayStatusFromJSON,
  couponWayStatusToJSON,
} from "../../coupon/coupon_common";
import { TicketStatus, ticketStatusFromJSON, ticketStatusToJSON } from "../../coupon/coupon_detail_common";
import { CouponInfoUseRuleModel } from "../../coupon/model/coupon_info_model";
import Long from "long";

export const protobufPackage = "chilat.basis";

/** 我的优惠券列表 */
export interface CouponUsableModelResp {
  result:
    | Result
    | undefined;
  /** 优惠券基本规则参数 */
  data: CouponUsableModel | undefined;
}

export interface CouponUsableModel {
  /** 佣金券集合 */
  commissionCouponList: CouponUsableDetailModel[];
  /** 产品券集合 */
  productCouponlList: CouponUsableDetailModel[];
}

/** 可用优惠券组装Model */
export interface CouponUsableDetailModel {
  /** 主键id */
  id: string;
  /** 优惠券id */
  couponId: string;
  /** 优惠券名称 */
  couponName: string;
  /** 用户使用说明 */
  userInstructions: string;
  /** 优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券 */
  couponType: CouponTypeStatus;
  /** 优惠券状态: ticketNotUse 未使用 ticketUse 已使用 ticketLoseEfficacy 已失效 */
  ticketStatus: TicketStatus;
  /** 优惠方式: couponWayDiscount、满减券 couponWayDirectReduction、直减券 couponWayFullReduction、折扣券 */
  couponWay: CouponWayStatus;
  /** 使用条件(0不限制，达到多少金额可以使用，每满xxx可用) */
  useConditionsAmount: number;
  /** 优惠额度 */
  preferentialAmount: number;
  /** 折扣（券类型为折扣券使用） */
  discount: number;
  /** 活动编码 */
  activeId: string;
  /** 优惠券生效日期 */
  ticketStartExpirationDate: number;
  /** 优惠券结束日期 */
  ticketEndExpirationDate: number;
  /** 使用规则 */
  couponInfoUseRuleModelList: CouponInfoUseRuleModel[];
  /** 兑换码 */
  exchangeCode: string;
  /** 使用条件类型 */
  couponUseConditionsType: CouponUseConditionsTypestatus;
  /** 单张券实际金额（根据订单金额 以及优惠方式，使用条件计算） */
  ticketActualPrice: number;
  /** 使用张数 */
  ruleCount: number;
  /** 是否同类型 */
  sameType: boolean;
  /** 是否可用 */
  availableFlag: boolean;
  /** 是否选中 */
  check: boolean;
  /** 不可用原因 */
  notAvailableReason: string;
}

export interface CouponAvailableModelResp {
  result:
    | Result
    | undefined;
  /** 优惠券基本规则参数 */
  data: CouponUsableDetailModel[];
}

function createBaseCouponUsableModelResp(): CouponUsableModelResp {
  return { result: undefined, data: undefined };
}

export const CouponUsableModelResp = {
  encode(message: CouponUsableModelResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      CouponUsableModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponUsableModelResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponUsableModelResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = CouponUsableModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponUsableModelResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? CouponUsableModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: CouponUsableModelResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = CouponUsableModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponUsableModelResp>, I>>(base?: I): CouponUsableModelResp {
    return CouponUsableModelResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponUsableModelResp>, I>>(object: I): CouponUsableModelResp {
    const message = createBaseCouponUsableModelResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? CouponUsableModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseCouponUsableModel(): CouponUsableModel {
  return { commissionCouponList: [], productCouponlList: [] };
}

export const CouponUsableModel = {
  encode(message: CouponUsableModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.commissionCouponList) {
      CouponUsableDetailModel.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.productCouponlList) {
      CouponUsableDetailModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponUsableModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponUsableModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.commissionCouponList.push(CouponUsableDetailModel.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.productCouponlList.push(CouponUsableDetailModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponUsableModel {
    return {
      commissionCouponList: globalThis.Array.isArray(object?.commissionCouponList)
        ? object.commissionCouponList.map((e: any) => CouponUsableDetailModel.fromJSON(e))
        : [],
      productCouponlList: globalThis.Array.isArray(object?.productCouponlList)
        ? object.productCouponlList.map((e: any) => CouponUsableDetailModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CouponUsableModel): unknown {
    const obj: any = {};
    if (message.commissionCouponList?.length) {
      obj.commissionCouponList = message.commissionCouponList.map((e) => CouponUsableDetailModel.toJSON(e));
    }
    if (message.productCouponlList?.length) {
      obj.productCouponlList = message.productCouponlList.map((e) => CouponUsableDetailModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponUsableModel>, I>>(base?: I): CouponUsableModel {
    return CouponUsableModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponUsableModel>, I>>(object: I): CouponUsableModel {
    const message = createBaseCouponUsableModel();
    message.commissionCouponList = object.commissionCouponList?.map((e) => CouponUsableDetailModel.fromPartial(e)) ||
      [];
    message.productCouponlList = object.productCouponlList?.map((e) => CouponUsableDetailModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCouponUsableDetailModel(): CouponUsableDetailModel {
  return {
    id: "",
    couponId: "",
    couponName: "",
    userInstructions: "",
    couponType: 0,
    ticketStatus: 0,
    couponWay: 0,
    useConditionsAmount: 0,
    preferentialAmount: 0,
    discount: 0,
    activeId: "",
    ticketStartExpirationDate: 0,
    ticketEndExpirationDate: 0,
    couponInfoUseRuleModelList: [],
    exchangeCode: "",
    couponUseConditionsType: 0,
    ticketActualPrice: 0,
    ruleCount: 0,
    sameType: false,
    availableFlag: false,
    check: false,
    notAvailableReason: "",
  };
}

export const CouponUsableDetailModel = {
  encode(message: CouponUsableDetailModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.couponId !== "") {
      writer.uint32(18).string(message.couponId);
    }
    if (message.couponName !== "") {
      writer.uint32(26).string(message.couponName);
    }
    if (message.userInstructions !== "") {
      writer.uint32(34).string(message.userInstructions);
    }
    if (message.couponType !== 0) {
      writer.uint32(40).int32(message.couponType);
    }
    if (message.ticketStatus !== 0) {
      writer.uint32(48).int32(message.ticketStatus);
    }
    if (message.couponWay !== 0) {
      writer.uint32(56).int32(message.couponWay);
    }
    if (message.useConditionsAmount !== 0) {
      writer.uint32(65).double(message.useConditionsAmount);
    }
    if (message.preferentialAmount !== 0) {
      writer.uint32(73).double(message.preferentialAmount);
    }
    if (message.discount !== 0) {
      writer.uint32(81).double(message.discount);
    }
    if (message.activeId !== "") {
      writer.uint32(90).string(message.activeId);
    }
    if (message.ticketStartExpirationDate !== 0) {
      writer.uint32(96).int64(message.ticketStartExpirationDate);
    }
    if (message.ticketEndExpirationDate !== 0) {
      writer.uint32(104).int64(message.ticketEndExpirationDate);
    }
    for (const v of message.couponInfoUseRuleModelList) {
      CouponInfoUseRuleModel.encode(v!, writer.uint32(114).fork()).ldelim();
    }
    if (message.exchangeCode !== "") {
      writer.uint32(122).string(message.exchangeCode);
    }
    if (message.couponUseConditionsType !== 0) {
      writer.uint32(128).int32(message.couponUseConditionsType);
    }
    if (message.ticketActualPrice !== 0) {
      writer.uint32(137).double(message.ticketActualPrice);
    }
    if (message.ruleCount !== 0) {
      writer.uint32(144).int32(message.ruleCount);
    }
    if (message.sameType !== false) {
      writer.uint32(152).bool(message.sameType);
    }
    if (message.availableFlag !== false) {
      writer.uint32(160).bool(message.availableFlag);
    }
    if (message.check !== false) {
      writer.uint32(168).bool(message.check);
    }
    if (message.notAvailableReason !== "") {
      writer.uint32(178).string(message.notAvailableReason);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponUsableDetailModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponUsableDetailModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.couponId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.couponName = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.userInstructions = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.couponType = reader.int32() as any;
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.ticketStatus = reader.int32() as any;
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.couponWay = reader.int32() as any;
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }

          message.useConditionsAmount = reader.double();
          continue;
        case 9:
          if (tag !== 73) {
            break;
          }

          message.preferentialAmount = reader.double();
          continue;
        case 10:
          if (tag !== 81) {
            break;
          }

          message.discount = reader.double();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.activeId = reader.string();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.ticketStartExpirationDate = longToNumber(reader.int64() as Long);
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.ticketEndExpirationDate = longToNumber(reader.int64() as Long);
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.couponInfoUseRuleModelList.push(CouponInfoUseRuleModel.decode(reader, reader.uint32()));
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.exchangeCode = reader.string();
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.couponUseConditionsType = reader.int32() as any;
          continue;
        case 17:
          if (tag !== 137) {
            break;
          }

          message.ticketActualPrice = reader.double();
          continue;
        case 18:
          if (tag !== 144) {
            break;
          }

          message.ruleCount = reader.int32();
          continue;
        case 19:
          if (tag !== 152) {
            break;
          }

          message.sameType = reader.bool();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.availableFlag = reader.bool();
          continue;
        case 21:
          if (tag !== 168) {
            break;
          }

          message.check = reader.bool();
          continue;
        case 22:
          if (tag !== 178) {
            break;
          }

          message.notAvailableReason = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponUsableDetailModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      couponId: isSet(object.couponId) ? globalThis.String(object.couponId) : "",
      couponName: isSet(object.couponName) ? globalThis.String(object.couponName) : "",
      userInstructions: isSet(object.userInstructions) ? globalThis.String(object.userInstructions) : "",
      couponType: isSet(object.couponType) ? couponTypeStatusFromJSON(object.couponType) : 0,
      ticketStatus: isSet(object.ticketStatus) ? ticketStatusFromJSON(object.ticketStatus) : 0,
      couponWay: isSet(object.couponWay) ? couponWayStatusFromJSON(object.couponWay) : 0,
      useConditionsAmount: isSet(object.useConditionsAmount) ? globalThis.Number(object.useConditionsAmount) : 0,
      preferentialAmount: isSet(object.preferentialAmount) ? globalThis.Number(object.preferentialAmount) : 0,
      discount: isSet(object.discount) ? globalThis.Number(object.discount) : 0,
      activeId: isSet(object.activeId) ? globalThis.String(object.activeId) : "",
      ticketStartExpirationDate: isSet(object.ticketStartExpirationDate)
        ? globalThis.Number(object.ticketStartExpirationDate)
        : 0,
      ticketEndExpirationDate: isSet(object.ticketEndExpirationDate)
        ? globalThis.Number(object.ticketEndExpirationDate)
        : 0,
      couponInfoUseRuleModelList: globalThis.Array.isArray(object?.couponInfoUseRuleModelList)
        ? object.couponInfoUseRuleModelList.map((e: any) => CouponInfoUseRuleModel.fromJSON(e))
        : [],
      exchangeCode: isSet(object.exchangeCode) ? globalThis.String(object.exchangeCode) : "",
      couponUseConditionsType: isSet(object.couponUseConditionsType)
        ? couponUseConditionsTypestatusFromJSON(object.couponUseConditionsType)
        : 0,
      ticketActualPrice: isSet(object.ticketActualPrice) ? globalThis.Number(object.ticketActualPrice) : 0,
      ruleCount: isSet(object.ruleCount) ? globalThis.Number(object.ruleCount) : 0,
      sameType: isSet(object.sameType) ? globalThis.Boolean(object.sameType) : false,
      availableFlag: isSet(object.availableFlag) ? globalThis.Boolean(object.availableFlag) : false,
      check: isSet(object.check) ? globalThis.Boolean(object.check) : false,
      notAvailableReason: isSet(object.notAvailableReason) ? globalThis.String(object.notAvailableReason) : "",
    };
  },

  toJSON(message: CouponUsableDetailModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.couponId !== "") {
      obj.couponId = message.couponId;
    }
    if (message.couponName !== "") {
      obj.couponName = message.couponName;
    }
    if (message.userInstructions !== "") {
      obj.userInstructions = message.userInstructions;
    }
    if (message.couponType !== 0) {
      obj.couponType = couponTypeStatusToJSON(message.couponType);
    }
    if (message.ticketStatus !== 0) {
      obj.ticketStatus = ticketStatusToJSON(message.ticketStatus);
    }
    if (message.couponWay !== 0) {
      obj.couponWay = couponWayStatusToJSON(message.couponWay);
    }
    if (message.useConditionsAmount !== 0) {
      obj.useConditionsAmount = message.useConditionsAmount;
    }
    if (message.preferentialAmount !== 0) {
      obj.preferentialAmount = message.preferentialAmount;
    }
    if (message.discount !== 0) {
      obj.discount = message.discount;
    }
    if (message.activeId !== "") {
      obj.activeId = message.activeId;
    }
    if (message.ticketStartExpirationDate !== 0) {
      obj.ticketStartExpirationDate = Math.round(message.ticketStartExpirationDate);
    }
    if (message.ticketEndExpirationDate !== 0) {
      obj.ticketEndExpirationDate = Math.round(message.ticketEndExpirationDate);
    }
    if (message.couponInfoUseRuleModelList?.length) {
      obj.couponInfoUseRuleModelList = message.couponInfoUseRuleModelList.map((e) => CouponInfoUseRuleModel.toJSON(e));
    }
    if (message.exchangeCode !== "") {
      obj.exchangeCode = message.exchangeCode;
    }
    if (message.couponUseConditionsType !== 0) {
      obj.couponUseConditionsType = couponUseConditionsTypestatusToJSON(message.couponUseConditionsType);
    }
    if (message.ticketActualPrice !== 0) {
      obj.ticketActualPrice = message.ticketActualPrice;
    }
    if (message.ruleCount !== 0) {
      obj.ruleCount = Math.round(message.ruleCount);
    }
    if (message.sameType !== false) {
      obj.sameType = message.sameType;
    }
    if (message.availableFlag !== false) {
      obj.availableFlag = message.availableFlag;
    }
    if (message.check !== false) {
      obj.check = message.check;
    }
    if (message.notAvailableReason !== "") {
      obj.notAvailableReason = message.notAvailableReason;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponUsableDetailModel>, I>>(base?: I): CouponUsableDetailModel {
    return CouponUsableDetailModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponUsableDetailModel>, I>>(object: I): CouponUsableDetailModel {
    const message = createBaseCouponUsableDetailModel();
    message.id = object.id ?? "";
    message.couponId = object.couponId ?? "";
    message.couponName = object.couponName ?? "";
    message.userInstructions = object.userInstructions ?? "";
    message.couponType = object.couponType ?? 0;
    message.ticketStatus = object.ticketStatus ?? 0;
    message.couponWay = object.couponWay ?? 0;
    message.useConditionsAmount = object.useConditionsAmount ?? 0;
    message.preferentialAmount = object.preferentialAmount ?? 0;
    message.discount = object.discount ?? 0;
    message.activeId = object.activeId ?? "";
    message.ticketStartExpirationDate = object.ticketStartExpirationDate ?? 0;
    message.ticketEndExpirationDate = object.ticketEndExpirationDate ?? 0;
    message.couponInfoUseRuleModelList =
      object.couponInfoUseRuleModelList?.map((e) => CouponInfoUseRuleModel.fromPartial(e)) || [];
    message.exchangeCode = object.exchangeCode ?? "";
    message.couponUseConditionsType = object.couponUseConditionsType ?? 0;
    message.ticketActualPrice = object.ticketActualPrice ?? 0;
    message.ruleCount = object.ruleCount ?? 0;
    message.sameType = object.sameType ?? false;
    message.availableFlag = object.availableFlag ?? false;
    message.check = object.check ?? false;
    message.notAvailableReason = object.notAvailableReason ?? "";
    return message;
  },
};

function createBaseCouponAvailableModelResp(): CouponAvailableModelResp {
  return { result: undefined, data: [] };
}

export const CouponAvailableModelResp = {
  encode(message: CouponAvailableModelResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      CouponUsableDetailModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CouponAvailableModelResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCouponAvailableModelResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(CouponUsableDetailModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CouponAvailableModelResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => CouponUsableDetailModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CouponAvailableModelResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => CouponUsableDetailModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CouponAvailableModelResp>, I>>(base?: I): CouponAvailableModelResp {
    return CouponAvailableModelResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CouponAvailableModelResp>, I>>(object: I): CouponAvailableModelResp {
    const message = createBaseCouponAvailableModelResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => CouponUsableDetailModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
