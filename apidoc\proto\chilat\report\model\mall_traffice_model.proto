syntax = "proto3";
package chilat.report;

option java_package = "com.chilat.rpc.report.model";

import "common.proto";

//查询访客列表的结果
message QueryVisitorListResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated QueryVisitorListModel data = 3;
}

//查询访客列表数据
message QueryVisitorListModel {
    string id = 1; //唯一ID（根据“访客ID与日期”生成）
    string visitorId = 10; //访客ID
    string visitDate = 20; //日期
    int32 pvCount = 30; //页面访问次数
    string stayMinutes = 40; //停留时间（分钟）
    string firstVisitSiteName = 45; //首访网站
    string trafficSourceBornGroupName = 50; //流量来源（最初）
    string trafficSourceGroupName = 60; //流量来源（最近）
    string trafficSource = 70; //流量跟踪码
    string utmCampaign = 80; //广告系列
    string utmMedium = 90; //广告媒介
    string utmTerm = 100; //广告关键字
    string utmContent = 110; //广告内容
    string deviceSite = 120; //访问站点
    string deviceType = 130; //设备标识
    string geoCountryName = 140; //IP归属国家
    string firstVisitDate = 150; //首次访问日期
    int32 diffFirstDays = 160; //距首次访问天数
    int32 diffPrevDays = 170; //距上次访问天数
    int32 visitDaySeq = 180; //第几天访问
    string timeZoneDesc = 190; //访客时区
    string visitTimePeriod = 200; //访问时间段（已按访客时区转换）
    int32 visitCategoryCount = 210; //查看分类数
    int32 visitGoodsCount = 220; //查看商品数
    int32 searchKeywordCount = 230; //搜索关键字数
    int32 imageSearchCount = 240; //图片搜索次数
    string addCartSkuCount = 250; //加购SKU数
    int32 lookingCountSummary = 260; //历史询盘数
    int32 lookingCount = 270; //当日询盘数
    string registerUsers = 280; //注册账号
    string loginUsers = 290; //登录账号
}

//查询访客PV的结果
message QueryPageVisitResp {
    common.Result result = 1;
    QueryPageVisitModel data = 2;
}

//查询访客PV的数据
message QueryPageVisitModel {
    VisitorInfoByDateModel visitorInfoModel = 10; //访客信息（按当前日期统计，默认为最近1天）
    repeated VisitDateOptionModel visitDateOptions = 20; //可选日期列表（倒序，含PV数）
    repeated PageVisitDetailModel pvDetails = 30; //页面访问明细（按“访问日期”返回全部轨迹，最大1000个PV；若展开全部事件，可能会达到4000行）
}

//访客PV明细
message PageVisitDetailModel {
    string visitTime = 10; //访问时间
    int32 clickSeq = 20; //访问序号
    string siteName = 25; //网站名称
    string pageType = 30; //页面类型
    string pageName = 40; //页面名称
    string prevSpmName = 50; //来源SPM位置名称
    double onlineTime = 60; //在线时长（单位：秒）
    bool isLoginVisit = 70; //是否以登录态访问
    string url = 80; //访问页面（备注：加链接，点击后新窗口打开）
    repeated PageEventDetailModel pageEvents = 90; //页面事件列表
}

//访客的访问日期选项
message VisitDateOptionModel {
    string visitDate = 10; //访问日期
    int32 pvCount = 20; //PV数
    bool selected = 30; //是否选中
}

//页面事件明细
message PageEventDetailModel {
    string offsetTime = 10; //时序
    string eventName = 30; //事件名称
    string remark = 40; //事件描述
}

//访客信息（基于访问日期查询）
message VisitorInfoByDateModel {
    string visitorId = 10; //访客ID
    string visitDate = 20; //日期
    int32 pvCount = 30; //页面访问次数
    string stayMinutes = 40; //停留时间（分钟）
    string firstVisitSiteName = 45; //首访网站
    string trafficSourceBornGroupName = 50; //流量来源（最初）
    string trafficSourceGroupName = 60; //流量来源（最近）
    string trafficSource = 70; //流量跟踪码
    string utmCampaign = 80; //广告系列
    string utmMedium = 90; //广告媒介
    string utmTerm = 100; //广告关键字
    string utmContent = 110; //广告内容
    string deviceSite = 120; //访问站点
    string deviceType = 130; //设备标识
    string geoCountryName = 140; //IP归属国家
    string firstVisitDate = 150; //首次访问日期
    int32 diffFirstDays = 160; //距首次访问天数
    int32 diffPrevDays = 170; //距上次访问天数
    int32 visitDaySeq = 180; //第几天访问
    string timeZoneDesc = 190; //访客时区
    string visitTimePeriod = 200; //访问时间段（已按访客时区转换）
    int32 visitCategoryCount = 210; //查看分类数
    int32 visitGoodsCount = 220; //查看商品数
    int32 searchKeywordCount = 230; //搜索关键字数
    int32 imageSearchCount = 240; //图片搜索次数
    string addCartSkuCount = 250; //加购SKU数
    int32 lookingCountSummary = 260; //历史询盘数
    int32 lookingCount = 270; //当日询盘数
    string registerUsers = 280; //注册账号
    string loginUsers = 290; //登录账号

    //以上字段，与 QueryVisitorListModel 相同

    string clientIp = 510; //IP地址
    string geoProvinceName = 520; //IP归属省
    string geoCityName = 530; //IP归属市
    string geoAsnName = 540; //IP归属ASN
    string screenSize = 550; //屏幕大小
    string windowSize = 560; //窗口大小
    string userAgent = 570; //浏览器标识
    string refererUrl = 580; //来源URL
    string lookingOrders = 590; //询盘单号
}

///////////////////     会话信息     ////////////////////

//查询会话列表的结果
message QueryVisitSessionListResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated QueryVisitSessionListModel data = 3;
}

//查询访客列表数据
message QueryVisitSessionListModel {
    string id = 1; //唯一ID（根据“会话ID与日期”生成）
    string visitSessionId = 5; //会话ID
    int32 sessionSeqNo = 6; //会话序号（页面显示会话序号）
    string visitorId = 10; //访客ID
    string visitDate = 20; //日期
    int32 pvCount = 30; //页面访问次数
    string stayMinutes = 40; //停留时间（分钟）
    string firstVisitSiteName = 45; //首访网站
    string trafficSourceBornGroupName = 50; //流量来源（最初）
    string trafficSourceGroupName = 60; //流量来源（最近）
    string trafficSource = 70; //流量跟踪码
    string utmCampaign = 80; //广告系列
    string utmMedium = 90; //广告媒介
    string utmTerm = 100; //广告关键字
    string utmContent = 110; //广告内容
    string deviceSite = 120; //访问站点
    string deviceType = 130; //设备标识
    string geoCountryName = 140; //IP归属国家
    string firstVisitDate = 150; //首次访问日期
    int32 diffFirstDays = 160; //距首次访问天数
    int32 diffPrevDays = 170; //距上次访问天数
    int32 visitDaySeq = 180; //第几天访问
    string timeZoneDesc = 190; //访客时区
    string visitTimePeriod = 200; //访问时间段（已按访客时区转换）
    int32 visitCategoryCount = 210; //查看分类数
    int32 visitGoodsCount = 220; //查看商品数
    int32 searchKeywordCount = 230; //搜索关键字数
    int32 imageSearchCount = 240; //图片搜索次数
    string addCartSkuCount = 250; //加购SKU数
    int32 lookingCountSummary = 260; //历史询盘数
    int32 lookingCount = 270; //当日询盘数
    string registerUsers = 280; //注册账号
    string loginUsers = 290; //登录账号
}

//查询访客PV的结果
message QueryPageVisitBySessionResp {
    common.Result result = 1;
    QueryPageVisitBySessionModel data = 2;
}

//查询会话PV的数据
message QueryPageVisitBySessionModel {
    VisitSessionInfoModel sessionInfoModel = 10; //会话信息
    repeated VisitSessionOptionModel sessionOptions = 20; //可选会话列表（倒序，含PV数）
    repeated PageVisitDetailModel pvDetails = 30; //页面访问明细（按“会话”返回全部轨迹，最大1000个PV；若展开全部事件，可能会达到4000行）
}

//会话选项
message VisitSessionOptionModel {
    int32 sessionSeqNo = 6; //会话序号
    string visitDate = 10; //访问日期
    int32 pvCount = 20; //PV数
    bool selected = 30; //是否选中
}

//会话信息（基于 访客ID+会话序号 查询）
message VisitSessionInfoModel {
    string visitSessionId = 5; //会话ID
    int32 sessionSeqNo = 6; //会话序号
    string visitorId = 10; //访客ID
    string visitDate = 20; //日期
    int32 pvCount = 30; //页面访问次数
    string stayMinutes = 40; //停留时间（分钟）
    string firstVisitSiteName = 45; //首访网站
    string trafficSourceBornGroupName = 50; //流量来源（最初）
    string trafficSourceGroupName = 60; //流量来源（最近）
    string trafficSource = 70; //流量跟踪码
    string utmCampaign = 80; //广告系列
    string utmMedium = 90; //广告媒介
    string utmTerm = 100; //广告关键字
    string utmContent = 110; //广告内容
    string deviceSite = 120; //访问站点
    string deviceType = 130; //设备标识
    string geoCountryName = 140; //IP归属国家
    string firstVisitDate = 150; //首次访问日期
    int32 diffFirstDays = 160; //距首次访问天数
    int32 diffPrevDays = 170; //距上次访问天数
    int32 visitDaySeq = 180; //第几天访问
    string timeZoneDesc = 190; //访客时区
    string visitTimePeriod = 200; //访问时间段（已按访客时区转换）
    int32 visitCategoryCount = 210; //查看分类数
    int32 visitGoodsCount = 220; //查看商品数
    int32 searchKeywordCount = 230; //搜索关键字数
    int32 imageSearchCount = 240; //图片搜索次数
    string addCartSkuCount = 250; //加购SKU数
    int32 lookingCountSummary = 260; //历史询盘数
    int32 lookingCount = 270; //当日询盘数
    string registerUsers = 280; //注册账号
    string loginUsers = 290; //登录账号

    //以上字段，与 QueryVisitorListModel 相同

    string clientIp = 510; //IP地址
    string geoProvinceName = 520; //IP归属省
    string geoCityName = 530; //IP归属市
    string geoAsnName = 540; //IP归属ASN
    string screenSize = 550; //屏幕大小
    string windowSize = 560; //窗口大小
    string userAgent = 570; //浏览器标识
    string refererUrl = 580; //来源URL
    string lookingOrders = 590; //询盘单号
}



//查询页面内事件的结果
message QueryPageEventResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated QueryPageEventModel data = 3;
}
message QueryPageEventModel {
    string offsetTime = 10; //时序
    string eventCode = 20; //事件代码
    string eventName = 30; //事件名称
    string remark = 40; //事件描述
}

