syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing.common";

import "common.proto";

enum GoodsOnlineDays {
  GOODS_ONLINE_DAYS_UNKNOWN = 0;
  GOODS_ONLINE_DAYS_TODAY = 1; //近24小时
  GOODS_ONLINE_DAYS_SEVEN = 2; //近7天
  GOODS_ONLINE_DAYS_FOURTEEN = 3; //近14天
}

enum StockLocation {
  STOCK_LOCATION_UNKNOWN = 0;
  STOCK_LOCATION_CHINA = 1; //中国有库存
  STOCK_LOCATION_LOCAL = 2; //墨西哥本地有库存
}

//促销活动用户类型
enum PromotionUserScope {
  PROMOTION_USER_SCOPE_UNSPECIFIED = 0;
  PROMOTION_USER_SCOPE_SYSTEM = 10; //系统活动
  PROMOTION_USER_SCOPE_CUSTOMER_UNIVERSAL = 30; //客户通用活动
  PROMOTION_USER_SCOPE_CUSTOMER_EXCLUSIVE = 40; //客户专属活动
}

// 促销活动商品筛选器
message PromotionGoodsSelector {
  string selectorId = 10; // 商品筛选器ID（必填；根据 selectorType 确定：营销规则ID or 商品标签ID）
  string selectorName = 20; // 商品筛选器名称（自动获取最新的筛选器配置名称）
  string bannerUrl = 30; // 横幅广告图片URL（楼层展示类型的活动使用；空值表示不显示banner图片；保留字段）
  double markupFactor = 40; // 加点系数（1688价格基础上加的点数，前端只能填1.03~1.15，后端检查不小于1；促销活动类型为“加价系数”时有效）
  string goodsGroupTitle = 50; //商品组名称（楼层展示类型的活动中，在商城前台显示；保留字段）
}

// 促销活动专属客户信息
message PromotionExclusiveCustomer {
  string userId = 10; //用户ID（必填）
  string userName = 20; //用户名称（自动获取最新的用户名更新）
}

//营销分类的商品映射类型
enum CategoryMappingType {
  CATEGORY_MAPPING_TYPE_UNSPECIFIED = 0;
  CATEGORY_MAPPING_TYPE_GOODS_CATEGORY = 10; //商品后台分类
  CATEGORY_MAPPING_TYPE_MARKETING_RULE = 20; //营销规则
}
