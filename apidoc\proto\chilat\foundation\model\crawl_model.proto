syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";

import "chilat/foundation/foundation_common.proto";
import "common.proto";

message CrawlGoodsListResp {
  common.Result result = 1;
  repeated CrawlGoodsModel data = 2;
}

message CrawlGoodsPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated CrawlGoodsModel data = 3;
}

message CrawlGoodsDetailResp {
  common.Result result = 1;
  CrawlGoodsDetailModel data = 2;
}

message CrawlGoodsSupplyListResp {
  common.Result result = 1;
  repeated CrawlGoodsSupplyModel data = 2;
}

message CrawlSupplyDetailResp {
  common.Result result = 1;
  CrawlSupplyDetailModel data = 2;
}

message CrawlCheckUpdateResp {
  common.Result result = 1;
  CrawlCheckUpdateModel data = 2;
}

message CrawlErrorListResp {
  common.Result result = 1;
  repeated CrawlErrorModel data = 2;
}

// 商品信息
message CrawlGoodsModel {
  string id = 1;
  string sourceGoodsId = 2; // 源商品ID
  string relatedGoodsId = 3; // 关联商品ID
  //  string relatedGoodsNo = 4; // 关联商品编号
  string goodsTitle = 5; // 商品标题
  string goodsDesc = 6; // 商品描述
  string goodsDescText = 7; // 商品原描述
  string goodsUrl = 8; // 商品链接
  string categoryId = 9; // 分类ID
  string categoryPath = 10; // 分类路径
  CrawlSourcePlatform sourcePlatform = 11; // 商品来源
  string sourcePlatformDesc = 12; // 商品来源描述
  string goodsImage = 13; // 商品图片
  CrawlSyncState syncState = 14; // 同步状态
  string syncStateDesc = 15; // 同步状态描述
  int32 supplyCount = 16; // 货源数量
  int32 minBuyQuantity = 17; // 最小购买数量
  int32 minIncreaseQuantity = 18; // 最小加购数量
  string priceUnit = 19; // 价格单位
  CrawlMethod method = 20; // 抓取方式
  bool translated = 21; // 是否翻译
  string tag = 22; // 1688找货LATGID
  string operator = 23; // 操作人
  int64 cdate = 24; // 创建时间
  int64 udate = 25; // 更新时间
  string status = 26; // 商品状态
  double minPrice = 27; // 最小价格
  double maxPrice = 28; // 最大价格
  string goodsTitleCn = 101; // 商品标题(中文)
  string priceUnitCn = 102; // 价格单位(中文)
  int32 soldOut = 103; // 商品销量
  double tradeMedalLevel = 104; // 卖家交易勋章
  double compositeServiceScore = 105; // 综合服务分
  double logisticsExperienceScore = 106; // 物流体验分
  double disputeComplaintScore = 107; // 纠纷解决分
  double offerExperienceScore = 108; // 商品体验分
  double consultingExperienceScore = 109; // 咨询体验分
  double repeatPurchasePercent = 110; // 卖家回头率
  double afterSalesExperienceScore = 111; // 退换体验分
  string sellerOpenId = 120; // 卖家加密标识
  string imageId = 200; // 以图搜图，图片ID
  repeated common.IdNameModel goodsTags = 201; // 商品标签
}

// 商品详情
message CrawlGoodsDetailModel {
  CrawlGoodsModel baseInfo = 1; // 基本信息
  repeated CrawlGoodsMediaModel images = 2; // 图片
  repeated CrawlGoodsMediaModel videos = 3; // 视频
  repeated CrawlGoodsAttrModel attrs = 4; // 属性
  repeated CrawlGoodsSpecModel specs = 5; // 规格
  CrawlGoodsPriceModel price = 6; // 价格
}

// 媒体信息
message CrawlGoodsMediaModel {
  string mediaCover = 1; // 封面
  string mediaUrl = 2; // 链接
}

// 商品属性
message CrawlGoodsAttrModel {
  string attrName = 1; // 属性名称
  string attrValue = 2; // 属性值
  string attrNameCn = 101; // 属性名称中文
  string attrValueCn = 102; // 属性值中文
}

// 商品规格
message CrawlGoodsSpecModel {
  string specId = 1; // 规格ID
  string specName = 2; // 规格名称
  CrawlGoodsSpecValueModel singleValue = 3; // 规格值
  repeated CrawlGoodsSpecValueModel specValues = 4; // 规格值列表
  string specNameCn = 101; // 规格名称中文
}

// 商品规格值
message CrawlGoodsSpecValueModel {
  string id = 1; // 规格值ID
  string name = 2; // 规格值名称
  string color = 3; // 颜色
  string fileName = 4; // 图片地址
  string nameCn = 101; // 规格值中文
}

// 商品价格
message CrawlGoodsPriceModel {
  double rate = 1; // 汇率
  string currency = 2; // 币种
  int32 rule = 3; // 规则，1、固定价 2、阶梯价 3、规格价
  repeated CrawlGoodsPriceInfoModel prices = 4; // 价格信息
}

// 价格信息
message CrawlGoodsPriceInfoModel {
  string skuId = 1; // sku号
  double dollarPrice = 2; // 美元价格
  double price = 3; // 本地价格
  int32 min = 4; // 最小数量
  int32 max = 5; // 最大数量
  double dollarPriceLow = 6; // 美元最低价格
  double dollarPriceHigh = 7; // 美元最高价格
  double priceLow = 8; // 本地最低价格
  double priceHigh = 9; // 本地最高价格
  repeated CrawlGoodsSpecModel goodsSpecs = 10; // 对应规格组合
  repeated string specs = 11; // 对应规格字符串
  repeated CrawlGoodsStepPriceModel stepPrices = 12; // 规格阶梯价
  int32 amountOnSale = 13; // 库存
  string specId = 14; // sku规格号
}

message CrawlGoodsStepPriceModel {
  double price = 1; // 本地价格
  int32 min = 2; // 最小数量
  int32 max = 3; // 最大数量
}

// 商品货源
message CrawlGoodsSupplyModel {
  string id = 1; // 货源ID
  string goodsId = 2; // 商品ID
  CrawlSourcePlatform sourcePlatform = 3; // 货源平台
  string sourcePlatformDesc = 4; // 货源平台描述
  string supplyId = 5; // 货源标识
  string supplyLink = 6; // 货源链接
  string supplyImage = 7; // 货源图片
  double supplyPrice = 8; // 货源价格
  int32 priority = 9; // 优先级
  string operator = 10; // 找货人
}

// 货源详情
message CrawlSupplyDetailModel {
  CrawlGoodsSupplyModel baseInfo = 1; // 基本信息
  repeated CrawlGoodsSpecModel specs = 2; // 规格
  CrawlGoodsPriceModel price = 3; // 价格
}

// 检查更新
message CrawlCheckUpdateModel {
  string version = 1; // 版本
  string description = 2; // 更新说明
  string link = 3; // 下载链接
  string desc = 4; // 下一次更新作废
}

// 错误信息
message CrawlErrorModel {
  string errorMessage = 1; // 错误信息
  string scriptURI = 2; // 脚本URI
  int32 lineNumber = 3; // 行号
  int32 columnNumber = 4; // 列号
  string stack = 5; // 错误堆栈
  string version = 6; // 插件版本
  int64 cdate = 7; // 日期
}

message CrawlSubAccountListResp {
  common.Result result = 1;
  repeated CrawlSubAccountListModel data = 2;
}

message CrawlSubAccountListModel {
  string id = 10;
  string nickname = 20;
}

message CrawlTaskListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated CrawlTaskModel data = 3;
}

message CrawlTaskModel {
  string sourceGoodsId = 1; // 1688商品ID
  string shopName = 2; // 店铺名称
  int64 crawlCount = 3; // 抓取成功商品总数
  int64 publishCount = 4; // 发布成功商品数
  int64 onlineCount = 5; // 上架商品数
  CrawlTaskState  state = 6; // 状态
  string creator = 7; // 抓取人
  string crawlStartTime = 8; // 抓取开始时间
  string finishTime = 9; // 抓取完成时间
  string taskStateDesc = 10; // 状态描述
  string id  = 11; // 任务ID
}
