<template>
  <div
    class="w-full flex justify-between bg-[#F2F2F2] py-4 px-4 items-center h-full text-[#333]"
  >
    <div class="flex-1 flex">
      <n-image
        lazy
        preview-disabled
        object-fit="fill"
        class="w-[48px] h-[48px] shrink-0 mr-3"
        :src="sku.skuImage"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
      <div class="flex flex-col justify-between">
        <div class="flex items-center">
          <n-ellipsis :line-clamp="1" class="w-full" :tooltip="false">
            {{ getSkuName(sku) }}
          </n-ellipsis>
          <slot></slot>
        </div>
        <div>
          <span class="text-[#222222] font-medium">{{
            setUnit(sku.salePrice)
          }}</span>
          / {{ goods.goodsPriceUnitName }}
          <n-popover trigger="hover" placement="bottom" v-if="!disabledInput">
            <template #trigger>
              <icon-card
                size="18"
                name="mingcute:warning-line"
                color="#797979"
                class="mr-2 cursor-pointer"
              ></icon-card>
            </template>
            <div>
              <div>
                <icon-card
                  size="18"
                  name="mingcute:warning-line"
                  color="#797979"
                  class="cursor-pointer mr-1"
                ></icon-card>
                <span class="font-medium">
                  {{ authStore.i18n("cm_find.stepPrices") }}
                </span>
              </div>
              <div
                v-for="(step, index) in sku?.stepPrices"
                :key="index"
                class="mt-3 text-[#797979] text-xs flex items-center justify-between px-2"
                :class="{
                  '!text-[#DB1925]': pageData.currentStepPriceEnd == step.end,
                }"
              >
                <span>
                  {{ filterPriceRange(step) }}
                  {{ goods?.goodsPriceUnitName }}
                </span>
                <span>{{ setUnit(step.price) }}</span>
              </div>
            </div>
          </n-popover>
        </div>
      </div>
    </div>
    <div class="flex items-center justify-between ml-[20px]">
      <!-- readonly 这里设置只读 是因为某些浏览器设置禁用状态 input值没有显示 -->
      <n-input-number
        :min="0"
        :max="10000000"
        :precision="0"
        :step="step"
        v-model:value="sku.buyQty"
        class="max-w-[120px] mr-4 input-number"
        button-placement="both"
        :on-update:value="(value) => onCartQtyUpdate(value, sku)"
        :readonly="disabledInput"
      />
      <div class="text-black font-medium shrink-0">
        {{ setUnit(sku.subtotalSalePrice) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="SkuCard">
import { setUnit } from "@/utils/mixin";
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const emit = defineEmits(["onCartQtyUpdate"]);
const props = defineProps({
  goods: {
    type: Object,
    default: () => {},
  },
  sku: {
    type: Object,
    default: () => {},
  },
  disabledInput: {
    type: Boolean,
    default: false,
  },
  step: {
    type: Number,
    default: 1,
  },
});
const pageData = reactive(<any>{
  currentStepPriceEnd: null,
});
watch(
  () => props.goods,
  (newVal: any) => {
    if (newVal) {
      for (const step of props.sku.stepPrices) {
        if (newVal.skuTotalQuantity <= step.end || step.end === -1) {
          pageData.currentStepPriceEnd = step.end;
          break;
        }
      }
    }
  },
  { immediate: true, deep: true }
);

onMounted(async () => {});

function getSkuName(sku: any) {
  const names = sku?.specItemList?.map((spec: any) => {
    return `${spec.groupName}: ${spec.itemName}`;
  });
  return names?.join("; ");
}

// 销售规格的加购数修改
function onCartQtyUpdate(value: any, sku: any) {
  emit("onCartQtyUpdate", value, sku, props.goods);
}

function filterPriceRange(price: any) {
  if (price.end == -1) {
    return `>=${price.start}`;
  } else {
    if (price.start == price.end) {
      return `${price.start}`;
    }
    return `${price.start}-${price.end}`;
  }
}
</script>

<style scoped lang="scss">
:deep(.input-number .n-input__input-el) {
  text-align: center;
}
</style>
