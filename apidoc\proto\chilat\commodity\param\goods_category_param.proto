syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";

// 商品分类列表参数
message GoodsCategoryPageQueryParam {
    common.PageParam page = 1;
    GoodsCategoryQueryParam query = 2;
}

// 商品分类列表查询参数
message GoodsCategoryQueryParam {
    string id = 1; // 分类ID
    string categoryName = 2; // 分类名称
    int64 startCdate = 3; // 开始创建时间
    int64 endCdate = 4; // 结束创建时间
    bool enabled = 5; // 是否启用
}

// 商品分类保存信息
message GoodsCategorySaveParam {
    string id = 1; // 商品类别id
    string cateCode = 2; // 类别编码
    string cateCodeUnique = 3; // 类别编码—多系统唯一记录编码
    string extendData = 4; // 扩展数据字段
    string parentId = 5; // 商品类别父id
    int32 cateLevel = 6; // 商品类别级别,1:一级类目,2:二级类目,3:三级类目
    int32 showType = 7; // 默认展示模式
    int32 idx = 8; // 排序
    string cateName = 9; // 商品类别名称
    string cateAlias = 10; // 商品类别-别名
    string cateAppName = 11; // 商品类别-小程序别名
    string cateLogo = 12; // 商品类别logo
    string cateIcon = 13; // 商品类别Icon
    double cateFee = 14; // 商品类别服务费率(只有三级有)
    double cateMoney = 15; // 商品类别平台使用费(只有一级有)
    string taxCode = 16; // 商品税收类型编码，只需要设置一级类型，其子集默认赋值处理
    repeated string priceUnitIdList = 17; // 可下单包装Id编码集合
    bool enabled = 18; //是否启用
    string cateCnName = 19; // 商品类别中文名称
    int32 adjustRank = 20; //搜索权重分（负数表示降权，分类树中的权重相加，有效值范围：正负100万）
}

message UpdateCategoryAdjustRankParam {
    string id = 1; // 商品类别id
    int32 adjustRank = 20; //搜索权重分（负数表示降权，分类树中的权重相加，有效值范围：正负100万）
}