<template>
  <n-upload
    :accept="props.accept"
    @change="onUploadFile"
    :show-file-list="false"
    @before-upload="onBeforeUpload"
    @mousedown.stop.prevent="onImageSearchClick"
    :input-props="{ 'data-spm-box': 'navigation-image-search' }"
  >
    <slot></slot>
  </n-upload>
  <div v-show="showLoading" class="loading-overlay">
    <n-spin stroke="#e50113" :show="showLoading"> </n-spin>
  </div>
</template>

<script setup lang="ts" name="ImageSearch">
import { ImageCompressor } from '@/utils/imageCompressor';
const route = useRoute();
const showLoading = ref(false);
const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const props = defineProps({
  accept: {
    // 可用的文件类型
    type: String,
    default: "image/*",
  },
});

function onImageSearchClick(event: any) {
  event.stopPropagation();
  event.preventDefault();
}

function onBeforeUpload(data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}) {
  if ((data?.file?.type || "").startsWith("image/")) {
    return true;
  } else {
    return false;
  }
}

async function onUploadFile(opt: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
  event: any;
}) {
  try {
    showLoading.value = true;
    // 压缩图片
    const compressedFile = await ImageCompressor.compress(opt.file.file as File, {
      maxSizeKB: 300,
      quality: 1,
      maxWidth: 500,
    });
    const formData = new FormData();
    formData.append("file", compressedFile);

    const BASEURL =
      typeof window == "object"
        ? useRuntimeConfig().public.clientURL
        : useRuntimeConfig().public.baseURL;

    const { data }: any = await useFetch(
      "/commodity/GoodsSearch/uploadImage1688",
      {
        baseURL: BASEURL,
        method: "POST",
        body: formData,
        cache: "no-cache",
      }
    );
    if (data?.value?.result?.code === 200) {
      if (isMobile.value) {
        navigateToPage(
          "/h5/search/list",
          {
            type: "imgSearch",
            imageId: data?.value?.data?.imageId,
            imageUrl: data?.value?.data?.imageUrl,
          },
          false,
          opt.event
        );
      } else {
        navigateToPage(
          "/goods/list/all",
          {
            type: "imgSearch",
            imageId: data?.value?.data?.imageId,
            imageUrl: data?.value?.data?.imageUrl,
          },
          false,
          opt.event
        );
      }
    } else {
      showToast(data?.value?.result?.message);
    }
  } catch (error) {
    console.error('图片处理失败:', error);
  } finally {
    showLoading.value = false;
  }
}
</script>

<style scoped lang="scss">
.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
:deep(.n-base-loading) {
  width: 50px;
  height: 50px;
}
:deep(.n-base-loading .n-base-loading__container .n-base-loading__icon) {
  height: 50px;
  width: 50px;
}
</style>
