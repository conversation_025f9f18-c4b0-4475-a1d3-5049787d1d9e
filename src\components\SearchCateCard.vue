<template>
  <div class="w-full bg-white">
    <div :style="{ width: searchWidth }" class="mx-auto bg-white">
      <search-card :searchWidth="searchWidth"></search-card>
      <n-divider />
      <search-tabs
        :categories="categories"
        :cleareCate="cleareCate"
      ></search-tabs>
      <!-- <n-divider /> -->
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  categories: {
    type: Array,
    default: () => [],
  },
  cleareCate: {
    type: Boolean,
    default: false,
  },
  searchWidth: {
    type: String,
    default: "1280px",
  },
});
</script>
<style scoped lang="scss">
.n-divider {
  margin: 0px;
}
</style>
