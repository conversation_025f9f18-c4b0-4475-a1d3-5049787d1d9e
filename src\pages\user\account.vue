<template>
  <div class="h-full flex-1 flex flex-col">
    <div class="px-5 py-7 rounded bg-white mb-4">
      <div class="flex justify-between items-center pr-10">
        <div class="flex">
          <icon-card
            size="86"
            color="#E72528"
            class="ml-2 mr-3"
            name="iconamoon:profile-circle-fill"
          ></icon-card>
          <span class="text-xl mt-2 break-all w-[88%]">{{
            pageData.userInfo.email
          }}</span>
        </div>
        <a href="/user/inquiry" data-spm-box="myhome-account-top">
          <div class="text-center flex-shrink-0">
            <div class="text-4xl font-medium mb-2">
              {{ pageData.userInfo.inquiryCount }}
            </div>
            <div class="text-base">
              {{ authStore.i18n("cm_user.consultas") }}
            </div>
          </div>
        </a>
      </div>
    </div>
    <div
      class="rounded bg-white p-5 flex-1"
      v-if="!pageData.verifyMailResult?.isMailVerified"
    >
      <div
        class="invite-link flex justify-between items-center mx-auto bg-[#F2F2F2]"
        v-if="!pageData.verifyMailResult?.isMailVerified"
      >
        <div class="flex justify-between items-center mr-4">
          <img
            loading="lazy"
            class="w-[48px] mr-2"
            alt="image-search"
            src="@/assets/icons/inviteWarning.svg"
            referrerpolicy="no-referrer"
          />
          <span>{{ authStore.i18n("cm_invite.verificationMailbox") }}</span>
        </div>
        <div>
          <n-button
            class="px-4"
            color="#E50113"
            round
            @click="onMailVerified"
            :loading="pageData.verifyLoading"
          >
            <span class="text-white">{{
              authStore.i18n("cm_invite.viewNow")
            }}</span>
          </n-button>
        </div>
      </div>
    </div>
    <div class="rounded bg-white p-5" v-else>
      <div class="text-xl font-medium mb-7">
        {{ authStore.i18n("cm_user.invite") }}
      </div>
      <div class="flex justify-between pr-16 mb-4">
        <div>
          <span class="inline-block mr-2 text-[#999] w-40">{{
            authStore.i18n("cm_user.inviteCode")
          }}</span>
          <span class="text-sm">{{ pageData.userInfo.inviteCode }}</span>
        </div>
        <n-button
          size="medium"
          secondary
          strong
          @click="onHandleCopyText(pageData.userInfo.inviteCode, 'code')"
        >
          {{ authStore.i18n("cm_user.copy") }}
        </n-button>
      </div>
      <div class="flex justify-between pr-16 mb-4">
        <div>
          <span class="inline-block mr-2 text-[#999] w-40">{{
            authStore.i18n("cm_user.inviteLink")
          }}</span>
          <span class="text-sm">{{ pageData.userInfo.inviteLink }}</span>
        </div>
        <n-button
          size="medium"
          secondary
          strong
          @click="onHandleCopyText(pageData.userInfo.inviteLink)"
        >
          {{ authStore.i18n("cm_user.copy") }}
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const userInfo = computed(() => useAuthStore().getUserInfo);
const pageData = reactive(<any>{
  userInfo: <any>{},
});

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

authStore.getCartList();
await onVerifyMailResult();
onUserDetail();

async function onVerifyMailResult() {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo.value?.username,
    isNeedCoupon: false,
  });
  if (res?.result?.code === 200) {
    pageData.verifyMailResult = res?.data;
  }
}

async function onMailVerified() {
  pageData.verifyLoading = true;
  const res: any = await useSendVerifyMail({
    verifyMailScene: "INVITE_FRIEND",
  });
  pageData.verifyLoading = false;
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.reload();
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      // 跳转邮箱
      navigateToEmail();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

async function onUserDetail() {
  const res: any = await useUserDetail({});
  if (res?.result?.code === 200) {
    const protocol = window.location.protocol;
    const host = window.location.host;
    pageData.userInfo = res?.data;
    pageData.userInfo.inviteLink = `${protocol}//${host}/?utm_source=invite_code_${pageData.userInfo.inviteCode}`;
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  }
}

function onGoHome() {
  window.location.href = "/";
}

function onHandleCopyText(val: any, type?: any) {
  let eventName, remark;
  if (type === "code") {
    eventName = "copy_invite_code";
    remark = "复制邀请代码";
  } else {
    eventName = "copy_invite_link";
    remark = "复制邀请链接";
  }
  window?.MyStat?.addPageEvent(eventName, remark); // 埋点
  onCopyText(val);
}
</script>

<style lang="scss" scoped>
.invite-link {
  border-radius: 8px;
  padding: 30px 20px;
}
</style>
