syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/basis/model/dictionary_model.proto";
import "chilat/foundation/param/task_param.proto";
import "chilat/foundation/model/task_model.proto";
import "common.proto";

// 任务管理
service JobManage {
    // 获取所有中间件的任务列表
    rpc getAllMidWareJobList(common.EmptyParam) returns (basis.JobStatListResp);
}