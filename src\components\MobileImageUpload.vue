<template>
  <div class="mobile-image-upload">
    <!-- 文件列表显示区域 -->
    <div class="mobile-file-grid">
      <div
        v-for="item in pageData.fileList"
        :key="item.id"
        class="mobile-file-item"
        :class="{
          uploading: item.status === 'uploading',
          error: item.status === 'error',
        }"
      >
        <div class="image-container">
          <n-image
            :src="item.thumbnailUrl || item.url"
            :preview-src="item.url"
            class="upload-image"
            object-fit="cover"
          />
          <!-- 自定义删除按钮 - 只在成功状态下显示 -->
          <div
            class="mobile-delete-btn"
            @click="handleCustomRemove(item)"
            v-if="item.status === 'finished'"
          >
            <icon-card
              name="material-symbols:close-rounded"
              size="12"
              color="#fff"
            ></icon-card>
          </div>
          <!-- 上传进度遮罩 -->
          <div v-if="item.status === 'uploading'" class="upload-mask">
            <n-spin size="small" />
            <div class="upload-text">Subiendo...</div>
          </div>
          <!-- 错误状态遮罩 -->
          <div v-if="item.status === 'error'" class="error-mask">
            <div class="error-actions">
              <!-- 重试按钮 -->
              <div
                class="error-action-btn retry-btn"
                @click="handleRetry(item)"
              >
                <icon-card
                  name="material-symbols:refresh-rounded"
                  size="14"
                  color="#fff"
                />
              </div>
              <!-- 删除按钮 -->
              <div
                class="error-action-btn delete-btn"
                @click="handleCustomRemove(item)"
              >
                <icon-card
                  name="material-symbols:close-rounded"
                  size="14"
                  color="#fff"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传按钮 -->
      <div
        v-if="pageData.fileList.length < props.max"
        class="mobile-upload-trigger"
        @click="triggerFileInput"
      >
        <icon-card
          name="fluent:add-28-regular"
          size="32"
          color="#66676A"
        ></icon-card>
      </div>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInputRef"
      type="file"
      :accept="props.accept"
      :multiple="multiple"
      @change="handleFileSelect"
      style="display: none"
    />
  </div>
</template>

<script setup lang="ts" name="MobileImageUpload">
import { useMessage } from "naive-ui";
import type { UploadFileInfo } from "naive-ui";
import { ImageCompressor } from "@/utils/imageCompressor";

const message = useMessage();
const emit = defineEmits(["uploadEvent"]);

// Props定义
const props = withDefaults(
  defineProps<{
    max?: number;
    width?: string;
    multiple?: boolean;
    accept?: string;
    files?: any[];
    spmCode?: string;
  }>(),
  {
    max: 1,
    width: "100%",
    multiple: false,
    accept: "image/*",
    files: () => [],
    spmCode: "",
  }
);

const fileInputRef = ref<HTMLInputElement>();

const multiple = computed(() => props.max > 1);

const pageData = reactive({
  fileList: [] as UploadFileInfo[],
  uploading: false,
});

// 待上传文件列表
let pendingFiles: UploadFileInfo[] = [];
let isBatchUploading = false;
let isExternalUpdate = false;

// 监听外部文件变化
watch(
  () => props.files,
  (newFiles) => {
    if (isExternalUpdate) {
      isExternalUpdate = false;
      return;
    }

    if (newFiles && newFiles.length > 0) {
      pageData.fileList = newFiles.map((file, index) => {
        // 处理两种数据格式：对象格式和字符串格式
        if (typeof file === "string") {
          // 字符串格式：直接是URL
          return {
            id: `external-${Date.now()}-${index}`,
            name: `image-${index + 1}`,
            status: "finished" as const,
            url: file,
            thumbnailUrl: file,
          };
        } else {
          // 对象格式：包含fileName和fileUrl
          return {
            id: `external-${Date.now()}-${index}`,
            name: file.fileName || `image-${index + 1}`,
            status: "finished" as const,
            url: file.fileUrl,
            thumbnailUrl: file.fileUrl,
          };
        }
      });
    } else {
      pageData.fileList = [];
    }
  },
  { immediate: true, deep: true }
);

// 触发文件选择
const triggerFileInput = () => {
  fileInputRef.value?.click();
};

// 处理文件选择
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = target.files;

  if (!files || files.length === 0) return;

  // 计算可以上传的文件数量
  const currentCount = pageData.fileList.length;
  const remainingSlots = props.max - currentCount;

  if (remainingSlots <= 0) {
    return;
  }

  // 只处理允许数量内的文件
  const filesToProcess = Array.from(files).slice(0, remainingSlots);
  filesToProcess.forEach((file) => {
    // 验证文件类型
    if (!file.type.startsWith("image/")) {
      return;
    }

    // 创建文件信息对象
    const fileInfo: UploadFileInfo = {
      id: `file-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      name: file.name,
      status: "pending",
      file: file,
      thumbnailUrl: URL.createObjectURL(file),
      url: undefined,
    };

    // 添加到文件列表
    pageData.fileList.push(fileInfo);
    pendingFiles.push(fileInfo);

    // 开始上传
    uploadFile(fileInfo);
  });

  // 清空input值，允许重复选择同一文件
  target.value = "";
};

// 上传单个文件
const uploadFile = async (file: UploadFileInfo) => {
  try {
    pageData.uploading = true;
    file.status = "uploading";

    // 图片压缩处理
    let fileToUpload = file.file as File;

    // 检查文件大小，超过500KB进行压缩
    if (fileToUpload.size > 500 * 1024) {
      try {
        fileToUpload = await ImageCompressor.compress(fileToUpload, {
          maxSizeKB: 500,
          quality: 0.8,
          maxWidth: 1500,
          maxHeight: 1500,
        });
      } catch (compressError) {
        console.warn("图片压缩失败，使用原始文件:", compressError);
      }
    }

    const formData = new FormData();
    formData.append("file", fileToUpload);

    const BASEURL =
      typeof window === "object"
        ? useRuntimeConfig().public.clientURL
        : useRuntimeConfig().public.baseURL;

    const { data: response, error } = await useFetch(
      "/foundation/FileManager/uploadStream",
      {
        baseURL: BASEURL,
        method: "POST",
        body: formData,
        headers: {
          Authorization: `Bearer ${useAuthStore().token}`,
        },
        key: `upload-${file.id}-${Date.now()}`,
        cache: "no-cache",
      }
    );

    if (error.value) {
      throw error.value;
    }

    const res = response.value as any;
    if (res?.result?.code === 200) {
      file.status = "finished";
      file.url = res.data;
      file.thumbnailUrl = res.data;

      // 强制触发响应式更新
      const fileIndex = pageData.fileList.findIndex((f) => f.id === file.id);
      if (fileIndex !== -1) {
        pageData.fileList[fileIndex] = { ...file };
      }

      // 从待上传列表移除
      pendingFiles = pendingFiles.filter((f) => f.id !== file.id);

      // 触发更新
      emitFileList();
    } else {
      throw new Error(res?.result?.message);
    }
  } catch (err: any) {
    // 立即设置错误状态
    file.status = "error";

    // 强制触发响应式更新
    const fileIndex = pageData.fileList.findIndex((f) => f.id === file.id);
    if (fileIndex !== -1) {
      pageData.fileList[fileIndex] = { ...file };
    }

    // 从待上传列表移除
    pendingFiles = pendingFiles.filter((f) => f.id !== file.id);
    message.error(`${file.name}: ${err.message || err}`, { duration: 3000 });
  } finally {
    // 检查是否还有其他文件在上传
    const hasUploadingFiles = pageData.fileList.some(
      (f) => f.status === "uploading"
    );
    if (!hasUploadingFiles) {
      pageData.uploading = false;
    }
  }
};

// 自定义删除方法
const handleCustomRemove = (file: UploadFileInfo) => {
  pageData.fileList = pageData.fileList.filter((f) => f.id !== file.id);
  // 同时从待上传列表移除
  pendingFiles = pendingFiles.filter((f) => f.id !== file.id);
  emitFileList();
};

// 重试上传方法
const handleRetry = (file: UploadFileInfo) => {
  // 重置文件状态为上传中
  file.status = "uploading";

  // 重新开始上传
  uploadFile(file);
};

// 发送文件列表更新事件
const emitFileList = () => {
  const finishedFiles = pageData.fileList.filter(
    (f) => f.status === "finished"
  );

  const files = finishedFiles.map((file) => ({
    fileName: file.name,
    fileUrl: file.url,
  }));

  // 设置标志位并触发更新
  isExternalUpdate = true;
  emit("uploadEvent", files);
};

// 检查是否有文件正在上传
const hasUploadingFiles = () => {
  return pageData.fileList.some((f) => f.status === "uploading");
};

// 暴露方法给父组件
defineExpose({
  hasUploadingFiles,
});
</script>

<style scoped>
/* 移动端图片上传容器 */
.mobile-image-upload {
  width: 100%;
}

.mobile-file-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.mobile-file-item {
  position: relative;
  width: 94px;
  height: 94px;
  border-radius: 8px;
  overflow: hidden;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.mobile-file-item.uploading {
  border-color: #e50113;
}

.mobile-file-item.error {
  border-color: #ff4d4f;
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.upload-image .n-image) {
  width: 100%;
  height: 100%;
}

:deep(.upload-image .n-image img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mobile-delete-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 16px;
  height: 16px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 0 0 0 12px;
  font-size: 8px;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 上传进度遮罩 - 移动端优化 */
.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  gap: 8px;
}

.upload-mask .upload-text {
  color: #333;
  font-size: 14px;
  text-align: center;
  margin: 0;
}

/* 错误状态遮罩 */
.error-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 77, 79, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

/* 错误状态操作按钮容器 */
.error-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}

/* 错误状态操作按钮 */
.error-action-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 重试按钮 */
.retry-btn {
  background-color: rgba(24, 144, 255, 0.9);
}

.retry-btn:hover {
  background-color: rgba(24, 144, 255, 1);
  transform: scale(1.05);
}

.retry-btn:active {
  transform: scale(0.95);
}

/* 删除按钮 */
.delete-btn {
  background-color: rgba(255, 77, 79, 0.9);
}

.delete-btn:hover {
  background-color: rgba(255, 77, 79, 1);
  transform: scale(1.05);
}

.delete-btn:active {
  transform: scale(0.95);
}

/* 上传按钮 - 移动端优化 */
.mobile-upload-trigger {
  width: 96px;
  height: 96px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafc;
}
</style>
