<template>
  <ClientOnly>
    <div
      class="floating-buttons"
      :class="showVideoPlaying ? 'home-floating-buttons' : ''"
      id="floating-buttons"
      v-if="
        showVideoPlaying ||
        showWhatsApp ||
        showCopyShortLink ||
        showAddToDesktop ||
        (showBackTopPage && pageData.showBackTop)
      "
    >
      <div
        v-if="showVideoPlaying"
        @click="onOpenVideo"
        class="video-playing inline-flex flex-col items-center justify-center gap-[2px] px-[4px] pt-[10px] pb-[8px] bg-white text-center"
      >
        <img
          class="w-[38px]"
          src="@/assets/icons/common/playing.gif"
          :alt="authStore.i18n('cm_common.videoPlaying')"
        />
        <div class="w-[38px] h-[20px] overflow-hidden">
          <div
            class="text-[#4D4D4D] text-[12px] origin-left scale-67 leading-[12px] w-[56px]"
          >
            {{ authStore.i18n("cm_common.videoPlaying") }}
          </div>
        </div>
      </div>
      <div class="buttons-wrapper">
        <!-- WhatsApp -->
        <n-popover
          :overlap="false"
          placement="left"
          trigger="hover"
          raw
          :show-arrow="false"
          v-if="showWhatsApp"
        >
          <template #trigger>
            <div
              class="w-[34px] h-[34px] flex items-center justify-center rounded-full cursor-pointer mb-[2px]"
            >
              <img
                alt="whatsapp"
                class="whatsapp-icon flex-shrink-0"
                @click="onWhatsAppClick"
                src="@/assets/icons/common/whatsapp.svg"
              />
            </div>
          </template>
          <div class="popover-content">WhatsApp</div>
        </n-popover>

        <!-- 复制短链 -->
        <n-popover
          :overlap="false"
          placement="left"
          trigger="hover"
          raw
          :show-arrow="false"
          v-if="showCopyShortLink"
        >
          <template #trigger>
            <div
              class="w-[34px] flex items-center justify-center rounded-full cursor-pointer"
            >
              <div
                class="w-[24px] h-[24px] flex items-center justify-center hover:bg-[#F2F2F2] rounded-full cursor-pointer transition-all duration-300"
              >
                <img
                  alt="copy"
                  @click="onCopyShortLink"
                  src="@/assets/icons/common/copy.svg"
                />
              </div>
            </div>
          </template>
          <div class="popover-content">Copiar enlace</div>
        </n-popover>

        <!-- 添加到桌面 -->
        <div
          v-if="
            ($pwa?.showInstallPrompt || $pwa?.offlineReady) && showAddToDesktop
          "
        >
          <n-popover
            :overlap="false"
            placement="left"
            trigger="hover"
            raw
            :show-arrow="false"
          >
            <template #trigger>
              <div
                class="w-[34px] flex items-center justify-center rounded-full cursor-pointer"
              >
                <div
                  class="w-[24px] h-[24px] flex items-center justify-center hover:bg-[#F2F2F2] rounded-full cursor-pointer transition-all duration-300"
                >
                  <img
                    alt="download"
                    @click="() => onAddToDesktop($pwa)"
                    src="@/assets/icons/common/download.svg"
                  />
                </div>
              </div>
            </template>
            <div class="popover-content">Añadir al escritorio</div>
          </n-popover>
        </div>

        <!-- 返回顶部 -->
        <n-popover
          :overlap="false"
          placement="left"
          trigger="hover"
          raw
          :show-arrow="false"
        >
          <template #trigger>
            <div
              v-show="pageData.showBackTop && showBackTopPage"
              class="w-[34px] h-[34px] flex items-center justify-center rounded-full cursor-pointer mt-[2px]"
            >
              <img
                alt="backtop"
                @click="onBackTop"
                src="@/assets/icons/common/back-top.svg"
                class="hover:scale-141 transition-all duration-300"
              />
            </div>
          </template>
          <div class="popover-content">Volver arriba</div>
        </n-popover>
      </div>
    </div>
    <a
      target="_blank"
      href="/goods/looking"
      v-if="showCustomSearch"
      data-spm-box="button-find-fixed-bottom"
      class="fixed bottom-[2px] right-[8px] z-[101] custom-search cursor-pointer"
    >
      <img
        :alt="authStore.i18n('cm_goods.customSearch')"
        class="absolute bottom-[8px] left-[-28px]"
        src="@/assets/icons/common/find-red.svg"
      />
      <div class="text mb-[8px]">
        {{ authStore.i18n("cm_goods.customSearch") }}
      </div>
    </a>
    <video-modal ref="videoModalRef"></video-modal>
  </ClientOnly>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const videoModalRef = ref<any>(null);
const pageData = reactive<any>({
  originLink: "",
  shortLink: "",
  showBackTop: false,
});

const videoShortsData = [
  {
    id: "0YCpG_CqNAE",
    title: "Sobre confianza con Chilat",
    titleCh: "关于与Chilat的信任",
    isVertical: true, // 竖屏视频
  },
  {
    id: "xW1j7rwWOso",
    title: "Los beneficios de colaborar con Chilat",
    titleCh: "与Chilat合作的好处",
    isVertical: true,
  },
  {
    id: "4j14aCzhZxk",
    title: "¿Quieres desarrollar tu propia marca?",
    titleCh: "想打造自己的品牌吗？",
    isVertical: true,
  },
  {
    id: "-XZkMkY__vn8",
    title: "¡Feliz colaboración! ¡Éxito en nuestra asociación!",
    titleCh: "合作愉快！共创成功！",
    isVertical: true,
  },
  {
    id: "9fqSJGC4ZlE",
    title: "Directo exclusivo de Chilatshop",
    titleCh: "Chilatshop独家直播",
    isVertical: true,
  },
];

watch(
  () => authStore.hasCartData,
  (hasData: boolean) => {
    updateFloatingButtonPosition();
  },
  { immediate: true }
);

// 页面路径黑名单配置
const HIDDEN_PAGES = {
  whatsApp: [
    "/notas",
    "/open/notas",
    "/vip",
    "/viajar-a-china",
    "/inquiry",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
  copyShortLink: [
    "/open",
    "/notas",
    "/open/notas",
    "/vip",
    "/viajar-a-china",
    "/inquiry",
    "/login",
    "/register",
    "/modifyPwd",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
  addToDesktop: [
    "/open",
    "/notas",
    "/open/notas",
    "/vip",
    "/viajar-a-china",
    "/inquiry",
    "/login",
    "/register",
    "/modifyPwd",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
  backTop: [
    "/login",
    "/register",
    "/modifyPwd",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ],
  customSearch: [
    "/open",
    "/notas",
    "/open/notas",
    "/vip",
    "/viajar-a-china",
    "/survey",
    "/inquiry",
    "/login",
    "/register",
    "/modifyPwd",
    "/goods/looking",
    "/looking",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
    "/order/details",
  ],
};

// 缓存当前路径，避免重复计算
const currentPath = computed(() => normalizePath(route.path));

const showVideoPlaying = computed(() => route.path === "/");

// 计算属性
const showWhatsApp = computed(
  () => !HIDDEN_PAGES.whatsApp.includes(currentPath.value)
);
const showCopyShortLink = computed(
  () =>
    !HIDDEN_PAGES.copyShortLink.includes(currentPath.value) &&
    route.query?.preview !== "true"
);
const showAddToDesktop = computed(
  () => !HIDDEN_PAGES.addToDesktop.includes(currentPath.value)
);
const showBackTopPage = computed(
  () => !HIDDEN_PAGES.backTop.includes(currentPath.value)
);
const showCustomSearch = computed(
  () => !HIDDEN_PAGES.customSearch.includes(currentPath.value)
);

// 复制短链
function onCopyShortLink() {
  if (pageData.originLink === window.location.href && !!pageData.shortLink) {
    onCopyText(pageData.shortLink);
  } else {
    pageData.originLink = window.location.href;
    useShortLink({ url: window.location.href }).then((res: any) => {
      if (res?.result?.code === 200) {
        pageData.shortLink = res?.data?.shortUrl;
        onCopyText(pageData.shortLink);
      } else {
        showToast(
          res?.result?.message || authStore.i18n("cm_find.errorMessage")
        );
      }
    });
  }
}

// PWA 添加到桌面
function onAddToDesktop(pwa: any) {
  window?.MyStat?.addPageEvent(
    "click_nav_shortcut_icon",
    `点击添加首页快捷方式按钮`
  );

  const onHandleResult = (result: any) => {
    if (result.outcome === "accepted") {
      window?.MyStat?.addPageEvent(
        "shortcut_desktop_install_success",
        "安装成功"
      );
    } else if (result.outcome === "dismissed") {
      window?.MyStat?.addPageEvent(
        "shortcut_desktop_install_cancel",
        "安装取消"
      );
    }
  };

  const onHandleError = () => {
    window?.MyStat?.addPageEvent("shortcut_desktop_install_fail", "安装失败");
  };

  if (pwa?.offlineReady) {
    pwa.cancelPrompt();
    setTimeout(
      () => pwa.install().then(onHandleResult).catch(onHandleError),
      2000
    );
  } else {
    pwa.install().then(onHandleResult).catch(onHandleError);
  }
}

function onBackTop() {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}

// 监听页面滚动
onMounted(() => {
  window.addEventListener("scroll", onScroll);
  window.addEventListener("resize", onResize);
  updateFloatingButtonPosition();
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
  window.removeEventListener("resize", onResize);
});

function onResize() {
  updateFloatingButtonPosition();
}

function onScroll(e: any) {
  let scrollHeight =
    document.documentElement.scrollTop ||
    document.body.scrollTop ||
    e.target.scrollTop;
  const halfScreenHeight = window.innerHeight / 2;
  pageData.showBackTop = scrollHeight > halfScreenHeight;

  // x轴滚动
  if (e.deltaX !== 0) {
    onScrollX(e);
  }
}
// 页面横向滚动时，悬浮按钮跟随right-wrapper一起滚动
function onScrollX(e: any) {
  if (typeof document === "undefined") return;

  const floatingButtonsElement = document.getElementById("floating-buttons");
  const rightWrapper = document.getElementById("right-wrapper");

  if (!floatingButtonsElement) return;

  const scrollLeft =
    document.documentElement.scrollLeft ||
    document.body.scrollLeft ||
    e?.target?.scrollLeft ||
    0;

  // 计算滚动位置
  const scrollLeftMax = document.body.scrollWidth - document.body.clientWidth;

  // 如果存在right-wrapper（购物车抽屉），则与其保持相对位置
  if (rightWrapper && authStore.hasCartData) {
    // 使用与right-wrapper相同的计算逻辑
    floatingButtonsElement.style.right =
      scrollLeft - scrollLeftMax + 220 + 8 + "px";
  } else {
    // 如果没有购物车抽屉，则使用默认位置
    floatingButtonsElement.style.right = scrollLeft - scrollLeftMax + 8 + "px";
  }
}

// 更新悬浮按钮位置
function updateFloatingButtonPosition() {
  nextTick(() => {
    onScrollX({});
  });
}

function onOpenVideo() {
  if (videoModalRef.value) {
    videoModalRef.value.onOpenVideo(videoShortsData[0]);
  }
}
</script>
<style lang="scss" scoped>
.floating-buttons {
  position: fixed;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: end;
  // min-height: 232px;
}

@media (max-width: 1300px) and (max-height: 740px) {
  .home-floating-buttons {
    top: 486px;
  }
}

.buttons-wrapper {
  width: 40px;
  display: flex;
  padding: 13px 3px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: 500px;
  background: #fff;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
}

.video-playing {
  width: 46px;
  border-radius: 500px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(25px);
  margin-bottom: 12px;
  cursor: pointer;
}

.whatsapp-icon {
  &:hover {
    content: url("@/assets/icons/common/whatsapp-active.svg");
  }
}

.popover-content {
  display: flex;
  padding: 5px 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.75);
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.1);
  color: #fff;
  text-align: center;
  font-size: 12px;
  line-height: 12px;
}

.custom-search {
  .text {
    display: inline-flex;
    padding: 10px 22px 10px 50px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 12px 12px 50px 50px;
    background: #11263b;
    box-shadow: 0px 4px 5px 0px rgba(30, 30, 30, 0.25);
    color: #fff;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    transition: all 0.3s ease;
  }
  img {
    transition: all 0.3s ease;
  }
  &:hover {
    img {
      transform: scale(1.2);
    }
    .text {
      padding: 13px 22px 13px 50px;
      background: #007fff;
    }
  }
}
</style>
