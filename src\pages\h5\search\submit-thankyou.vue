<template>
  <div class="mobile-container">
    <!-- 头部信息 -->
    <mobile-search-bar :showCart="true"></mobile-search-bar>
    <div
      class="pt-[0.52rem] flex flex-col items-center text-center px-[0.46rem]"
    >
      <img
        :alt="authStore.i18n('cm_goods.customSearch')"
        class="w-[4.36rem]"
        src="@/assets/icons/common/find-submitted.svg"
      />
      <div
        class="text-[0.52rem] leading-[0.52rem] font-medium mt-[0.52rem] text-[#11263B]"
      >
        {{ authStore.i18n("cm_search.sentSuccess") }}
      </div>
      <div class="text-[0.28rem] leading-[0.4rem] text-[#7F7F7F] mt-[0.24rem]">
        {{ authStore.i18n("cm_search.sentSuccessTip") }}
      </div>
      <div class="w-full h-[0.02rem] bg-[#D9D9D9] mt-[0.52rem]"></div>
      <div
        class="w-full flex flex-col justify-between items-center mt-[0.36rem] gap-[0.24rem]"
      >
        <a
          href="/h5/search/looking"
          data-spm-box="button-find-again-find"
          class="w-full h-[0.84rem] text-center rounded-[10rem] text-[#fff] bg-[#11263B] text-[0.32rem] leading-[0.32rem] cursor-pointer flex justify-center items-center"
        >
          {{ authStore.i18n("cm_search.sendNewRequest") }}
        </a>
        <a
          href="/h5"
          class="w-full h-[0.84rem] box-border border-1 border-[#A6A6A6] text-center rounded-[10rem] text-[#333] text-[0.32rem] leading-[0.32rem] cursor-pointer flex justify-center items-center"
        >
          {{ authStore.i18n("cm_search.goToHome") }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();
</script>
<style scoped lang="scss"></style>
