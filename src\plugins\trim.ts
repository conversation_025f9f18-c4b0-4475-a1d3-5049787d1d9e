import { defineNuxtPlugin } from "#app";

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive("trim", {
    mounted(el) {
      // 获取n-input的input元素
      const inputEl = el.querySelector("input") || el;
      if (inputEl) {
        inputEl.addEventListener("blur", () => {
          const trimmedValue = inputEl.value.trim();
          inputEl.value = trimmedValue;

          // 触发input事件以更新Vue的响应式数据
          inputEl.dispatchEvent(new Event("input"));
        });
      }
    },
  });
});
