import { defineStore } from "pinia";

export const useConfigStore = defineStore("use-config", {
  state: () => ({
    pageTheme: {
      name: "",
      icon: "",
      logo: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/04/6552d857-0770-4091-a929-c66961de1daf.png",
      homeLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/04/5ede27ed-7753-48ad-8f90-481b6abb175b.png",
      searchLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/qa/2024/04/12/e7df3074-946c-4412-86e1-4586646a660c.png",
      cateLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/qa/2024/03/05/fbfa47a9-2976-46a3-8209-73e9d5e7dc1c.jpg",
      bgLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/qa/2024/04/11/7d5dbddf-9d5e-4f07-9e6b-3bb882c9757f.jpg",
      guideLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/07/c1158497-05a4-4666-b8e2-a10055d72221.png",
      mobileGuideLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/07/397dc481-987f-48d9-9995-7ce8075feb13.png",
      guidePoster:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/21/022e4a42-a22a-4440-8add-3dc98d0964ea.png",
      guideVideo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/21/fcb3a224-2e5b-4373-b7af-b29df6abe721.mp4",
      Guide:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/22/b1798be2-736d-420d-a4f2-2d63ae7ff377.png",
      mobileGuide:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/22/ba160b19-ce52-4859-b230-2bb60cb705df.png",
      chilatLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/10/3ec2873e-0b0e-4b5c-b310-83674bd770c4.png",
      chilatWhiteLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/02/03f7e590-d92c-42b3-8dce-3da169eca5a7.png",
      notasVideo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/03/a6f1d085-2916-4f64-a3ca-de239fb21669.mp4",
      mobileNotasVideo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/03/a01a99b4-5af2-4013-ad64-9b0427702892.mp4",
      loading: {
        color: "#E50113",
        height: 3,
        duration: 2000,
        throttle: 200,
      },
      naiveBar: {
        color: "#E50113",
      },
      header: {
        icon: "ep:arrow-left-bold",
        display: false,
      },
    },
  }),
  getters: {
    getPageTheme(state) {
      return state.pageTheme;
    },
  },
  actions: {},
});
