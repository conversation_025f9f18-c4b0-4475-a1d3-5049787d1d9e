/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { PageParam } from "../../../common";
import { MallOrderStatus, mallOrderStatusFromJSON, mallOrderStatusToJSON } from "../../../common/business";

export const protobufPackage = "mall.pages";

/** 分页查询订单列表 */
export interface GetOrderListRequestParam {
  page:
    | PageParam
    | undefined;
  /** 指定订单状态进行过滤 */
  mallOrderStatusList: MallOrderStatus[];
}

/** 取消订单 */
export interface CancelOrderParam {
  /** 订单号 */
  orderNo: string;
  /** 取消理由id */
  cancelReasonId: number;
  /** 取消理由内容 */
  cancelReason: string;
  /** 取消订单备注 */
  cancelRemark: string;
}

/** 查询订单详情 */
export interface GetOrderDetailParam {
  /** 订单号 */
  orderNo: string;
}

/** 查询收银台信息，包括待支付金额、支付方式列表、是否已支付 */
export interface GetCashDeskInfoParam {
  /** 订单号 */
  orderNo: string;
  /** 支付单id */
  paymentId: string;
}

/** 提交支付 */
export interface SubmitPaymentParam {
  /** 订单号 */
  orderNo: string;
  /** 跳转收银台时后端返回的支付单id */
  paymentId: string;
  /** 支付金额 */
  amount: number;
  /** 支付方式 */
  payMethod: string;
}

/** 查询支付结果 */
export interface QueryPayResultParam {
  /** 订单号 */
  orderNo: string;
  /** 支付单id */
  paymentId: string;
}

/** 确认收货 */
export interface ConfirmReceiptParam {
  /** 订单号 */
  orderNo: string;
}

/** 未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息 */
export interface OpenCashDeskParam {
  /** 订单号 */
  orderNo: string;
  /** 待支付金额 */
  amount: number;
  /** 订单备注 */
  orderRemark: string;
  /** 线路id */
  transportId: string;
}

function createBaseGetOrderListRequestParam(): GetOrderListRequestParam {
  return { page: undefined, mallOrderStatusList: [] };
}

export const GetOrderListRequestParam = {
  encode(message: GetOrderListRequestParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(82).fork()).ldelim();
    }
    writer.uint32(162).fork();
    for (const v of message.mallOrderStatusList) {
      writer.int32(v);
    }
    writer.ldelim();
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderListRequestParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderListRequestParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag === 160) {
            message.mallOrderStatusList.push(reader.int32() as any);

            continue;
          }

          if (tag === 162) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.mallOrderStatusList.push(reader.int32() as any);
            }

            continue;
          }

          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderListRequestParam {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      mallOrderStatusList: globalThis.Array.isArray(object?.mallOrderStatusList)
        ? object.mallOrderStatusList.map((e: any) => mallOrderStatusFromJSON(e))
        : [],
    };
  },

  toJSON(message: GetOrderListRequestParam): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.mallOrderStatusList?.length) {
      obj.mallOrderStatusList = message.mallOrderStatusList.map((e) => mallOrderStatusToJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderListRequestParam>, I>>(base?: I): GetOrderListRequestParam {
    return GetOrderListRequestParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderListRequestParam>, I>>(object: I): GetOrderListRequestParam {
    const message = createBaseGetOrderListRequestParam();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.mallOrderStatusList = object.mallOrderStatusList?.map((e) => e) || [];
    return message;
  },
};

function createBaseCancelOrderParam(): CancelOrderParam {
  return { orderNo: "", cancelReasonId: 0, cancelReason: "", cancelRemark: "" };
}

export const CancelOrderParam = {
  encode(message: CancelOrderParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    if (message.cancelReasonId !== 0) {
      writer.uint32(160).int32(message.cancelReasonId);
    }
    if (message.cancelReason !== "") {
      writer.uint32(242).string(message.cancelReason);
    }
    if (message.cancelRemark !== "") {
      writer.uint32(322).string(message.cancelRemark);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CancelOrderParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCancelOrderParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.cancelReasonId = reader.int32();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.cancelReason = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.cancelRemark = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CancelOrderParam {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      cancelReasonId: isSet(object.cancelReasonId) ? globalThis.Number(object.cancelReasonId) : 0,
      cancelReason: isSet(object.cancelReason) ? globalThis.String(object.cancelReason) : "",
      cancelRemark: isSet(object.cancelRemark) ? globalThis.String(object.cancelRemark) : "",
    };
  },

  toJSON(message: CancelOrderParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.cancelReasonId !== 0) {
      obj.cancelReasonId = Math.round(message.cancelReasonId);
    }
    if (message.cancelReason !== "") {
      obj.cancelReason = message.cancelReason;
    }
    if (message.cancelRemark !== "") {
      obj.cancelRemark = message.cancelRemark;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CancelOrderParam>, I>>(base?: I): CancelOrderParam {
    return CancelOrderParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CancelOrderParam>, I>>(object: I): CancelOrderParam {
    const message = createBaseCancelOrderParam();
    message.orderNo = object.orderNo ?? "";
    message.cancelReasonId = object.cancelReasonId ?? 0;
    message.cancelReason = object.cancelReason ?? "";
    message.cancelRemark = object.cancelRemark ?? "";
    return message;
  },
};

function createBaseGetOrderDetailParam(): GetOrderDetailParam {
  return { orderNo: "" };
}

export const GetOrderDetailParam = {
  encode(message: GetOrderDetailParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetOrderDetailParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetOrderDetailParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetOrderDetailParam {
    return { orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "" };
  },

  toJSON(message: GetOrderDetailParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetOrderDetailParam>, I>>(base?: I): GetOrderDetailParam {
    return GetOrderDetailParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrderDetailParam>, I>>(object: I): GetOrderDetailParam {
    const message = createBaseGetOrderDetailParam();
    message.orderNo = object.orderNo ?? "";
    return message;
  },
};

function createBaseGetCashDeskInfoParam(): GetCashDeskInfoParam {
  return { orderNo: "", paymentId: "" };
}

export const GetCashDeskInfoParam = {
  encode(message: GetCashDeskInfoParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    if (message.paymentId !== "") {
      writer.uint32(162).string(message.paymentId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetCashDeskInfoParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCashDeskInfoParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.paymentId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCashDeskInfoParam {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "",
    };
  },

  toJSON(message: GetCashDeskInfoParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCashDeskInfoParam>, I>>(base?: I): GetCashDeskInfoParam {
    return GetCashDeskInfoParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCashDeskInfoParam>, I>>(object: I): GetCashDeskInfoParam {
    const message = createBaseGetCashDeskInfoParam();
    message.orderNo = object.orderNo ?? "";
    message.paymentId = object.paymentId ?? "";
    return message;
  },
};

function createBaseSubmitPaymentParam(): SubmitPaymentParam {
  return { orderNo: "", paymentId: "", amount: 0, payMethod: "" };
}

export const SubmitPaymentParam = {
  encode(message: SubmitPaymentParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    if (message.paymentId !== "") {
      writer.uint32(162).string(message.paymentId);
    }
    if (message.amount !== 0) {
      writer.uint32(241).double(message.amount);
    }
    if (message.payMethod !== "") {
      writer.uint32(322).string(message.payMethod);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SubmitPaymentParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSubmitPaymentParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.paymentId = reader.string();
          continue;
        case 30:
          if (tag !== 241) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.payMethod = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SubmitPaymentParam {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      payMethod: isSet(object.payMethod) ? globalThis.String(object.payMethod) : "",
    };
  },

  toJSON(message: SubmitPaymentParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.payMethod !== "") {
      obj.payMethod = message.payMethod;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SubmitPaymentParam>, I>>(base?: I): SubmitPaymentParam {
    return SubmitPaymentParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitPaymentParam>, I>>(object: I): SubmitPaymentParam {
    const message = createBaseSubmitPaymentParam();
    message.orderNo = object.orderNo ?? "";
    message.paymentId = object.paymentId ?? "";
    message.amount = object.amount ?? 0;
    message.payMethod = object.payMethod ?? "";
    return message;
  },
};

function createBaseQueryPayResultParam(): QueryPayResultParam {
  return { orderNo: "", paymentId: "" };
}

export const QueryPayResultParam = {
  encode(message: QueryPayResultParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    if (message.paymentId !== "") {
      writer.uint32(162).string(message.paymentId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryPayResultParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryPayResultParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.paymentId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryPayResultParam {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      paymentId: isSet(object.paymentId) ? globalThis.String(object.paymentId) : "",
    };
  },

  toJSON(message: QueryPayResultParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.paymentId !== "") {
      obj.paymentId = message.paymentId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryPayResultParam>, I>>(base?: I): QueryPayResultParam {
    return QueryPayResultParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryPayResultParam>, I>>(object: I): QueryPayResultParam {
    const message = createBaseQueryPayResultParam();
    message.orderNo = object.orderNo ?? "";
    message.paymentId = object.paymentId ?? "";
    return message;
  },
};

function createBaseConfirmReceiptParam(): ConfirmReceiptParam {
  return { orderNo: "" };
}

export const ConfirmReceiptParam = {
  encode(message: ConfirmReceiptParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ConfirmReceiptParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseConfirmReceiptParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ConfirmReceiptParam {
    return { orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "" };
  },

  toJSON(message: ConfirmReceiptParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ConfirmReceiptParam>, I>>(base?: I): ConfirmReceiptParam {
    return ConfirmReceiptParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfirmReceiptParam>, I>>(object: I): ConfirmReceiptParam {
    const message = createBaseConfirmReceiptParam();
    message.orderNo = object.orderNo ?? "";
    return message;
  },
};

function createBaseOpenCashDeskParam(): OpenCashDeskParam {
  return { orderNo: "", amount: 0, orderRemark: "", transportId: "" };
}

export const OpenCashDeskParam = {
  encode(message: OpenCashDeskParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(82).string(message.orderNo);
    }
    if (message.amount !== 0) {
      writer.uint32(161).double(message.amount);
    }
    if (message.orderRemark !== "") {
      writer.uint32(242).string(message.orderRemark);
    }
    if (message.transportId !== "") {
      writer.uint32(322).string(message.transportId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): OpenCashDeskParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseOpenCashDeskParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 20:
          if (tag !== 161) {
            break;
          }

          message.amount = reader.double();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.orderRemark = reader.string();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.transportId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): OpenCashDeskParam {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      orderRemark: isSet(object.orderRemark) ? globalThis.String(object.orderRemark) : "",
      transportId: isSet(object.transportId) ? globalThis.String(object.transportId) : "",
    };
  },

  toJSON(message: OpenCashDeskParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.orderRemark !== "") {
      obj.orderRemark = message.orderRemark;
    }
    if (message.transportId !== "") {
      obj.transportId = message.transportId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<OpenCashDeskParam>, I>>(base?: I): OpenCashDeskParam {
    return OpenCashDeskParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OpenCashDeskParam>, I>>(object: I): OpenCashDeskParam {
    const message = createBaseOpenCashDeskParam();
    message.orderNo = object.orderNo ?? "";
    message.amount = object.amount ?? 0;
    message.orderRemark = object.orderRemark ?? "";
    message.transportId = object.transportId ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
