<template>
  <div class="!bg-[#f5f3f3]" v-if="pageData.showErrorPage">
    <div class="wrapper">
      <div class="bg-white">
        <search-card></search-card>
      </div>
      <div
        class="cwidth mx-auto text-[#555] bg-white p-[50px] px-[100px] min-h-[50vh] mt-[20px]"
      >
        <div class="flex items-center">
          <img loading="lazy"
            src="@/assets/icons/submitError.svg"
            alt="error"
            class="w-[40px] mr-3"
          />
          <div class="text-[28px] font-medium text-[#333]">
            {{ authStore.i18n("cm_common.linkExpired") }}
          </div>
        </div>

        <div class="mt-[30px]">
          <n-button
            color="#E50113"
            text-color="#fff"
            :loading="pageData.loading"
            @click="resendVerification(true)"
            class="rounded-[4px] w-[260px] h-[40px] text-[16px] mt-2 mx-auto"
          >
            <div>{{ authStore.i18n("cm_common_emailActivateAgain") }}</div>
          </n-button>
        </div>

        <div class="border-t-1 mt-[50px]">
          <div class="mt-[20px] mb-[12px] text-[18px] font-medium">
            {{ authStore.i18n("cm_common.verificationEmail") }}
          </div>
          <ul>
            <n-space vertical :style="{ gap: '6px 0' }">
              <li>
                {{ authStore.i18n("cm_common.spamCheck") }}
              </li>
              <li>{{ authStore.i18n("cm_common.deliveryDelay") }}</li>
              <li>
                {{ authStore.i18n("cm_common.verificationDelay") }}
                <span
                  @click="resendVerification"
                  class="text-[#636ded] cursor-pointer"
                >
                  {{ authStore.i18n("cm_common.resendVerification") }}
                </span>
              </li>
            </n-space>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<any>({});
userInfo.value = config.public.userInfo as object;

const pageData = reactive({
  scene: null,
  verifyResult: null,
  loading: false,
  showErrorPage: false,
});

useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});

onVerifyMail();
// 激活邮箱链接
async function onVerifyMail() {
  const res: any = await useVerifyMail({
    id: route.query?.verifyId,
  });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    if (pageData.verifyResult !== "EXPIRED") {
      window?.MyStat?.addPageEvent(
        "passport_mail_verify_success",
        `邮箱验证成功：${pageData.verifyResult}`,
        true
      ); // 埋点
      navigateTo("/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_mail_verify_error",
        `邮箱验证失败：${pageData.verifyResult}`
      ); // 埋点
      pageData.showErrorPage = true;
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_mail_verify_error",
      `邮箱验证失败：${res?.result?.message}`
    ); // 埋点
    pageData.showErrorPage = true;
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

// 发送验证邮箱的邮件
async function resendVerification(isGoEmail?: boolean) {
  if (isGoEmail) {
    pageData.loading = true;
  }
  const res: any = await useSendVerifyMail({
    verifyMailScene: pageData.scene,
  });
  pageData.loading = false;
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.replace("/user/coupon");
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      showToast(authStore.i18n("cm_common.resendSuccess"));
      if (isGoEmail) {
        // 跳转邮箱
        navigateToEmail();
      }
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  min-height: 70vh;
  width: 100%;
}
.page-content {
  background: url("@/assets/icons/submitSuccess.svg") no-repeat 40px 50px;
  background-size: 50px 50px;
  box-sizing: border-box;
  margin: 0 auto;
  padding: 50px 50px 30px 110px;
  position: relative;
  background-color: #fff;
  min-height: 60vh;
  margin-top: 20px;
}
ul {
  list-style-type: disc;
  padding-left: 16px;
}
</style>
