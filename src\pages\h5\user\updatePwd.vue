<template>
  <div class="bg-white min-h-[100vh] pt-[1rem]">
    <div
      class="w-full border-b-1 border-solid border-gray-200 py-[0.2rem] items-center justify-center fixed top-0 bg-white"
    >
      <icon-card
        color="#555"
        size="0.48rem"
        name="ep:arrow-left-bold"
        class="fixed left-[0.2rem]"
        data-spm-box="navigation-back-icon"
        @click="onBackClick"
      ></icon-card>
      <div class="text-center font-medium text-[0.36rem] leading-[0.56rem]">
        {{ authStore.i18n("cm_user.updatePwd") }}
      </div>
    </div>

    <n-form
      :rules="rules"
      ref="updateFromRef"
      :model="pageData.updateFrom"
      class="px-[0.32rem] mt-[0.4rem]"
    >
      <n-form-item
        path="oldPassword"
        :label="authStore.i18n('cm_common.oldPassword')"
        class="flex-col"
      >
        <n-input
          v-trim
          clearable
          @keydown.enter.prevent
          :type="pageData.showOldPwd ? 'text' : 'password'"
          v-model:value="pageData.updateFrom.oldPassword"
          :placeholder="authStore.i18n('cm_common.inputOldPwd')"
        >
          <template #suffix>
            <icon-card
              size="24"
              color="#7F7F7F"
              class="cursor-pointer"
              :name="
                pageData.showOldPwd
                  ? 'weui:eyes-on-outlined'
                  : 'weui:eyes-off-outlined'
              "
              @click="pageData.showOldPwd = !pageData.showOldPwd"
            /> </template
        ></n-input>
      </n-form-item>

      <n-form-item
        path="newPassword"
        :label="authStore.i18n('cm_common.newPassword')"
      >
        <n-input
          v-trim
          clearable
          @keydown.enter.prevent
          :type="pageData.showNewPwd ? 'text' : 'password'"
          v-model:value="pageData.updateFrom.newPassword"
          :placeholder="authStore.i18n('cm_common.inputNewPwd')"
        >
          <template #suffix>
            <icon-card
              size="24"
              color="#7F7F7F"
              class="cursor-pointer"
              :name="
                pageData.showNewPwd
                  ? 'weui:eyes-on-outlined'
                  : 'weui:eyes-off-outlined'
              "
              @click="pageData.showNewPwd = !pageData.showNewPwd"
            /> </template
        ></n-input>
      </n-form-item>
      <n-form-item
        path="conPassword"
        :label="authStore.i18n('cm_common.conPassword')"
      >
        <n-input
          v-trim
          clearable
          @keydown.enter.prevent
          :type="pageData.showConPwd ? 'text' : 'password'"
          v-model:value="pageData.updateFrom.conPassword"
          :placeholder="authStore.i18n('cm_common.inputConPwd')"
        >
          <template #suffix>
            <icon-card
              size="24"
              color="#7F7F7F"
              class="cursor-pointer"
              :name="
                pageData.showConPwd
                  ? 'weui:eyes-on-outlined'
                  : 'weui:eyes-off-outlined'
              "
              @click="pageData.showConPwd = !pageData.showConPwd"
            /> </template
        ></n-input>
      </n-form-item>
      <span class="text-[#0078FF] float-right text-[0.28rem]" @click="onForgetPwdClick">
        {{ authStore.i18n("cm_common.forgotMyPwd") }}
      </span>
      <div class="flex justify-center mt-[1rem]">
        <n-button
          size="large"
          color="#E50113"
          text-color="#fff"
          class="rounded-[0.6rem] text-[0.32rem] w-[96%] h-[0.6rem]"
          @click="onUpdatePwd"
        >
          {{ authStore.i18n("cm_common.submitOpt") }}
        </n-button>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { useMessage } from "naive-ui";
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";
const route = useRoute();
const router = useRouter();
const message = useMessage();
const authStore = useAuthStore();
const updateFromRef = ref<FormInst | null>(null);

const pageData = reactive({
  updateFrom: <any>{
    oldPassword: "",
    newPassword: "",
    conPassword: "",
  },
  showOldPwd: false,
  showNewPwd: false,
  showConPwd: false,
});

const rules: FormRules = {
  oldPassword: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_common.inputOldPwd"),
  },
  newPassword: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      const specialCharPattern = /[^A-Za-z\d]/; // 特殊字符匹配
      const pattern = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$/; // 8-16位包含数字及字母
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputNewPwd"));
      }
      // 校验是否包含特殊字符
      else if (specialCharPattern.test(value)) {
        return new Error(authStore.i18n("cm_common.pwdFormatTips"));
      }
      // 校验8-16位包含数字及字母
      else if (!pattern.test(value)) {
        return new Error(authStore.i18n("cm_common.pwdLengthTips"));
      }
      return true;
    },
  },
  conPassword: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any) {
      if (!value) {
        return new Error(authStore.i18n("cm_common.inputConPwd"));
      } else if (value !== pageData.updateFrom.newPassword) {
        return new Error(authStore.i18n("cm_common.conPwdTips"));
      }
      return true;
    },
  },
};

const userInfo = computed(() => useAuthStore().getUserInfo);

function onForgetPwdClick() {
  navigateToPage(
    "/h5/user/modifyPwd",
    { ...route.query, pageSource: window.location.href },
    false
  );
}

// 修改密码
async function onUpdatePwd() {
  await updateFromRef.value?.validate();
  try {
    const res: any = await useUpdatePassword(pageData.updateFrom);
    if (res?.result?.code === 200) {
      showToast(authStore.i18n("cm_common.updatePwdSuccess"));
      setTimeout(() => {
        onLogin({
          username: userInfo.value?.username,
          password: pageData.updateFrom.newPassword,
        });
      }, 3000);
    } else {
      showToast(res.result?.message);
    }
  } catch (error) {
    showToast(error);
  }
}

// 登录
async function onLogin(params: any) {
  try {
    const res: any = await useLogin(params);
    if (res?.result?.code === 200) {
      await authStore.setUserInfo(res?.data);
      window.location.reload();
    } else {
      showToast(res.result?.message);
    }
  } catch (error) {
    showToast(error);
  }
}

// 返回
function onBackClick() {
  router.go(-1);
}
</script>
<style scoped lang="scss">
:deep(.n-form-item .n-form-item-blank) {
  display: flex;
  flex-direction: column;
  align-items: end;
}
:deep(.n-input__input-el) {
  height: 0.96rem;
}
:deep(.n-input .n-input__placeholder) {
  font-size: 0.24rem;
}
:deep(.n-form-item-label__text){
  font-size: 0.28rem;
}
</style>
