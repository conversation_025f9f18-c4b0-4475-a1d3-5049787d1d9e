/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { CouponUsableDetailModel } from "../../../chilat/basis/model/coupon_usable_model";
import {
  CouponTypeStatus,
  couponTypeStatusFromJSON,
  couponTypeStatusToJSON,
} from "../../../chilat/coupon/coupon_common";
import { TicketStatus, ticketStatusFromJSON, ticketStatusToJSON } from "../../../chilat/coupon/coupon_detail_common";
import { PageParam } from "../../../common";

export const protobufPackage = "mall.marketing";

export interface MallMyCouponDetailParam {
  /** 分页参数 */
  page:
    | PageParam
    | undefined;
  /** 优惠券状态 */
  ticketStatus: TicketStatus;
  /** 产品券：couponTypeProduct ,佣金券：couponTypeCommission */
  couponType: CouponTypeStatus;
}

/** 可使用优惠券参数 */
export interface MallCouponUsableParam {
  /** 订单号 */
  orderNo: string;
  /** 优惠券类型: couponTypeProduct 产品券 couponTypeCommission 佣金券 */
  couponType: CouponTypeStatus;
}

/** 检查可使用优惠券校验参数 */
export interface MallCouponCheckParam {
  /** 订单号 */
  orderNo: string;
  /** 选中优惠券集合 */
  selectCouponModelsList: CouponUsableDetailModel[];
  /** 未选中优惠券集合 */
  notCouponModelsList: CouponUsableDetailModel[];
}

function createBaseMallMyCouponDetailParam(): MallMyCouponDetailParam {
  return { page: undefined, ticketStatus: 0, couponType: 0 };
}

export const MallMyCouponDetailParam = {
  encode(message: MallMyCouponDetailParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.page !== undefined) {
      PageParam.encode(message.page, writer.uint32(10).fork()).ldelim();
    }
    if (message.ticketStatus !== 0) {
      writer.uint32(16).int32(message.ticketStatus);
    }
    if (message.couponType !== 0) {
      writer.uint32(24).int32(message.couponType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MallMyCouponDetailParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMallMyCouponDetailParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.page = PageParam.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.ticketStatus = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.couponType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MallMyCouponDetailParam {
    return {
      page: isSet(object.page) ? PageParam.fromJSON(object.page) : undefined,
      ticketStatus: isSet(object.ticketStatus) ? ticketStatusFromJSON(object.ticketStatus) : 0,
      couponType: isSet(object.couponType) ? couponTypeStatusFromJSON(object.couponType) : 0,
    };
  },

  toJSON(message: MallMyCouponDetailParam): unknown {
    const obj: any = {};
    if (message.page !== undefined) {
      obj.page = PageParam.toJSON(message.page);
    }
    if (message.ticketStatus !== 0) {
      obj.ticketStatus = ticketStatusToJSON(message.ticketStatus);
    }
    if (message.couponType !== 0) {
      obj.couponType = couponTypeStatusToJSON(message.couponType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MallMyCouponDetailParam>, I>>(base?: I): MallMyCouponDetailParam {
    return MallMyCouponDetailParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallMyCouponDetailParam>, I>>(object: I): MallMyCouponDetailParam {
    const message = createBaseMallMyCouponDetailParam();
    message.page = (object.page !== undefined && object.page !== null) ? PageParam.fromPartial(object.page) : undefined;
    message.ticketStatus = object.ticketStatus ?? 0;
    message.couponType = object.couponType ?? 0;
    return message;
  },
};

function createBaseMallCouponUsableParam(): MallCouponUsableParam {
  return { orderNo: "", couponType: 0 };
}

export const MallCouponUsableParam = {
  encode(message: MallCouponUsableParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(10).string(message.orderNo);
    }
    if (message.couponType !== 0) {
      writer.uint32(16).int32(message.couponType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MallCouponUsableParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMallCouponUsableParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.couponType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MallCouponUsableParam {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      couponType: isSet(object.couponType) ? couponTypeStatusFromJSON(object.couponType) : 0,
    };
  },

  toJSON(message: MallCouponUsableParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.couponType !== 0) {
      obj.couponType = couponTypeStatusToJSON(message.couponType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MallCouponUsableParam>, I>>(base?: I): MallCouponUsableParam {
    return MallCouponUsableParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallCouponUsableParam>, I>>(object: I): MallCouponUsableParam {
    const message = createBaseMallCouponUsableParam();
    message.orderNo = object.orderNo ?? "";
    message.couponType = object.couponType ?? 0;
    return message;
  },
};

function createBaseMallCouponCheckParam(): MallCouponCheckParam {
  return { orderNo: "", selectCouponModelsList: [], notCouponModelsList: [] };
}

export const MallCouponCheckParam = {
  encode(message: MallCouponCheckParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.orderNo !== "") {
      writer.uint32(10).string(message.orderNo);
    }
    for (const v of message.selectCouponModelsList) {
      CouponUsableDetailModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.notCouponModelsList) {
      CouponUsableDetailModel.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MallCouponCheckParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMallCouponCheckParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.orderNo = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.selectCouponModelsList.push(CouponUsableDetailModel.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.notCouponModelsList.push(CouponUsableDetailModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MallCouponCheckParam {
    return {
      orderNo: isSet(object.orderNo) ? globalThis.String(object.orderNo) : "",
      selectCouponModelsList: globalThis.Array.isArray(object?.selectCouponModelsList)
        ? object.selectCouponModelsList.map((e: any) => CouponUsableDetailModel.fromJSON(e))
        : [],
      notCouponModelsList: globalThis.Array.isArray(object?.notCouponModelsList)
        ? object.notCouponModelsList.map((e: any) => CouponUsableDetailModel.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MallCouponCheckParam): unknown {
    const obj: any = {};
    if (message.orderNo !== "") {
      obj.orderNo = message.orderNo;
    }
    if (message.selectCouponModelsList?.length) {
      obj.selectCouponModelsList = message.selectCouponModelsList.map((e) => CouponUsableDetailModel.toJSON(e));
    }
    if (message.notCouponModelsList?.length) {
      obj.notCouponModelsList = message.notCouponModelsList.map((e) => CouponUsableDetailModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MallCouponCheckParam>, I>>(base?: I): MallCouponCheckParam {
    return MallCouponCheckParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallCouponCheckParam>, I>>(object: I): MallCouponCheckParam {
    const message = createBaseMallCouponCheckParam();
    message.orderNo = object.orderNo ?? "";
    message.selectCouponModelsList =
      object.selectCouponModelsList?.map((e) => CouponUsableDetailModel.fromPartial(e)) || [];
    message.notCouponModelsList = object.notCouponModelsList?.map((e) => CouponUsableDetailModel.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
