syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_info_param.proto";
import "chilat/commodity/param/goods_stock_param.proto";
import "chilat/commodity/model/goods_stock_model.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

// 商品库存管理
service GoodsStock {
//  // 查询列表
//  rpc listPage (GoodsInfoPageQueryParam) returns (GoodsStockPageResp);
//  // 查询商品库存详情（参数为：商品ID）
//  rpc getGoodsStock (common.IdParam) returns (GoodsStockDetailResp);
//  // 查询商品库存详情（参数为：商品ID）
//  rpc getGoodsSkuStock (common.IdParam) returns (GoodsSkuStockResp);
  // 保存库存信息
  rpc saveStock (GoodsStockSaveParam) returns (common.ApiResult);
//  // 查询日志（根据SKU ID查询）
//  rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
}