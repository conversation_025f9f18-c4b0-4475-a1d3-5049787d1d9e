syntax = "proto3";
package chilat.basis;


option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/sales_order_model.proto";
import "chilat/basis/param/sales_order_param.proto";
import "common.proto";

// 订单服务
service SalesOrder {
    // 获取订单创建页的选项（查询费用选项列表等）
    rpc getPageOptions (common.EmptyParam) returns (SalesOrderPageOptionResp);
    //开单
    rpc createSo(SalesOrderCreateParam) returns (SalesOrderCreateResp);
    //开单预览（先用于计算阶梯价，传：SalesOrderLineParam soLineList，返回：SalesOrderLineModel soLineList）
    rpc previewSo(SalesOrderCreateParam) returns (SalesOrderDetailResp);
    //订单列表
    rpc pageList(SalesOrderPageParam) returns (SalesOrderPageListResp);
    //订单详情
    rpc soDetail(common.IdParam) returns (SalesOrderDetailResp);
    //开单
    rpc updateSo(SalesOrderCreateParam) returns (common.ApiResult);
    //取消订单
    rpc cancelSo(SalesOrderCancelParam) returns (common.ApiResult);

    //订单审核
    rpc auditSo(common.IdParam) returns (common.ApiResult);

    //导出PI给业务员（含采购价）
    rpc exportPiToStaff(common.IdParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };
    //发送开单邮件给客户
    rpc sendCreateOrderByStaffMail(common.IdParam) returns (common.ApiResult);

    //查询离线支付信息
    rpc queryOffLinePayInfo (QueryOffLinePayInfoParam) returns (QueryOffLinePayInfoResp);

    //提交离线支付
    rpc submitOffLinePay (SubmitOffLinePayParam) returns (common.ApiResult);
    
    //获取商城订单详情预览链接
    rpc getMallOrderDetailPreviewUrl(OrderNoParam) returns (MallOrderDetailPreviewResp);

    //业务员列表
    rpc getSalesManList (common.IdParam) returns (GetSalesManListResp);

    //从2.0订单获取采购单下单所需的商品数据
    rpc getPurchaseDataBySalesOrder (common.IdParam) returns (PurchaseDataBySalesOrderResp);

    //从2.0订单创建采购单
    rpc createPurchaseBySalesOrder (CreatePurchaseBySalesOrderParam) returns (common.ApiResult);

    // 根据orderNo获取可用的状态列表----输入为orderNo
    rpc getValidOrderStatusByOrderNoList (common.IdParam) returns (GetOrderStatusListResp);

    // 修改订单状态
    rpc updateOrderStatus (UpdateOrderStatusParam) returns (common.ApiResult);
}