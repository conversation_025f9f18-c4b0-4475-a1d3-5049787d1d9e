<template>
  <!-- 福利明细抽屉 -->
  <n-drawer
    v-model:show="pageData.dialogVisible"
    width="100%"
    placement="bottom"
    :trap-focus="false"
    default-height="11.12rem"
  >
    <n-drawer-content closable>
      <template #header>
        <div class="flex justify-center gap-[0.08rem] items-center">
          <img
            alt="d"
            loading="lazy"
            class="w-[0.6rem] flex-shrink-0 mt-[-0.16rem]"
            src="@/assets/icons/home/<USER>"
          />
          <span
            class="text-[0.4rem] leading-[0.44rem] text-[#8C111B] font-normal"
            >esglose de beneficios</span
          >
        </div>
      </template>

      <!-- 福利明细内容 -->
      <div
        class="benefit-content relative pt-[0.44rem] pb-[0.44rem] px-[0.4rem] overflow-hidden"
      >
        <img
          alt="bg"
          loading="lazy"
          class="absolute top-[2.04rem] right-[-1.66rem] w-[5.6rem]"
          src="@/assets/icons/home/<USER>"
        />
        <div
          v-for="(item, index) in props.benefitData"
          :key="index"
          class="benefit-item flex items-start gap-[0.32rem] mb-[0.56rem] last:mb-0"
        >
          <!-- 图标 -->
          <div
            class="w-[0.72rem] h-[0.72rem] bg-[#8C111B] rounded-full flex-shrink-0 flex items-center justify-center"
          >
            <img
              :src="item.icon"
              :alt="item.title"
              loading="lazy"
              class="w-[0.42rem] h-[0.42rem]"
            />
          </div>

          <!-- 内容 -->
          <div class="flex-1">
            <h3
              class="text-[0.36rem] leading-[0.36rem] text-[#333] mb-[0.16rem]"
            >
              {{ item.title }}
            </h3>
            <p class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F]">
              {{ item.desc }}
            </p>
          </div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
const props = defineProps({
  benefitData: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive({
  dialogVisible: false,
});

const openDrawer = () => {
  pageData.dialogVisible = true;
};

const closeDrawer = () => {
  pageData.dialogVisible = false;
};

defineExpose({
  openDrawer,
  closeDrawer,
});
</script>

<style lang="scss" scoped>
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
}
:deep(.n-drawer-header) {
  padding: 0.34rem 0.36rem !important;
  border-bottom: none !important;
}
:deep(.n-base-icon svg) {
  width: 0.32rem !important;
  height: 0.32rem !important;
}
:deep(.n-base-close) {
  color: #333 !important;
}
</style>
