syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "common.proto";
import "mall/pages/model/home_page_model.proto";
import "chilat/basis/model/goods_model.proto";
import "mall/pages/param/homepage_param.proto";


// 首页
service HomePage {
  // 获取页面数据
  rpc getPageData (common.EmptyParam) returns (HomePageResp);
  // 推荐商品
  rpc recommendGoods(common.EmptyParam) returns (chilat.basis.MidRecommendGoodsResp);
  // 首页商品
  rpc homePageGoods(common.EmptyParam) returns (chilat.basis.HomePageGoodsResp);
  // pc首页
  rpc pcHomePageGoods(common.EmptyParam) returns (chilat.basis.PcHomePageGoodsResp);
  // pc首页分类
  rpc homePageCategory(common.EmptyParam) returns (chilat.basis.HomePageCategoryResp);
  // 推荐商品v2,包含义乌热销和美客多热销
  rpc recommendGoodsV2(RecommendGoodsParam) returns (RecommendGoodsV2Resp);
}
