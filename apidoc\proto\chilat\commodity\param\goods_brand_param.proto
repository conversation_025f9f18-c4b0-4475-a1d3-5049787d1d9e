syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";

// 品牌列表参数
message GoodsBrandPageQueryParam {
  common.PageParam page = 1;
  GoodsBrandQueryParam query = 2;
}

// 品牌列表查询参数
message GoodsBrandQueryParam {
  string id = 1; // 品牌ID
  string brandName = 2; //品牌名称
  string categoryId = 3; //分类ID
  int64 startCdate = 4; // 开始创建时间
  int64 endCdate = 5; // 结束创建时间
}

// 品牌保存信息
message GoodsBrandSaveParam {
  string id = 1; //品牌ID
  int32 idx = 2; //顺序
  string brandName = 3; //品牌名称
  string firstLettler = 4; //首字母大写
  string spellLettler = 5; //字母简拼
  string groupLettler = 6; //所属字母分组
  string brandAlias = 7; //品牌别名
  string brandLogo = 8; //品牌logo
  string brandRemark = 9; //品牌描述
  string brandSiteUrl = 10; //品牌官网URL
  string brandStory = 11; //品牌故事
  repeated string categoryIds = 12; //类目id列表
  bool enabled = 13; //是否启用
}
