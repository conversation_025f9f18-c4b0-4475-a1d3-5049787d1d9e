<template>
  <div class="mobile-container">
    <seo-data :pageData="pageData"></seo-data>
    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>
    <div v-if="!pageData.isLoadingUser">
      <!-- 已登录首页 -->
      <div v-if="userInfo?.username">
        <!-- 轮播 -->
        <!-- <n-carousel
          autoplay
          :transition-style="{ transitionDuration: '500ms' }"
        >
          <n-image
            lazy
            preview-disabled
            class="carousel-img"
            :src="carousel.image"
            v-for="(carousel, index) in carouselData"
            :key="index"
          />
        </n-carousel> -->
        <div class="news-wrapper" data-spm-box="homepage-welcome-bar">
          <a v-for="(news, index) in newsData" :key="index" :href="news.path">
            <div
              class="news-item"
              :style="`background-color: ${cateColorArr[index].cateColor}`"
            >
              <div>{{ news.title }}</div>
              <icon-card
                :name="news.icon"
                size="30"
                color="#FFF"
              ></icon-card></div
          ></a>
        </div>
        <!-- 美客多和义乌推荐 -->
        <div
          v-if="pageData.mercadoHotSaleGoods?.goodsList?.length > 0"
          class="mt-[0.48rem]"
          data-spm-box="homepage-hot-mercado"
        >
          <n-image
            lazy
            preview-disabled
            class="carousel-img pb-1"
            :src="pageData.mercadoHotSaleGoods.banner"
            @click="onMercadoHotViewMore(pageData.mercadoHotSaleGoods, $event)"
          />
          <category-card
            :moreButton="true"
            :cateInfo="pageData.mercadoHotSaleGoods"
            data-spm-box="homepage-hot-mercado"
          ></category-card>
        </div>
        <div
          v-if="pageData.yiwuHotSaleGoods?.goodsList?.length > 0"
          data-spm-box="homepage-hot-yiwu"
        >
          <n-image
            lazy
            preview-disabled
            class="carousel-img pb-1"
            :src="pageData.yiwuHotSaleGoods.banner"
            @click="onYiWuHotViewMore(pageData.yiwuHotSaleGoods, $event)"
          />
          <category-card
            :moreButton="true"
            :cateInfo="pageData.yiwuHotSaleGoods"
            data-spm-box="homepage-hot-yiwu"
          ></category-card>
        </div>

        <!-- 推荐商品 -->
        <category-card
          :cateInfo="pageData.recommendGoods"
          data-spm-box="homepage-recommend-goods"
        ></category-card>
        <!-- 分类 -->
        <div class="mt-[0.32rem]">
          <category-card
            v-for="(cate, index) in pageData.categoryGoods"
            :key="index"
            :cateInfo="cate"
            :cateColor="cateColorArr[index]"
            data-spm-box="homepage-recommend-category"
            v-bind:data-spm-index="index + 1"
          ></category-card>
        </div>
      </div>
      <!-- 未登录首页 -->
      <guest-home
        v-if="!userInfo?.username"
        :yiwuHotSaleGoods="pageData.yiwuHotSaleGoods"
        :mercadoHotSaleGoods="pageData.mercadoHotSaleGoods"
      ></guest-home>

      <!-- 底部信息 -->
      <mobile-page-footer></mobile-page-footer>
    </div>
    <div v-show="pageData.isLoadingUser" class="loading-overlay">
      <n-spin stroke="#e50113" :show="pageData.isLoadingUser"> </n-spin>
    </div>

    <!-- 底部栏 -->
    <mobile-tab-bar :naiveBar="0" />
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import GuestHome from "./components/GuestHome.vue";
import CategoryCard from "./components/CategoryCard.vue";
// import homeMCarousel1 from "@/assets/icons/homeMCarousel1.jpg";
// import homeMCarousel2 from "@/assets/icons/homeMCarousel2.jpg";
// import homeMCarousel3 from "@/assets/icons/homeMCarousel3.jpg";
// import homeMCarousel4 from "@/assets/icons/homeMCarousel4.png";
// import homeMCarousel5 from "@/assets/icons/homeMCarousel5.png";
import mercadoHotBanner from "@/assets/icons/mercadoHotH5Banner.jpg";
import yiwuHotBanner from "@/assets/icons/yiwuHotH5Banner.jpg";

const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const userInfo = ref(null);
const pageData = reactive(<any>{
  keyword: "",
  showInputButton: false,
  recommendGoods: <any>{},
  categoryGoods: <any>[],
  isLoadingUser: true,
  mercadoHotSaleGoods: <any>[],
  yiwuHotSaleGoods: <any>[],
});

// const carouselData = [
//   {
//     image: homeMCarousel1,
//   },
//   {
//     image: homeMCarousel4,
//   },
//   {
//     image: homeMCarousel5,
//   },
//   {
//     image: homeMCarousel2,
//   },
//   {
//     image: homeMCarousel3,
//   },
// ];

const newsData = [
  {
    icon: "mdi:information-variant-circle",
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/h5/article/about-us",
  },
  {
    icon: "mdi:compass",
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/h5/article/quick-guide",
  },
  {
    icon: "material-symbols:help",
    title: authStore.i18n("cm_news.askedQuestions"),
    path: `/h5/article/frequently-questions`,
  },
  // {
  //   icon: "material-symbols:security",
  //   title: authStore.i18n("cm_news.warrantyService"),
  //   path: "/h5/article?code=10002",
  // },
  {
    icon: "f7:money-dollar-circle-fill",
    title: authStore.i18n("cm_news.commission"),
    path: "/h5/article/commission",
  },
];

const cateColorArr = reactive(<any>[
  {
    textColor: "#FFF",
    cateColor: "#ff3242",
  },
  {
    textColor: "#FFF",
    cateColor: "#ff5e6a",
  },
  {
    textColor: "#FFF",
    cateColor: "#ff7659",
  },
  {
    textColor: "#FFF",
    cateColor: "#ffae42",
  },
  {
    textColor: "#FFF",
    cateColor: "#ffd700",
  },
  {
    textColor: "#FFF",
    cateColor: "#7a7687",
  },
  {
    textColor: "#FFF",
    cateColor: "#9ec8c7",
  },
  {
    textColor: "#FFF",
    cateColor: "#548fad",
  },
  {
    textColor: "#FFF",
    cateColor: "#99aabe",
  },
  {
    textColor: "#FFF",
    cateColor: "#4b928e",
  },
  {
    textColor: "#FFF",
    cateColor: "#8f73c0",
  },
  {
    textColor: "#FFF",
    cateColor: "#4671f3",
  },
  {
    textColor: "#FFF",
    cateColor: "#314a95",
  },
  {
    textColor: "#FFF",
    cateColor: "#66cc99",
  },
  {
    textColor: "#FFF",
    cateColor: "#993366",
  },
]);

authStore.getCartList();
onHomePageData();

onBeforeMount(async () => {
  await getUserInfo(); //判断是显示未登录首页 还是登录首页
  onRecommendGoods();
  onHomePageGoods();
});

onMounted(() => {
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
});

function getUserInfo() {
  pageData.isLoadingUser = true;
  const cachedAuthData = localStorage.getItem("use-auth");
  if (cachedAuthData) {
    const parsedAuthData = JSON.parse(cachedAuthData);
    if (parsedAuthData.userInfo) {
      userInfo.value = parsedAuthData.userInfo;
    }
  }
  pageData.isLoadingUser = false;
}

async function onHomePageData() {
  const res: any = await useHomePageData({});
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
  }
}

// 推荐商品
async function onRecommendGoods() {
  const res: any = await useRecommendGoodsV2({
    goodsCount: 24,
    deviceType: 2,
  });
  if (res?.result?.code === 200) {
    pageData.recommendGoods.goodsList = res.data?.recommendGoods;
    pageData.recommendGoods.categoryName = authStore.i18n(
      "cm_home.recommendGoods"
    );
    pageData.mercadoHotSaleGoods = {
      banner: mercadoHotBanner,
      categoryId: res.data?.h5MercadoHotSaleGoods?.categoryId,
      categoryName: res.data?.h5MercadoHotSaleGoods?.categoryName,
      tagId: res.data?.h5MercadoHotSaleGoods?.tagId,
      goodsList: res.data?.h5MercadoHotSaleGoods?.hotSaleGoods,
    };

    pageData.yiwuHotSaleGoods = {
      banner: yiwuHotBanner,
      categoryId: res.data?.h5YiwuHotSaleGoods?.categoryId,
      categoryName: res.data?.h5YiwuHotSaleGoods?.categoryName,
      tagId: res.data?.h5YiwuHotSaleGoods?.tagId,
      goodsList: res.data?.h5YiwuHotSaleGoods?.hotSaleGoods,
    };
  }
}

function onMercadoHotViewMore(item: any, event: any) {
  navigateToPage(
    "/h5/search/list",
    { tagId: item.tagId, tag: "mercado" },
    false,
    event
  );
}

function onYiWuHotViewMore(item: any, event: any) {
  navigateToPage(
    "/h5/search/list",
    { tagId: item.tagId, tag: "yiwu" },
    false,
    event
  );
}

// 商品分类
async function onHomePageGoods() {
  const res: any = await useHomePageGoods({});
  if (res?.result?.code === 200) {
    pageData.categoryGoods = res?.data;
  }
}

function onKeywordClick(event: any) {
  const keyword = pageData.keyword?.trim();
  window?.MyStat?.addPageEvent("click_search", `搜索关键词：${keyword}`); // 埋点

  navigateToPage(
    `/h5/search/list`,
    {
      keyword,
    },
    false,
    event
  );
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  padding-bottom: 1.6rem;
  overflow-y: auto;
  background-color: #f6f6f6;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-wrapper {
  display: flex;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  padding-left: 0.26667rem;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  margin-top: 0.48rem;
  &::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }
  .news-item {
    display: inline-block;
    flex: 0 0 2rem;
    height: 1.28rem;
    border-radius: 0.08rem;
    margin-right: 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0.2rem;
    div {
      width: 1.8rem;
      height: 0.64rem;
      font-size: 0.26rem;
      font-weight: 500;
      color: #fff;
      line-height: 0.4rem;
      white-space: normal;
    }
  }
}
.loading-overlay {
  position: fixed;
  margin: 0;
  top: 2rem;
  right: 0;
  bottom: 1.3rem;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
</style>
