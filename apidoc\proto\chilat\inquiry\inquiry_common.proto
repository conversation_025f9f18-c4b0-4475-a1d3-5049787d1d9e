syntax = "proto3";
package chilat.inquiry;


option java_package = "com.chilat.rpc.inquiry.common";

/*------------------------------ 业务相关公共参数 ------------------------------*/
// 询价列表里的询价状态
enum InquiryStatus {
  NO_INQUIRY = 0; //未询价
  PROGRESS_INQUIRY = 1; //询价中
  HAS_INQUIRY = 2; //已询价
}
// sku状态
enum SkuStatus {
  NO_SKU_INQUIRY = 0; //sku未询价
  HAS_SKU_INQUIRY = 1; //sku已询价
}


// 询盘列表的询价状态
enum InquiryXpState {
  NO_XP_INQUIRY = 0; //未询价
  PROGRESS_XP_INQUIRY = 1; //询价中
  HAS_XP_INQUIRY = 2; //询价完成（待开单）
  HAS_XP_ORDER_INQUIRY = 3; //询价完成(已开单)
}