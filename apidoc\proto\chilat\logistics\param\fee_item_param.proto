syntax = "proto3";
package chilat.logistics;

option java_package = "com.chilat.rpc.logistics.param";

import "common.proto";
import "chilat/logistics/logistics_common.proto";

message SaveFeeItemParam {
  string id = 1;
  string parentId = 2;
  string feeName = 3; //费用名称
  string feeAlias = 4; //费用别名
  FeeStage feeStage = 5; //收费阶段
  repeated string routeIds = 6; //适用线路
  int32 idx = 7; //排序
  bool enabled = 8; //是否启用
}

message FeeItemByTypeParam {
  FeeStage feeStage = 1; //收费阶段
}