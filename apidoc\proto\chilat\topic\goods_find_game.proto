syntax = "proto3";
package chilat.topic;

option java_package = "com.chilat.rpc.topic";

import "chilat/commodity/commodity_common.proto";

import "chilat/topic/model/goods_find_game_model.proto";
import "chilat/topic/param/goods_find_game_param.proto";
import "common.proto";

service GoodsFindGame {
  rpc save (GoodsFindGameSaveParam) returns (common.ApiResult);

  //任务列表
  rpc list (common.EmptyParam) returns (GoodsFindGameListResp);

  //根据id查询任务详情
  rpc detail (common.IdParam) returns (GoodsFindGameDetailResp);

  // 组列表
  rpc groupList(common.EmptyParam) returns (GoodsFindGameGroupListResp);

  rpc exportExcel(common.IdsParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };
}