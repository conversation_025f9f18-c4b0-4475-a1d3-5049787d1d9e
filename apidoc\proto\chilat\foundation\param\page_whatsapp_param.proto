syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.param";

import "chilat/foundation/foundation_common.proto";
import "common.proto";
import "common/business.proto";

// 查询WhatsApp列表参数
message PageWhatsAppPageQueryParam {
  common.PageParam page = 10; //分页（默认当前页1，默认分页大小20）
  common.VisitSiteSelectEnum visitSite = 20; //网站选择
  string whatsapp = 30; //WhatsApp号码
}

// 保存页面内的WhatsApp信息
message PageWhatsAppSaveParam {
  string id = 5; //ID（不传ID则新增）
  string pageName = 10; //页面名称
  string pageCode = 20; //页面代码
  common.VisitSiteSelectEnum visitSite = 30; //网站ID（10：主站，20：Shop）
  string pagePath = 40; //页面路径
  string pageUrl = 50; //页面链接
  string whatsapp = 60; //WhatsApp号码
  string preFilledText = 70; //WhatsApp预填文本
  int32 idx = 80; //排序（小的在前）
}

// 查询日志参数
message PageWhatsAppLogQueryParam {
  common.PageParam page = 1; //分页查询参数
  string id = 2; //数据ID
}