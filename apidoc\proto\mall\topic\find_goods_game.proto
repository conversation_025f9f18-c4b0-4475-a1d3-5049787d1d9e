syntax = "proto3";
package mall.topic;

option java_package = "com.chilat.rpc.topic";

import "common.proto";
import "mall/topic/model/find_goods_game_model.proto";
import "mall/topic/param/find_goods_game_param.proto";

// 找货活动
service FindGoodsGame {

  // 获取找货任务信息（若不存在，则自动创建；读取问题与已保存的回答数据，进入新增或编辑模式）
  rpc getFindGoodsInfo (GetFindGoodsInfoParam) returns (topic.GetFindGoodsInfoResp);

  // 根据小组ID，查询详情
  rpc getDetailByGroupId (common.IdParam) returns (topic.GetFindGoodsInfoResp);

  // 单次保存找货信息（保存一个找货任务中的问题）
  rpc saveFindGoodsOnce (SaveFindGoodsOnceParam) returns (topic.GetFindGoodsInfoResp);

  // 暂存找货信息（每完成一个字段的变更，即刻调用暂存接口）
  rpc tempStoreFindGoods (SaveFindGoodsOnceParam) returns (common.ApiResult);

}
