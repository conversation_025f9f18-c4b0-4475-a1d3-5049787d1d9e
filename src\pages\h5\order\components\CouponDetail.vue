<template>
  <div class="px-[0.16rem] flex justify-between">
    <div class="flex">
      <div class="w-[2.74rem]">{{ title }}:</div>

      <div
        class="border-1 border-[#FF94A8] bg-[#FEF7F8] text-[#FF4056] rounded-[0.06rem] px-[0.08rem] py-[0.04rem] text-[0.24rem] leading-[0.28rem] ml-[0.16rem] cursor-pointer flex items-center"
        @click="pageData.showDetail = !pageData.showDetail"
      >
        <span>{{ authStore.i18n("cm_coupon.viewCoupons") }}</span>
        <img
          loading="lazy"
          :src="pageData.showDetail ? top : down"
          alt="view"
          class="w-[0.2rem] ml-[0.04rem]"
          referrerpolicy="no-referrer"
        />
      </div>
    </div>
    <span>{{ setUnit(amount) }} </span>
  </div>
  <!-- 选择优惠券 -->
  <n-drawer
    width="100%"
    placement="bottom"
    :trap-focus="false"
    default-height="60%"
    v-model:show="pageData.showDetail"
  >
    <div class="h-full flex flex-col py-0">
      <div class="relative pt-[0.28rem] pb-[0.48rem]">
        <div class="text-[0.36rem] leading-[0.36rem] font-medium text-center">
          {{
            props.couponType === "COUPON_TYPE_PRODUCT"
              ? authStore.i18n("cm_coupon.productCoupons")
              : authStore.i18n("cm_coupon.commissionCoupons")
          }}
        </div>
        <icon-card
          color="#222"
          name="iconamoon:close-light"
          @click="pageData.showDetail = false"
          class="w-[0.48rem] absolute right-[0.26rem] top-[0.26rem]"
        ></icon-card>
      </div>
      <n-scrollbar class="flex-1 px-[0.16rem]" trigger="none">
        <div
          v-for="(coupon, index) in couponList"
          :key="coupon.id"
          class="w-full coupon-card flex items-center pb-[0.2rem]"
          :class="{
            'mb-[0.2rem] border-b border-[#F2F2F2]':
              index !== couponList.length - 1,
          }"
        >
          <div
            class="coupon-border py-[0.28rem] border-[0.04rem] border-dotted w-[1.56rem] text-center text-[0.28rem] leading-[0.28rem] font-medium rounded-[0.08rem]"
          >
            <span v-if="coupon?.couponWay === 'COUPON_WAY_DISCOUNT'">
              {{ discountToPercentage(coupon.discount) }}
              {{ authStore.i18n("cm_coupon.discount").toLowerCase() }}
            </span>
            <span v-else>
              {{ setNewUnit(coupon?.preferentialAmount) }}
            </span>
          </div>
          <div class="flex-1 mx-[0.14rem]">
            <div class="text-[0.26rem] leading-[0.26rem] font-medium">
              <!-- 满多少金额使用 -->
              <template v-if="coupon?.couponUseConditionsType === 'FULL'">
                <span>
                  {{ authStore.i18n("cm_coupon.minimumRequired") }}
                </span>
                {{ setNewUnit(coupon?.useConditionsAmount) }}
              </template>
              <!-- 每满多少金额使用 -->
              <template v-if="coupon?.couponUseConditionsType === 'EVERY_FULL'">
                <span>
                  {{ authStore.i18n("cm_coupon.minimumUnmet") }}
                </span>
                {{ setNewUnit(coupon?.useConditionsAmount) }}
                <span>
                  {{ authStore.i18n("cm_coupon.minimumUnmetCost") }}
                </span>
              </template>
              <!-- 不限制金额 -->
              <div v-if="coupon?.couponUseConditionsType === 'UNLIMITED'">
                {{ authStore.i18n("cm_coupon.noLimit") }}
              </div>
            </div>
            <div
              v-if="
                coupon?.couponWay === 'COUPON_WAY_DISCOUNT' &&
                coupon?.preferentialAmount
              "
              class="text-[0.24rem] leading-[0.24rem] flex items-center mt-[0.14rem]"
            >
              {{ authStore.i18n("cm_coupon.upToMoney") }}
              {{ setNewUnit(coupon?.preferentialAmount) }}
            </div>
          </div>
        </div>
      </n-scrollbar>
      <div
        class="px-[0.16rem] my-[0.16rem] w-full flex justify-between items-center"
      >
        <n-button
          color="#E50113"
          text-color="#fff"
          @click="pageData.showDetail = false"
          class="flex-1 rounded-[10rem] h-[0.8rem] text-[0.32rem] font-semibold"
        >
          {{ authStore.i18n("cm_order.closeDetail") }}
        </n-button>
      </div>
    </div>
  </n-drawer>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

import top from "@/assets/icons/marketing/top.svg";
import down from "@/assets/icons/marketing/down.svg";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  amount: {
    type: Number,
    default: "",
  },
  couponList: {
    type: Array,
    default: () => [],
  },
  couponType: {
    type: String,
    default: "",
  },
});

const route = useRoute();
const authStore = useAuthStore();
const pageData = reactive(<any>{ showDetail: false });

function onUpdateShow(val: any) {
  pageData.showDetail = val;
}
</script>
<style scoped lang="scss">
.coupon-card {
  color: #333;
  .coupon-border {
    border-color: #4d4d4d;
  }
}
</style>
