syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.model";

import "common.proto";
import "common/business.proto";
import "chilat/basis/model/config_model.proto";
import "chilat/basis/model/goods_model.proto";
import "chilat/basis/model/cart_model.proto";
import "mall/commodity/commodity_common.proto";
import "mall/commodity/model/goods_info_model.proto";


// 浏览器打开页面返回的数据（服务器端渲染）
message GoodsListPageResp {
    common.Result result = 1;
    GoodsListPageModel data = 2;
}

message GoodsListPageModel {
    chilat.basis.ConfigModel globalConfig = 10; //商城全局配置（当前语言，翻译信息等）
    //passport.LoginUserModel loginUser = 20; //登录用户信息（空值表示未登录）
    common.SeoData seoData = 30; //SEO设置信息
    chilat.basis.MallCategoryTreeModel categoryTree = 40; //类目树
    repeated chilat.basis.MallCategoryPathItemModel categoryPath = 50; //商品分类路径
    //repeated common.IdNameModel optionalBrands = 60; //可选择的品牌（id为品牌ID，name为品牌名称）
    chilat.basis.MidCartModel cartInfo = 70; //购物车数据
    GoodsListDataModel pageData = 100; //页面内主要数据内容
}

// 页面内Ajax请求的返回的数据对象
message GoodsListDataResp {
    common.Result result = 1;
    GoodsListDataModel data = 2;
}

//商品列表数据
message GoodsListDataModel {
    common.Page page = 10; //商品列表的分页信息
    repeated commodity.GoodsListDataItemModel goodsList = 20; //商品列表
    string tokenizeWords = 30; //搜索关键字的分词价格（用空格分隔）
    repeated GoodsListSelectedFilterModel selectedFilters = 40; //选择的过滤项
    repeated GoodsListCategoryFilterModel categoryFilters = 50; //商品分类过滤选项
}

//商品列表已选择的过滤项
message GoodsListSelectedFilterModel {
    commodity.GoodsListFilterType filterType = 10;
    string id = 20; //ID（可为空，比如价格过滤）
    string name = 30; //名称
}

//商品列表中商品分类过滤选项
message GoodsListCategoryFilterModel {
    string id = 10; //营销分类ID
    string name = 20; //营销分类名称
    int32 goodsCount = 25; //商品数量
    bool selected = 30; //是否已选中（null表示未选中，字段不输出到前端）
    repeated GoodsListCategoryFilterModel children = 40; //下级分类过滤项（null表示不存在，字段不输出到前端）
}

