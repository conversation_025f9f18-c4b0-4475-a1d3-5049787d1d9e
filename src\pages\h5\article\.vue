<template>
  <div class="mobile-wrapper">
    <mobile-search-bar></mobile-search-bar>
    <div class="w-full px-[0.32rem] pt-[0.84rem] pb-[2rem]">
      <div class="text-[0.68rem] leading-[0.68rem] font-medium text-center">
        Formas de pago
      </div>
      <div class="text-center mt-[52px]">
        <div class="text-[0.48rem] leading-[0.56rem] font-medium">
          1. ¿Cuáles son las modalidades de pago?
        </div>
        <div
          class="text-[0.28rem] leading-[0.32rem] text-[#7F7F7F] mt-[0.32rem]"
        >
          Chilatshop ofrece diferentes opciones en cuanto a métodos de pago. No
          sólo admite los principales métodos de pago internacionales, sino que
          también ofrece una amplia gama de opciones de pago en moneda local.
          Esto significa que puede realizar transacciones utilizando métodos de
          pago conocidos sin gastos de conversión adicionales. Esta flexibilidad
          en los métodos de pago hace que sus transacciones transfronterizas
          sean más cómodas.
        </div>
        <div
          class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.6rem]"
        ></div>
        <n-space
          class="flex justify-center items-center mt-[0.92rem]"
          :style="{ gap: '0.24rem 0.24rem' }"
        >
          <img
            alt="Visa"
            class="h-[0.92rem]"
            src="@/assets/icons/article/Visa.png"
          />
          <img
            alt="BBVA"
            class="h-[0.88rem]"
            src="@/assets/icons/article/BBVA.png"
          />
          <img
            alt="Interbank"
            class="h-[0.88rem]"
            src="@/assets/icons/article/Interbank.png"
          />
          <img
            alt="Scotiabank"
            class="h-[0.88rem]"
            src="@/assets/icons/article/Scotiabank.png"
          />
          <img
            alt="Paypal"
            class="h-[0.8rem]"
            src="@/assets/icons/article/Paypal.png"
          />
          <img
            alt="Yape"
            class="h-[0.88rem]"
            src="@/assets/icons/article/Yape.png"
          />
          <img
            alt="Plin"
            class="h-[0.88rem]"
            src="@/assets/icons/article/Plin.png"
          />
          <img
            alt="Tunki"
            class="h-[0.88rem]"
            src="@/assets/icons/article/Tunki.png"
          />
        </n-space>
        <n-space
          class="flex justify-center items-center mt-[52px]"
          :style="{ gap: '0.2rem 0.2rem' }"
        >
          <div
            class="w-[3.32rem] h-[3.4rem] px-[0.24rem] py-[0.28rem] bg-[#f5f5f5] rounded-[0.4rem] flex flex-col items-center"
          >
            <img
              alt="transferencia"
              class="w-[0.8rem]"
              src="@/assets/icons/article/transfer.svg"
            />
            <div
              class="text-[0.32rem] leading-[0.36rem] font-medium mt-[0.16rem]"
            >
              Deposite /<br />
              Transferencia bancaria
            </div>
          </div>
          <div
            class="w-[3.32rem] h-[3.4rem] px-[0.24rem] py-[0.28rem] bg-[#f5f5f5] rounded-[0.4rem] flex flex-col items-center text-center"
          >
            <img
              alt="cash"
              class="w-[0.8rem]"
              src="@/assets/icons/article/cash.svg"
            />
            <div
              class="text-[0.32rem] leading-[0.36rem] font-medium mt-[0.16rem]"
            >
              Pago en efectivo
            </div>
          </div>
          <div
            class="w-[3.32rem] h-[3.4rem] px-[0.24rem] py-[0.28rem] bg-[#f5f5f5] rounded-[0.4rem] flex flex-col items-center"
          >
            <img
              alt="ewallet"
              class="w-[0.8rem]"
              src="@/assets/icons/article/ewallet.svg"
            />
            <div
              class="text-[0.32rem] leading-[0.36rem] font-medium mt-[0.16rem]"
            >
              Billetera<br />
              electrónica
            </div>
            <div class="text-[0.28rem] leading-[0.32rem] mt-[0.12rem]">
              Tupay (including Yape, PLlN, Tunki, etc.)
            </div>
          </div>

          <div
            class="w-[3.32rem] h-[3.4rem] px-[0.4rem] py-[0.28rem] bg-[#f5f5f5] rounded-[0.4rem] flex flex-col items-center"
          >
            <img
              alt="others"
              class="w-[0.8rem]"
              src="@/assets/icons/article/others.svg"
            />
            <div
              class="text-[0.32rem] leading-[0.36rem] font-medium mt-[0.16rem]"
            >
              Otros métodos de pago
            </div>
            <div class="text-[0.28rem] leading-[0.32rem] mt-[0.12rem]">
              Paypal, pago fuera de línea en USD
            </div>
          </div>
        </n-space>
      </div>
      <div class="text-center mt-[1.6rem]">
        <div class="text-[0.48rem] leading-[0.56rem] font-medium">
          2. ¿Cómo se paga?
        </div>
        <div
          class="text-[0.28rem] leading-[0.32rem] text-[#7F7F7F] mt-[0.32rem]"
        >
          Chilatshop admite el pago en línea, cuando envíe una consulta y
          confirme el pedido, se generará un enlace de pago en su centro
          personal, puede pagar directamente haciendo clic en el enlace, no es
          necesario ir al banco para transferir dinero, es muy cómodo y rápido.
        </div>
        <div
          class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.6rem]"
        ></div>
        <img
          alt="payOrder"
          class="w-full mt-[36px]"
          src="@/assets/icons/article/mobilePayOrder.png"
        />
      </div>
      <div class="text-center mt-[1.6rem]">
        <div class="text-[0.48rem] leading-[0.56rem] font-medium">
          3. Países en los que actualmente es posible pagar en moneda local
        </div>
        <div
          class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.6rem]"
        ></div>
        <n-grid :cols="2" :x-gap="24" :y-gap="28" class="mt-[54px]">
          <n-grid-item
            class="w-[156px] flex flex-col"
            v-for="pay in payMethods"
            :key="pay.country"
          >
            <img
              :alt="pay.country"
              :src="pay.icon"
              class="w-[1.6rem] mx-auto"
            />
            <div
              class="text-[0.28rem] leading-[0.28rem] text-[#7F7F7F] mt-[0.24rem]"
            >
              {{ pay.country }}
            </div>
            <div class="text-[0.32rem] leading-[0.32rem] mt-[0.12rem]">
              {{ pay.currency }}
            </div>
          </n-grid-item>
        </n-grid>
      </div>
      <div class="text-center mt-[1.6rem]">
        <div class="text-[0.48rem] leading-[0.56rem] font-medium">
          4. Pagar por tutoriales
        </div>
        <div
          class="w-[1rem] h-[0.06rem] bg-[#e50113] mx-auto mt-[0.6rem]"
        ></div>
        <div class="mt-[0.8rem] rounded-[0.2rem] overflow-hidden">
          <video-you-tube
            :width="6.86 * resetFontSize"
            :height="3.858 * resetFontSize"
            youtubeId="-5ZBcr0rgJs?si=HZgwkVdHGUeahmDC"
            poster="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2025/01/02/f18bcaa1-de0b-4a0b-bcfe-f1832a229174.png"
          ></video-you-tube>
        </div>
      </div>
    </div>
    <mobile-page-footer></mobile-page-footer>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();
const loginRegister = inject<any>("loginRegister");
const userInfo = computed(() => useAuthStore().getUserInfo);

import Mexico from "@/assets/icons/article/Mexico.png";
import Peru from "@/assets/icons/article/Peru.png";
import Argentina from "@/assets/icons/article/Argentina.png";
import Chile from "@/assets/icons/article/Chile.png";
import Colombia from "@/assets/icons/article/Colombia.png";
import CostaRica from "@/assets/icons/article/CostaRica.png";
import Ecuador from "@/assets/icons/article/Ecuador.png";
import Panama from "@/assets/icons/article/Panama.png";

onBeforeMount(() => {
  document.title = authStore.i18n("cm_common.documentTitle");
});

const payMethods = [
  {
    icon: Mexico,
    country: "México",
    currency: "Peso Mexicano",
  },
  {
    icon: Peru,
    country: "Perú",
    currency: "Nuevo Sol",
  },
  {
    icon: Argentina,
    country: "Argentina",
    currency: "Peso argentino",
  },
  {
    icon: Chile,
    country: "Chile",
    currency: "Peso chileno",
  },
  {
    icon: Colombia,
    country: "Colombia",
    currency: "Peso colombiano",
  },
  {
    icon: CostaRica,
    country: "Costa Rica",
    currency: "Colón costarricense",
  },
  {
    icon: Ecuador,
    country: "Ecuador",
    currency: "Dólar estadounidense",
  },
  {
    icon: Panama,
    country: "Panamá",
    currency: "Balboa",
  },
];

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

const onLoginClick = async () => {
  window?.MyStat?.addPageEvent(
    "passport_open_nav_register",
    "点击顶部导航注册，打开注册窗口"
  ); // 埋点

  loginRegister?.openLogin("", 0);
};
</script>

<style scoped lang="scss">
.mobile-wrapper {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
  font-size: 0.28rem;
}

.page-header {
  width: 100%;
  height: 410px;
  position: relative;
  object-fit: cover;
  background-size: 100%100%;
  background-image: url("@/assets/icons/article/inviteBg.jpg");
  background-repeat: no-repeat;
}
.page-footer {
  width: 100%;
  height: 150px;
  position: relative;
  background-size: 100%100%;
  background-image: url("@/assets/icons/article/footerBg.jpg");
  background-repeat: no-repeat;
}
</style>
