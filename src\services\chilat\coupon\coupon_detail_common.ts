/* eslint-disable */

export const protobufPackage = "chilat.coupon";

/** 优惠券主体状态 */
export enum TicketStatus {
  /** TICKET_NOT_USE - 未使用 */
  TICKET_NOT_USE = 0,
  /** TICKET_USE - 已使用 */
  TICKET_USE = 1,
  /** TICKET_LOCK - 锁定 */
  TICKET_LOCK = 2,
  /** TICKET_LOSE_EFFICACY - 卡券失效 */
  TICKET_LOSE_EFFICACY = 3,
  UNRECOGNIZED = -1,
}

export function ticketStatusFromJSON(object: any): TicketStatus {
  switch (object) {
    case 0:
    case "TICKET_NOT_USE":
      return TicketStatus.TICKET_NOT_USE;
    case 1:
    case "TICKET_USE":
      return TicketStatus.TICKET_USE;
    case 2:
    case "TICKET_LOCK":
      return TicketStatus.TICKET_LOCK;
    case 3:
    case "TICKET_LOSE_EFFICACY":
      return TicketStatus.TICKET_LOSE_EFFICACY;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TicketStatus.UNRECOGNIZED;
  }
}

export function ticketStatusToJSON(object: TicketStatus): string {
  switch (object) {
    case TicketStatus.TICKET_NOT_USE:
      return "TICKET_NOT_USE";
    case TicketStatus.TICKET_USE:
      return "TICKET_USE";
    case TicketStatus.TICKET_LOCK:
      return "TICKET_LOCK";
    case TicketStatus.TICKET_LOSE_EFFICACY:
      return "TICKET_LOSE_EFFICACY";
    case TicketStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** 优惠券发放场景状态 */
export enum TicketSceneStatus {
  /** REGISTER - 注册 */
  REGISTER = 0,
  /** INVITATIN - 邀约 */
  INVITATIN = 1,
  /** INVITATION_REGISTER - 邀请注册 */
  INVITATION_REGISTER = 2,
  /** INQUIRY - 询盘 */
  INQUIRY = 3,
  /** PAYMENT - 支付 */
  PAYMENT = 4,
  UNRECOGNIZED = -1,
}

export function ticketSceneStatusFromJSON(object: any): TicketSceneStatus {
  switch (object) {
    case 0:
    case "REGISTER":
      return TicketSceneStatus.REGISTER;
    case 1:
    case "INVITATIN":
      return TicketSceneStatus.INVITATIN;
    case 2:
    case "INVITATION_REGISTER":
      return TicketSceneStatus.INVITATION_REGISTER;
    case 3:
    case "INQUIRY":
      return TicketSceneStatus.INQUIRY;
    case 4:
    case "PAYMENT":
      return TicketSceneStatus.PAYMENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TicketSceneStatus.UNRECOGNIZED;
  }
}

export function ticketSceneStatusToJSON(object: TicketSceneStatus): string {
  switch (object) {
    case TicketSceneStatus.REGISTER:
      return "REGISTER";
    case TicketSceneStatus.INVITATIN:
      return "INVITATIN";
    case TicketSceneStatus.INVITATION_REGISTER:
      return "INVITATION_REGISTER";
    case TicketSceneStatus.INQUIRY:
      return "INQUIRY";
    case TicketSceneStatus.PAYMENT:
      return "PAYMENT";
    case TicketSceneStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}
