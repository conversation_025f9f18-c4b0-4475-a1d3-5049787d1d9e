<template>
  <div>
    <div
      class="cwidth flex items-center justify-between mx-auto bg-white rounded-lg my-3 px-[48px] py-[16px]"
    >
      <div
        v-for="(item, index) in pageData.orderStatusList"
        :key="index"
        class="flex items-center relative"
        :class="index < pageData.orderStatusList.length - 1 ? 'flex-grow' : ''"
      >
        <!-- 状态图标和文字 -->
        <div
          class="status-item"
          :class="{
            'text-[#e50113]': pageData.orderStatusIndex === index,
            'text-[#4D4D4D]': pageData.orderStatusIndex > index,
            'text-[#CCC]':
              pageData.orderStatusIndex < index ||
              pageData.orderStatusIndex == -1,
          }"
        >
          <div
            class="status-icon"
            :class="{
              'bg-[#E50113] text-[#e50113]':
                pageData.orderStatusIndex === index,
              'bg-[#F2F2F2] text-[#4D4D4D]': pageData.orderStatusIndex > index,
              'bg-transparent border-[2px] border-dashed border-[#E6E6E6] text-[#CCC]':
                pageData.orderStatusIndex < index ||
                pageData.orderStatusIndex == -1,
            }"
          >
            <img
              loading="lazy"
              class="w-[46px] h-[46px]"
              :src="
                pageData.orderStatusIndex === index
                  ? item.iconActive
                  : item.icon
              "
              referrerpolicy="no-referrer"
            />
          </div>
          <div class="status-text">
            {{ item.label }}
          </div>
        </div>

        <!-- 横线：状态之间的间隔 -->
        <div
          v-if="index !== pageData.orderStatusList.length - 1"
          class="status-line border-b-2 border-[#e6e6e6]"
          :class="{
            'border-dashed': pageData.orderStatusIndex <= index,
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import payAllFee from "@/assets/icons/order/payAllFee.svg";
import payAllFeeAc from "@/assets/icons/order/payAllFeeAc.svg";
import payProduct from "@/assets/icons/order/payProduct.svg";
import payProductAc from "@/assets/icons/order/payProductAc.svg";
import calculateInternationalFee from "@/assets/icons/order/calculateInternationalFee.svg";
import calculateInternationalFeeAc from "@/assets/icons/order/calculateInternationalFeeAc.svg";
import payInternationalFee from "@/assets/icons/order/payInternationalFee.svg";
import payInternationalFeeAc from "@/assets/icons/order/payInternationalFeeAc.svg";
import purchasing from "@/assets/icons/order/purchasing.svg";
import purchasingAc from "@/assets/icons/order/purchasingAc.svg";
import sendOut from "@/assets/icons/order/sendOut.svg";
import sendOutAc from "@/assets/icons/order/sendOutAc.svg";
import transporting from "@/assets/icons/order/transporting.svg";
import transportingAc from "@/assets/icons/order/transportingAc.svg";
import delivering from "@/assets/icons/order/delivering.svg";
import deliveringAc from "@/assets/icons/order/deliveringAc.svg";
import userSigned from "@/assets/icons/order/userSigned.svg";
import userSignedAc from "@/assets/icons/order/userSignedAc.svg";
import customsClearing from "@/assets/icons/order/customsClearing.svg";
import customsClearingAc from "@/assets/icons/order/customsClearingAc.svg";

const authStore = useAuthStore();
const props = defineProps({
  payMode: {
    type: String,
    default: "",
  },
  quotationMode: {
    type: String,
    default: "",
  },
  mallOrderStatus: {
    type: String,
    default: "",
  },
});

watch(
  () => props.payMode,
  (newValue: any) => {
    if (newValue) {
      onHandleOrderStep();
    }
  }
);

const MALL_WAIT_PAY_ALL_FEE = {
  icon: payAllFee,
  iconActive: payAllFeeAc,
  value: "MALL_WAIT_PAY_ALL_FEE",
  label: authStore.i18n("cm_order.payAllFee"),
};

const MALL_WAIT_PAY_PRODUCT = {
  icon: payProduct,
  iconActive: payProductAc,
  value: "MALL_WAIT_PAY_PRODUCT",
  label: authStore.i18n("cm_order.payProduct"),
};

const MALL_WAIT_CAL_INTER_FEE = {
  icon: calculateInternationalFee,
  iconActive: calculateInternationalFeeAc,
  value: "MALL_WAIT_CAL_INTER_FEE",
  label: authStore.i18n("cm_order.calculateInternationalFee"),
};

const MALL_WAIT_PAY_INTER_FEE = {
  icon: payInternationalFee,
  iconActive: payInternationalFeeAc,
  value: "MALL_WAIT_PAY_INTER_FEE",
  label: authStore.i18n("cm_order.payInternationalFee"),
};

const MALL_PURCHASING = {
  icon: purchasing,
  iconActive: purchasingAc,
  value: "MALL_PURCHASING",
  label: authStore.i18n("cm_order.purchasing"),
};

const MALL_WAIT_SEND_OUT = {
  icon: sendOut,
  iconActive: sendOutAc,
  value: "MALL_WAIT_SEND_OUT",
  label: authStore.i18n("cm_order.sendOut"),
};

const MALL_TRANSPORTING = {
  icon: transporting,
  iconActive: transportingAc,
  value: "MALL_TRANSPORTING",
  label: authStore.i18n("cm_order.transporting"),
};

const MALL_CUSTOMS_CLEARING = {
  icon: customsClearing,
  iconActive: customsClearingAc,
  value: "MALL_CUSTOMS_CLEARING",
  label: authStore.i18n("cm_order.customsClearing"),
};

const MALL_DELIVERING = {
  icon: delivering,
  iconActive: deliveringAc,
  value: "MALL_DELIVERING",
  label: authStore.i18n("cm_order.delivering"),
};

const MALL_USER_SIGNED = {
  icon: userSigned,
  iconActive: userSignedAc,
  value: "MALL_USER_SIGNED",
  label: authStore.i18n("cm_order.userSigned"),
};

const pageData = reactive(<any>{
  orderStatusList: <any>[],
  orderStatusIndex: 0,
});

function onHandleOrderStep() {
  // 一次性支付
  if (props.payMode === "PAY_MODE_ALL") {
    // DDP [支付订单费用、采购、待发货、国际运输、清关中、派送、已签收]
    if (props.quotationMode === "QUOTATION_MODE_DDP") {
      pageData.orderStatusList = [
        MALL_WAIT_PAY_ALL_FEE,
        MALL_PURCHASING,
        MALL_WAIT_SEND_OUT,
        MALL_TRANSPORTING,
        MALL_CUSTOMS_CLEARING,
        MALL_DELIVERING,
        MALL_USER_SIGNED,
      ];
    }

    // EXW [支付订单费用、采购、已签收]
    if (props.quotationMode === "QUOTATION_MODE_EXW") {
      pageData.orderStatusList = [
        MALL_WAIT_PAY_ALL_FEE,
        MALL_PURCHASING,
        MALL_USER_SIGNED,
      ];
    }

    // CIF [支付订单费用、采购、待发货、国际运输、清关中、已签收]
    if (props.quotationMode === "QUOTATION_MODE_CIF") {
      pageData.orderStatusList = [
        MALL_WAIT_PAY_ALL_FEE,
        MALL_PURCHASING,
        MALL_WAIT_SEND_OUT,
        MALL_TRANSPORTING,
        MALL_CUSTOMS_CLEARING,
        MALL_USER_SIGNED,
      ];
    }

    // FOB [支付订单费用、采购、已签收]
    // LCL [支付订单费用、采购、已签收]
    if (
      props.quotationMode === "QUOTATION_MODE_FOB" ||
      props.quotationMode === "QUOTATION_MODE_LCL"
    ) {
      pageData.orderStatusList = [
        MALL_WAIT_PAY_ALL_FEE,
        MALL_PURCHASING,
        MALL_USER_SIGNED,
      ];
    }
  } else {
    // 分开支付
    // DDP [支付产品成本、采购、待计算国际费用、支付国际费用、待发货、国际运输、清关中、派送、已签收]
    if (props.quotationMode === "QUOTATION_MODE_DDP") {
      pageData.orderStatusList = [
        MALL_WAIT_PAY_PRODUCT,
        MALL_PURCHASING,
        MALL_WAIT_CAL_INTER_FEE,
        MALL_WAIT_PAY_INTER_FEE,
        MALL_WAIT_SEND_OUT,
        MALL_TRANSPORTING,
        MALL_CUSTOMS_CLEARING,
        MALL_DELIVERING,
        MALL_USER_SIGNED,
      ];
    }

    // CIF [支付产品成本、采购、待计算国际费用、支付国际费用、待发货、国际运输、清关中、  已签收]
    if (props.quotationMode === "QUOTATION_MODE_CIF") {
      pageData.orderStatusList = [
        MALL_WAIT_PAY_PRODUCT,
        MALL_PURCHASING,
        MALL_WAIT_CAL_INTER_FEE,
        MALL_WAIT_PAY_INTER_FEE,
        MALL_WAIT_SEND_OUT,
        MALL_TRANSPORTING,
        MALL_CUSTOMS_CLEARING,
        MALL_USER_SIGNED,
      ];
    }
  }
  pageData.orderStatusIndex = pageData.orderStatusList.findIndex(
    (item: any) => item.value === props.mallOrderStatus
  );
}
</script>

<style scoped lang="scss">
.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 64px;
}
.status-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.status-text {
  margin-top: 16px;
  width: 120px;
  height: 64px;
  text-align: center;
}

.status-line {
  flex-grow: 1;
  margin-left: 4px;
  margin-right: 4px;
  margin-bottom: 78px;
}
</style>
