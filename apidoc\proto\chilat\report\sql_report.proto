syntax = "proto3";
package chilat.report;

import "chilat/report/model/sql_report_model.proto";
import "chilat/report/param/sql_report_param.proto";
import "common.proto";

option java_package = "com.chilat.rpc.report";

service SqlReport {
  // 查询列表
  rpc listPage (SqlReportPageQueryParam) returns (SqlReportPageResp);
  // 获取默认的数据表格（已废弃，改用 getDataTable 接口）
  rpc getDefaultDataTable (common.IdParam) returns (SqlReportDataTableResp);
  // 查询SQL报表筛选参数列表
  rpc getFilterOptions (common.IdParam) returns (SqlReportFilterOptionsResp);
  // 获取默认的数据表格
  rpc getDataTable (SqlReportDataQueryParam) returns (SqlReportDataTableResp);
  // Excel导出
  rpc excelExport(SqlReportDataQueryParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };
}