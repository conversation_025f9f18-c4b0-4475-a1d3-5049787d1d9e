syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation.model";

import "common.proto";

message ExchangeRatePageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated ExchangeRateModel data = 3;
}

message ExchangeRateModel {
  string id = 1;
  string currencyCode = 2; //货币代码
  double exchangeRate = 3; //汇率
  int64 udate = 4; //更新时间
  string uoperator = 5; //更新人
}

message ExchangeRateLogListResp {
  common.Result result = 1;
  repeated ExchangeRateLogModel data = 2;
}

message ExchangeRateLogModel {
  string id = 1;
  string coperator = 2; //创建人
  int64 cdate = 3; //创建时间
  string remark = 4; //操作内容
}