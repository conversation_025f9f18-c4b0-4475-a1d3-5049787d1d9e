<template>
  <div class="w-full bg-[#FAFAFA]">
    <div class="w-full bg-white">
      <search-card></search-card>
    </div>

    <div
      class="w-[732px] min-h-[50vh] mx-auto pt-[30px] flex flex-col items-center text-center"
    >
      <img
        :alt="authStore.i18n('cm_goods.customSearch')"
        class="w-[244px]"
        src="@/assets/icons/common/find-submitted.svg"
      />
      <div
        class="text-[26px] leading-[26px] font-medium mt-[24px] text-[#11263B]"
      >
        {{ authStore.i18n("cm_search.sentSuccess") }}
      </div>
      <div class="text-[16px] leading-[20px] text-[#7F7F7F] mt-[16px]">
        {{ authStore.i18n("cm_search.sentSuccessTip") }}
      </div>
      <div class="w-full h-[1px] bg-[#D9D9D9] mt-[30px]"></div>
      <div
        class="w-full flex justify-between items-center mt-[24px] mb-[100px]"
      >
        <a
          target="_blank"
          href="/"
          class="w-[354px] h-[42px] flex justify-center items-center border-1 border-[#A6A6A6] text-center rounded-[500px] text-[#333] text-[18px] leading-[18px] hover:border-[#e50113] hover:text-[#e50113] transition-all duration-300 cursor-pointer"
        >
          {{ authStore.i18n("cm_search.goToHome") }}
        </a>
        <a
          target="_blank"
          href="/goods/looking"
          data-spm-box="button-find-again-find"
          class="w-[354px] h-[42px] box-border flex justify-center items-center text-center rounded-[500px] text-[#fff] bg-[#11263B] text-[18px] leading-[18px] hover:bg-[#007FFF] transition-all duration-300 cursor-pointer"
        >
          {{ authStore.i18n("cm_search.sendNewRequest") }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();
</script>

<style scoped lang="scss"></style>
