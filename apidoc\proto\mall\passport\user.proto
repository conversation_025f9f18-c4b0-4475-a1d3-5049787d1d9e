syntax = "proto3";
package mall.passport;

option java_package = "com.chilat.rpc.passport";

import "common.proto";
import "mall/passport/model/user_model.proto";
import "mall/passport/param/user_param.proto";


// 用户
service User {
  // 查询用户详情
  rpc detail(common.EmptyParam) returns (UserDetailResp);
  // 修改密码
  rpc updatePassword(UpdatePasswordParam) returns (common.ApiResult);
  // 保存地址
  rpc saveUserAddress(SaveUserAddressParam) returns (common.ApiResult);
  // 查询所有地址
  rpc listUserAddress(common.EmptyParam) returns (UserAddressResp);
  // 设为默认
  rpc addressToDefault(common.IdParam) returns (common.ApiResult);
  // 删除地址
  rpc deleteUserAddress(common.IdParam) returns (common.ApiResult);

  // 发送验证邮箱的邮件
  rpc sendVerifyMail (SendVerifyMailParam) returns (SendVerifyMailResp);

  // 查询邮箱是否已验证, 以及验证后可以得到的优惠券----入参email
  rpc queryVerifyMailResult (queryVerifyMailResultParam) returns (QueryVerifyMailResultResp);

  // 点击激活链接 ---- 入参为激活链接上的code参数
  rpc verifyMail (common.IdParam) returns (VerifyMailResp);

  // 查询被邀请好友用户的邮箱验证情况
  rpc invitedUserMailStatus (InvitedUserMailStatusParam) returns (InvitedUserMailStatusResp);
}