<template>
  <div>
    <NuxtPwaManifest />
    <n-config-provider
      inline-theme-disabled
      :theme-overrides="themeOverrides"
      :locale="locale"
      :date-locale="dateLocale"
    >
      <n-message-provider>
        <!-- 站点选择弹窗 -->
        <site-select-modal v-if="!hideSiteSelectModal" />
        <NuxtLayout name="mobile" v-if="isMobileView">
          <NuxtLoadingIndicator :color="pageTheme.loading.color" />
          <NuxtPage keepalive />
        </NuxtLayout>
        <NuxtLayout name="default" v-else>
          <NuxtLoadingIndicator :color="pageTheme.loading.color" />
          <NuxtPage keepalive />
        </NuxtLayout>
      </n-message-provider>
    </n-config-provider>
  </div>
</template>

<script lang="ts" setup>
import { NConfigProvider, type GlobalThemeOverrides } from "naive-ui";
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import SiteSelectModal from "@/components/SiteSelectModal.vue";
import { initSiteInfo } from "@/utils/siteUtils";

const { isMobile } = useDevice();
const route = useRoute();
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const isMobileView = ref(false);
const isFullscreen = ref(false);
const initialDeviceType = ref(isMobile);
const pageData = reactive(<any>{});
const config = useRuntimeConfig();

// 首次加载时使用 useDevice 判断设备类型
isMobileView.value = isMobile;

const locale = computed(() => useAuthStore().customLocal);
const dateLocale = computed(() => useAuthStore().customLocalDate);
const pageTheme = computed(() => useConfigStore().getPageTheme);

// 检测是否处于全屏状态
function checkFullscreen() {
  return !!(
    document.fullscreenElement ||
    (document as any).webkitFullscreenElement ||
    (document as any).mozFullScreenElement ||
    (document as any).msFullscreenElement
  );
}

// 监听全屏变化
function handleFullscreenChange() {
  isFullscreen.value = checkFullscreen();
}

// 初始化数据
await useAuthStore().nuxtServerInit();

if (process.server) {
  const res: any = await useGetNuxtConfig({
    requestUri: route.fullPath,
    abtestPage: "homepage2409",
  });

  if (res?.result?.code === 200) {
    Object.assign(pageData, res.data);
    config.public.abtestMode = "B";
    config.public.defaultCountryCode = pageData?.defaultCountryCode || "";
    config.public.userInfo = pageData.loginUser || {};
    config.public.siteId = pageData.siteId;
    config.public.siteList = pageData.siteList;

    nuxtApp.$setResponseHeaders(pageData.responseHeaders);
    let globalData = nuxtApp.$getGlobalData();
    globalData.visitCode = pageData.visitCode;
    if (pageData.headFirstScript) {
      useHead({
        script: [{ innerHTML: pageData.headFirstScript }],
      });
    }
    if (pageData.headLastScript) {
      useHead({
        script: [{ innerHTML: pageData.headLastScript }],
      });
    }
  }

  await redirectToURL();
  if (isMobileView.value) {
    useHead({
      script: [
        {
          src: "/scripts/resetFontSize.js?20250220",
          async: false,
        },
      ],
    });
  }
} else {
  // 初始化站点数据到window.siteData
  initSiteInfo(config.public.siteId, config.public.siteList || []);
}

// 防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: NodeJS.Timeout | null = null;
  return function (...args: any[]) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn(...args), delay);
  };
};

function resetFontSize() {
  const width = Math.max(document.documentElement.clientWidth, 300);
  // 如果是全屏状态，保持当前视图类型不变
  if (!isFullscreen.value) {
    const isMobilePage = width < 600;
    if (isMobileView.value !== isMobilePage) {
      // 只在非全屏状态下更新视图类型
      isMobileView.value = isMobilePage;
      redirectToURL();
    }
  }
  const fontSize = width >= 600 ? 16 : (80 * width) / 600;
  document.documentElement.style.fontSize = `${fontSize}px`;
}
const debouncedResetFontSize = debounce(resetFontSize, 50);

onMounted(() => {
  // 保存初始设备类型
  initialDeviceType.value = isMobile;

  // 添加全屏变化监听
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
  document.addEventListener("mozfullscreenchange", handleFullscreenChange);
  document.addEventListener("MSFullscreenChange", handleFullscreenChange);

  window.addEventListener("resize", debouncedResetFontSize);
  window.addEventListener("orientationchange", resetFontSize);

  window?.MyStat?.addPageEvent(`system_vue_mounted`, "开始响应用户操作");
  // 指定页面增加Google reCAPTCHA 验证码
  const recaptchaPaths = [
    "/notas",
    "/h5/notas",
    "/open/notas",
    "/h5/open/notas",
  ];
  if (recaptchaPaths.includes(normalizePath(route.path))) {
    useHead({
      script: [{ src: "/scripts/recaptcha.js", async: true }],
    });
  }
  resetWhatsAppClickState();
});

onUnmounted(() => {
  // 移除全屏变化监听
  document.removeEventListener("fullscreenchange", handleFullscreenChange);
  document.removeEventListener(
    "webkitfullscreenchange",
    handleFullscreenChange
  );
  document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
  document.removeEventListener("MSFullscreenChange", handleFullscreenChange);

  window.removeEventListener("resize", debouncedResetFontSize);
  window.removeEventListener("orientationchange", resetFontSize);
});

/**
 * 页面重定向
 * 增加全屏状态判断，全屏时不进行重定向
 */
function redirectToURL() {
  // 如果处于全屏状态，不进行重定向
  if (isFullscreen.value) {
    return;
  }

  // 如果是移动设备，优先使用初始设备类型判断
  if (
    initialDeviceType.value &&
    isMobileView.value !== initialDeviceType.value
  ) {
    isMobileView.value = initialDeviceType.value;
  }

  const currentURL = normalizePath(route.path);

  // 首页重定向
  if (isMobileView.value && currentURL === "/") {
    return navigateTo(`/h5${route.fullPath}`);
  }
  if (!isMobileView.value && (currentURL === "/h5" || currentURL === "/h5/")) {
    return navigateTo({
      path: "/",
      query: route.query,
    });
  }

  // 商品详情页重定向
  const isGoodsDetailPage = /^\/goods\/\d+$/.test(normalizePath(route.path));
  const isGoodsDetailPageMobile = /^\/h5\/goods\/\d+$/.test(
    normalizePath(route.path)
  );
  if (isMobileView.value && isGoodsDetailPage) {
    return navigateTo(`/h5${route.fullPath}`);
  }
  if (!isMobileView.value && isGoodsDetailPageMobile) {
    return navigateTo(route.fullPath.replace("/h5", ""));
  }

  // 商品列表页面重定向
  /**
   * 1. PC -> H5:
   *    - 根据PC的params.id生成H5的categoryId，并拼接到查询参数中，避免重复添加categoryId。
   *    - 如果params.id为'all'，则不添加categoryId，直接跳转到H5列表页。
   * 2. H5 -> PC:
   *    - 从H5的查询参数categoryId转换为PC的params.id。
   *    - 如果没有categoryId，则将params.id设置为'all'。
   */

  const isPcGoodsListPage = /^\/goods\/list/.test(route.path);
  const isMobileGoodsListPage = /^\/h5\/search\/list/.test(route.path);

  // 移动端访问PC端商品列表，处理route.params.id
  if (isMobileView.value && isPcGoodsListPage) {
    const categoryId = route.params?.id;
    if (categoryId !== "all") {
      let mobilePath = route.fullPath.replace(
        `/goods/list/${categoryId}`,
        "/h5/search/list"
      );
      const querySymbol = mobilePath.includes("?") ? "&" : "?";
      if (!route.query.categoryId) {
        mobilePath = mobilePath.concat(
          `${querySymbol}categoryId=${categoryId}`
        );
      } else {
        route.query.categoryId = categoryId;
      }
      return navigateTo(mobilePath);
    } else {
      const mobilePath = route.fullPath.replace(
        `/goods/list/all`,
        "/h5/search/list"
      );
      return navigateTo(mobilePath);
    }
  }
  // PC端访问移动端商品列表，处理query.categoryId
  if (!isMobileView.value && isMobileGoodsListPage) {
    const categoryId = route.query.categoryId || "all";
    const pcPath = route.fullPath.replace(
      "/h5/search/list",
      `/goods/list/${categoryId}`
    );

    return navigateTo(pcPath);
  }

  // Tienda 页面重定向
  const isTiendaPage = /^\/tienda\/[^/]+$/.test(normalizePath(route.path));
  const isTiendaPageMobile = /^\/h5\/tienda\/[^/]+$/.test(
    normalizePath(route.path)
  );

  if (isMobileView.value && isTiendaPage) {
    return navigateTo(`/h5${route.fullPath}`);
  }
  if (!isMobileView.value && isTiendaPageMobile) {
    return navigateTo(route.fullPath.replace(/^\/h5/, ""));
  }

  // 文章页面或者其他页面重定向
  const urlMap = {
    "/article": "/h5/article",
    "/article/about-us": "/h5/article/about-us",
    "/article/help-center": "/h5/article/help-center",
    "/article/quick-guide": "/h5/article/quick-guide",
    "/article/frequently-questions": "/h5/article/frequently-questions",
    "/article/commission": "/h5/article/commission",
    "/user/account": "/h5/user",
    "/user/address": "/h5/user/address",
    "/user/inquiry": "/h5/user/inquiry",
    "/user/updatePwd": "/h5/user/updatePwd",
    "/goods/looking": "/h5/search/looking",
    "/user/orderList": "/h5/user/orderList",
    "/order/details": "/h5/order/details",
    "/order/payment": "/h5/order/payment",
    "/find": "/h5/find",
    "/find/submit": "/h5/find/submit",
    "/find/submit-thankyou": "/h5/find/submit-thankyou",
    "/login": "/h5/user/login",
    "/modifyPwd": "/h5/user/modifyPwd",
    "/register": "/h5/user/register",
    "/notas": "/h5/notas",
    "/notas/success": "/h5/notas/success",
    "/open/notas": "/h5/open/notas",
    "/user/coupon": "/h5/user/coupon",
    "/activate": "/h5/activate",
    "/register/success": "/h5/user/register-success",
    "/user/invite": "/h5/user/invite",
    "/viajar-a-china": "/h5/viajar-a-china",
    "/vip": "/h5/vip",
    "/blog": "/h5/blog",
    "/article/invite": "/h5/article/invite",
    "/article/payment-methods": "/h5/article/payment-methods",
    "/article/tutorials": "/h5/article/tutorials",
    "/survey": "/h5/survey",
    "/selector": "/h5/selector",
    "/tiendas-panoramicas-en-3d": "/h5/tiendas-panoramicas-en-3d",
    "/goods/submit-thankyou": "/h5/search/submit-thankyou",
  } as const;
  if (isMobileView.value) {
    const mobileURL = urlMap[currentURL as keyof typeof urlMap];
    if (mobileURL) {
      return navigateTo({
        path: mobileURL,
        query: route.query,
      });
    }
  } else {
    const pcURL = Object.keys(urlMap).find(
      (pcUrl) => urlMap[pcUrl as keyof typeof urlMap] === currentURL
    );
    if (pcURL) {
      return navigateTo({
        path: pcURL,
        query: route.query,
      });
    }
  }
}

const hiddenPaths = [
  "/notas",
  "/h5/notas",
  "/open/notas",
  "/h5/open/notas",
  "/survey",
  "/h5/survey",
  "/open",
  "/inquiry",
  "/vip",
  "/viajar-a-china",
  "/h5/comprar",
  "/h5/comprar/goods",
  "/h5/comprar/success",
];
const hideSiteSelectModal = computed(() =>
  hiddenPaths.includes(normalizePath(route.path))
);

const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: "#E50113",
  },
  Button: {
    colorPrimary: "#E50113",
    colorHoverPrimary: "#E50113",
    colorPressedPrimary: "#E50113",
    colorFocusPrimary: "#E50113",
    colorDisabledPrimary: "#E50113",
    textColorDisabledPrimary: "#E50113",
    textColorTextPrimary: "#E50113",
    textColorTextHoverPrimary: "#E50113",
    textColorTextFocusPrimary: "#E50113",
    textColorTextDisabledPrimary: "#E50113",
    textColorGhostHoverPrimary: "#E50113",
    textColorGhostPressedPrimary: "#E50113",
    borderHoverPrimary: "#E50113",
    borderPressedPrimary: "#E50113",
    borderFocusPrimary: "#E50113",
    rippleColorPrimary: "#E50113",
    textColorTextHover: "#E50113",
  },
};
</script>
