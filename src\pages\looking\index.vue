<template>
  <div class="wrapper">
    <div
      class="w-full flex skus-center justify-between p-[16px] border-b-1 border-solid border-gray-200"
    >
      <n-image
        lazy
        preview-disabled
        object-fit="fill"
        :src="pageTheme.logo"
        class="w-[160px]"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
    </div>
    <div class="px-[16px] flex flex-col gap-y-[12px] pt-[20px] pb-[10px]">
      <div class="text-[18px] leading-[28px] font-medium">
        <span>{{ authStore.i18n("cm_inquiry_queryNumber") }}: </span>
        <span>{{ pageData.goodsFindNo }}</span>
      </div>
      <div class="text-[18px] leading-[28px] flex items-center font-medium">
        Información sobre el producto
      </div>
      <div class="text-[16px]" v-if="pageData.remark">
        <span>
          {{ authStore.i18n("cm_search.searchDescription") }}:
          <span>{{ pageData.remark }}</span>
        </span>
      </div>
    </div>

    <div class="relative w-fit mb-[80px]">
      <table cellspacing="0" class="mx-[16px] mb-[32px] overflow-x-auto">
        <thead>
          <tr>
            <th>
              {{ authStore.i18n("cm_search_createTime") }}
            </th>
            <th>{{ authStore.i18n("cm_submit.country") }}</th>
            <th>Whatsapp</th>
            <th>{{ authStore.i18n("cm_submit.username") }}</th>
            <th>{{ authStore.i18n("cm_search.email") }}</th>
            <th>
              {{ authStore.i18n("cm_search.isAcceptSimilarProduct") }}
            </th>
            <th>
              {{ authStore.i18n("cm_search.totalModelCount") }}
            </th>
          </tr>
        </thead>
        <tbody align="center">
          <tr>
            <td>
              {{ timeFormatByZone(pageData.createTime) }}
            </td>
            <td>{{ pageData.country }}</td>
            <td>{{ pageData.whatsapp }}</td>
            <td>{{ pageData.name }}</td>
            <td class="break-all">{{ pageData.email }}</td>
            <td class="break-all">
              {{ pageData.isAcceptSimilarProduct ? "Sí" : "No" }}
            </td>
            <td class="break-all">{{ pageData.goodsLineCount }}</td>
          </tr>
        </tbody>
      </table>
      <!-- 商品信息 -->
      <table
        cellspacing="0"
        class="mx-[16px] overflow-x-auto w-full"
        v-if="pageData.goodsLines.length"
      >
        <thead>
          <tr>
            <th>NO.</th>
            <th>{{ authStore.i18n("cm_search.goodsName") }}</th>
            <th>
              {{ authStore.i18n("cm_search.goodsImages") }}
            </th>
            <th>{{ authStore.i18n("cm_search.quantity") }}</th>
            <th>
              {{ authStore.i18n("cm_search.searchPriceLimit") }}
            </th>
          </tr>
        </thead>
        <tbody align="center">
          <tr v-for="(goods, index) in pageData.goodsLines" :key="index">
            <td class="w-[60px]">
              {{ goods.lineNo }}
            </td>
            <td class="max-w-[320px] text-left">
              {{ goods.goodsName }}
            </td>
            <td>
              <div class="flex">
                <n-image
                  v-for="(image, imageIndex) in goods.imageList"
                  :key="imageIndex"
                  lazy
                  object-fit="fill"
                  class="w-[60px] h-[60px] shrink-0 cursor-pointer"
                  :src="image"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
              </div>
            </td>
            <td class="min-w-[120px]">{{ goods.count }}</td>
            <td class="min-w-[120px]">{{ onGetPriceLimit(goods) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup lang="ts">
const message = useMessage();
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import { timeFormatByZone } from "@/utils/date";

const route = useRoute();
const authStore = useAuthStore();
const pageTheme = computed(() => useConfigStore().getPageTheme);
const pageData = reactive<any>({});

await onGetGoodsFindDetail();
async function onGetGoodsFindDetail() {
  const res: any = await useGoodsFindDetail({ id: route.query?.id });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}

function onGetPriceLimit(goods: any) {
  if (!goods.isPriceLimited) {
    return authStore.i18n("cm_search.priceUnlimit");
  } else {
    if (goods.minPrice && goods.maxPrice) {
      return `${goods.minPrice} - ${goods.maxPrice}`;
    } else if (goods.minPrice && !goods.maxPrice) {
      return `${goods.minPrice} - `;
    } else if (!goods.minPrice && goods.maxPrice) {
      return `0 - ${goods.maxPrice}`;
    }
  }
}
</script>
<style scoped lang="scss">
table,
th,
td {
  padding: 8px;
  border: 1px solid #d7d7d7;
  border-collapse: collapse; /* 移除单元格之间的间隔 */
}
thead th {
  color: #7f7f7f;
  background-color: #f2f2f2; /* 设置背景色为灰色 */
}
.n-divider {
  margin: 10px 0;
}
.wrapper {
  height: 100vh;
  width: 100%;
  overflow: auto;
}
</style>
