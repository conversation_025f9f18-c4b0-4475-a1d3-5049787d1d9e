/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { MidCartModel } from "../../../chilat/basis/model/cart_model";
import { MallCategoryPathItemModel, MallCategoryTreeModel } from "../../../chilat/basis/model/goods_model";
import { ConfigModel } from "../../../chilat/basis/model/sys_translate_model";
import { Result } from "../../../common";
import { SeoData } from "../../../common/business";
import { GoodsInfoModel } from "../../commodity/model/goods_info_model";

export const protobufPackage = "mall.pages";

export interface GoodsDetailPageResp {
  result: Result | undefined;
  data: GoodsDetailPageModel | undefined;
}

export interface GoodsDetailPageModel {
  /** 商城全局配置（当前语言，翻译信息等） */
  globalConfig:
    | ConfigModel
    | undefined;
  /** passport.LoginUserModel loginUser = 20; //登录用户信息（空值表示未登录） */
  seoData:
    | SeoData
    | undefined;
  /** 类目树 */
  categoryTree:
    | MallCategoryTreeModel
    | undefined;
  /** 商品分类路径 */
  categoryPath: MallCategoryPathItemModel[];
  /** 购物车数据 */
  cartInfo:
    | MidCartModel
    | undefined;
  /** 页面内主要数据内容 */
  pageData: GoodsInfoModel | undefined;
}

export interface GoodsDetailDataResp {
  result:
    | Result
    | undefined;
  /** 页面内主要数据内容 */
  data: GoodsInfoModel | undefined;
}

function createBaseGoodsDetailPageResp(): GoodsDetailPageResp {
  return { result: undefined, data: undefined };
}

export const GoodsDetailPageResp = {
  encode(message: GoodsDetailPageResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GoodsDetailPageModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsDetailPageResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsDetailPageResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = GoodsDetailPageModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsDetailPageResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GoodsDetailPageModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GoodsDetailPageResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GoodsDetailPageModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsDetailPageResp>, I>>(base?: I): GoodsDetailPageResp {
    return GoodsDetailPageResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsDetailPageResp>, I>>(object: I): GoodsDetailPageResp {
    const message = createBaseGoodsDetailPageResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GoodsDetailPageModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseGoodsDetailPageModel(): GoodsDetailPageModel {
  return {
    globalConfig: undefined,
    seoData: undefined,
    categoryTree: undefined,
    categoryPath: [],
    cartInfo: undefined,
    pageData: undefined,
  };
}

export const GoodsDetailPageModel = {
  encode(message: GoodsDetailPageModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.globalConfig !== undefined) {
      ConfigModel.encode(message.globalConfig, writer.uint32(82).fork()).ldelim();
    }
    if (message.seoData !== undefined) {
      SeoData.encode(message.seoData, writer.uint32(242).fork()).ldelim();
    }
    if (message.categoryTree !== undefined) {
      MallCategoryTreeModel.encode(message.categoryTree, writer.uint32(322).fork()).ldelim();
    }
    for (const v of message.categoryPath) {
      MallCategoryPathItemModel.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    if (message.cartInfo !== undefined) {
      MidCartModel.encode(message.cartInfo, writer.uint32(562).fork()).ldelim();
    }
    if (message.pageData !== undefined) {
      GoodsInfoModel.encode(message.pageData, writer.uint32(802).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsDetailPageModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsDetailPageModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.globalConfig = ConfigModel.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.seoData = SeoData.decode(reader, reader.uint32());
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.categoryTree = MallCategoryTreeModel.decode(reader, reader.uint32());
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.categoryPath.push(MallCategoryPathItemModel.decode(reader, reader.uint32()));
          continue;
        case 70:
          if (tag !== 562) {
            break;
          }

          message.cartInfo = MidCartModel.decode(reader, reader.uint32());
          continue;
        case 100:
          if (tag !== 802) {
            break;
          }

          message.pageData = GoodsInfoModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsDetailPageModel {
    return {
      globalConfig: isSet(object.globalConfig) ? ConfigModel.fromJSON(object.globalConfig) : undefined,
      seoData: isSet(object.seoData) ? SeoData.fromJSON(object.seoData) : undefined,
      categoryTree: isSet(object.categoryTree) ? MallCategoryTreeModel.fromJSON(object.categoryTree) : undefined,
      categoryPath: globalThis.Array.isArray(object?.categoryPath)
        ? object.categoryPath.map((e: any) => MallCategoryPathItemModel.fromJSON(e))
        : [],
      cartInfo: isSet(object.cartInfo) ? MidCartModel.fromJSON(object.cartInfo) : undefined,
      pageData: isSet(object.pageData) ? GoodsInfoModel.fromJSON(object.pageData) : undefined,
    };
  },

  toJSON(message: GoodsDetailPageModel): unknown {
    const obj: any = {};
    if (message.globalConfig !== undefined) {
      obj.globalConfig = ConfigModel.toJSON(message.globalConfig);
    }
    if (message.seoData !== undefined) {
      obj.seoData = SeoData.toJSON(message.seoData);
    }
    if (message.categoryTree !== undefined) {
      obj.categoryTree = MallCategoryTreeModel.toJSON(message.categoryTree);
    }
    if (message.categoryPath?.length) {
      obj.categoryPath = message.categoryPath.map((e) => MallCategoryPathItemModel.toJSON(e));
    }
    if (message.cartInfo !== undefined) {
      obj.cartInfo = MidCartModel.toJSON(message.cartInfo);
    }
    if (message.pageData !== undefined) {
      obj.pageData = GoodsInfoModel.toJSON(message.pageData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsDetailPageModel>, I>>(base?: I): GoodsDetailPageModel {
    return GoodsDetailPageModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsDetailPageModel>, I>>(object: I): GoodsDetailPageModel {
    const message = createBaseGoodsDetailPageModel();
    message.globalConfig = (object.globalConfig !== undefined && object.globalConfig !== null)
      ? ConfigModel.fromPartial(object.globalConfig)
      : undefined;
    message.seoData = (object.seoData !== undefined && object.seoData !== null)
      ? SeoData.fromPartial(object.seoData)
      : undefined;
    message.categoryTree = (object.categoryTree !== undefined && object.categoryTree !== null)
      ? MallCategoryTreeModel.fromPartial(object.categoryTree)
      : undefined;
    message.categoryPath = object.categoryPath?.map((e) => MallCategoryPathItemModel.fromPartial(e)) || [];
    message.cartInfo = (object.cartInfo !== undefined && object.cartInfo !== null)
      ? MidCartModel.fromPartial(object.cartInfo)
      : undefined;
    message.pageData = (object.pageData !== undefined && object.pageData !== null)
      ? GoodsInfoModel.fromPartial(object.pageData)
      : undefined;
    return message;
  },
};

function createBaseGoodsDetailDataResp(): GoodsDetailDataResp {
  return { result: undefined, data: undefined };
}

export const GoodsDetailDataResp = {
  encode(message: GoodsDetailDataResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      GoodsInfoModel.encode(message.data, writer.uint32(802).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GoodsDetailDataResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGoodsDetailDataResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 100:
          if (tag !== 802) {
            break;
          }

          message.data = GoodsInfoModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GoodsDetailDataResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? GoodsInfoModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: GoodsDetailDataResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = GoodsInfoModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GoodsDetailDataResp>, I>>(base?: I): GoodsDetailDataResp {
    return GoodsDetailDataResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsDetailDataResp>, I>>(object: I): GoodsDetailDataResp {
    const message = createBaseGoodsDetailDataResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? GoodsInfoModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
