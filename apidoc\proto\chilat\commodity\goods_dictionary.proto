syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_dictionary_param.proto";
import "chilat/commodity/model/goods_dictionary_model.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

// 商品包装规格
service GoodsDictionary {
  // 包装规格列表
  rpc pageList (GoodsDictionaryPageQueryParam) returns (GoodsDictionaryPageResp);
  // 保存包装规格
  rpc saveGoodsDictionary (GoodsDictionarySaveParam) returns (common.ApiResult);
  // 删除包装规格
  rpc removeGoodsDictionary (common.IdsParam) returns (common.ApiResult);
  // 查询日志
  rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
}