syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common/business.proto";
import "common.proto";

message GoodsSearchPageQueryResp {
    common.Result result = 1;
    common.Page page = 2;
    repeated GoodsSearchQueryModel data = 3;
}

message GoodsSearchQueryResp {
    common.Result result = 1;
    GoodsSearchQueryModel data = 2;
}

message GoodsSearchImageResp {
    common.Result result = 1;
    GoodsSearchImageModel data = 2;
}

// 上传图片
message GoodsSearchImageModel {
    string imageId = 1; // 图片ID
    string imageUrl = 2; // 图片链接
}

// 商品列表
message GoodsSearchQueryModel {
    string goodsId = 10; // 商品ID
    string goodsNo = 20; // 商品编码
    string goodsName = 30; // 商品名称
    string goodsTitle = 40; // 商品标题
    string goodsPriceUnitName = 45; // 价格单位
    common.CurrencyType currency = 46; // 价格对应的货币
    double minPrice = 50; // 最低价
    double maxPrice = 55; // 最高价（若最低价与最高价相同，则在列表显示一个价格）
    int32 minBuyQuantity = 110; //最小购买数量（起订量）
    int32 minIncreaseQuantity = 120; //最小加购数量（一次加购数量）
    string mainImageUrl = 60; // 商品主图
    string sourceGoodsId = 70; // 来源商品ID
}