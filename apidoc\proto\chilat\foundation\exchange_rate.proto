syntax = "proto3";
package chilat.foundation;

option java_package = "com.chilat.rpc.foundation";

import "chilat/foundation/model/exchange_rate_model.proto";
import "chilat/foundation/param/exchange_rate_param.proto";
import "common.proto";

// 汇率管理
service ExchangeRate {
  // 查询列表
  rpc pageList (ExchangeRatePageQueryParam) returns (ExchangeRatePageResp);
  // 保存汇率
  rpc saveExchangeRate (ExchangeRateSaveParam) returns (common.ApiResult);
  // 查询汇率日志
  rpc listLog (common.IdParam) returns (ExchangeRateLogListResp);

}