<template>
  <div class="page-container">
    <div>
      <img
        loading="lazy"
        :src="headerBg"
        width="100%"
        referrerpolicy="no-referrer"
      />
    </div>

    <div
      class="flex justify-center items-center px-[0.1rem] py-[0.3rem] text-[#e50113] text-[0.28rem] bg-[#f4f4f4]"
    >
      <img
        loading="lazy"
        :src="noticeLogo"
        class="mr-[0.1rem] w-[0.52rem]"
        referrerpolicy="no-referrer"
      />
      {{ authStore.i18n("cm_guestHome.noticeTitle") }}
    </div>

    <!-- 一站式购物服务-->
    <div class="service-wrapper">
      <ul class="service-item-wrapper">
        <li
          class="service-item"
          v-for="(link, index) in serviceData"
          :key="index"
          @mouseenter="onLinkHover(index)"
          :class="{
            'item-enter': pageData.activatedLinkIndex == index,
          }"
          :click-title="`${index + 1}. ${link.title} `"
        >
          <n-image
            lazy
            preview-disabled
            class="item-icon"
            :src="link.img"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
          <div class="item-content-wrapper">
            <div class="item-title">
              {{ link.title }}
            </div>
            <div class="item-desc">
              <div class="item-content">
                <n-space vertical :style="{ gap: '0.04rem 0' }">
                  <div
                    v-for="(content, contentIndex) in link.content"
                    :key="contentIndex"
                  >
                    {{ content }}
                  </div>
                </n-space>
              </div>
            </div>
          </div>
        </li>

        <div class="item-tail"></div>
      </ul>
    </div>
    <!-- 美客多和义乌推荐 -->
    <div
      v-if="props.yiwuHotSaleGoods?.goodsList?.length > 0"
      data-spm-box="homepage-hot-yiwu"
    >
      <n-image
        lazy
        preview-disabled
        class="carousel-img pb-1"
        :src="props.yiwuHotSaleGoods.banner"
        @click="onYiWuHotViewMore(props.yiwuHotSaleGoods, $event)"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
      <category-card
        :moreButton="true"
        :cateInfo="props.yiwuHotSaleGoods"
        data-spm-box="homepage-hot-yiwu"
      ></category-card>
    </div>
    <div
      v-if="props.mercadoHotSaleGoods?.goodsList?.length > 0"
      data-spm-box="homepage-hot-mercado"
    >
      <n-image
        lazy
        preview-disabled
        class="carousel-img pb-1"
        :src="props.mercadoHotSaleGoods?.banner"
        @click="onMercadoHotViewMore(props.mercadoHotSaleGoods, $event)"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
      <category-card
        :moreButton="true"
        :cateInfo="props.mercadoHotSaleGoods"
        data-spm-box="homepage-hot-mercado"
      ></category-card>
    </div>

    <!-- 降低成本，增加效益 -->
    <div class="simplify-purchase">
      <div class="purchase-title">
        {{ authStore.i18n("cm_guestHome.purchaseTitle") }}
      </div>
      <div class="purchase-desc">
        {{ authStore.i18n("cm_guestHome.purchaseDesc") }}
      </div>
      <img
        loading="lazy"
        :src="purchaseImg"
        class="w-[60%] mx-auto"
        referrerpolicy="no-referrer"
      />
    </div>

    <!-- 商品分类 -->
    <div
      class="category-wrapper"
      id="NavItem1"
      data-spm-box="homepage-top-categories"
    >
      <div class="w-full">
        <div class="category-title">
          {{ authStore.i18n("cm_guestHome.categoryTitle") }}
        </div>
        <div class="category-number">
          <div
            class="number-item"
            v-for="(item, index) in categoryIntroData"
            :key="index"
          >
            <span>{{ item.number }}</span>
            <p>
              {{ item.title }}
            </p>
          </div>
        </div>
        <div class="category-list">
          <div
            class="category-row"
            v-for="(cate, index) in pageData.categoryData"
            :key="index"
          >
            <a
              v-for="cateItem in cate"
              :key="cateItem.id"
              v-bind:data-spm-index="index + 1"
              :href="`/h5/search/list?categoryId=${cateItem.id}&cateName=${cateItem.name}`"
              ><div class="category-item">
                <n-image
                  lazy
                  preview-disabled
                  :src="cateItem.cateLogo"
                  class="img"
                  :img-props="{ referrerpolicy: 'no-referrer' }"
                />
                <div class="category-name">
                  <span>{{ cateItem.name }}</span>
                </div>
              </div></a
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 安全 -->
    <div
      class="security-floor"
      id="NavItem3"
      :style="`background-image: url(${securityBg})`"
    >
      <div class="security-floor-title">
        {{ authStore.i18n("cm_guestHome.securityTitle") }}
      </div>
      <div>
        <div
          class="security-floor-item"
          v-for="(item, index) in securityData"
          :key="index"
        >
          <div class="flex items-center">
            <img
              loading="lazy"
              class="floor-item-icon"
              :src="item.icon"
              referrerpolicy="no-referrer"
            />
            <div class="floor-item-title">
              {{ item.title }}
            </div>
          </div>
          <div class="floor-item-desc">
            {{ item.desc }}
          </div>
        </div>
      </div>
    </div>

    <!-- 为什么选择我们？ -->
    <div class="full-link">
      <div class="full-title">
        {{ authStore.i18n("cm_guestHome.fullTitle") }}
      </div>
      <ul class="full-link_item_wrapper">
        <li
          class="full-link_item"
          v-for="(link, index) in chooseData"
          :key="index"
        >
          <div class="full-link_content">
            <div class="full-link_item_title">
              <span class="text-[#e50113] text-[0.32rem]"
                >0{{ index + 1 }}</span
              >
              {{ link.title }}
            </div>
            <div class="full-link_item_desc">
              {{ link.desc }}
            </div>
          </div>
        </li>
      </ul>
      <div>
        <n-carousel
          autoplay
          show-dots
          style="height: 4.2rem; overflow: hidden"
          :space-between="10"
          :transition-style="{ transitionDuration: '500ms' }"
          :interval="3000"
        >
          <n-image
            lazy
            preview-disabled
            :src="carousel"
            v-for="(carousel, index) in carouselData"
            :key="index"
            class="h-[4.2rem]"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
        </n-carousel>
      </div>
    </div>

    <!-- 注册登录 -->
    <div class="login-guide" :style="`background-image: url(${loginBg})`">
      <div class="login-guide-wrapper">
        <div class="login-title">
          {{ authStore.i18n("cm_guestHome.loginTitle") }}
        </div>
        <div class="login-desc">
          {{ authStore.i18n("cm_guestHome.loginDesc") }}
        </div>
        <a
          href="/h5/user/register?pageSource=/h5"
          data-spm-box="homepage-body-register"
        >
          <n-button color="#db2221" class="section_banner-button">
            {{ authStore.i18n("cm_common.createAccount") }}
          </n-button>
        </a>
        <div class="mt-[0.2rem] text-[0.28rem]">
          {{ authStore.i18n("cm_common.haveAccount") }}
          <a
            href="/h5/user/login?pageSource=/h5"
            data-spm-box="homepage-body-login"
          >
            <span class="text-[#e50113] text-[0.32rem]">{{
              authStore.i18n("cm_common.loginAccount")
            }}</span>
          </a>
        </div>
      </div>
    </div>

    <!-- 用户评价 -->
    <div class="user-video">
      <div class="video-title">
        {{ authStore.i18n("cm_guestHome.userVideoTitle") }}
      </div>
      <div class="video-wrapper">
        <div
          class="video-item"
          v-for="(video, index) in userVideoData"
          :key="video.id"
          @click="onOpenVideo(video, index)"
        >
          <n-image
            lazy
            preview-disabled
            :src="video.videoBg"
            class="img"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
          <div class="video-icon">
            <icon-card
              name="mingcute:play-fill"
              size="20"
              color="#322623"
            ></icon-card>
          </div>
        </div>
      </div>
    </div>
    <video-modal ref="videoModalRef"></video-modal>
    <login-register-modal ref="loginRegisterModalRef"></login-register-modal>
  </div>
</template>

<script setup lang="ts">
import CategoryCard from "./CategoryCard.vue";
import { useAuthStore } from "@/stores/authStore";
import headerBg from "@/assets/icons/guestMHome.jpg";
import LoginRegisterModal from "./LoginRegisterModal.vue";

const props = defineProps({
  yiwuHotSaleGoods: {
    type: Object,
    default: () => <any>{},
  },
  mercadoHotSaleGoods: {
    type: Object,
    default: () => <any>{},
  },
});

const authStore = useAuthStore();
const videoModalRef = ref<any>(null);
const pageData = reactive(<any>{
  categoryData: <any>[],
  activatedLinkIndex: 0,
});

const purchaseImg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/e7a794cc-524c-485c-bc14-50932983530c.png";
const securityBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/b9cb4cb8-7189-4a3e-8241-10e39bc0fafa.png";
const loginBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/27/afb3f499-42c5-4ca4-8f0d-760e36a4b591.png";
const noticeLogo =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/268c3fd6-85ef-4db5-8640-549d908d570b.png";
const step1Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/c25eb123-2827-4975-b1bc-de430fd6048f.png";
const step2Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f7ac575b-89ac-49e9-be5c-afc4b8e88700.png";
const step3Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/21d38827-c33d-4772-9ea1-7a109dc175ec.png";
const step4Img =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/9b3fe249-e8d8-46a5-8137-c71bc9d524fa.png";

const serviceData = [
  {
    img: step1Img,
    title: authStore.i18n("cm_guestHome.chooseGoods"),
    content: [
      authStore.i18n("cm_guestHome.addGoods"),
      authStore.i18n("cm_guestHome.orderGoods"),
    ],
  },
  {
    img: step2Img,
    title: authStore.i18n("cm_guestHome.confirmPrice"),
    content: [
      authStore.i18n("cm_guestHome.countPrice"),
      authStore.i18n("cm_guestHome.predictPrice"),
      authStore.i18n("cm_guestHome.payPrice"),
    ],
  },
  {
    img: step3Img,
    title: authStore.i18n("cm_guestHome.payProduct"),
    content: [
      authStore.i18n("cm_guestHome.transProduct"),
      authStore.i18n("cm_guestHome.checkProduct"),
      authStore.i18n("cm_guestHome.storageProduct"),
    ],
  },
  {
    img: step4Img,
    title: authStore.i18n("cm_guestHome.interLogistics"),
    content: [
      authStore.i18n("cm_guestHome.chooseLogistics"),
      authStore.i18n("cm_guestHome.trackLogistics"),
      authStore.i18n("cm_guestHome.confirmLogistics"),
    ],
  },
];

const categoryIntroData = [
  {
    number: authStore.i18n("cm_guestHome.number1"),
    title: authStore.i18n("cm_guestHome.numberDesc1"),
  },
  {
    number: authStore.i18n("cm_guestHome.number2"),
    title: authStore.i18n("cm_guestHome.numberDesc2"),
  },
];

const securityData = [
  {
    title: authStore.i18n("cm_guestHome.securityQual"),
    desc: authStore.i18n("cm_guestHome.securityQualDesc"),
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/3447a23e-9bb7-4824-a978-97ee506721b8.png",
  },
  {
    title: authStore.i18n("cm_guestHome.securityPay"),
    desc: authStore.i18n("cm_guestHome.securityPayDesc"),
    icon: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/001a3c9b-c5b1-42be-8eb9-ca6eb718d918.png",
  },
];

const chooseData = [
  {
    title: authStore.i18n("cm_guestHome.brandTitle"),
    desc: authStore.i18n("cm_guestHome.brandDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.teamTitle"),
    desc: authStore.i18n("cm_guestHome.teamDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.resourceTitle"),
    desc: authStore.i18n("cm_guestHome.resourceDesc"),
  },
];

const carouselData = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
  },
  {
    id: "Tj0nrnhxgXw",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
  },
  {
    id: "_omi5a-pHkA",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
  },
  {
    id: "4FVIz0PvEcE",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
  },
];

onPageCategoryData();
onMounted(() => {
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
});

function scrollToNav(nav: any) {
  if (!nav.navId) return;
  const element = document.getElementById(nav.navId);
  if (!element) return;
  const additionalOffset = 2.4 * 50; //固定头部搜索栏的高度
  const elementRect = element.getBoundingClientRect();
  const offsetPosition = elementRect.top + window.scrollY - additionalOffset;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth",
  });
}

async function onPageCategoryData() {
  const res: any = await useHomePageCategory({});
  if (res?.result?.code === 200) {
    pageData.categoryData = organizeDataByColumns(res?.data);
  }
}

function organizeDataByColumns(data: any[]) {
  const numRows = 2;
  const organizedData = [];
  for (let i = 0; i < data.length; i += numRows) {
    const column = data.slice(i, i + numRows);
    organizedData.push(column);
  }
  return organizedData;
}

const onLinkHover = (index: any) => {
  pageData.activatedLinkIndex = index;
};

function onMercadoHotViewMore(item: any, event: any) {
  navigateToPage(
    "/h5/search/list",
    { tagId: item.tagId, tag: "mercado" },
    false,
    event
  );
}

function onYiWuHotViewMore(item: any, event: any) {
  navigateToPage(
    "/h5/search/list",
    { tagId: item.tagId, tag: "yiwu" },
    false,
    event
  );
}

function onOpenVideo(video: any, index: any) {
  if (videoModalRef.value) {
    window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
    videoModalRef.value.onOpenVideo(video);
  }
}
</script>
<style scoped lang="scss">
.page-container {
  height: 100%;
  overflow-y: auto;
  background-color: #f6f6f6;
  min-height: 100vh;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-wrapper {
  width: 100%;
  background-color: #fff;
  padding: 0.6rem 0.32rem 0.6rem;
  .service-title {
    font-size: 0.48rem;
    line-height: 0.6rem;
    font-weight: 500;
    color: #222;
  }
  .service-item-wrapper {
    -webkit-flex-direction: column;
    flex-direction: column;
    // margin-top: 0.6rem;
    position: relative;
    width: 100%;
    display: flex;
    .service-item {
      cursor: pointer;
      z-index: 1;
      display: flex;
    }
    .item-enter:not(:last-of-type) {
      margin-bottom: 0.08rem;
    }
    .service-item:not(:last-of-type) {
      margin-bottom: 0.32rem;
    }
    .service-item:not(:first-child) {
      margin-top: 0.32rem;
    }

    .item-icon {
      height: 0.74rem;
      width: 0.74rem;
      flex-shrink: 0;
      position: relative;
      margin-inline-end: 0.28rem;
      background-color: #fff;
      box-shadow: 0 0 0 0.16rem #fff;
      z-index: 2;
    }
    .full-link_item_title {
      font-size: 0.28rem;
      line-height: 0.48rem;
    }
    .item-desc {
      color: #767676;
      display: none;
      font-size: 0.24rem;
      height: fit-content;
      line-height: 0.48rem;
      margin-top: 0.12rem;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-enter .item-icon {
      border-width: 0;
      -webkit-box-shadow: 0 0 0 0.12rem #fff;
      box-shadow: 0 0 0 0.12rem #fff;
      transform: scale(1.2);
    }
    .item-enter .item-title {
      color: #51200b;
      font-size: 0.32rem;
      font-weight: 500;
      line-height: 0.48rem;
      margin-top: -0.1rem;
    }

    .item-enter .item-desc {
      display: -webkit-box;
    }
    .item-tail {
      background-color: #ddd;
      height: 100%;
      left: 0.322rem;
      position: absolute;
      width: 0.03rem;
    }
  }
  .item-enter:last-of-type ~ .item-tail {
    height: calc(100% - 1.32rem);
  }
}

.category-wrapper {
  width: 100%;
  padding: 0.6rem 0.32rem;
  background-color: #fff;
  .category-title {
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #222;
  }
  .category-number {
    display: grid;
    row-gap: 0.2rem;
    column-gap: 0.2rem;
    grid-template-columns: 1fr 1fr;
    height: fit-content;
    .number-item {
      padding-left: 0.32rem;
      position: relative;
      margin: 0.32rem 0;
      &::before {
        background-color: #ddd;
        border-radius: 0.04rem;
        content: "";
        display: inline-block;
        height: 100%;
        left: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 0.06rem;
      }
      span {
        color: #db2221;
        font-size: 0.32rem;
        font-weight: 500;
        letter-spacing: -0.0146rem;
        line-height: 0.6rem;
      }
      p {
        font-size: 0.24rem;
        line-height: 0.56rem;
      }
    }
  }
  .category-list {
    margin-top: 0.32rem;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }

    .category-row {
      height: 100%;
      a {
        color: inherit;
        text-decoration: inherit;
      }
      .category-item {
        align-items: center;
        border: 0.04rem solid #eaeaea;
        border-radius: 1.4rem;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        height: 1.84rem;
        justify-content: center;
        margin-bottom: 0.2rem;
        margin-right: 0.16rem;
        width: 1.84rem;
        &:hover {
          border: 0.04rem solid #db2221;
        }
        .img {
          display: initial;
          height: 0.4rem;
          vertical-align: middle;
          width: 0.4rem;
          padding: 0.04rem;
          box-sizing: content-box;
          margin-bottom: 0.04rem;
        }
        .category-name {
          height: 0.64rem;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            color: #222;
            display: -webkit-box;
            font-size: 0.24rem;
            font-weight: 400;
            line-height: 0.32rem;
            overflow: hidden;
            padding: 0 0.08rem;
            text-align: center;
            width: 1.4rem;
          }
        }
      }
    }
  }
}

.simplify-purchase {
  width: 100%;
  padding: 0.6rem 0.32rem;
  background-color: #f0f0f0;
  .purchase-title {
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #222;
  }
  .purchase-desc {
    font-size: 0.24rem;
    margin: 0.28rem 0;
  }
}

.security-floor {
  width: 100%;
  background-color: #442b20;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 180% 100%;
  color: #fff;
  margin: auto;
  overflow: hidden;
  width: 100%;
  padding: 0.6rem 0.32rem;
  .security-floor-title {
    width: 100%;
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 0.4rem;
  }
  .security-floor-item {
    background: hsla(0, 0%, 100%, 0.11);
    border-radius: 0.16rem;
    font-size: 0.36rem;
    line-height: 0.48rem;
    overflow: hidden;
    padding: 0.32rem 0.16rem;
    position: relative;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    width: 100%;
    margin-bottom: 0.2rem;
    .floor-item-icon {
      display: block;
      height: 0.48rem;
      margin-right: 0.08rem;
    }
    .floor-item-title {
      font-size: 0.32rem;
      font-weight: 500;
      line-height: 0.48rem;
      margin-bottom: 0.12rem;
    }
    .floor-item-desc {
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 0.24rem;
      line-height: 0.36rem;
    }
  }
}

.full-link {
  width: 100%;
  color: #222;
  background-color: #fff;
  padding: 0.6rem 0.32rem;
  .full-title {
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #222;
    margin-bottom: 0.3rem;
    text-align: center;
  }

  .full-link_title {
    -webkit-box-orient: vertical;
    color: #222;
    display: -webkit-box;
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
  }
  .full-link_item_wrapper {
    -webkit-flex-direction: column;
    flex-direction: column;
    position: relative;
    width: 100%;
    display: flex;
    margin-bottom: 0.32rem;
    .full-link_item {
      cursor: pointer;
      z-index: 1;
      display: flex;
    }
    .full-link_item:not(:last-of-type) {
      margin-bottom: 0.2rem;
    }
    .full-link_item:not(:first-child) {
      margin-top: 0.2rem;
    }
    .full-link_item_title {
      font-size: 0.28rem;
      line-height: 0.48rem;
      font-weight: bold;
    }
    .full-link_item_desc {
      color: #767676;
      font-size: 0.24rem;
      height: fit-content;
      line-height: 0.4rem;
      margin-top: 0.12rem;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .full-link_icon {
      width: 0.4rem;
      height: 0.4rem;
      margin-inline-end: 0.2rem;
      position: relative;
      flex-shrink: 0;
      background-color: #e50113;
      border-radius: 50%;
      margin-top: 0.2rem;
      flex-shrink: 0;
    }
    .full-link_item_tail {
      background-color: #3a180b;
      height: 104%;
      left: 0.18rem;
      position: absolute;
      width: 0.04rem;
      border-radius: 0.04rem;
      top: -0.12rem;
    }
  }
}

.login-guide {
  width: 100%;
  background-color: #473229;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  text-align: center;
  width: 100%;
  position: relative;
  .login-guide-wrapper {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.6rem 0.32rem;
    color: #000;
    z-index: 2;
  }
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1;
  }

  .login-title {
    font-size: 0.4rem;
    font-weight: 500;
    line-height: 0.6rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .login-desc {
    font-size: 0.28rem;
    line-height: 0.4rem;
    margin-top: 0.2rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .section_banner-button {
    width: 3.8rem;
    font-size: 0.32rem;
    line-height: 0.7rem;
    padding: 0.2rem 0.34rem;
    border-radius: 0.4rem;
    margin-top: 0.36rem;
    box-shadow: 0.06rem 0.06rem 0.1rem rgba(0, 0, 0, 0.3);
  }
}

.user-video {
  width: 100%;
  padding: 0.6rem 0.32rem;

  .video-title {
    font-size: 0.36rem;
    font-weight: 500;
    line-height: 0.6rem;
    text-align: center;
  }
  .video-wrapper {
    width: 100%;
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }
  .video-item {
    flex: 0 0 2.8rem;
    width: 2.8rem;
    margin: 0.2rem 0.2rem 0.2rem 0;
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      border-radius: 0.24rem;
    }
    .video-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #fff;
      width: 0.6rem;
      height: 0.6rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

:deep(.n-carousel .n-carousel__slides .n-carousel__slide) {
  text-align: center;
}
:deep(.n-timeline-item-timeline__line) {
  background-color: #e50113 !important;
  background-image: linear-gradient(
    180deg,
    #fff,
    #fff 50%,
    transparent 50%,
    transparent 100%
  ) !important;
}
</style>
