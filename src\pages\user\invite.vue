<template>
  <n-scrollbar class="bg-white h-full px-8 pt-12 pb-4 overflow-y-auto">
    <n-flex vertical>
      <n-flex vertical class="w-[600px] mx-auto">
        <div class="text-[22px] font-medium">
          {{ authStore.i18n("cm_invite.inviteFriendTitle") }}
        </div>
        <div>
          <n-ellipsis :line-clamp="3">
            {{ authStore.i18n("cm_invite.inviteFriendDesc") }}
          </n-ellipsis>
        </div>
        <div class="flex">
          <span class="mr-2">
            <img
              loading="lazy"
              class="min-w-[30px] w-[30px]"
              alt="image-search"
              src="@/assets/icons/inviteCouponSelf.svg"
              referrerpolicy="no-referrer"
            />
          </span>
          <div>
            <div class="text-[18px] font-medium">
              {{ authStore.i18n("cm_invite.selfGain") }}
            </div>
            <div class="mt-1 pb-4">
              {{ authStore.i18n("cm_invite.selfGainUseDesc") }}
            </div>
          </div>
        </div>

        <div class="flex">
          <span class="mr-2">
            <img
              loading="lazy"
              class="min-w-[30px] w-[30px]"
              alt="image-search"
              src="@/assets/icons/inviteCouponOther.svg"
              referrerpolicy="no-referrer"
            />
          </span>
          <div>
            <span class="text-[18px] font-medium">{{
              authStore.i18n("cm_invite.friendGain")
            }}</span>
            <div class="mt-1 pb-4">
              {{ authStore.i18n("cm_invite.friendGainUseDesc") }}
            </div>
          </div>
        </div>
      </n-flex>

      <div
        class="invite-link flex justify-between items-center w-[76%] mx-auto bg-[#F2F2F2]"
        v-if="!pageData.verifyMailResult?.isMailVerified"
      >
        <div class="flex justify-between items-center mr-4">
          <img
            loading="lazy"
            class="min-w-[48px] w-[48px] mr-2"
            alt="image-search"
            src="@/assets/icons/inviteWarning.svg"
            referrerpolicy="no-referrer"
          />
          <span>{{ authStore.i18n("cm_invite.verificationMailbox") }}</span>
        </div>
        <div>
          <n-button
            class="px-4"
            color="#E50113"
            round
            @click="onMailVerified"
            :loading="pageData.verifyLoading"
          >
            <span class="text-white">{{
              authStore.i18n("cm_invite.viewNow")
            }}</span>
          </n-button>
        </div>
      </div>
      <div v-else>
        <div
          class="invite-link flex justify-between items-center w-[76%] mx-auto bg-[#F2F2F2]"
        >
          <div class="flex mr-8">
            <n-ellipsis class="text-gray-800 mr-3 flex-shrink-0"
              >{{ authStore.i18n("cm_user.inviteLink") }}:</n-ellipsis
            >
            <div style="max-width: 600px" class="text-sm break-all">
              {{ pageData.userInfo.inviteLink }}
            </div>
          </div>
          <n-button
            round
            color="#F6D2D4"
            text-color="#E50113"
            @click="onHandleCopyText(pageData.userInfo.inviteLink)"
          >
            {{ authStore.i18n("cm_user.copy") }}
          </n-button>
        </div>
        <div class="invite-link mt-4 w-[76%] mx-auto bg-[#fafafa]">
          <div>
            <span>
              <n-button
                text
                class="text-base !text-[#333]"
                @click="onVerifyClick(0)"
                ><span
                  :class="
                    pageData.verifySelect === 0
                      ? 'text-lg font-medium underline decoration-1.5 underline-offset-6'
                      : ''
                  "
                  >{{ authStore.i18n("cm_invite.verify") }}</span
                ></n-button
              >
            </span>
            <span class="px-1">
              <n-divider vertical />
            </span>
            <span
              ><n-button
                text
                class="text-base !text-[#333]"
                @click="onVerifyClick(1)"
                ><span
                  :class="
                    pageData.verifySelect === 1
                      ? 'text-lg font-medium underline decoration-1.5 underline-offset-6'
                      : ''
                  "
                  >{{ authStore.i18n("cm_invite.notVerify") }}</span
                ></n-button
              ></span
            >
          </div>
          <div class="mt-4 mb-2">
            <span>
              <n-button
                text
                class="sub-button"
                :class="
                  pageData.dateSelect === 0
                    ? 'text-[#e50113] !border-[#e50113]'
                    : ''
                "
                @click="onDateClick(0)"
                >{{ authStore.i18n("cm_invite.lastMonth") }}</n-button
              >
            </span>
            <span class="px-1">
              <n-divider vertical />
            </span>
            <span>
              <n-button
                text
                class="sub-button"
                :class="
                  pageData.dateSelect === 1
                    ? 'text-[#e50113] !border-[#e50113]'
                    : ''
                "
                @click="onDateClick(1)"
                >{{ authStore.i18n("cm_invite.pastThreeMonths") }}</n-button
              >
            </span>
            <span class="px-1">
              <n-divider vertical />
            </span>
            <span>
              <n-button
                text
                class="sub-button"
                :class="
                  pageData.dateSelect === 2
                    ? 'text-[#e50113] !border-[#e50113]'
                    : ''
                "
                @click="onDateClick(2)"
                >{{ authStore.i18n("cm_invite.pastSixMonths") }}</n-button
              >
            </span>
            <span class="px-1">
              <n-divider vertical />
            </span>
            <span>
              <n-button
                text
                class="sub-button"
                :class="
                  pageData.dateSelect === 3
                    ? 'text-[#e50113] !border-[#e50113]'
                    : ''
                "
                @click="onDateClick(3)"
                >{{ authStore.i18n("cm_invite.withinOneYear") }}</n-button
              >
            </span>
            <span class="px-1">
              <n-divider vertical />
            </span>
            <span>
              <n-button
                text
                class="sub-button"
                :class="
                  pageData.dateSelect === 4
                    ? 'text-[#e50113] !border-[#e50113]'
                    : ''
                "
                @click="onDateClick(4)"
                >{{ authStore.i18n("cm_invite.previous") }}</n-button
              >
            </span>
          </div>
          <n-data-table
            remote
            :columns="userCols"
            :data="pageData.inviteFriendList"
            size="small"
            :scroll-x="350"
            :loading="pageData.loading"
            :pagination="pagination"
          ></n-data-table>
        </div>
      </div>
    </n-flex>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { useAuthStore } from "@/stores/authStore";
import { type DataTableColumns } from "naive-ui";

const route = useRoute();
const authStore = useAuthStore();
useHead({
  title: authStore.i18n("cm_common.chilatTitle"),
});
const userInfo = computed(() => useAuthStore().getUserInfo);
const pageData = reactive<any>({
  loading: false,
  verifySelect: 0,
  dateSelect: 0,
  datetime: onDateTimestamp(1),
  userInfo: {},
  inviteFriendList: [],
  verifyMailResult: {},
});

const userCols: DataTableColumns<any> = [
  {
    title: authStore.i18n("cm_invite.registerTime"),
    key: "registerTime",
    width: 150,
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return timezoneFormat(row.registerTime);
    },
  },
  {
    title: authStore.i18n("cm_invite.friendEmail"),
    key: "email",
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
];

const pagination = reactive({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [20, 50, 100],
  onChange: (page: number) => {
    pagination.page = page;
    onPageList();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.page = 1;
    pagination.pageSize = pageSize;
    onPageList();
  },
  pageCount: 0,
  itemCount: 0,
  prefix(item: any) {
    return `Total ${item.itemCount}`;
  },
});

await onVerifyMailResult();
onBeforeMount(() => {
  onUserDetail();
  onPageList();
});

async function onMailVerified() {
  pageData.verifyLoading = true;
  const res: any = await useSendVerifyMail({
    verifyMailScene: "INVITE_FRIEND",
  });
  pageData.verifyLoading = false;
  if (res?.result?.code === 200) {
    if (res?.data?.isMailVerified) {
      window.location.reload();
    } else {
      window?.MyStat?.addPageEvent(
        "passport_resend_active_mail",
        `重发邮箱验证邮件：成功`
      ); // 埋点
      // 跳转邮箱
      navigateToEmail();
    }
  } else if (res?.result?.code === 403) {
    navigateTo(`/login?pageSource=${route.fullPath}`);
  } else {
    window?.MyStat?.addPageEvent(
      "passport_resend_active_mail",
      `重发邮箱验证邮件：${res?.result?.message}`
    ); // 埋点
    showToast(res?.result?.message || authStore.i18n("cm_find.errorMessage"));
  }
}

async function onVerifyMailResult() {
  const res: any = await useQueryVerifyMailResult({
    email: userInfo.value?.username,
    isNeedCoupon: false,
  });
  if (res?.result?.code === 200) {
    pageData.verifyMailResult = res?.data;
  }
}

async function onPageList() {
  const res: any = await useInvitedUserMailStatus({
    datetime: pageData.datetime,
    isMailVerified: pageData.verifySelect === 0,
    page: {
      current: pagination.page,
      size: pagination.pageSize,
    },
  });
  if (res?.result?.code === 200) {
    pageData.inviteFriendList = res?.data;
    pagination.pageCount = res?.page?.pages;
    pagination.itemCount = res?.page?.total;
  }
}

async function onUserDetail() {
  const res: any = await useUserDetail({});
  if (res?.result?.code === 200) {
    const protocol = window.location.protocol;
    const host = window.location.host;
    pageData.userInfo = res?.data;
    pageData.userInfo.inviteLink = `${protocol}//${host}/?utm_source=invite_code_${pageData.userInfo.inviteCode}`;
  } else if (res?.result?.code === 403) {
    window.location.href = `/h5/user/login?pageSource=${window.location.href}`;
  }
}

function onHandleCopyText(val: any, type?: any) {
  let eventName, remark;
  if (type === "code") {
    eventName = "copy_invite_code";
    remark = "复制邀请代码";
  } else {
    eventName = "copy_invite_link";
    remark = "复制邀请链接";
  }
  window?.MyStat?.addPageEvent(eventName, remark); // 埋点
  onCopyText(val);
}

function onVerifyClick(val: any) {
  if (val === 0) {
    window?.MyStat?.addPageEvent(
      "invite_select_verified_user",
      `选择已验证的邀请用户`
    ); // 埋点
  } else {
    window?.MyStat?.addPageEvent(
      "invite_select_unverified_user",
      `选择未验证的邀请用户`
    ); // 埋点
  }
  pagination.page = 1;
  pageData.verifySelect = val;
  onPageList();
}

function onDateClick(val: any) {
  pagination.page = 1;
  pageData.dateSelect = val;
  if (val === 0) {
    pageData.datetime = onDateTimestamp(1);
  } else if (val === 1) {
    pageData.datetime = onDateTimestamp(3);
  } else if (val === 2) {
    pageData.datetime = onDateTimestamp(6);
  } else if (val === 3) {
    pageData.datetime = onDateTimestamp(12);
  } else {
    pageData.datetime = 0;
  }
  onPageList();
}

function onDateTimestamp(val: any) {
  // 获取当前时间的日期对象
  const currentDate = new Date();

  // 获取N个月之前的日期对象，并将时间设置为当天的零点
  const sixMonthsAgo = new Date(currentDate);
  sixMonthsAgo.setMonth(currentDate.getMonth() - val);
  sixMonthsAgo.setHours(0, 0, 0, 0);

  // 获取当前时间零点的日期对象
  const currentZeroDate = new Date(currentDate);
  currentZeroDate.setHours(0, 0, 0, 0);

  // 获取六个月前零点的时间戳（单位是毫秒）
  const startTimestamp = sixMonthsAgo.getTime();
  return startTimestamp;
}
</script>

<style lang="scss" scoped>
.invite-link {
  border-radius: 10px;
  padding: 1rem 1.5rem;
}
.sub-button {
  border: 1px solid #666;
  border-radius: 50px;
  padding: 6px 9px;
  font-size: 13px;
  &:hover {
    border: 1px solid #e50113;
  }
}
</style>
