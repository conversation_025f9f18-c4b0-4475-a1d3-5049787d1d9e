syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user";

import "chilat/user/param/user_param.proto";
import "chilat/user/model/user_model.proto";
import "chilat/user/model/role_model.proto";
import "common.proto";

// 用户管理
service User {
  // 用户列表
  rpc pageList(UserPageQueryParam) returns (UserPageResp);
  // 添加编辑用户
  rpc save(UserSaveParam) returns (UserResp);
  // 删除用户
  rpc remove(common.IdParam) returns (common.ApiResult);
  // 启用禁用用户
  rpc enable(common.EnabledParam) returns (common.ApiResult);
  // 获取用户权限
  rpc getPermission(GetPermissionParam) returns (UserPermissionResp);
  // 设置用户权限
  rpc setPermission(SetPermissionParam) returns (common.ApiResult);
  // 获取角色
  rpc getRole(GetPermissionParam) returns (RoleListResp);
  // 设置角色
  rpc setRole(SetPermissionParam) returns (common.ApiResult);
  // 重置密码
  rpc resetPassword(common.IdValueParam) returns (common.StringResult);
  // 获取业务员
  rpc listSalesman(common.EmptyParam) returns (UserListResp);
  // 获取采购员
  rpc listPurchaser(common.EmptyParam) returns (UserListResp);
  // 操作日志（员工管理）
  rpc log (common.IdParam) returns (common.LogResult);
  // 操作日志（用户管理）
  rpc listUserLog (common.IdParam) returns (LogResultResp);
  // 根据ID获取用户列表
  rpc listByIds(common.IdsParam) returns (UserListResp);
  // 根据用户名或者信息获取用户列表
  rpc listByNames(common.StringsParam) returns (UserListResp);
  // 根据ID获取用户基础信息(UserModel中只返回用户基础字段,敏感字段不返回)
  rpc getUserBaseInfoById(common.IdParam) returns (UserResp);
  // 查询全部用户基础信息(UserModel中只返回用户基础字段,敏感字段不返回)
  rpc getAllUserBaseInfo(common.EmptyParam) returns (UserListResp);
  // 导出用户
  rpc exportUser (UserQueryParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  }
  // 根据岗位职责获取员工列表,采购员查采购员只能查到自己,查业务员可以查到全部,业务员查业务员只能查到自己,查采购员员可以查到全部,管理员查采购员或业务员都是查全部
  rpc listByJobDuty(ListByJobDutyParam) returns (UserListResp);

  rpc listDepartment(common.EmptyParam) returns (DepartmentListResp);
}
