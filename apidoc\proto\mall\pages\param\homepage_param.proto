syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.param";

import "common.proto";
import "common/business.proto";
import "mall/commodity/commodity_common.proto";

message RecommendGoodsParam {
    int32 goodsCount = 1; //返回多少个商品
    common.VisitDeviceType deviceType = 2; //访问设备类型
    int32 siteId = 30; //站点ID
    repeated RecommendGoodsPlugTagParam goodsPlugTagParams = 40; //推荐商品补充标签参数（非必填，最多10组）
}

// 推荐商品补充 - 前端自定义标签搜索
message RecommendGoodsPlugTagParam {
    string tagId = 10; //标签ID
    int32 count = 20; //抓取商品数（默认24个，最大100个）
}

