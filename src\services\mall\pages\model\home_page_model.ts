/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { ConfigModel } from "../../../chilat/basis/model/config_model";
import { MallCategoryTreeModel } from "../../../chilat/basis/model/goods_model";
import { Result } from "../../../common";
import { SeoData } from "../../../common/business";
import { GoodsListDataModel } from "./goods_list_page_model";

export const protobufPackage = "mall.pages";

export interface HomePageResp {
  result: Result | undefined;
  data: HomePageModel | undefined;
}

export interface HomePageModel {
  /** 商城全局配置（当前语言，翻译信息等） */
  globalConfig:
    | ConfigModel
    | undefined;
  /** passport.LoginUserModel loginUser = 20; //登录用户信息（空值表示未登录） */
  seoData:
    | SeoData
    | undefined;
  /** 类目树 */
  categoryTree:
    | MallCategoryTreeModel
    | undefined;
  /** 热搜词 */
  hotKeywords: string[];
  /** 最新商品数据（第1页数据，分页信息在page属性中） */
  newestGoodsData:
    | GoodsListDataModel
    | undefined;
  /** 查询最新商品的营销规则代码（调用GoodsListPage.searchGoods查询第2页及之后的页码） */
  newestGoodsRuleCode: string;
  /** 推荐商品 */
  recommendedGoods: string[];
}

function createBaseHomePageResp(): HomePageResp {
  return { result: undefined, data: undefined };
}

export const HomePageResp = {
  encode(message: HomePageResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      HomePageModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): HomePageResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHomePageResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = HomePageModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HomePageResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? HomePageModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: HomePageResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = HomePageModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HomePageResp>, I>>(base?: I): HomePageResp {
    return HomePageResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomePageResp>, I>>(object: I): HomePageResp {
    const message = createBaseHomePageResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? HomePageModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseHomePageModel(): HomePageModel {
  return {
    globalConfig: undefined,
    seoData: undefined,
    categoryTree: undefined,
    hotKeywords: [],
    newestGoodsData: undefined,
    newestGoodsRuleCode: "",
    recommendedGoods: [],
  };
}

export const HomePageModel = {
  encode(message: HomePageModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.globalConfig !== undefined) {
      ConfigModel.encode(message.globalConfig, writer.uint32(82).fork()).ldelim();
    }
    if (message.seoData !== undefined) {
      SeoData.encode(message.seoData, writer.uint32(242).fork()).ldelim();
    }
    if (message.categoryTree !== undefined) {
      MallCategoryTreeModel.encode(message.categoryTree, writer.uint32(642).fork()).ldelim();
    }
    for (const v of message.hotKeywords) {
      writer.uint32(722).string(v!);
    }
    if (message.newestGoodsData !== undefined) {
      GoodsListDataModel.encode(message.newestGoodsData, writer.uint32(802).fork()).ldelim();
    }
    if (message.newestGoodsRuleCode !== "") {
      writer.uint32(882).string(message.newestGoodsRuleCode);
    }
    for (const v of message.recommendedGoods) {
      writer.uint32(962).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): HomePageModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHomePageModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 82) {
            break;
          }

          message.globalConfig = ConfigModel.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.seoData = SeoData.decode(reader, reader.uint32());
          continue;
        case 80:
          if (tag !== 642) {
            break;
          }

          message.categoryTree = MallCategoryTreeModel.decode(reader, reader.uint32());
          continue;
        case 90:
          if (tag !== 722) {
            break;
          }

          message.hotKeywords.push(reader.string());
          continue;
        case 100:
          if (tag !== 802) {
            break;
          }

          message.newestGoodsData = GoodsListDataModel.decode(reader, reader.uint32());
          continue;
        case 110:
          if (tag !== 882) {
            break;
          }

          message.newestGoodsRuleCode = reader.string();
          continue;
        case 120:
          if (tag !== 962) {
            break;
          }

          message.recommendedGoods.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): HomePageModel {
    return {
      globalConfig: isSet(object.globalConfig) ? ConfigModel.fromJSON(object.globalConfig) : undefined,
      seoData: isSet(object.seoData) ? SeoData.fromJSON(object.seoData) : undefined,
      categoryTree: isSet(object.categoryTree) ? MallCategoryTreeModel.fromJSON(object.categoryTree) : undefined,
      hotKeywords: globalThis.Array.isArray(object?.hotKeywords)
        ? object.hotKeywords.map((e: any) => globalThis.String(e))
        : [],
      newestGoodsData: isSet(object.newestGoodsData) ? GoodsListDataModel.fromJSON(object.newestGoodsData) : undefined,
      newestGoodsRuleCode: isSet(object.newestGoodsRuleCode) ? globalThis.String(object.newestGoodsRuleCode) : "",
      recommendedGoods: globalThis.Array.isArray(object?.recommendedGoods)
        ? object.recommendedGoods.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: HomePageModel): unknown {
    const obj: any = {};
    if (message.globalConfig !== undefined) {
      obj.globalConfig = ConfigModel.toJSON(message.globalConfig);
    }
    if (message.seoData !== undefined) {
      obj.seoData = SeoData.toJSON(message.seoData);
    }
    if (message.categoryTree !== undefined) {
      obj.categoryTree = MallCategoryTreeModel.toJSON(message.categoryTree);
    }
    if (message.hotKeywords?.length) {
      obj.hotKeywords = message.hotKeywords;
    }
    if (message.newestGoodsData !== undefined) {
      obj.newestGoodsData = GoodsListDataModel.toJSON(message.newestGoodsData);
    }
    if (message.newestGoodsRuleCode !== "") {
      obj.newestGoodsRuleCode = message.newestGoodsRuleCode;
    }
    if (message.recommendedGoods?.length) {
      obj.recommendedGoods = message.recommendedGoods;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<HomePageModel>, I>>(base?: I): HomePageModel {
    return HomePageModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomePageModel>, I>>(object: I): HomePageModel {
    const message = createBaseHomePageModel();
    message.globalConfig = (object.globalConfig !== undefined && object.globalConfig !== null)
      ? ConfigModel.fromPartial(object.globalConfig)
      : undefined;
    message.seoData = (object.seoData !== undefined && object.seoData !== null)
      ? SeoData.fromPartial(object.seoData)
      : undefined;
    message.categoryTree = (object.categoryTree !== undefined && object.categoryTree !== null)
      ? MallCategoryTreeModel.fromPartial(object.categoryTree)
      : undefined;
    message.hotKeywords = object.hotKeywords?.map((e) => e) || [];
    message.newestGoodsData = (object.newestGoodsData !== undefined && object.newestGoodsData !== null)
      ? GoodsListDataModel.fromPartial(object.newestGoodsData)
      : undefined;
    message.newestGoodsRuleCode = object.newestGoodsRuleCode ?? "";
    message.recommendedGoods = object.recommendedGoods?.map((e) => e) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
