<template>
  <n-drawer
    v-model:show="pageData.showSelectAddressModal"
    placement="bottom"
    height="80%"
    :trap-focus="false"
  >
    <n-drawer-content closable>
      <template #header>
        <div class="text-[0.36rem] font-medium text-center">
          {{ authStore.i18n("cm_addr.selectShippingAddress") }}
        </div>
      </template>
      <div class="h-[100%] flex flex-col">
        <div class="mb-[0.24rem]">
          <div
            @click="onOpenAddAddr"
            class="inline-block px-[0.32rem] py-[0.12rem] border-1 border-[#A6A6A6] rounded-[10rem] hover:text-[#fff] hover:bg-[#E50113] hover:border-[#E50113] transition-all duration-200 cursor-pointer"
          >
            + {{ authStore.i18n("cm_addr.createNewAddress") }}
          </div>
        </div>

        <!-- 地址列表 -->
        <div class="space-y-[0.24rem] overflow-y-auto flex-1 pb-[0.4rem]">
          <div
            v-for="address in props.addressList"
            :key="address.id"
            class="border border-gray-200 rounded-[0.16rem] p-[0.16rem] cursor-pointer transition-colors"
            @click="onSelectAddress(address)"
            :style="{
              borderColor:
                pageData.selectedAddress?.id === address.id
                  ? '#e50113'
                  : '#eee',
            }"
          >
            <div class="flex items-start justify-between">
              <div class="flex items-start flex-1">
                <!-- 选择按钮 -->
                <n-radio
                  :checked="pageData.selectedAddress?.id === address.id"
                  @click.stop="onSelectAddress(address)"
                  class="mr-[0.12rem] mt-[0.08rem]"
                />

                <!-- 地址信息 -->
                <div class="flex-1">
                  <div class="mb-[0.16rem]">
                    <span
                      class="font-medium text-[0.32rem] leading-[0.36rem] mr-[0.24rem]"
                      >{{ address.contactName }}</span
                    >
                    <span class="text-gray-600 whitespace-nowrap">{{
                      onFormatPhone(address)
                    }}</span>
                  </div>
                  <div
                    class="text-gray-700 text-[0.28rem] leading-relaxed mb-[0.16rem]"
                  >
                    {{ address.fullAddress }}
                  </div>
                  <div
                    v-if="address.isDefault"
                    class="text-[#f0a020] bg-[#f0a02026] inline-block px-[0.08rem]"
                  >
                    {{ authStore.i18n("cm_addr.defaultShippingAddress") }}
                  </div>
                  <div
                    v-else
                    text
                    @click.stop="onAddrToDefault(address)"
                    class="underline"
                  >
                    {{ authStore.i18n("cm_addr.setAsDefault") }}
                  </div>
                </div>
              </div>

              <div class="flex gap-[0.32rem] items-center ml-[0.16rem]">
                <icon-card
                  color="#333"
                  size="24"
                  name="la:edit-solid"
                  @click.stop="onOpenEditAddr(address)"
                ></icon-card>
                <!-- <n-popconfirm
                :positive-text="authStore.i18n('cm_addr.confirmBtn')"
                :negative-text="authStore.i18n('cm_addr.cancelBtn')"
                @positive-click="onConfirmDelete(address)"
                placement="top"
                to="body"
                :show-icon="false"
              >
                <template #trigger>
                  <icon-card
                    color="#333"
                    size="18"
                    name="uiw:delete"
                    @click.stop
                  ></icon-card>
                </template>
                {{ authStore.i18n("cm_addr.delAddrTip") }}
              </n-popconfirm> -->
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-center mt-[0.24rem] gap-[0.24rem]">
          <n-button round class="w-[3.4rem]" @click="onCloseSelectAddress">{{
            authStore.i18n("cm_addr.cancelAddress")
          }}</n-button>
          <n-button
            round
            type="primary"
            @click="onConfirmSelectAddress"
            :disabled="!pageData.selectedAddress"
            class="w-[3.4rem]"
          >
            {{ authStore.i18n("cm_addr.sendToThisAddress") }}
          </n-button>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const authStore = useAuthStore();

const props = defineProps({
  addressList: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits([
  "refresh", // 刷新地址列表
  "addAddress",
  "editAddress",
  "confirmAddress",
]);

const pageData = reactive<any>({
  addressList: <any>[],
  selectedAddress: <any>{},
  showSelectAddressModal: false,
});

// 方法
function onFormatPhone(address: any) {
  if (!address?.phone) return "";
  if (address.areaCode) {
    return `${address.areaCode}${address.phone}`;
  }
  return address.phone;
}

function onSelectAddress(address: any) {
  pageData.selectedAddress = address ? JSON.parse(JSON.stringify(address)) : {};
}

function onOpenAddAddr() {
  emit("addAddress", "modal");
}

function onOpenEditAddr(address: any) {
  emit("editAddress", "modal", address);
}

function onCloseSelectAddress() {
  pageData.showSelectAddressModal = false;
}

function onConfirmSelectAddress() {
  pageData.showSelectAddressModal = false;
  emit("confirmAddress", pageData.selectedAddress);
}

// 获取地址列表
async function onListUserAddress() {
  try {
    const config = useRuntimeConfig();
    const res: any = await useListUserAddress({});
    if (res?.result?.code === 200) {
      // 只保留秘鲁国家的地址
      const allAddresses = res?.data || [];
      pageData.addressList = allAddresses.filter(
        (item: any) => item.countryId === config.public.peruCountryId
      );
    } else if (res?.result?.code === 403) {
      // 未登录，跳转到登录页面
      window.location.href = `/h5/user/login?pageSource=${window.location.href}`;
    }
  } catch (error) {
    console.error("获取地址列表失败:", error);
    pageData.addressList = [];
  }
}

// 设为默认地址
async function onAddrToDefault(address: any) {
  try {
    // 保存当前选中的地址ID，避免设置默认地址时被重置
    const currentSelectedAddressId = pageData.selectedAddress?.id;

    const res: any = await useAddressToDefault({ id: address.id });
    if (res?.result?.code === 200) {
      showToast(authStore.i18n("cm_addr.editSuccess"));

      // 重新获取地址列表
      await onListUserAddress();

      // 恢复之前选中的地址，如果该地址仍然存在
      if (currentSelectedAddressId) {
        const previousSelectedAddress = props.addressList.find(
          (item: any) => item.id === currentSelectedAddressId
        );
        if (previousSelectedAddress) {
          // 地址仍然存在，恢复选中状态
          pageData.selectedAddress = JSON.parse(
            JSON.stringify(previousSelectedAddress)
          );
        } else {
          // 地址不存在了，选择新的默认地址或第一个地址
          const defaultAddress = props.addressList.find(
            (item: any) => item.isDefault
          );
          pageData.selectedAddress = defaultAddress
            ? JSON.parse(JSON.stringify(defaultAddress))
            : props.addressList.length > 0
            ? JSON.parse(JSON.stringify(props.addressList[0]))
            : {};
        }
      } else {
        // 之前没有选中地址，选择默认地址或第一个地址
        const defaultAddress = props.addressList.find(
          (item: any) => item.isDefault
        );
        pageData.selectedAddress = defaultAddress
          ? JSON.parse(JSON.stringify(defaultAddress))
          : props.addressList.length > 0
          ? JSON.parse(JSON.stringify(props.addressList[0]))
          : {};
      }

      // 通知父组件地址列表已更新
      emit("refresh");
    } else {
      showToast(
        res?.result?.message || authStore.i18n("cm_addr.setDefaultFailed")
      );
    }
  } catch (error) {
    console.error("设为默认地址失败:", error);
    showToast(authStore.i18n("cm_addr.setDefaultFailed"));
  }
}

function onShowDrawer(address: any) {
  pageData.selectedAddress = address || {};
  pageData.showSelectAddressModal = true;
}

defineExpose({
  onShowDrawer,
});
</script>

<style scoped lang="scss">
:deep(.n-drawer-body-content-wrapper) {
  padding: 0.24rem 0.16rem 0.16rem !important;
}
</style>
