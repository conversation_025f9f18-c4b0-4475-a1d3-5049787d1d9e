syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing.param";

import "chilat/marketing/marketing_common.proto";
import "common.proto";
import "common/business.proto";


message PromotionPageListParam {
  common.PageParam page = 1;
  PromotionPageQueryParam query = 2;
}

// 促销活动列表查询参数
message PromotionPageQueryParam {
  string id = 10; // 活动ID
  string promotionCode = 20; //活动代码
  string keyword = 30; //搜索关键字（搜索：活动名称、西语名称，兼容ID搜索）
  int64 beginTime = 40; // 开始时间
  int64 endTime = 50; // 结束时间
  PromotionUserScope userScope = 60; //活动类型（促销活动用户范围）
  string salesmanId = 70; //业务员ID
  string tagId = 80; //标签ID
}

// 促销活动“添加/编辑”保存接口
message SavePromotionParam {
  string id = 10; // 活动ID（新增或复制，此字段留空）
  string promotionCode = 20; //活动代码（复制时，若存在活动编码，则默认加“-Copy”；保留字段）
  string promotionName = 30; //活动名称（中文）
  string displayName = 40; //商城前台显示名称（西语）
  int64 beginTime = 50; // 开始时间
  int64 endTime = 60; // 结束时间
  PromotionUserScope userScope = 70; //活动类型（促销活动用户范围）
  string salesmanId = 80; //业务员ID（系统活动，须将业务员ID置为空）
  string remark = 90; //备注
  common.GoodsSelectorType selectorType = 100; // 商品筛选器类型（默认GOODS_SELECTOR_TYPE_GOODS_TAG；保留字段）
  repeated PromotionGoodsSelector goodsSelectors = 110; //商品筛选器列表；非null传值则更新（若传空数组，则清除现有商品筛选器）
  repeated PromotionExclusiveCustomer exclusiveCustomers = 120; // 专属客户列表；非null传值则更新（若传空数组，则清除现有专属客户）
}

// 促销活动“复制”保存接口
message CopySavePromotionParam {
  string sourceId = 10; // 元活动ID（新增或复制，此字段留空）
  string promotionName = 30; //活动名称（中文）
  string salesmanId = 80; //业务员ID（系统活动，须将业务员ID置为空）
}

// 保存活动商品信息参数
message SavePromotionGoodsSelectorParam {
  string id = 10; // 活动ID
  repeated PromotionGoodsSelector goodsSelectors = 20; //商品筛选器列表；必填（若传空数组，则清除现有商品筛选器）
}

// 保存专属客户信息参数
message SavePromotionExclusiveCustomerParam {
  string id = 10; // 活动ID
  repeated PromotionExclusiveCustomer exclusiveCustomers = 20; // 专属客户列表；必填（若传空数组，则清除现有专属客户）
}

// 商品操作日志查询参数
message PromotionOperationLogQueryParam {
  common.PageParam page = 1; //分页查询参数
  string promotionId = 2; //优惠促销ID（必填）
}