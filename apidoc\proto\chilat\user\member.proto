syntax = "proto3";
package chilat.user;

option java_package = "com.chilat.rpc.user";

import "chilat/user/param/member_param.proto";
import "chilat/user/model/member_model.proto";
import "common.proto";

// 客户管理
service Member {
  // 客户列表
  rpc pageList (MemberPageQueryParam) returns (MemberPageResp);
  // 添加编辑客户
  rpc save (MemberSaveParam) returns (MemberResp);
  // 删除客户
  rpc remove (common.IdParam) returns (common.ApiResult);
  // 下载客户模板
  rpc downloadTemplate (common.EmptyParam) returns (common.DownloadResult) {
    option (common.webapi).download = true;
  };
  // 导入客户
  rpc importMember (common.EmptyParam) returns (common.ImportResult) {
    option (common.webapi).upload = true;
  };
}
