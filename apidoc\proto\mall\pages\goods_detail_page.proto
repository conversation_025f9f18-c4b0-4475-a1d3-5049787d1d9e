syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages";

import "common.proto";
import "mall/pages/model/goods_detail_page_model.proto";
import "mall/pages/param/goods_detail_page_param.proto";


// 商品详情页（暂不使用）
service GoodsDetailPage {
    // 获取页面数据
    rpc getPageData (GoodsDetailQueryParam) returns (GoodsDetailPageResp);
    // 获取商品详情数据
    rpc getGoodsInfo (GoodsDetailQueryParam) returns (GoodsDetailDataResp);
}
