/* eslint-disable */
import * as _m0 from "protobufjs/minimal";
import { Result } from "../../../common";

export const protobufPackage = "chilat.basis";

export interface CountryResp {
  result:
    | Result
    | undefined;
  /** 国家 */
  data: CountryModel[];
}

export interface CountryModel {
  id: string;
  /** 国家名称（使用商城前台语言） */
  countryName: string;
  /** 国家中文名 */
  countryCnName: string;
  /** 国家英文名 */
  countryEnName: string;
  /** 国家西文名 */
  countryEsName: string;
  /** 国家二字代码 */
  countryCodeTwo: string;
  /** 国家三字代码 */
  countryCodeThree: string;
  /** 区号 */
  areaCode: string;
  /** 电话格式 */
  phoneRegexes: string[];
  /** 电话位数 */
  phoneCount: string;
}

export interface CountryInfoResp {
  result:
    | Result
    | undefined;
  /** 国家 */
  data: CountryModel | undefined;
}

export interface ListRegionResp {
  result: Result | undefined;
  data: RegionModel[];
}

export interface RegionModel {
  id: string;
  /** 父级编号 */
  parentCode: string;
  /** 名称 */
  name: string;
  /** 编号 */
  code: string;
  /** 层级 */
  level: string;
  /** 排序 */
  sort: number;
  children: RegionModel[];
  communityAddress: string[];
}

export interface RegionResp {
  result: Result | undefined;
  data: RegionModel | undefined;
}

function createBaseCountryResp(): CountryResp {
  return { result: undefined, data: [] };
}

export const CountryResp = {
  encode(message: CountryResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      CountryModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CountryResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCountryResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(CountryModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CountryResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => CountryModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: CountryResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => CountryModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CountryResp>, I>>(base?: I): CountryResp {
    return CountryResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CountryResp>, I>>(object: I): CountryResp {
    const message = createBaseCountryResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => CountryModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCountryModel(): CountryModel {
  return {
    id: "",
    countryName: "",
    countryCnName: "",
    countryEnName: "",
    countryEsName: "",
    countryCodeTwo: "",
    countryCodeThree: "",
    areaCode: "",
    phoneRegexes: [],
    phoneCount: "",
  };
}

export const CountryModel = {
  encode(message: CountryModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.countryName !== "") {
      writer.uint32(90).string(message.countryName);
    }
    if (message.countryCnName !== "") {
      writer.uint32(18).string(message.countryCnName);
    }
    if (message.countryEnName !== "") {
      writer.uint32(26).string(message.countryEnName);
    }
    if (message.countryEsName !== "") {
      writer.uint32(34).string(message.countryEsName);
    }
    if (message.countryCodeTwo !== "") {
      writer.uint32(42).string(message.countryCodeTwo);
    }
    if (message.countryCodeThree !== "") {
      writer.uint32(50).string(message.countryCodeThree);
    }
    if (message.areaCode !== "") {
      writer.uint32(58).string(message.areaCode);
    }
    for (const v of message.phoneRegexes) {
      writer.uint32(74).string(v!);
    }
    if (message.phoneCount !== "") {
      writer.uint32(82).string(message.phoneCount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CountryModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCountryModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.countryName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.countryCnName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.countryEnName = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.countryEsName = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.countryCodeTwo = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.countryCodeThree = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.areaCode = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.phoneRegexes.push(reader.string());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.phoneCount = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CountryModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      countryName: isSet(object.countryName) ? globalThis.String(object.countryName) : "",
      countryCnName: isSet(object.countryCnName) ? globalThis.String(object.countryCnName) : "",
      countryEnName: isSet(object.countryEnName) ? globalThis.String(object.countryEnName) : "",
      countryEsName: isSet(object.countryEsName) ? globalThis.String(object.countryEsName) : "",
      countryCodeTwo: isSet(object.countryCodeTwo) ? globalThis.String(object.countryCodeTwo) : "",
      countryCodeThree: isSet(object.countryCodeThree) ? globalThis.String(object.countryCodeThree) : "",
      areaCode: isSet(object.areaCode) ? globalThis.String(object.areaCode) : "",
      phoneRegexes: globalThis.Array.isArray(object?.phoneRegexes)
        ? object.phoneRegexes.map((e: any) => globalThis.String(e))
        : [],
      phoneCount: isSet(object.phoneCount) ? globalThis.String(object.phoneCount) : "",
    };
  },

  toJSON(message: CountryModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.countryName !== "") {
      obj.countryName = message.countryName;
    }
    if (message.countryCnName !== "") {
      obj.countryCnName = message.countryCnName;
    }
    if (message.countryEnName !== "") {
      obj.countryEnName = message.countryEnName;
    }
    if (message.countryEsName !== "") {
      obj.countryEsName = message.countryEsName;
    }
    if (message.countryCodeTwo !== "") {
      obj.countryCodeTwo = message.countryCodeTwo;
    }
    if (message.countryCodeThree !== "") {
      obj.countryCodeThree = message.countryCodeThree;
    }
    if (message.areaCode !== "") {
      obj.areaCode = message.areaCode;
    }
    if (message.phoneRegexes?.length) {
      obj.phoneRegexes = message.phoneRegexes;
    }
    if (message.phoneCount !== "") {
      obj.phoneCount = message.phoneCount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CountryModel>, I>>(base?: I): CountryModel {
    return CountryModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CountryModel>, I>>(object: I): CountryModel {
    const message = createBaseCountryModel();
    message.id = object.id ?? "";
    message.countryName = object.countryName ?? "";
    message.countryCnName = object.countryCnName ?? "";
    message.countryEnName = object.countryEnName ?? "";
    message.countryEsName = object.countryEsName ?? "";
    message.countryCodeTwo = object.countryCodeTwo ?? "";
    message.countryCodeThree = object.countryCodeThree ?? "";
    message.areaCode = object.areaCode ?? "";
    message.phoneRegexes = object.phoneRegexes?.map((e) => e) || [];
    message.phoneCount = object.phoneCount ?? "";
    return message;
  },
};

function createBaseCountryInfoResp(): CountryInfoResp {
  return { result: undefined, data: undefined };
}

export const CountryInfoResp = {
  encode(message: CountryInfoResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      CountryModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CountryInfoResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCountryInfoResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = CountryModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CountryInfoResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? CountryModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: CountryInfoResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = CountryModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CountryInfoResp>, I>>(base?: I): CountryInfoResp {
    return CountryInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CountryInfoResp>, I>>(object: I): CountryInfoResp {
    const message = createBaseCountryInfoResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? CountryModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseListRegionResp(): ListRegionResp {
  return { result: undefined, data: [] };
}

export const ListRegionResp = {
  encode(message: ListRegionResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.data) {
      RegionModel.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ListRegionResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListRegionResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(RegionModel.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListRegionResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => RegionModel.fromJSON(e)) : [],
    };
  },

  toJSON(message: ListRegionResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => RegionModel.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListRegionResp>, I>>(base?: I): ListRegionResp {
    return ListRegionResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRegionResp>, I>>(object: I): ListRegionResp {
    const message = createBaseListRegionResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = object.data?.map((e) => RegionModel.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRegionModel(): RegionModel {
  return { id: "", parentCode: "", name: "", code: "", level: "", sort: 0, children: [], communityAddress: [] };
}

export const RegionModel = {
  encode(message: RegionModel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.parentCode !== "") {
      writer.uint32(18).string(message.parentCode);
    }
    if (message.name !== "") {
      writer.uint32(26).string(message.name);
    }
    if (message.code !== "") {
      writer.uint32(34).string(message.code);
    }
    if (message.level !== "") {
      writer.uint32(42).string(message.level);
    }
    if (message.sort !== 0) {
      writer.uint32(48).int32(message.sort);
    }
    for (const v of message.children) {
      RegionModel.encode(v!, writer.uint32(58).fork()).ldelim();
    }
    for (const v of message.communityAddress) {
      writer.uint32(66).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RegionModel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegionModel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.parentCode = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.code = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.level = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.sort = reader.int32();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.children.push(RegionModel.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.communityAddress.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RegionModel {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      parentCode: isSet(object.parentCode) ? globalThis.String(object.parentCode) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      code: isSet(object.code) ? globalThis.String(object.code) : "",
      level: isSet(object.level) ? globalThis.String(object.level) : "",
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      children: globalThis.Array.isArray(object?.children)
        ? object.children.map((e: any) => RegionModel.fromJSON(e))
        : [],
      communityAddress: globalThis.Array.isArray(object?.communityAddress)
        ? object.communityAddress.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: RegionModel): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.parentCode !== "") {
      obj.parentCode = message.parentCode;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.code !== "") {
      obj.code = message.code;
    }
    if (message.level !== "") {
      obj.level = message.level;
    }
    if (message.sort !== 0) {
      obj.sort = Math.round(message.sort);
    }
    if (message.children?.length) {
      obj.children = message.children.map((e) => RegionModel.toJSON(e));
    }
    if (message.communityAddress?.length) {
      obj.communityAddress = message.communityAddress;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RegionModel>, I>>(base?: I): RegionModel {
    return RegionModel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegionModel>, I>>(object: I): RegionModel {
    const message = createBaseRegionModel();
    message.id = object.id ?? "";
    message.parentCode = object.parentCode ?? "";
    message.name = object.name ?? "";
    message.code = object.code ?? "";
    message.level = object.level ?? "";
    message.sort = object.sort ?? 0;
    message.children = object.children?.map((e) => RegionModel.fromPartial(e)) || [];
    message.communityAddress = object.communityAddress?.map((e) => e) || [];
    return message;
  },
};

function createBaseRegionResp(): RegionResp {
  return { result: undefined, data: undefined };
}

export const RegionResp = {
  encode(message: RegionResp, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Result.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.data !== undefined) {
      RegionModel.encode(message.data, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RegionResp {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegionResp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Result.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data = RegionModel.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RegionResp {
    return {
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      data: isSet(object.data) ? RegionModel.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: RegionResp): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = Result.toJSON(message.result);
    }
    if (message.data !== undefined) {
      obj.data = RegionModel.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RegionResp>, I>>(base?: I): RegionResp {
    return RegionResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegionResp>, I>>(object: I): RegionResp {
    const message = createBaseRegionResp();
    message.result = (object.result !== undefined && object.result !== null)
      ? Result.fromPartial(object.result)
      : undefined;
    message.data = (object.data !== undefined && object.data !== null)
      ? RegionModel.fromPartial(object.data)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
