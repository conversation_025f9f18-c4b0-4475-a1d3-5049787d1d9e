syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing.param";

import "common.proto";
import "chilat/marketing/marketing_common.proto";


message MarketingRulePageQueryParam {
  common.PageParam page = 1;
  MarketingRuleQueryParam query = 2;
}

message MarketingRuleQueryParam {
  string ruleName = 1; // 规则名称
  string id = 2;
}

message MarketingRuleSaveParam {
  string id = 1;
  string ruleName = 2; // 规则名称
  repeated string categoryIds = 4; //分类
  repeated string brandIds = 5; //品牌
  int32 minimumSales = 6; //最低销量
  int32 quantity = 7; //展示数量
  GoodsOnlineDays goodsOnlineDays = 8; //商品上架时间
  string logo = 9; //logo
  StockLocation stockLocation = 10; // 库存位置
  repeated string goodsTagIds = 11; //标签id
}

message MarketingCategorySaveParam {
  string id = 1; // 营销分类id
  string parentId = 5; // 营销分类父id
  int32 idx = 8; // 排序
  string cateName = 9; // 营销分类名称
  string cateAlias = 10; // 营销分类-别名
  string cateLogo = 12; // 营销分类logo
  CategoryMappingType mappingType = 60; //营销分类的商品映射类型（“营销规则ID”与“商品列表ID取其一”）
  repeated string goodsCategoryIds = 70; //商品列表ID列表（当 mappingType == CATEGORY_MAPPING_TYPE_GOODS_CATEGORY 时传值）
  repeated string marketingRuleIds = 80; //营销规则ID列表（当 mappingType == CATEGORY_MAPPING_TYPE_MARKETING_RULE 时传值）
}

message MarketingRuleRemoveCategoryParam {
  repeated string categoryIds = 1; //分类id
}