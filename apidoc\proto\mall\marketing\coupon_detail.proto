syntax = "proto3";
package mall.marketing;

import "chilat/basis/model/coupon_usable_model.proto";
import "chilat/basis/model/my_coupon_detail_model.proto";
import "mall/marketing/param/mall_my_coupon_detail_param.proto";

option java_package = "com.chilat.rpc.marketing";
// 卡券管理
service CouponDetail {

  // 我的优惠券列表查询
  rpc getMyCouponDetailList (MallMyCouponDetailParam) returns (chilat.basis.MyCouponDetailModelResp);

  // 可用产品券/佣金券/列表
  rpc getCouponUsableList (MallCouponUsableParam) returns (chilat.basis.CouponUsableModelResp);

  // checkbox优惠券校验
  rpc checkAvailableList (MallCouponCheckParam) returns (chilat.basis.CouponAvailableModelResp);

}