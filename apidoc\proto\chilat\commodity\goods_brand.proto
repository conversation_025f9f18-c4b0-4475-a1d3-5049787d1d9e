syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/param/goods_brand_param.proto";
import "chilat/commodity/model/goods_brand_model.proto";
import "chilat/commodity/commodity_common.proto";
import "common.proto";

// 商品品牌管理
service GoodsBrand {
//  // 查询列表
//  rpc listPage (GoodsBrandPageQueryParam) returns (GoodsBrandPageResp);
//  // 查询详情
//  rpc getDetail (common.IdParam) returns (GoodsBrandDetailResp);
//  // 保存品牌信息（id不存在，则为新增）
//  rpc saveBrand (GoodsBrandSaveParam) returns (common.ApiResult);
//  // 查询日志
//  rpc listLog (GoodsOperationLogQueryParam) returns (GoodsOperationLogResp);
}