<template>
  <div class="w-full bg-white px-[20px]">
    <search-card />
    <n-divider />
    <div class="flex pb-[12px]">
      <div class="step-progress w-[780px]">
        <div class="step-progress-line"></div>

        <div
          class="step-progress-line completed"
          :style="{ width: activeLineWidth }"
        ></div>

        <div
          v-for="(step, index) in steps"
          :key="index"
          class="step-item"
          :class="[
            {
              completed: index < currentStep,
              active: index === currentStep,
            },
            index === steps.length - 1 ? '!w-[212px] !flex-none	' : '',
          ]"
        >
          <div
            class="step-circle w-[48px] h-[48px] flex items-center mt-[4px] z-10 bg-transparent"
          >
            <template v-if="index === currentStep && step?.iconActive">
              <div
                class="w-[48px] h-[48px] rounded-full flex items-center justify-center bg-[#e50113]"
              >
                <img
                  :src="step.iconActive"
                  :alt="step.label"
                  class="w-[36px] h-[36px] flex-shrink-0"
                  referrerpolicy="no-referrer"
                />
              </div>
            </template>
            <template v-else-if="index > currentStep && step?.icon">
              <div
                class="w-[36px] h-[36px] rounded-full flex items-center justify-center border border-dashed border-[#A6A6A6] bg-white"
              >
                <img
                  :src="step.icon"
                  :alt="step.label"
                  class="w-[26px] h-[26px] flex-shrink-0"
                  referrerpolicy="no-referrer"
                />
              </div>
            </template>
            <template v-else>
              <div
                class="w-[36px] h-[36px] bg-[#E50113] text-[14px] leading-[14px] text-white rounded-full flex items-center justify-center"
              >
                0{{ index + 1 }}
              </div>
            </template>
          </div>
          <div
            class="flex mt-[-8px]"
            :class="currentStep === index ? 'ml-[16px]' : 'ml-[10px]'"
          >
            <div class="flex flex-col items-center mr-[6px]">
              <div
                class="w-[1px] h-[24px] bg-[length:1px_4px] bg-repeat-y"
                :class="[
                  currentStep < index
                    ? 'bg-gradient-to-b from-gray-400 via-transparent to-gray-400'
                    : 'bg-gradient-to-b from-[#E50113] via-transparent to-[#E50113]',
                ]"
              ></div>
              <div
                v-if="currentStep < index"
                class="w-[16px] h-[16px] border border-[#A6A6A6] rounded-full"
              ></div>
              <img
                v-else
                :src="currentStep === index ? unCheckCircle : checkCircle"
                referrerpolicy="no-referrer"
              />
            </div>
            <div
              class="w-[166px] text-[16px] leading-[20px] mt-[22px]"
              :class="currentStep < index ? 'text-[#4D4D4D]' : 'text-[#E50113]'"
            >
              {{ step.label }}
            </div>
          </div>
        </div>
      </div>
      <div class="w-[8px] h-[10px] bg-[#e50113] mt-[23px] mx-[8px]"></div>
      <div>
        <img
          src="@/assets/icons/find/step-line.svg"
          alt="step"
          class="mt-[24px]"
          referrerpolicy="no-referrer"
        />
        <div class="flex mt-[-24px]">
          <div class="w-[76px] flex flex-col gap-[20px] items-center ml-[35px]">
            <div
              class="w-[36px] h-[36px] rounded-full flex items-center justify-center border border-dashed border-[#E6E6E6] bg-white"
            >
              <img
                :alt="authStore.i18n('cm_common.step4')"
                src="@/assets/icons/find/paid.svg"
                class="w-[26px] h-[26px] flex-shrink-0"
                referrerpolicy="no-referrer"
              />
            </div>
            <div class="text-[14px] leading-[16px] text-[#A6A6A6] text-center">
              {{ authStore.i18n("cm_common.step4") }}
            </div>
          </div>
          <div class="flex flex-col gap-[20px] items-center ml-[63px]">
            <div class="flex gap-[10px]">
              <div
                class="w-[36px] h-[36px] rounded-full flex items-center justify-center border border-dashed border-[#E6E6E6] bg-white"
              >
                <img
                  :alt="authStore.i18n('cm_common.step5')"
                  src="@/assets/icons/order/purchasing.svg"
                  class="w-[26px] h-[26px] flex-shrink-0"
                  referrerpolicy="no-referrer"
                />
              </div>
              <div
                class="w-[36px] h-[36px] rounded-full flex items-center justify-center border border-dashed border-[#E6E6E6] bg-white"
              >
                <img
                  :alt="authStore.i18n('cm_common.step5')"
                  src="@/assets/icons/find/check.svg"
                  class="w-[26px] h-[26px] flex-shrink-0"
                  referrerpolicy="no-referrer"
                />
              </div>
            </div>

            <div
              class="w-[76px] text-[14px] leading-[16px] text-[#A6A6A6] text-center"
            >
              {{ authStore.i18n("cm_common.step5") }}
            </div>
          </div>
          <div class="flex flex-col gap-[20px] items-center ml-[63px]">
            <div
              class="w-[36px] h-[36px] rounded-full flex items-center justify-center bg-[#E50113]"
            >
              <img
                :alt="authStore.i18n('cm_common.step6')"
                src="@/assets/icons/order/deliveringAc.svg"
                class="w-[26px] h-[26px] flex-shrink-0"
                referrerpolicy="no-referrer"
              />
            </div>
            <div
              class="w-[76px] text-[14px] leading-[16px] text-[#A6A6A6] text-center"
            >
              {{ authStore.i18n("cm_common.step6") }}
            </div>
          </div>
          <div
            class="w-[24px] h-[24px] relative circle-container ml-[21px] mt-[6px]"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import checkCircle from "@/assets/icons/find/check-circle.svg";
import unCheckCircle from "@/assets/icons/find/uncheck-circle.svg";
import unpaid from "@/assets/icons/find/unpaid.svg";
import quote from "@/assets/icons/find/quote.svg";
import quoteActive from "@/assets/icons/find/quote-active.svg";
const authStore = useAuthStore();
const props = defineProps({
  currentStep: {
    type: Number,
    default: 0,
  },
});

const containerWidth = 780;
const lastStepWidth = 212;
const activeLineWidth = computed(() => {
  if (props.currentStep === 0) return "0px";

  const stepCount = steps.length;
  const averageStepWidth = (containerWidth - lastStepWidth) / (stepCount - 1);

  const width = props.currentStep * averageStepWidth;
  return `${width > containerWidth ? containerWidth : width}px`;
});

const steps = [
  {
    icon: checkCircle,
    label: authStore.i18n("cm_common.step1"),
  },
  {
    iconActive: unpaid,
    label: authStore.i18n("cm_common.step2"),
  },
  {
    icon: quote,
    iconActive: quoteActive,
    label: authStore.i18n("cm_common.step3"),
  },
];
</script>
<style scoped lang="scss">
.n-divider {
  margin: 0;
}

.step-progress {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.step-progress-line {
  position: absolute;
  top: 27px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 0;
}

.step-progress-line.completed {
  background-color: #e50113;
  height: 2px;
  z-index: 1;
  transition: width 0.4s ease;
}

.step-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  position: relative;
  z-index: 2;
}

.circle-container {
  border-radius: 50%;
  background-color: rgba(229, 1, 19, 0.1);
  &::after {
    content: "";
    display: block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #e50113;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
