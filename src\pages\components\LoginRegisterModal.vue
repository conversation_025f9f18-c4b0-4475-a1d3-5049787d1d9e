<template>
  <n-modal
    preset="dialog"
    class="login-modal-dialog"
    :closable="false"
    :show-icon="false"
    :block-scroll="true"
    v-model:show="pageData.showModal"
    :on-close="onCloseGuide"
    :on-esc="onCloseGuide"
    :on-mask-click="onCloseGuide"
    :style="{
      width: '634px',
      padding: 0,
    }"
  >
    <div>
      <img
        loading="lazy"
        src="@/assets/icons/home/<USER>"
        class="w-[100%] block object-fill"
      />
      <icon-card
        size="26"
        color="#fff"
        class="modal-icon"
        name="iconamoon:close-light"
        @click="onCloseGuide"
        v-show="pageData.showModal"
      ></icon-card>
      <div class="modal-content">
        <n-button color="#fff" class="modal-button" @click="onLoginClick(0)">
          {{ authStore.i18n("cm_common.createAccount") }}
        </n-button>
        <div class="mt-[12px] text-[15px] leading-[15px] text-center">
          {{ authStore.i18n("cm_common.haveAccount") }}
          <span
            class="text-[17px] underline underline-offset-2 cursor-pointer"
            @click="onLoginClick(1)"
            >{{ authStore.i18n("cm_common.loginAccount") }}</span
          >
        </div>
      </div>
    </div>
  </n-modal>
</template>

<script setup lang="ts" name="GuideModal">
import { useAuthStore } from "@/stores/authStore";
const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();

const loginRegister = inject<any>("loginRegister");
const pageData = reactive({
  showModal: false,
});

const userInfo = ref<object>({});
userInfo.value = config.public.userInfo as object;

onMounted(() => {
  if (isEmptyObject(userInfo.value)) {
    setTimeout(() => {
      if (!window?.MyStat.getSessionValue("isClickLoginModal")) {
        onOpenGuide();
      }
    }, 30000);
  }
});

function onOpenGuide() {
  pageData.showModal = true;
  window?.MyStat?.setSessionValue("isClickLoginModal", "true");
  window?.MyStat?.addPageEvent("unlogin_dialog_open", "未登录弹窗打开"); // 埋点
}
function onCloseGuide() {
  pageData.showModal = false;
  window?.MyStat?.addPageEvent("unlogin_dialog_close", "未登录弹窗关闭"); // 埋点
}

function onLoginClick(type?: any) {
  if (type === 1) {
    window?.MyStat?.addPageEvent("unlogin_dialog_go_login", "未登录弹窗去登录"); // 埋点
  } else {
    window?.MyStat?.addPageEvent(
      "unlogin_dialog_go_register",
      "未登录弹窗去注册"
    ); // 埋点
  }
  loginRegister?.openLogin("", type);
}

defineExpose({
  onOpenGuide,
});
</script>
<style scoped lang="scss">
.modal-icon {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 1000;
  cursor: pointer;
}
.modal-content {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 30px;
  z-index: 1000;
  color: #fff;
  .modal-button {
    height: 38px;
    padding: 10px 74px;
    border-radius: 100px;
    color: #e50113;
    font-size: 18px;
    font-weight: 500;
    line-height: 18px;
  }
}
</style>
<style>
.login-modal-dialog.n-dialog {
  background: none !important;
}
.login-modal-dialog.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0px !important;
}
</style>
