<template>
  <div class="mobile-container w-full overflow-hidden">
    <seo-data :pageData="pageData"></seo-data>
    <div class="!bg-white pt-[0.16rem]">
      <n-marquee auto-fill>
        <div
          class="flex items-center gap-[0.12rem] text-[0.28rem] leading-[0.28rem]"
        >
          <img
            src="@/assets/icons/home/<USER>"
            alt="US$ 2,000"
            loading="lazy"
            class="w-[0.44rem] h-[0.44rem] mr-[0.04rem] ml-[0.16rem]"
          />
          <span class="text-[#333] font-medium"> M<PERSON>imo desde US$ 2,000 </span>
          <span class="text-[#7F7F7F]">hasta contenedor cerrado</span>
          <span class="text-[#333]"
            >hace compras directamente del mercado mayorista chino</span
          >
        </div>
      </n-marquee>
    </div>

    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>

    <!-- 轮播 -->
    <div class="w-full min-h-[4.8rem] px-[0.08rem] bg-white">
      <n-carousel
        autoplay
        :transition-style="{ transitionDuration: '500ms' }"
        class="home-header-carousel"
        :space-between="2"
      >
        <img
          loading="lazy"
          class="w-full rounded-[0.16rem] overflow-hidden"
          :src="carousel"
          v-for="(carousel, index) in carouselData"
          :key="index"
          :img-props="{ referrerpolicy: 'no-referrer' }"
        />
        <template #dots="{ total, currentIndex, to }">
          <ul class="custom-dots">
            <li
              v-for="index of total"
              :key="index"
              :class="{ ['is-active']: currentIndex === index - 1 }"
              :style="getCarouselDotStyle(index - 1, currentIndex, total)"
              @click="to(index - 1)"
            ></li>
          </ul>
        </template>
      </n-carousel>
    </div>

    <!-- 福利明细 -->
    <div class="w-full pt-[0.48rem] bg-white">
      <div
        class="w-[6.7rem] mx-auto h-[0.6rem] rounded-tl-[0.4rem] rounded-tr-[0.4rem] bg-[#F2F3F5] flex justify-center items-center gap-[0.1rem]"
      >
        <img
          alt="d"
          loading="lazy"
          class="w-[0.6rem] flex-shrink-0 mt-[-0.16rem]"
          src="@/assets/icons/home/<USER>"
        />
        <img
          loading="lazy"
          class="w-[4.5rem] h-[0.28rem] flex-shrink-0"
          alt="esglose de beneficios"
          src="@/assets/icons/home/<USER>"
        />
      </div>
      <div class="relative w-full benefit-breakdown overflow-hidden">
        <img
          alt="bg"
          loading="lazy"
          class="absolute top-[0.32rem] right-[-0.26rem] w-[2.8rem]"
          src="@/assets/icons/home/<USER>"
        />
        <div
          class="hidden-scrollbar pt-[0.44rem] pb-[0.36rem] pl-[0.08rem] pr-[0.08rem] flex gap-[0.02rem] relative z-2 overflow-x-auto"
        >
          <div
            v-for="(item, index) in benefitBreakdown"
            :key="index"
            class="flex flex-col items-center gap-[0.16rem]"
          >
            <div
              class="w-[0.72rem] h-[0.72rem] bg-[#8C111B] rounded-full flex-shrink-0 flex items-center justify-center"
            >
              <img
                :src="item.icon"
                :alt="item.title"
                loading="lazy"
                class="w-[0.42rem]"
              />
            </div>
            <div
              class="w-[2.04rem] text-[0.32rem] leading-[0.36rem] text-center"
              :class="{
                'px-[0.32rem]': index === 3,
                '!w-[2.28rem]': index === 4,
              }"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
        <div class="gradient-line"></div>
        <div
          class="pt-[0.28rem] pb-[0.24rem] flex items-center justify-center gap-[0.12rem] cursor-pointer"
          @click="onOpenBenefitDrawer"
        >
          <span class="text-[0.36rem] leading-[0.36rem] text-[#8C111B]">{{
            authStore.i18n("cm_home.viewDetails")
          }}</span>
          <img
            loading="lazy"
            class="w-[0.32rem] flex-shrink-0 mb-[0.02rem]"
            :alt="authStore.i18n('cm_home.viewDetails')"
            src="@/assets/icons/home/<USER>"
          />
        </div>
      </div>
    </div>

    <!-- 购买流程 -->
    <div class="w-full mt-[0.24rem] pt-[0.4rem] bg-white mb-[0.08rem]">
      <div
        class="w-[4.08rem] text-[0.4rem] leading-[0.44rem] mx-auto text-center"
      >
        {{ authStore.i18n("cm_home_3stepPurchasing") }}
      </div>
      <div
        class="hidden-scrollbar flex gap-[0.08rem] mt-[0.4rem] overflow-x-auto pb-[0.36rem] px-[0.16rem]"
      >
        <div v-for="(item, index) in buySteps" :key="index">
          <img
            class="w-[2.8rem] h-[1.02rem] rounded-[0.08rem] overflow-hidden"
            :src="item.image"
            :alt="item.title"
          />
          <div class="flex items-center gap-[0.2rem] mt-[0.04rem]">
            <div
              class="w-[0.48rem] h-[0.48rem] rounded-full border-1 border-[#e50113] text-[0.32rem] leading-[0.32rem] text-[#e50113] flex items-center justify-center"
            >
              {{ index + 1 }}
            </div>
            <div
              class="w-[1.92rem] h-[1.08rem] flex items-center text-[0.32rem] leading-[0.36rem]"
              :class="{
                '!w-[2.32rem]': index === 2,
              }"
            >
              <div v-if="index === 0">Selección<br />& Cotización</div>
              <div v-else>
                {{ item.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="gradient-line"></div>
      <div
        class="pt-[0.28rem] pb-[0.24rem] flex items-center justify-center gap-[0.12rem] cursor-pointer"
        @click="onOpenBuyStepsDrawer"
      >
        <span class="text-[0.36rem] leading-[0.36rem]">{{
          authStore.i18n("cm_home.viewDetails")
        }}</span>
        <img
          loading="lazy"
          class="w-[0.32rem] flex-shrink-0 mb-[0.02rem]"
          :alt="authStore.i18n('cm_home.viewDetails')"
          src="@/assets/icons/home/<USER>"
        />
      </div>
    </div>

    <!-- 客户专属活动 -->
    <div
      class="mt-[0.16rem]"
      v-if="pageData.customerExclusiveActivities.length"
    >
      <category-card
        data-spm-box="homepage-exclusive-activity"
        v-for="(item, index) in pageData.customerExclusiveActivities"
        :key="index"
        :cateColor="cateColorArr[index]"
        :cateInfo="item"
        class="mt-[0.16rem] first:mt-0"
      ></category-card>
    </div>

    <!-- 直接下单 -->
    <div class="mt-[0.16rem]" v-if="pageData.topRecommendActivities.length">
      <category-card
        data-spm-box="homepage-exclusive-activity"
        v-for="(item, index) in pageData.topRecommendActivities"
        :key="index"
        :cateColor="cateColorArr[index]"
        :cateInfo="item"
      ></category-card>
    </div>

    <!-- 热销货盘 -->
    <category-card
      class="mt-[0.16rem]"
      :cateColor="cateColorArr[pageData.customerExclusiveActivities.length]"
      :cateInfo="pageData.recommendPackingGoods"
    ></category-card>

    <!-- 热销货盘集合 -->
    <div class="px-[0.16rem] py-[0.32rem] bg-white mt-[0.16rem]">
      <div
        class="w-[5.2rem] mx-auto text-center line-clamp-2 text-[0.4rem] leading-[0.44rem]"
      >
        Descubra su próxima oportunidad de negocio
      </div>
      <div class="w-full flex justify-between mt-[0.28rem]">
        <a
          data-spm-box="homepage-banner-yiwu"
          href="/h5/selector?code=yiwu-index"
        >
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            alt="Los productos populares del Mercado de Yiwu"
            class="w-[3.54rem] rounded-[0.08rem]"
            referrerpolicy="no-referrer"
          />
        </a>
        <a
          href="/selector?code=festival-index"
          data-spm-box="homepage-banner-festival"
        >
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            alt="Lista de los productos de fiesta más vendidos"
            class="w-[3.54rem] rounded-[0.08rem]"
            referrerpolicy="no-referrer"
          />
        </a>
      </div>
    </div>

    <!-- 快速搜索 -->
    <div class="relative w-full mt-[0.24rem]">
      <img
        src="@/assets/icons/home/<USER>"
        alt=""
        class="w-full"
        loading="lazy"
      />
      <div class="absolute top-0 right-0 pt-[6.8rem] px-[0.4rem] text-center">
        <div class="flex">
          <div class="w-[3.34rem] flex flex-col items-center gap-[0.2rem]">
            <img
              loading="lazy"
              class="w-[0.6rem] h-[0.6rem]"
              src="@/assets/icons/home/<USER>"
              :alt="authStore.i18n('cm_home.quickPreciseSearch')"
            />
            <div class="text-[0.32rem] leading-[0.48rem] text-[#11263B]">
              {{ authStore.i18n("cm_home.quickPreciseSearch") }}
            </div>
          </div>
          <div class="w-[3.34rem] flex flex-col items-center gap-[0.2rem]">
            <img
              loading="lazy"
              class="w-[0.6rem] h-[0.6rem]"
              src="@/assets/icons/home/<USER>"
              :alt="authStore.i18n('cm_home.fullSpanishSupport')"
            />
            <div class="text-[0.32rem] leading-[0.48rem] text-[#11263B]">
              {{ authStore.i18n("cm_home.fullSpanishSupport") }}
            </div>
          </div>
        </div>
        <a
          href="/h5/search/looking"
          class="custom-search mt-[0.88rem]"
          data-spm-box="button-find-homepage-mid"
        >
          <img
            loading="lazy"
            class="w-[0.48rem] h-[0.48rem]"
            src="@/assets/icons/home/<USER>"
            :alt="authStore.i18n('cm_home.guidedSearchInit')"
          />
          <div class="text-[0.32rem] leading-[0.32rem] whitespace-nowrap">
            {{ authStore.i18n("cm_home.guidedSearchInit") }}
          </div>
          <img
            loading="lazy"
            alt="arrow"
            class="w-[0.2rem]"
            src="@/assets/icons/common/arrow-right-white.svg"
          />
        </a>
      </div>
    </div>

    <category-card
      class="mt-[0.16rem]"
      :cateColor="cateColorArr[pageData.customerExclusiveActivities.length + 1]"
      :cateInfo="pageData.habitableCapsuleGoods"
    ></category-card>

    <!-- “优质供应商”商品推荐 -->
    <category-card
      class="mt-[0.16rem]"
      :cateColor="cateColorArr[pageData.customerExclusiveActivities.length + 2]"
      :cateInfo="pageData.recommendSupplierGoods"
    ></category-card>

    <!-- 美客多,开学季,义乌推荐货盘-->
    <div v-for="(bannerConfig, index) in pageData.bannerConfigs" :key="index">
      <category-card
        class="mt-[0.16rem]"
        :cateInfo="bannerConfig"
      ></category-card>
    </div>

    <!-- 分类 -->
    <div
      v-for="(categoryConfig, index) in pageData.categoryConfigs"
      :key="index"
    >
      <category-card
        class="mt-[0.16rem]"
        :cateInfo="categoryConfig"
        :cateColor="cateColorArr[index + 5]"
      ></category-card>
    </div>

    <!-- 为什么选择我们 -->
    <div class="mt-[0.24rem] pt-[0.48rem] pb-[0.8rem] bg-white">
      <div class="flex flex-col items-center mb-[0.4rem] w-[5.1rem] mx-auto">
        <img
          alt="point"
          loading="lazy"
          class="w-[1.64rem] mb-[0.28rem]"
          src="@/assets/icons/home/<USER>"
        />
        <div class="w-[5.08rem] text-[0.44rem] leading-[0.52rem] text-center">
          {{ authStore.i18n("cm_home_whyGlobalBuyers") }}
        </div>
        <div class="flex items-center mt-[0.16rem]">
          <img
            src="@/assets/icons/common/logo.svg"
            alt="logo"
            loading="lazy"
            class="w-[2.32rem] mr-[0.04rem] ml-[0.24rem]"
          />
          <span class="text-[0.44rem] leading-[0.44rem]">?</span>
        </div>

        <div
          class="w-[0.6rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.4rem]"
        ></div>
      </div>
      <n-carousel
        draggable
        :loop="true"
        :show-dots="false"
        :show-arrow="true"
        :space-between="20"
        ref="feedbackCarousel"
        :current-index="pageData.activatedWhyUsDataIndex"
        @update:current-index="pageData.activatedWhyUsDataIndex = $event"
        class="feedback-carousel h-[8.52rem]"
      >
        <n-carousel-item v-for="(item, index) in whyUsData" :key="index">
          <div
            class="w-[7.16rem] h-[7.44rem] mx-auto flex flex-col text-left pt-[0.4rem] pb-[0.48rem] px-[0.64rem] relative border-1 border-[#F2F2F2] rounded-[0.4rem]"
          >
            <img
              :src="item.icon"
              :alt="item.title"
              loading="lazy"
              class="w-[0.96rem] h-[0.96rem] mx-auto mb-[0.36rem]"
            />
            <div class="text-[0.36rem] leading-[0.44rem] mb-[0.16rem]">
              {{ item.title }}
            </div>
            <div v-if="index !== 0" class="flex flex-col gap-[0.16rem]">
              <div
                v-for="desc in item.desc"
                :key="desc"
                class="flex gap-[0.2rem]"
              >
                <img
                  src="@/assets/icons/home/<USER>"
                  :alt="desc"
                  class="w-[0.28rem] h-[0.28rem] mt-[0.04rem]"
                />
                <span
                  class="text-[0.32rem] leading-[0.384rem] text-[#7F7F7F]"
                  >{{ desc }}</span
                >
              </div>
            </div>
            <div v-else class="text-[0.32rem] leading-[0.4rem] text-[#7F7F7F]">
              <div>
                ¿Cansado de esperar respuestas manuales para cotizaciones?
              </div>
              <div class="flex gap-[0.2rem] mt-[0.16rem]">
                <img
                  loading="lazy"
                  class="w-[0.28rem] h-[0.28rem] mt-[0.04rem]"
                  src="@/assets/icons/home/<USER>"
                  alt="¿Cansado de esperar respuestas manuales para cotizaciones?"
                />
                Nuestra plataforma actualiza precios de fábricas chinas en
                tiempo real, puede consultar, comparar y ordenar en cualquier
                momento, sin depender de agentes.
              </div>
            </div>
          </div>
        </n-carousel-item>
        <template #arrow="{ prev, next }">
          <div class="custom-arrow">
            <icon-card
              @click="prev"
              name="fe:arrow-left"
              size="0.4rem"
              :color="
                pageData.activatedWhyUsDataIndex === 0 ? '#D9D9D9' : '#e50113'
              "
            >
            </icon-card>
            <div
              class="text-[0.32rem] leading-[0.32rem] h-[0.32rem] mx-[0.2rem] text-[#D9D9D9]"
            >
              <span class="text-[#999]">{{
                pageData.activatedWhyUsDataIndex + 1
              }}</span
              >/{{ whyUsData.length }}
            </div>
            <icon-card
              @click="next"
              name="fe:arrow-right"
              size="0.4rem"
              :color="
                pageData.activatedWhyUsDataIndex === whyUsData.length - 1
                  ? '#D9D9D9'
                  : '#e50113'
              "
            >
            </icon-card>
          </div>
        </template>
      </n-carousel>
    </div>

    <div class="w-full pt-[0.68rem] pb-[0.8rem] mt-[0.16rem] bg-white">
      <div class="text-[0.44rem] leading-[0.44rem] text-center">
        {{ authStore.i18n("cm_home_whyChooseUs") }}
      </div>
      <div
        class="w-[0.6rem] h-[0.04rem] mx-auto mt-[0.36rem] mb-[0.6rem] bg-[#e50113]"
      ></div>
      <div>
        <MobileImageCarousel
          :images="carouselChooseData1"
          direction="left"
          :speed="30"
          :autoplay="true"
          :autoPlayWhenVisible="true"
          :gap="2"
          imageWidth="auto"
          imageHeight="3rem"
        />
        <MobileImageCarousel
          :images="carouselChooseData2"
          direction="right"
          :speed="30"
          :autoplay="true"
          :autoPlayWhenVisible="true"
          :gap="2"
          imageWidth="auto"
          imageHeight="3rem"
          class="mt-[0.08rem]"
        />
      </div>
      <div
        class="mt-[0.36rem] px-[0.4rem] flex justify-between overflow-x-auto hidden-scrollbar"
      >
        <div
          v-for="(item, index) in chooseData"
          :key="item.title"
          class="flex flex-col w-[4.08rem] flex-shrink-0"
        >
          <img
            :src="item.icon"
            :alt="item.title"
            loading="lazy"
            class="w-[0.56rem] h-[0.56rem]"
          />
          <div
            class="text-[0.32rem] leading-[0.4rem] h-[1.2rem] mt-[0.24rem]"
            :class="index === 1 ? 'pr-[1rem]' : ''"
          >
            {{ item.title }}
          </div>
          <div
            class="text-[0.32rem] leading-[0.36rem] mt-[0.16rem] h-[2.12rem] text-[#7F7F7F]"
          >
            {{ item.desc }}
          </div>
          <div class="w-[0.48rem] h-[0.04rem] bg-[#e50113] mt-[0.6rem]"></div>
        </div>
      </div>
    </div>

    <!-- 客户评价 -->
    <div class="w-full pt-[0.68rem] pb-[2.84rem] mt-[0.16rem] bg-white">
      <div
        class="w-[5.6rem] mx-auto text-[0.44rem] leading-[0.44rem] text-center"
      >
        <span class="text-[0.48rem]">5,000+</span>
        {{ authStore.i18n("cm_guestHome.userVideoTitle") }}
      </div>
      <div
        class="w-[0.6rem] h-[0.04rem] bg-[#e50113] mx-auto mt-[0.36rem]"
      ></div>
      <div
        class="video-carousel w-full flex justify-between mt-[0.6rem] px-[0.4rem]"
      >
        <n-carousel
          :space-between="8"
          :loop="false"
          draggable
          slides-per-view="auto"
          show-arrow
          :on-update:current-index="(index:any)=>pageData.activatedUserVideoIndex=index"
        >
          <n-carousel-item
            class="!w-[5.76rem] rounded-[0.08rem] relative overflow-hidden flex-shrink-0 relative"
            v-for="(video, index) in userVideoData"
            @click="onOpenVideo(video, index)"
            :key="index"
          >
            <img
              alt="video"
              loading="lazy"
              :src="video.poster"
              class="w-[5.76rem] h-[3.24rem] rounded-[0.08rem] overflow-hidden"
            />
            <img
              loading="lazy"
              src="@/assets/icons/open/videoPlay.svg"
              alt="video"
              class="w-[0.88rem] h-[0.66rem] absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
            />
          </n-carousel-item>
          <template #arrow="{ prev, next }">
            <div class="custom-arrow">
              <icon-card
                @click="prev"
                name="fe:arrow-left"
                size="24"
                :color="
                  pageData.activatedUserVideoIndex === 0 ? '#D9D9D9' : '#e50113'
                "
                class="mr-[0.6rem]"
              >
              </icon-card>
              <icon-card
                @click="next"
                name="fe:arrow-right"
                size="24"
                :color="
                  pageData.activatedUserVideoIndex === userVideoData.length - 1
                    ? '#D9D9D9'
                    : '#e50113'
                "
              >
              </icon-card>
            </div>
          </template>
          <template #dots="{ total, currentIndex, to }">
            <ul class="custom-dots">
              <li
                v-for="index of total"
                :key="index"
                :class="{ ['is-active']: index - 1 <= currentIndex }"
              ></li>
            </ul>
          </template>
        </n-carousel>
      </div>
    </div>

    <!-- 未登录首页 -->
    <!-- <template v-if="!userInfo?.username">
      <guest-home
        v-if="abtestMode === 'A'"
        :yiwuHotSaleGoods="pageData.yiwuHotSaleGoods"
        :mercadoHotSaleGoods="pageData.mercadoHotSaleGoods"
      ></guest-home>
      <new-guest-home
        v-else
        :yiwuHotSaleGoods="pageData.yiwuHotSaleGoods"
        :mercadoHotSaleGoods="pageData.mercadoHotSaleGoods"
      ></new-guest-home>
    </template> -->

    <!-- 底部信息 -->
    <mobile-page-footer></mobile-page-footer>
  </div>
  <!-- 底部栏 -->
  <mobile-tab-bar :naiveBar="0" />
  <!-- 视频播放 -->
  <video-modal ref="videoModalRef"></video-modal>
  <!-- 未登录优惠券弹窗 -->
  <login-register-modal ref="loginRegisterModalRef"></login-register-modal>
  <!-- 假期通知弹框 -->
  <!-- <holiday-notice-modal></holiday-notice-modal> -->
  <!-- 福利明细抽屉 -->
  <benefit-drawer ref="benefitDrawerRef" :benefit-data="benefitBreakdown" />
  <!-- 购买流程抽屉 -->
  <buy-steps-drawer ref="buyStepsDrawerRef" :buy-steps="buySteps" />
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { cateColorArr } from "@/utils/constant";
import CategoryCard from "./components/CategoryCard.vue";
import LoginRegisterModal from "./components/LoginRegisterModal.vue";
import HolidayNoticeModal from "@/pages/components/HolidayNoticeModal.vue";
import BenefitDrawer from "@/pages/h5/components/BenefitDrawer.vue";
import BuyStepsDrawer from "@/pages/h5/components/BuyStepsDrawer.vue";
import MobileImageCarousel from "@/pages/h5/components/MobileImageCarousel.vue";

import backSchoolHotBanner from "@/assets/icons/backSchoolHotBanner.jpg";
import homeCarouselThousands from "@/assets/icons/home/<USER>";
import homeCarouselFactories from "@/assets/icons/home/<USER>";
import homeCarouselUnique from "@/assets/icons/home/<USER>";
import homeCarouselReward from "@/assets/icons/home/<USER>";
import homeCarouselDiscount from "@/assets/icons/home/<USER>";
// import homeCarouselLive from "@/assets/icons/home/<USER>";

import factoryDirect from "@/assets/icons/home/<USER>";
import quickQuote from "@/assets/icons/home/<USER>";
import cargoConsolidation from "@/assets/icons/home/<USER>";
import totalControl from "@/assets/icons/home/<USER>";
import hassleFreeShipping from "@/assets/icons/home/<USER>";
import selectionQuotation from "@/assets/icons/home/<USER>";
import confirmationPayment from "@/assets/icons/home/<USER>";
import qualityControlShipping from "@/assets/icons/home/<USER>";
import selfServiceOrders from "@/assets/icons/home/<USER>";
import spanishPlatform from "@/assets/icons/home/<USER>";
import flexiblePurchasing from "@/assets/icons/home/<USER>";
import remoteSelection from "@/assets/icons/home/<USER>";
import brand from "@/assets/icons/home/<USER>";
import team from "@/assets/icons/home/<USER>";
import resource from "@/assets/icons/home/<USER>";
import videoPoster1 from "@/assets/icons/home/<USER>";
import videoPoster2 from "@/assets/icons/home/<USER>";
import videoPoster3 from "@/assets/icons/home/<USER>";
import videoPoster4 from "@/assets/icons/home/<USER>";

const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<object>({});
const abtestMode = ref<string>("");
const videoModalRef = ref<any>(null);
const benefitDrawerRef = ref<any>(null);
const buyStepsDrawerRef = ref<any>(null);
const pageData = reactive(<any>{
  recommendPackingGoods: <any>{},
  recommendSupplierGoods: <any>{},
  habitableCapsuleGoods: <any>{},
  bannerConfigs: <any>[],
  categoryConfigs: <any>[],
  topRecommendActivities: [], //直接下单
  customerExclusiveActivities: [], //客户专属活动
  activatedLinkIndex: 0,
  activatedWhyUsDataIndex: 0,
  activatedUserVideoIndex: 0,
});

abtestMode.value = config.public.abtestMode as string;
userInfo.value = config.public.userInfo as object;

/**
 * 为了解决轮播图在服务端渲染到浏览器渲染时，从最后一张切换到第一张可能出现的视觉跳动问题：
 * - 服务端渲染时在尾部增加首张图片
 * - 浏览器渲染时移除尾部的重复图片
 */
const carouselData = [
  homeCarouselFactories,
  homeCarouselDiscount,
  homeCarouselUnique,
  homeCarouselReward,
  homeCarouselThousands,
  homeCarouselFactories,
];

const benefitBreakdown = [
  {
    icon: factoryDirect,
    title: "Directo desde fábrica",
    desc: "Productos a precios competitivos sin intermediarios.",
  },
  {
    icon: quickQuote,
    title: "Cotización rápida",
    desc: "Generas tu lista de pedido online y te damos precios inmediatos.",
  },
  {
    icon: cargoConsolidation,
    title: "Consolidación de carga",
    desc: "Compras a varios proveedores y enviamos todo en un mismo contenedor.",
  },
  {
    icon: totalControl,
    title: "Control total",
    desc: "Verificamos calidad y gestionamos el pago por ti.",
  },
  {
    icon: hassleFreeShipping,
    title: "Envío sin complicaciones",
    desc: "Asistimos en la logística internacional hasta tu país.",
  },
];

const buySteps = [
  {
    image: selectionQuotation,
    title: "Selección & Cotización",
    desc: [
      "Explore millones de productos, agregue al carrito",
      "Envíe su solicitud (sin pago inmediato)",
    ],
  },
  {
    image: confirmationPayment,
    title: "Confirmación & Pago",
    desc: [
      "Nuestro equipo calcula costos finales (incluye envío e impuestos)",
      "Pago seguro tras confirmación",
    ],
  },
  {
    image: qualityControlShipping,
    title: "Control de Calidad & Envío",
    desc: [
      "Inspección manual + máquina en nuestro almacén",
      "Opciones de transporte aéreo/marítimo, seguimiento en tiempo real",
      "Entrega directa en su dirección",
    ],
  },
];

const whyUsData = [
  {
    icon: selfServiceOrders,
    title: "Pedidos autogestionados 24/7, sin límites por diferencia horaria",
  },
  {
    icon: spanishPlatform,
    title: "Plataforma 100% en español, sin barreras",
    desc: [
      "Somos el único sitio mayorista chino completamente en español, con descripciones, especificaciones y soporte al cliente en su idioma.",
    ],
  },
  {
    icon: flexiblePurchasing,
    title: "Flexibilidad para todo tipo de compras",
    desc: [
      'Pequeños pedidos: "Caja mínima", más de 50,000 proveedores directos, mezcla de artículos permitida.',
      "Grandes volúmenes: Sistema de comparación inteligente, revise precios de 30 proveedores en segundos.",
      "Contenedores completos: Mezcla de categorías para ahorrar en logística, con informes de control de calidad.",
    ],
  },
  {
    icon: remoteSelection,
    title: "Selección remota, ahorro de tiempo y costos",
    desc: [
      "No necesita viajar a China, todo el proceso se gestiona en línea.",
      'Si desea visitar, organizamos itinerarios "selección + tour de fábricas + logística" para mayor eficiencia.',
    ],
  },
];

const carouselChooseData1 = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
];

const carouselChooseData2 = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
];

const chooseData = [
  {
    icon: brand,
    title: authStore.i18n("cm_guestHome.brandTitle"),
    desc: authStore.i18n("cm_guestHome.brandDesc"),
  },
  {
    icon: team,
    title: authStore.i18n("cm_guestHome.teamTitle"),
    desc: authStore.i18n("cm_guestHome.teamDesc"),
  },
  {
    icon: resource,
    title: authStore.i18n("cm_guestHome.resourceTitle"),
    desc: authStore.i18n("cm_guestHome.resourceDesc"),
  },
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    poster: videoPoster1,
  },
  {
    id: "Tj0nrnhxgXw",
    poster: videoPoster2,
  },
  {
    id: "_omi5a-pHkA",
    poster: videoPoster3,
  },
  {
    id: "4FVIz0PvEcE",
    poster: videoPoster4,
  },
];

onBeforeMount(() => {
  // 初始化轮播图数据数组，确保首尾没有重复元素
  if (
    carouselData.length > 1 &&
    carouselData[0] === carouselData[carouselData.length - 1]
  ) {
    carouselData.pop();
  }
});

onMounted(async () => {
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
});

await loadPageData();
async function loadPageData() {
  try {
    await Promise.all([onHomePageData(), onRecommendGoods()]);
  } catch (error) {
    console.error("Error loading page data:", error);
  }
}

async function onHomePageData() {
  const res: any = await useHomePageData({});
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
  }
}

// 推荐商品
async function onRecommendGoods() {
  const HOME_CAPSULE_PRODUCT_ID =
    useRuntimeConfig().public.HOME_CAPSULE_PRODUCT_ID;
  const res: any = await useRecommendGoodsV2({
    goodsCount: 24,
    deviceType: 2,
    siteId: window?.siteData?.siteInfo?.id,
    goodsPlugTagParams: [
      {
        tagId: HOME_CAPSULE_PRODUCT_ID,
      },
    ],
  });
  if (res?.result?.code === 200) {
    // 客户专属活动
    pageData.customerExclusiveActivities =
      res.data?.customerExclusiveActivities || [];

    // 头部推荐活动列表(派送国家为秘鲁时，展示)
    if (window?.siteData?.siteInfo?.id == config.public.peruCountryId) {
      pageData.topRecommendActivities = res.data?.topRecommendActivities || [];
    }

    pageData.recommendPackingGoods = {
      spmCode: "tag-goods-packing", // 今日特惠
      tagName: authStore.i18n("cm_home.todaySpecial"),
      tagId: res.data?.recommendPackingGoods?.tagId,
      goodsList: res.data?.recommendPackingGoods?.goodsList,
    };

    //“优质供应商”商品推荐
    pageData.recommendSupplierGoods = {
      spmCode: "homepage-tag-goods",
      tagId: res.data?.recommendSupplierGoods?.tagId,
      tagName: authStore.i18n("cm_home.recommendSupplierGoods"),
      goodsList: res.data?.recommendSupplierGoods?.goodsList,
    };

    pageData.bannerConfigs = [
      {
        bannerUrl: backSchoolHotBanner,
        spmCode: "homepage-hot-school",
        tagId: res.data?.h5BackSchoolHotSaleGoods?.tagId,
        goodsList: res.data?.h5BackSchoolHotSaleGoods?.hotSaleGoods,
      },
    ];

    // 定义分类商品配置
    pageData.categoryConfigs = [
      {
        spmCode: "homepage-hot-camera",
        tagId: res.data?.h5CameraHotSaleGoods?.tagId,
        tagName: authStore.i18n("cm_guestHome.camera"),
        goodsList: res.data?.h5CameraHotSaleGoods?.hotSaleGoods,
      },
      {
        spmCode: "homepage-hot-humidifier",
        tagId: res.data?.h5HumidifierHotSaleGoods?.tagId,
        tagName: authStore.i18n("cm_guestHome.humidifier"),
        goodsList: res.data?.h5HumidifierHotSaleGoods?.hotSaleGoods,
      },
    ];

    pageData.habitableCapsuleGoods = {
      spmCode: "homepage-tag-goods",
      tagId: res.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.tagId,
      tagName: authStore.i18n("cm_home.habitableCapsuleGoods"),
      goodsList: res.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.goodsList,
    };
  }
}

function onOpenVideo(video: any, index: any) {
  if (videoModalRef.value) {
    window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
    videoModalRef.value.onOpenVideo(video);
  }
}

function onOpenBenefitDrawer() {
  benefitDrawerRef.value?.openDrawer();
}

function onOpenBuyStepsDrawer() {
  buyStepsDrawerRef.value?.openDrawer();
}

// 计算轮播图指示点的渐变大小样式
function getCarouselDotStyle(
  dotIndex: number,
  currentIndex: number,
  total: number
) {
  const distance = Math.abs(dotIndex - currentIndex);
  let scale = 1;
  let opacity = 0.5;

  if (distance === 0) {
    scale = 1.0;
    opacity = 1;
  } else if (distance === 1) {
    scale = 0.75;
    opacity = 0.5;
  } else {
    scale = 0.5;
    opacity = 0.5;
  }

  return {
    transform: `scale(${scale})`,
    opacity: opacity,
  };
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  padding-bottom: 1.16rem;
  overflow-y: auto;
  background-color: #fafafa;
}

.custom-search {
  display: inline-flex;
  padding: 0.34rem 0.48rem;
  align-items: center;
  gap: 0.16rem;
  border-radius: 10rem;
  background: #11263b;
  color: #fff;
  transition: background 0.3s ease;
  &:hover {
    background: #0f2e4d;
  }
}

:deep(.logo-header) {
  padding-top: 0 !important;
}

.benefit-breakdown {
  box-shadow: 0 -0.04rem 0.06rem -0.04rem rgba(0, 0, 0, 0.05);
}

.hidden-scrollbar {
  scroll-behavior: smooth;
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.gradient-line {
  width: 100%;
  height: 0.02rem;
  background: linear-gradient(
    90deg,
    rgba(242, 242, 242, 0.2) 0%,
    #f2f2f2 50%,
    rgba(242, 242, 242, 0.2) 100%
  );
}

.feedback-carousel {
  .custom-arrow {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
  }
}
:deep(.home-header-carousel) {
  .custom-dots {
    display: flex;
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: 0.4rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
  }

  .custom-dots li {
    display: inline-block;
    width: 0.16rem;
    height: 0.16rem;
    margin: 0 0.08rem;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    transform-origin: center;
  }

  .custom-dots li.is-active {
    background: #fff;
  }
}

:deep(.video-carousel) {
  .n-carousel {
    overflow: visible;
  }

  .custom-arrow {
    display: flex;
    position: absolute;
    bottom: -1.14rem;
    right: 0rem;
    z-index: 10;
  }

  .custom-dots {
    display: flex;
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: -0.94rem;
    left: 0rem;
    border-radius: 0.08rem;
    background-color: #d9d9d9;
  }

  .custom-dots li {
    display: inline-block;
    width: 1.14rem;
    max-width: none;
    height: 0.02rem;
    background-color: #d9d9d9;
    border-radius: 0;
    cursor: pointer;
    margin: 0;
  }

  .custom-dots li.is-active {
    height: 0.06rem;
    background: #e50113;
  }
}
</style>
