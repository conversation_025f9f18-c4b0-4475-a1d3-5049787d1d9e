<template>
  <div class="wrapper">
    <div
      class="w-full flex skus-center justify-between p-[16px] border-b-1 border-solid border-gray-200"
    >
      <n-image
        lazy
        preview-disabled
        object-fit="fill"
        :src="pageTheme.logo"
        class="w-[160px]"
        :img-props="{ referrerpolicy: 'no-referrer' }"
      />
    </div>
    <div class="px-[16px] font-semibold text-[18px] leading-[28px]">
      <div class="my-[12px]">
        <span>{{ authStore.i18n("cm_inquiry_queryNumber") }}：</span>
        <span>{{ pageData.goodsLookingNo }}</span>
      </div>
      <div class="my-[12px] flex items-center">
        <div class="my-[12px] mr-[40px]">
          {{ authStore.i18n("cm_inquiry_ productDetails") }}
        </div>
        <!-- <n-space :style="{ gap: '6px 40px' }">
          <n-button
            color="#0E7ED9"
            @click="onExportToCustomer"
            :loading="pageData.loadingToCustomer"
          >
            导出给客户
          </n-button>
          <n-button
            color="#E50113"
            @click="onExportToPurchase"
            :loading="pageData.loadingToPurchase"
          >
            导出给采购（产品和供应商信息）
          </n-button>
        </n-space> -->
      </div>
    </div>

    <div class="relative w-fit mb-[80px]">
      <!-- 用户信息 -->
      <table
        cellspacing="0"
        class="mx-[16px] mb-[32px] overflow-x-auto"
        v-if="pageData.skus.length"
      >
        <thead>
          <tr>
            <!-- 下单时间 -->
            <th class="p-[8px]">Hora de pedir</th>
            <!-- 国家 -->
            <th class="p-[8px]">Países</th>
            <!-- 派送国家 -->
            <th class="p-[8px]">Entregar a</th>
            <!-- Whatsapp -->
            <th class="p-[8px]">Whatsapp</th>
            <!-- 姓名 -->
            <th class="p-[8px]">Nombres</th>
            <!-- 邮箱 -->
            <th class="p-[8px]">Bandeja de entrada</th>
            <!-- 邮编 -->
            <th class="p-[8px]">Código postal</th>
            <!-- 地址 -->
            <th class="p-[8px]">Dirección</th>
            <!-- 备注 -->
            <th class="p-[8px]">Requisitos detallados</th>
          </tr>
        </thead>
        <tbody align="center">
          <tr>
            <!--下单时间 -->
            <td class="!p-[16px]">
              {{ timeFormatByZone(pageData.submitTime) }}
            </td>
            <!-- 国家 -->
            <td class="!p-[16px]">{{ pageData.countryName }}</td>
            <!-- 派送国家 -->
            <td class="!p-[16px]">{{ pageData.siteName }}</td>
            <!-- Whatsapp -->
            <td class="!p-[16px]">{{ pageData.whatsapp }}</td>
            <!--姓名 -->
            <td>{{ pageData.submitName }}</td>
            <!-- 邮箱 -->
            <td class="!p-[16px] break-all">{{ pageData.email }}</td>
            <!-- 邮编 -->
            <td class="!p-[16px] break-all">{{ pageData.postcode }}</td>
            <!-- 地址 -->
            <td class="!p-[16px] break-all">{{ pageData.address }}</td>
            <!-- 备注 -->
            <td class="!p-[16px] break-all">{{ pageData.remark }}</td>
          </tr>
        </tbody>
      </table>
      <!-- 商品信息 -->
      <table
        cellspacing="0"
        class="mx-[16px] overflow-x-auto w-full"
        v-if="pageData.skus.length"
      >
        <thead>
          <tr>
            <!-- 序号 -->
            <th class="p-[8px]">NO.</th>
            <!-- 商品名称 -->
            <th class="p-[8px]">Nombre de producto</th>
            <!-- sku图片 -->
            <th class="p-[8px]">Foto de producto</th>
            <!-- 规格 -->
            <th class="p-[8px]">Medida</th>
            <!-- 购买数量 -->
            <th class="p-[8px]">Cantidad</th>
            <!-- 价格单位 -->
            <th class="p-[8px]">Unidad</th>
            <!--销售价 -->
            <th class="p-[8px]">Precio unitario(USD)</th>
            <!-- 产品总价 -->
            <th class="p-[8px]">
              {{ authStore.i18n("cm_find_itemsCost") }}(USD)
            </th>
            <!-- 预估运费 -->
            <th class="p-[8px]">
              {{ authStore.i18n("cm_goods.shippingCost") }}(USD)
            </th>
          </tr>
        </thead>
        <tbody align="center">
          <tr v-for="(sku, index) in pageData.skus" :key="index">
            <!-- 序号 -->
            <td v-if="sku.rowspan" :rowspan="sku.rowspan" class="w-[60px]">
              {{ sku.dyIndex }}
            </td>
            <!-- 商品名称 -->
            <td
              v-if="sku.rowspan"
              :rowspan="sku.rowspan"
              class="max-w-[320px] text-left"
            >
              <div
                class="text-blue cursor-pointer break-all"
                @click="onGoodsDetail(sku)"
              >
                {{ sku.goodsName }}
              </div>
            </td>
            <!--sku图片  -->
            <td>
              <n-image
                lazy
                preview-disabled
                object-fit="fill"
                class="w-[60px] h-[60px] shrink-0 cursor-pointer"
                :src="sku.skuImage"
                :img-props="{ referrerpolicy: 'no-referrer' }"
              />
            </td>
            <!--规格 -->
            <td class="text-left">{{ onGetSpec(sku.specMap) }}</td>
            <!-- 购买数量 -->
            <td class="min-w-[120px]">{{ sku.buyQuantity }}</td>
            <!-- 价格单位 -->
            <td class="w-[100px]">{{ sku.priceUnitName }}</td>
            <!--销售价 -->
            <td>{{ sku.salePrice }}</td>
            <!-- 产品总价 -->
            <td class="min-w-[120px]">{{ sku.totalSalePrice }}</td>
            <td>
              {{
                sku?.estimateFreight
                  ? setUnit(sku.estimateFreight)
                  : authStore.i18n("cm_goods.pendingConfirmation")
              }}
            </td>
          </tr>
        </tbody>
      </table>
      <div
        class="mx-[16px] border border-[#d7d7d7] border-t-0 h-[180px] overflow-hidden w-full"
      >
        <div class="absolute right-[18px]">
          <div class="w-[400px] mt-[16px]">
            <n-space vertical>
              <n-row :span="12">
                <n-col :span="12" class="text-gray">Cantidad </n-col>
                <n-col :span="6">{{ pageData.totalGoodsCount }}</n-col>
              </n-row>
              <n-row :span="12">
                <n-col :span="12" class="text-gray"
                  >Total valor de productos
                </n-col>
                <n-col :span="12">US$ {{ pageData.subTotal }}</n-col>
              </n-row>
              <n-row :span="12">
                <n-col :span="12" class="text-gray"
                  >{{ authStore.i18n("cm_inquiry.shippingFeeTotal") }}
                </n-col>
                <n-col :span="12">{{
                  pageData?.totalEstimateFreight
                    ? setUnit(pageData?.totalEstimateFreight)
                    : authStore.i18n("cm_goods.pendingConfirmation")
                }}</n-col>
              </n-row>
              <n-row :span="12">
                <div class="my-[16px] font-semibold">
                  {{ authStore.i18n("cm_find_offerTip") }}
                </div>
              </n-row>
            </n-space>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const router = useRouter();
const message = useMessage();
import { useAuthStore } from "@/stores/authStore";
import { useConfigStore } from "@/stores/configStore";
import { timeFormatByZone } from "@/utils/date";

const route = useRoute();
const authStore = useAuthStore();
const pageTheme = computed(() => useConfigStore().getPageTheme);
const pageData = reactive<any>({
  skus: <any>[],
  loadingToCustomer: false,
  loadingToPurchase: false,
});

authStore.setShowAnchor(false);
authStore.setShowCarousel(false);

await onGetQueryInquiry();
// 获取询盘列表
async function onGetQueryInquiry() {
  const res: any = await useGetQueryInquiry({ str: route.query?.id });
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    pageData.skus = colspanMethod(pageData.skus);
  } else {
    message.error(
      res?.result?.message || authStore.i18n("cm_find.errorMessage"),
      {
        duration: 3000,
      }
    );
  }
}
function colspanMethod(skus: any) {
  // 多组数据 才会合并
  if (skus && skus.length && skus.length > 1) {
    let dyIndex = 0;
    for (let i = 0; i < skus.length; i++) {
      dyIndex++;
      let startRow;
      // 需合并的行数
      let rowspan = 1;
      // 循环到最后一行时
      if (i === skus.length - 1) {
        // 如果最后一行和倒数第二行属性不同，则rowspan=1；否则直接结束循环
        if (skus[i].goodsName !== skus[i - 1].goodsName) {
          skus[i].dyIndex = dyIndex;
          skus[i].rowspan = rowspan;
        }
        break;
      }
      // 内层循环记录rowspan的数量
      for (let j = i; j < skus.length - 1; j++) {
        // 记录循环结束的行数
        startRow = j;
        // 属性相同则rowspan+1；否则直接结束内循环
        if (skus[j].goodsName === skus[j + 1].goodsName) {
          rowspan++;
          skus[i].dyIndex = dyIndex;
        } else {
          skus[i].dyIndex = dyIndex;
          break;
        }
      }
      // 为数组添加rowspan属性
      skus[i].rowspan = rowspan;
      // 控制外循环从内循环结束的行数开始
      i = startRow;
    }
  } else {
    skus.forEach((item: any, index: any) => {
      item.dyIndex = index + 1;
      item.rowspan = 1;
    });
  }
  return skus;
}

function onGetSpec(specMap: any) {
  let specStr = "";
  for (const key in specMap) {
    specStr += `${key}: ${specMap[key].join(", ")} `;
  }
  return specStr;
}
function onGoodsDetail(sku: any) {
  const url = `/goods/${sku.goodsId}`;
  onOpenLink(url);
}
function onOpenLink(url: any) {
  window.open(url, "_blank");
}
async function onExportToCustomer() {
  pageData.loadingToCustomer = true;
  const res: any = await useExportToCustomer({ id: route.query?.id });
  pageData.loadingToCustomer = false;
  const contentDisposition = res.headers["content-disposition"] || "";
  const matches = contentDisposition.match(/filename=(.+)/);
  let filename = "";
  if (matches) {
    filename = matches[1];
  }
  if (res) {
    await downloadFile(new Blob([res.data]), filename);
  }
}
async function onExportToPurchase() {
  pageData.loadingToPurchase = true;
  const res: any = await useExportToPurchase({ id: route.query?.id });
  pageData.loadingToPurchase = false;
  const contentDisposition = res.headers["content-disposition"] || "";
  const matches = contentDisposition.match(/filename=(.+)/);
  let filename = "";
  if (matches) {
    filename = matches[1];
  }
  if (res) {
    await downloadFile(new Blob([res.data]), filename);
  }
}
</script>
<style scoped lang="scss">
.container {
  height: auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 100vh;
}
table,
th,
td {
  border: 1px solid #d7d7d7;
  border-collapse: collapse; /* 移除单元格之间的间隔 */
}
thead th {
  color: #7f7f7f;
  background-color: #f2f2f2; /* 设置背景色为灰色 */
}
tbody td {
  padding-left: 4px;
  padding-right: 4px;
}
.n-divider {
  margin: 10px 0;
}
.wrapper {
  height: 100vh;
  width: 100%;
  overflow: auto;
}
</style>
