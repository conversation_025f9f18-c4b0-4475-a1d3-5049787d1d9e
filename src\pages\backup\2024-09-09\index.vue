<template>
  <!-- SEO 部分 -->
  <seo-data :pageData="pageData"></seo-data>
  <div
    class="wrapper bg-[#F6F6F6]"
    v-if="!pageData.isLoadingUser && userInfo?.username"
    :class="pageData.isDrawerFixed ? 'min-w-[1660px]' : 'min-w-[1440px]'"
  >
    <product-drawer @updateDrawer="onUpdateDrawer"></product-drawer>
    <div class="left-wrapper">
      <search-card
        :categories="pageData.category"
        class="z-20 mx-auto"
        searchWidth="1400px"
      ></search-card>

      <div class="w-[1400px] mx-auto bg-[#F6F6F6]" id="page-content">
        <!-- <div class="carousel-wrapper h-[540px]">
          <n-carousel
            autoplay
            :transition-style="{ transitionDuration: '500ms' }"
            style="max-height: 540px"
          >
            <n-image
              lazy
              preview-disabled
              class="carousel-img"
              :src="carousel.image"
              v-for="(carousel, index) in carouselData"
              :key="index"
            />
          </n-carousel>
        </div> -->

        <div
          class="news-wrapper my-5 flex justify-between bg-white py-5 px-4 rounded"
          data-spm-box="homepage-welcome-bar"
        >
          <div class="text-base font-medium">
            {{ authStore.i18n("cm_home.welcomeChilat") }}
          </div>
          <div class="flex">
            <n-space :style="{ gap: '0 28px' }"
              ><a
                class="hover:underline cursor-pointer flex items-center"
                v-for="(news, index) in newsData"
                :key="index"
                :href="news.path"
                target="_blank"
              >
                <icon-card
                  :name="news.icon"
                  size="30"
                  color="#db2221"
                ></icon-card>
                <span class="ml-1">{{ news.title }}</span>
              </a></n-space
            >
          </div>
        </div>

        <div class="recommend-wrapper mt-8">
          <div class="mb-5 flex items-center justify-between">
            <div class="text-xl">
              {{ authStore.i18n("cm_home.recommendGoods") }}
            </div>
            <div class="custom-arrow">
              <div class="custom-arrow-icon">
                <icon-card
                  name="iconamoon:arrow-left-2-light"
                  color="white"
                  size="30"
                  @click="prevRecommendGoods"
                ></icon-card>
              </div>
              <div class="custom-arrow-icon">
                <icon-card
                  name="iconamoon:arrow-right-2-light"
                  color="white"
                  size="30"
                  @click="nextRecommendGoods"
                ></icon-card>
              </div>
            </div>
          </div>

          <!-- 推荐商品 -->
          <div class="recommend-goods" data-spm-box="homepage-recommend-goods">
            <n-carousel ref="recommendCarousel" :autoplay="true">
              <n-carousel-item
                v-for="(recommendGoods, index) in pageData.recommendGoods"
                :key="index"
                class="hover:cursor-pointer"
              >
                <n-grid :cols="6" x-gap="18">
                  <n-grid-item
                    v-for="(goods, Iindex) in recommendGoods"
                    :key="goods.id"
                  >
                    <home-goods-card
                      :goods="goods"
                      :goodsIndex="Iindex + 1"
                    ></home-goods-card>
                  </n-grid-item>
                </n-grid>
              </n-carousel-item>
            </n-carousel>
          </div>
        </div>
        <!-- 美客多和义乌推荐 -->
        <div class="bg-white my-8">
          <n-flex
            vertical
            justify="center"
            class="flex justify-between mx-auto pb-4"
          >
            <n-ellipsis class="font-3xl font-medium font-sans p-4">
              {{ authStore.i18n("cm_home.hotTitle") }}
            </n-ellipsis>
            <div class="flex w-full">
              <!-- 美客多推荐 -->
              <div class="w-1/2 mx-4" data-spm-box="homepage-hot-mercado">
                <div class="font-2xl pb-[50px]">
                  <div class="float-left">
                    <n-ellipsis class="font-sans font-medium">
                      {{ authStore.i18n("cm_home.mercadoHotTitle") }}
                    </n-ellipsis>
                  </div>
                  <div
                    class="float-right hover:cursor-pointer text-lg py-1 ml-4"
                    @click="onMercadoHotMore($event)"
                  >
                    <n-ellipsis class="font-sans underline">{{
                      authStore.i18n("cm_app.viewMore")
                    }}</n-ellipsis>
                  </div>
                </div>
                <n-card
                  class="bg-white p-2 rounded-xl"
                  @mouseenter="onMercadoMouseEnter"
                  @mouseleave="onMercadoMouseLeave"
                >
                  <n-carousel
                    ref="mercadoHotSaleRef"
                    :autoplay="true"
                    :show-arrow="pageData.mercadoArrow"
                    :interval="5000"
                    :show-dots="true"
                  >
                    <n-carousel-item
                      v-for="(item, index) in pageData.mercadoHotSaleGoods"
                      :key="index"
                      class="hover:cursor-pointer"
                      :data-spm-index="index + 1"
                    >
                      <n-flex vertical :style="{ gap: '2px' }">
                        <n-ellipsis
                          class="font-medium font-sans font-2lg leading-[26px]"
                          >{{ authStore.i18n("cm_home.hotCake") }}</n-ellipsis
                        >
                        <n-ellipsis
                          class="text-gray-500 font-sans font-lg leading-[22px] mt-1"
                          >{{ item.categoryName }}</n-ellipsis
                        >
                        <n-grid :cols="4">
                          <n-grid-item
                            v-for="(goods, index) in item.hotSaleGoods"
                            :key="index"
                            :span="index === 0 ? 4 : 1"
                            class="p-2"
                            @click="onMercadoHotClick(item, $event)"
                          >
                            <div v-if="index === 0" class="mt-2 flex-center">
                              <n-image
                                lazy
                                preview-disabled
                                :src="goods?.mainImageUrl"
                                class="h-92 rounded-2xl"
                              ></n-image>
                            </div>
                            <div v-else-if="index <= 4">
                              <n-image
                                lazy
                                preview-disabled
                                :src="goods?.mainImageUrl"
                                class="h-36 rounded-2xl"
                              ></n-image>
                            </div>
                          </n-grid-item>
                        </n-grid>
                      </n-flex>
                    </n-carousel-item>
                    <template #arrow="{ prev, next }">
                      <div class="custom-arrow-hot">
                        <div class="custom-arrow-icon custom-arrow--left">
                          <n-button
                            strong
                            secondary
                            circle
                            @click="prev"
                            class="w-12 h-12"
                          >
                            <template #icon>
                              <icon-card
                                size="48"
                                name="iconamoon:arrow-left-2-thin"
                                color="#000"
                                class="mr-1"
                              ></icon-card>
                            </template>
                          </n-button>
                        </div>
                        <div class="custom-arrow-icon custom-arrow--right">
                          <n-button
                            strong
                            secondary
                            circle
                            @click="next"
                            class="w-12 h-12"
                          >
                            <template #icon>
                              <icon-card
                                size="48"
                                name="iconamoon:arrow-right-2-thin"
                                color="#000"
                                class="mr-1"
                              ></icon-card>
                            </template>
                          </n-button>
                        </div>
                      </div>
                    </template>
                  </n-carousel>
                </n-card>
              </div>
              <!-- 义乌推荐 -->
              <div class="w-1/2 mx-4" data-spm-box="homepage-hot-yiwu">
                <div class="font-2xl pb-[50px]">
                  <div class="float-left">
                    <n-ellipsis class="font-sans font-medium">{{
                      authStore.i18n("cm_home.yiWuHotTitle")
                    }}</n-ellipsis>
                  </div>
                  <div
                    class="float-right hover:cursor-pointer text-lg py-1 ml-4"
                    @click="onYiWuHotMore($event)"
                  >
                    <n-ellipsis class="font-sans underline">{{
                      authStore.i18n("cm_app.viewMore")
                    }}</n-ellipsis>
                  </div>
                </div>
                <n-card
                  class="bg-white p-2 rounded-xl hover:cursor-pointer"
                  @click="onYiWuHotClick(pageData.yiwuHotSaleGoods, $event)"
                >
                  <n-flex :style="{ gap: '4px' }">
                    <n-ellipsis
                      class="h-7 px-4 text-lg font-medium font-sans font-2lg leading-[26px]"
                    >
                      <span>{{ pageData.yiwuTotalCount }}</span>
                      <span>+&nbsp;</span>
                      <span>{{
                        authStore.i18n("cm_home.todayGoodsAdded")
                      }}</span>
                    </n-ellipsis>
                    <n-grid :cols="3">
                      <n-grid-item
                        v-for="(item, index) in pageData.yiwuHotSaleGoods
                          ?.hotSaleGoods"
                        :key="index"
                        class="p-2"
                      >
                        <n-image
                          lazy
                          preview-disabled
                          :src="item?.mainImageUrl"
                          class="h-44 rounded-2xl"
                        ></n-image>
                      </n-grid-item>
                    </n-grid>
                  </n-flex>
                </n-card>
                <n-card
                  class="bg-white p-2 rounded-xl mt-5 hover:cursor-pointer min-h-44"
                  @click="onNewWeekClick($event)"
                >
                  <n-grid :cols="3">
                    <n-grid-item>
                      <n-image
                        lazy
                        preview-disabled
                        :src="pageData.hotSaleGoods?.mainImageUrl"
                        class="h-36 rounded-2xl"
                      ></n-image>
                    </n-grid-item>
                    <n-grid-item :span="2" class="h-39">
                      <div class="py-8">
                        <n-ellipsis
                          :line-clamp="2"
                          class="text-xl font-medium font-sans"
                          >{{
                            authStore.i18n("cm_home.newThisWeek")
                          }}</n-ellipsis
                        >
                        <n-ellipsis
                          :tooltip="false"
                          :line-clamp="2"
                          class="text-lg text-gray-400 font-sans"
                          >{{ pageData.hotSaleGoods?.goodsName }}</n-ellipsis
                        >
                      </div>
                    </n-grid-item>
                  </n-grid>
                </n-card>
              </div>
            </div>
          </n-flex>
        </div>
        <div
          class="category-wrapper mt-8"
          data-spm-box="homepage-recommend-category"
        >
          <div
            class="mb-15"
            v-for="(category, index) in pageData.categoryGoods"
            :key="category.categoryId"
            data-spm-box="homepage-recommend-category"
            :data-spm-index="index + 1"
          >
            <div class="flex">
              <a
                :href="`/goods/list/${category.categoryId}?cateName=${category.categoryName}`"
                target="_blank"
                ><div
                  class="category-title"
                  :style="{
                    backgroundColor: category?.cateColor,
                    color: !!category?.cateColor ? '#FFF' : '#333',
                  }"
                >
                  <h3>{{ category.categoryName }}</h3>
                </div></a
              >

              <div class="category-menus">
                <div
                  class="category-menu"
                  v-for="(categoryItem, itemIndex) in category.pageGoods"
                  :key="categoryItem.categoryId"
                  @mouseenter="onMouseEnterCategory(index, itemIndex)"
                >
                  <a
                    :href="`/goods/list/${categoryItem.categoryId}?cateName=${categoryItem.categoryName}`"
                    target="_blank"
                  >
                    <div
                      class="menus-name"
                      :style="{
                        backgroundColor:
                          pageData.actCategoryData[index].itemIndex ===
                          itemIndex
                            ? category?.subCateColor
                            : 'transparent',
                        color:
                          pageData.actCategoryData[index].itemIndex ===
                          itemIndex
                            ? category?.cateColor
                            : '#333',
                      }"
                    >
                      {{ categoryItem.categoryName }}
                    </div>
                    <div
                      class="menu-border"
                      :style="{
                        backgroundColor:
                          pageData.actCategoryData[index].itemIndex ===
                          itemIndex
                            ? category?.cateColor
                            : '#FFF',
                      }"
                    ></div
                  ></a>
                </div>
              </div>
            </div>
            <div class="flex mt-5">
              <a
                :href="`/goods/list/${category.categoryId}?cateName=${category.categoryName}`"
                target="_blank"
              >
                <div class="category-bg">
                  <n-image
                    lazy
                    preview-disabled
                    :src="category.image"
                    class="img"
                  />
                </div>
              </a>
              <div class="category-content pl-5 pr-[16px]">
                <n-grid cols="5 s:4 m:5 l:5 xl:5 2xl:5" :x-gap="16" :y-gap="12">
                  <n-grid-item
                    v-for="(goods, index) in category.pageGoods[
                      pageData.actCategoryData[index].itemIndex
                    ]?.goodsList"
                    :key="goods?.goodsId"
                  >
                    <home-goods-card
                      :goods="goods"
                      :goodsIndex="Iindex + 1"
                      imageWidth="166px"
                      imageHeight="166px"
                    ></home-goods-card>
                  </n-grid-item>
                </n-grid>
              </div>
            </div>
          </div>
        </div>
      </div>
      <page-footer footerWidth="1400px"></page-footer>
    </div>
  </div>

  <guest-home
    v-if="!pageData.isLoadingUser && !userInfo?.username"
    :hotKeywords="pageData.hotKeywords"
  ></guest-home>

  <back-top></back-top>
</template>

<script setup lang="ts">
import { goodsListPath } from "@/utils/constant";
import { useAuthStore } from "@/stores/authStore";
import GuestHome from "./components/GuestHome.vue";
import HomeGoodsCard from "./components/HomeGoodsCard.vue";
// import homeCarousel1 from "@/assets/icons/homeCarousel1.jpg";
// import homeCarousel2 from "@/assets/icons/homeCarousel2.jpg";
// import homeCarousel3 from "@/assets/icons/homeCarousel3.jpg";
// import homeCarousel4 from "@/assets/icons/homeCarousel4.png";
// import homeCarousel5 from "@/assets/icons/homeCarousel5.jpg";

const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const pageData = reactive(<any>{
  keyword: "",
  visible: false,
  isLoading: false,
  mercadoArrow: false, //美客多热卖箭头
  noGoodsData: false, //最新商品
  isHeaderBtnFixed: false,
  isDrawerFixed: false, // 是否显示侧边栏
  actCategoryIndex: 0,
  isLoadingUser: true,
  seoData: {},
  recommendGoods: [],
  mercadoHotSaleGoods: [],
  yiwuHotSaleGoods: [],
  yiwuTotalCount: 0,
  hotSaleGoods: {},
});

const recommendCarousel = ref(null);
const userInfo = ref(null);

// const carouselData = [
//   {
//     image: homeCarousel1,
//   },
//   {
//     image: homeCarousel4,
//   },
//   {
//     image: homeCarousel5,
//   },
//   {
//     image: homeCarousel2,
//   },
//   {
//     image: homeCarousel3,
//   },
// ];

const newsData = [
  {
    icon: "mdi:information-variant-circle",
    title: authStore.i18n("cm_news.aboutUs"),
    path: "/article/about-us",
  },
  {
    icon: "mdi:compass",
    title: authStore.i18n("cm_news.quickGuide"),
    path: "/article/quick-guide",
  },
  {
    icon: "material-symbols:help",
    title: authStore.i18n("cm_news.askedQuestions"),
    path: "/article/frequently-questions",
  },
  // {
  //   icon: "material-symbols:security",
  //   title: authStore.i18n("cm_news.warrantyService"),
  //   path: `/article?code=10002`,
  // },
  {
    icon: "f7:money-dollar-circle-fill",
    title: authStore.i18n("cm_news.commission"),
    path: "/article/commission",
  },
];

authStore.getCartList();
await onHomePageData();

onBeforeMount(async () => {
  await getUserInfo(); //判断是显示未登录首页 还是登录首页
});

onMounted(() => {
  // onRecommendGoods();
  onCategoryGoods();
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
  window.addEventListener("scroll", onScroll);
  window.addEventListener("resize", onResize);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
  window.removeEventListener("resize", onResize);
});

function getUserInfo() {
  pageData.isLoadingUser = true;
  const cachedAuthData = localStorage.getItem("use-auth");
  if (cachedAuthData) {
    const parsedAuthData = JSON.parse(cachedAuthData);
    if (parsedAuthData.userInfo) {
      userInfo.value = parsedAuthData.userInfo;
    }
  }
  pageData.isLoadingUser = false;
}

async function onHomePageData() {
  const [home, recommend]: any = await Promise.all([
    useHomePageData({}),
    useRecommendGoodsV2({ goodsCount: 7, deviceType: 1 }),
  ]);

  // const res: any = await useHomePageData({});
  if (home?.result?.code === 200) {
    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
    pageData.hotKeywords = home?.data.hotKeywords;
    pageData.seoData = home?.data.seoData;
    // 优先从首页接口获取 拿不到则从分类接口去拿
    pageData.category = home?.data?.categoryTree?.children;
    if (!pageData.category) {
      pageData.category = await authStore.getCategoryTree();
    }
  }
  if (recommend?.result?.code === 200) {
    pageData.hotSaleGoods = recommend.data?.hotSaleGoods;
    pageData.yiwuTotalCount = recommend.data?.yiwuTotalCount;
    pageData.recommendGoods = useChunk(recommend.data?.recommendGoods, 6);
    if (recommend.data.yiwuHotSaleGoods?.length > 0) {
      pageData.yiwuHotSaleGoods = recommend.data.yiwuHotSaleGoods[0];
    }
    if (pageData.yiwuHotSaleGoods?.hotSaleGoods?.length > 6) {
      pageData.yiwuHotSaleGoods.hotSaleGoods = useTake(
        pageData.yiwuHotSaleGoods?.hotSaleGoods,
        6
      );
    }

    pageData.mercadoHotSaleGoods = recommend.data?.mercadoHotSaleGoods?.filter(
      (item: any) => {
        return item?.hotSaleGoods?.length >= 5;
      }
    );
  }
}

// 推荐商品
async function onRecommendGoods() {
  const res: any = await useRecommendGoods({});
  if (res?.result?.code === 200) {
    pageData.recommendGoods = useChunk(res?.data, 6);
  }
}

// 商品分类
async function onCategoryGoods() {
  const res: any = await usePcHomePageGoods({});
  if (res?.result?.code === 200) {
    pageData.categoryGoods = res?.data;
    pageData.actCategoryData = res?.data.map(() => ({ itemIndex: 0 }));
  }
}

function onCategoryClick(cate: any, event: any) {
  navigateToPage(
    `/goods/list/${cate.categoryId}`,
    {
      cateName: cate.categoryName,
    },
    true,
    event
  );
}

function onKeywordClick(keyword: string, event: any) {
  const word =
    keyword?.trim() === "" ? pageData.keyword?.trim() : keyword?.trim();

  window?.MyStat?.addPageEvent("click_search", `搜索关键词：${word}`); // 埋点

  navigateToPage(
    `${goodsListAllPath}`,
    {
      keyword: word,
    },
    true,
    event
  );
}

function onOpenDetail(e: any, goods: any) {
  window?.MyStat?.addPageEvent(
    "click_goods_dialog",
    `商品编码：${goods.goodsNo}`
  ); // 埋点

  pageData.visible = true;
  goods.spm = window.MyStat.getPageSPM(e);
  pageData.currentGoods = goods;
}

function onCloseDetail() {
  pageData.visible = false;
}

// 商品加购后 更新商品列表的选中状态
async function onUpdateList(goods: any) {
  pageData.newestGoodsData.goodsList.map((item: any) => {
    if (item.goodsId === goods.goodsId) {
      item.selected = true;
    }
  });
}

function onUpdateDrawer(bol: any) {
  pageData.isDrawerFixed = bol;
  if (pageData.isDrawerFixed) {
    onSetRightWrapper();
  }
}

function onResize() {
  onSetRightWrapper();
  // const pageContent = document.querySelector("#page-content") || <any>{};
  // const fixedTitle = document.querySelector("#fixed-search") || <any>{};
  // fixedTitle.style.left = pageContent.offsetLeft + "px"; //误差
  // pageData.fixedLeft = pageContent.offsetLeft;
}

function onSetRightWrapper() {
  nextTick(() => {
    let rightWrapper = document.getElementById("right-wrapper") || <any>{};
    if (!rightWrapper.style) return;
    if (window.innerWidth <= 1660) {
      let disx = window.innerWidth - 1660;
      rightWrapper.style.right = disx + "px";
    } else {
      rightWrapper.style.right = 0 + "px";
    }
  });
}

async function onScroll(e: any) {
  // x轴滚动
  if (e.deltaX !== 0) {
    onScrollX(e);
  }
}

// 页面横向滚动时 左边内已固定的部分以及右边侧边栏部分跟随滚动
function onScrollX(e: any) {
  let rightWrapper = document.getElementById("right-wrapper") || <any>{};
  const scrollLeft =
    document.documentElement.scrollLeft ||
    document.body.scrollLeft ||
    e.target.scrollLeft ||
    0;
  if (rightWrapper.style) {
    const scrollLeftMax = document.body.scrollWidth - document.body.clientWidth;
    rightWrapper.style.right = scrollLeft - scrollLeftMax - 5 + "px";
  }
}

function prevRecommendGoods() {
  recommendCarousel.value?.prev();
}
function nextRecommendGoods() {
  recommendCarousel.value?.next();
}

function onMouseEnterCategory(index: any, itemIndex: any) {
  pageData.actCategoryData[index].itemIndex = itemIndex;
}

function onMercadoHotClick(item: any, event: any) {
  navigateToPage(
    `${goodsListPath}/all`,
    {
      tagId: item.tagId ?? "",
      cateId: item.categoryId,
      cateName: item.categoryName ?? "",
      tag: "mercado",
    },
    true,
    event
  );
}

function onYiWuHotClick(item: any, event: any) {
  navigateToPage(
    `${goodsListPath}/all`,
    { tagId: item.tagId ?? "", tag: "yiwu" },
    true,
    event
  );
}

function onNewWeekClick(event: any) {
  const url = `${goodsDetailPCPath}/${pageData.hotSaleGoods?.goodsId}`;
  navigateToPage(url, {}, true, event);
}

function onMercadoHotMore(event: any) {
  const first = pageData.mercadoHotSaleGoods?.[0] ?? {};
  navigateToPage(
    `${goodsListPath}/all`,
    { tagId: first.tagId, tag: "mercado" },
    true,
    event
  );
}

function onYiWuHotMore(event: any) {
  const first = pageData.yiwuHotSaleGoods ?? {};

  navigateToPage(
    `${goodsListPath}/all`,
    { tagId: first.tagId ?? "", tag: "yiwu" },
    true,
    event
  );
}

function onMercadoMouseEnter() {
  pageData.mercadoArrow = true;
}

function onMercadoMouseLeave() {
  pageData.mercadoArrow = false;
}
</script>

<style scoped lang="scss">
.wrapper {
  min-height: 100vh;
  display: flex;
  justify-content: flex-start;
  flex-direction: row-reverse;
  width: 100%;
}
.left-wrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 20px;
  min-width: 1400px;
  flex-shrink: 0;
}

.page-header {
  height: auto;
  background-image: url("https://ldnsso.oss-us-east-1.aliyuncs.com/qa/2024/04/11/7d5dbddf-9d5e-4f07-9e6b-3bb882c9757f.jpg");
  background-size: 100%100%;
  position: relative;
  padding-bottom: 20px;
  height: 370px;
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(51, 51, 51, 0.75);
  }
}
.header-fixed {
  position: fixed;
  top: 0;
}
.custom-arrow {
  display: flex;
  .custom-arrow-icon {
    width: 30px;
    height: 30px;
    z-index: 10;
    border-radius: 50%;
    margin-right: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #d7d7d7; /* 设置背景颜色为灰色 */
    &:hover {
      background-color: #aaaaaa;
    }
  }
}

.category-title {
  flex: 1;
  height: 60px;
  font-size: 20px;
  color: #fff;
  line-height: 60px;
  text-align: center;
  border-radius: 6px;
  transform: translateY(1px);
  h3 {
    width: 300px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
  }
}
.category-menus {
  flex: 4;
  margin-left: 20px;
  margin-top: 10px;
  border-bottom: 2px solid #fff;
  display: flex;
  flex-wrap: nowrap;
  .category-menu {
    display: inline-block;
    margin-right: 20px;
    height: 40px;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
    color: #2f2f2f;
    .menus-name {
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      border-radius: 4px;
      font-size: 15px;
      background: transparent;
    }
    .menu-border {
      position: absolute;
      left: 0;
      bottom: -10px;
      height: 2px;
      width: 100%;
      background: transparent;
    }
  }
}

.category-bg {
  background-color: #fff;
  border-radius: 6px;
  width: 300px;
  height: 580px;
  border: 1px solid #e8e8e8;
  text-align: center;
  line-height: 580px;
  overflow: hidden;
  .img {
    width: 100%;
    height: 100%;
  }
}
.category-content {
  flex: 4;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.custom-arrow-hot {
  .custom-arrow-icon {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff; /* 设置背景颜色为灰色 */
  }
  .custom-arrow--left {
    left: 0;
  }
  .custom-arrow--right {
    right: 0;
  }
}

.n-card :deep(.n-card__content) {
  padding: 0.25rem 0.25rem;
}
</style>
