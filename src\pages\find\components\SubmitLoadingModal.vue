<template>
  <transition name="custom-modal-fade">
    <div
      v-if="show"
      class="custom-modal-mask"
      :class="isMobile ? 'mobile-modal' : ''"
    >
      <div class="custom-modal-content">
        <img
          src="@/assets/icons/common/loading.gif"
          alt="loading"
          class="w-[44px] h-[44px] modal-img"
          referrerpolicy="no-referrer"
        />
        <div
          class="text-[14px] leading-[18px] mt-[18px] text-white w-[136px] modal-text"
        >
          {{
            authStore.i18n("cm_find.generatingOrder") ||
            "Generando su pedido, espere un momento..."
          }}
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const props = defineProps<{
  show: boolean;
}>();

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});
</script>

<style scoped lang="scss">
.custom-modal-mask {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-modal-content {
  background: rgba(0, 0, 0, 0.68);
  border-radius: 12px;
  padding: 20px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(1);
  opacity: 1;
}
.custom-modal-fade-enter-active,
.custom-modal-fade-leave-active {
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.custom-modal-fade-enter-from,
.custom-modal-fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
.mobile-modal {
  .custom-modal-content {
    padding: 0.4rem 0.2rem;
    border-radius: 0.24rem;
  }
  .modal-img {
    width: 0.88rem;
    height: 0.88rem;
  }
  .modal-text {
    margin-top: 0.36rem;
    width: 2.72rem;
    font-size: 0.28rem;
    line-height: 0.36rem;
  }
}
</style>
