syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity";

import "chilat/commodity/commodity_common.proto";
import "chilat/basis/param/goods_tag_param.proto";
import "chilat/basis/model/goods_tag_model.proto";
import "common.proto";

service GoodsTag {

  // 获取商品标签列表
  rpc listPage (basis.GoodsTagListPageQueryParam) returns (basis.GoodsTagListPageResp);

  // 保存商品标签
  rpc save (basis.GoodsTagSaveParam) returns (common.ApiResult);

  // 获取标签编辑日志
  rpc editLog (common.IdParam) returns (basis.GoodsTagEditLogResp);

  // 删除商品标签
  rpc deleteTag (basis.DeleteTagParam) returns (basis.DeleteTagResp);

  // 根据业务员ID获取标签列表(默认情况下可以不用填任何参数,如果登录账号是业务员则获取业务员名下的标签+系统标签，管理员获取全部标签)
  rpc getTagListBySalesManId (basis.GoodsTagListQueryParam) returns (basis.GoodsTagListResp);
}