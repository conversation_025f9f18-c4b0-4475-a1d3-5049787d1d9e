<template>
  <n-modal
    preset="dialog"
    :auto-focus="false"
    :block-scroll="true"
    :closable="false"
    :show-icon="false"
    v-model:show="pageData.showVideo"
    :on-close="onCloseVideo"
    :on-esc="onCloseVideo"
    :on-mask-click="onCloseVideo"
    class="home-video-dialog"
    :style="modalStyle"
  >
    <div class="flex items-center justify-between">
      <div
        class="text-[#222] text-[20px] break-all whitespace-nowrap overflow-hidden text-ellipsis"
        :class="
          isMobile
            ? 'h-[0.8rem] mr-[0.2rem]'
            : 'leading-[40px] font-semibold h-[40px] mr-[50px]'
        "
      >
        <!-- {{ pageData.videoInfo?.title }} -->
      </div>
      <icon-card
        size="28"
        color="#666"
        class="mr-[-4px] cursor-pointer"
        name="ic:round-close"
        @click="onCloseVideo"
      >
      </icon-card>
    </div>
    <div class="w-full flex justify-center">
      <you-tube-player
        :width="videoPlayerSize.width"
        :height="videoPlayerSize.height"
        :youtubeId="pageData.videoInfo?.id"
        :titleCh="pageData.videoInfo?.titleCh"
        :title="pageData.videoInfo?.title"
        :autoplay="true"
      ></you-tube-player>
    </div>
  </n-modal>
</template>

<script setup lang="ts" name="videoModal">
const route = useRoute();
const pageData = reactive(<any>{
  showVideo: false,
  videoInfo: <any>{},
  isVerticalVideo: false,
});

const isMobile = computed(() => {
  return route?.fullPath?.startsWith("/h5");
});

const resetFontSize = computed(() => {
  if (process.client) {
    const fontSize = getComputedStyle(document.documentElement).fontSize;
    return parseFloat(fontSize);
  }
  return 50;
});

// 计算视频播放器尺寸
const videoPlayerSize = computed(() => {
  if (isMobile.value) {
    if (pageData.isVerticalVideo) {
      // 竖屏视频：9:16 比例
      return {
        width: 6 * resetFontSize.value,
        height: 10.666 * resetFontSize.value,
      };
    } else {
      // 横屏视频：16:9 比例
      return {
        width: 6.4 * resetFontSize.value,
        height: 3.6 * resetFontSize.value,
      };
    }
  } else {
    if (pageData.isVerticalVideo) {
      return {
        width: 450,
        height: 800,
      };
    } else {
      return {
        width: 760,
        height: 427.5,
      };
    }
  }
});

// 计算模态框样式
const modalStyle = computed(() => {
  if (isMobile.value) {
    return {
      width: "100%",
      padding: "0 0.24rem",
      minHeight: pageData.isVerticalVideo ? "10.2rem" : "5.2rem",
      paddingBottom: pageData.isVerticalVideo ? "0.5rem" : "0",
    };
  } else {
    if (pageData.isVerticalVideo) {
      return {
        width: "520px",
        minWidth: "520px",
        padding: "0 20px",
        minHeight: "900px",
      };
    } else {
      // 横屏视频：适配760x427.5的播放器尺寸
      return {
        width: "800px",
        minWidth: "800px",
        padding: "0 20px",
        minHeight: "520px",
      };
    }
  }
});

// 设置视频方向（手动指定方式）
function setVideoOrientation(videoInfo: any) {
  // 直接从视频信息中获取方向设置
  if (videoInfo && typeof videoInfo.isVertical === "boolean") {
    pageData.isVerticalVideo = videoInfo.isVertical;
  } else {
    // 默认为横屏视频
    pageData.isVerticalVideo = false;
  }
}

async function onOpenVideo(video: any) {
  pageData.videoInfo = video;
  pageData.showVideo = true;

  // 设置视频方向（手动指定或默认）
  setVideoOrientation(video);
}

function onCloseVideo() {
  pageData.showVideo = false;

  setTimeout(() => {
    pageData.isVerticalVideo = false;
  }, 300);
}

defineExpose({
  onOpenVideo,
});
</script>
<style>
.home-video-dialog.n-dialog .n-dialog__content.n-dialog__content--last {
  margin: 0px !important;
}
</style>
