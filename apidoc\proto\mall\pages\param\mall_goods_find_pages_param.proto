syntax = "proto3";
package mall.pages;

option java_package = "com.chilat.rpc.pages.param";

import "common.proto";
import "common/business.proto";

message SubmitGoodsFindParam {
  string email = 10; // 邮箱
  string countryId = 20; // 国家ID
  string country = 21; // 国家ID
  string whatsapp = 30; // WhatsApp
  string areaCode = 31; //whatsapp区号
  string name = 32; // 姓名
  repeated GoodsFindLine goodsFindLineList = 40; // 商品信息列表
  bool isAcceptSimilarProduct = 50; // 是否接受相似商品
  string remark = 60; // 备注
}

message GoodsFindLine {
  repeated string imageList = 10; // 图片列表
  string goodsName = 20; // 商品名称
  int32 count = 30; // 数量
  bool isPriceLimited = 40;  // 是否限制价格
  double minPrice = 50; // 价格下限, isPriceLimited为false时不填，isPriceLimited为true时minPrice和maxPrice必须要至少填一个
  double maxPrice = 60; // 价格上限, isPriceLimited为false时不填，isPriceLimited为true时minPrice和maxPrice必须要至少填一个
}