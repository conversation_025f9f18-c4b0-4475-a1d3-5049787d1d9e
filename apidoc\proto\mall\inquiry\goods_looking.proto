syntax = "proto3";
package chilat.inquiry;

option java_package = "com.chilat.rpc.inquiry";

import "common.proto";
import "chilat/basis/param/goods_looking_param.proto";
import "chilat/basis/model/goods_looking_model.proto";
import "chilat/basis/model/cart_model.proto";

// 询盘
service GoodsLooking {
    // 询盘
    rpc inquiry (chilat.basis.InquiryParam) returns (chilat.basis.MidCartResp);
    // 提交询盘
    rpc submit (chilat.basis.SubmitSkuParam) returns (chilat.basis.QueryInquiryResp);
    // 暂存询盘数据
    rpc submitTemporary (chilat.basis.SubmitSkuParam) returns (common.ApiResult);
    // 查询询盘数据
    rpc queryInquiry (common.StringParam) returns (chilat.basis.QueryInquiryResp);
    // 导出给客户
    rpc exportToCustomer (common.IdParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };
    // 导出给采购
    rpc exportToPurchase (common.IdParam) returns (common.DownloadResult) {
        option (common.webapi).download = true;
    };
    // 校验询盘是否满足条件
    rpc validateInquiry (chilat.basis.InquiryParam) returns (common.ApiResult);
}