<template>
  <!-- 头部信息 -->
  <div v-if="props.type === 'logoHeader'" class="logo-header">
    <div class="relative w-full flex justify-center items-center">
      <a class="mx-auto" href="/h5" data-spm-box="navigation-logo-icon">
        <n-image
          lazy
          preview-disabled
          object-fit="fill"
          :src="pageTheme.logo"
          class="w-[2.76rem] pt-[0.16rem] pb-[0.24rem]"
          :img-props="{ referrerpolicy: 'no-referrer' }"
        />
      </a>
      <div
        v-if="showCart"
        class="mobile-cart-btn absolute right-[0.16rem]"
        @click="onFindListClick($event)"
        data-spm-box="navigation-cart-icon"
      >
        <img
          loading="lazy"
          class="w-[0.52rem]"
          alt="cart"
          src="@/assets/icons/cart.svg"
          referrerpolicy="no-referrer"
        />
        <div v-if="cartGoodsCount" class="fixed-count">
          {{ cartGoodsCount }}
        </div>
      </div>
    </div>
    <div class="w-full flex items-center">
      <a href="/h5" v-if="showHomeIcon" data-spm-box="navigation-top-homepage">
        <img
          loading="lazy"
          src="@/assets/icons/home.svg"
          alt="home"
          class="w-[0.52rem]"
          referrerpolicy="no-referrer"
        />
      </a>
      <text-image-search class="flex-1 px-[0.08rem]"></text-image-search>
    </div>
  </div>
  <div v-else class="back-header">
    <div class="flex items-center justify-between">
      <a
        href="/h5"
        v-if="pageData.showGoHomeIcon"
        data-spm-box="navigation-top-homepage"
      >
        <img
          loading="lazy"
          src="@/assets/icons/home.svg"
          alt="home"
          class="w-[0.52rem] mr-[0.05rem]"
          referrerpolicy="no-referrer"
        />
      </a>
      <img
        loading="lazy"
        v-else
        alt="back"
        class="w-[0.24rem] mr-[0.1rem]"
        @click="onBackClick"
        src="@/assets/icons/arrowLeft.svg"
        referrerpolicy="no-referrer"
      />

      <text-image-search class="flex-1"></text-image-search>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useConfigStore } from "@/stores/configStore";

const config = useRuntimeConfig();
const userInfo = ref<object>({});
userInfo.value = config.public.userInfo as object;

const props = defineProps({
  type: {
    type: String,
    default: "logoHeader", //H5页面有两个固定的头部 logoHeader 和 backHeader
  },
  showCart: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const pageTheme = computed(() => useConfigStore().getPageTheme);
const cartGoodsCount = computed(() => {
  const count = authStore.cartTotalCount;
  return count && count > 99 ? "99+" : count;
});

const showHomeIcon = computed(() => {
  return route.path !== "/h5" && route.path !== "/h5/";
});
const pageData = reactive({
  showGoHomeIcon: false,
});

/** 返回上一页 */
function onBackClick() {
  router.go(-1);
}

onBeforeMount(() => {
  if (props.type === "logoHeader") return;
  // 获取当前页面的域名
  const currentDomain = window.location.hostname;
  // 获取来源页面的 referrer URL
  const referrer = document.referrer;
  if (referrer) {
    // 提取来源页面的域名
    const referrerDomain = new URL(referrer).hostname;
    // 比较当前页面和来源页面的域名
    if (currentDomain === referrerDomain) {
      pageData.showGoHomeIcon = false;
    } else {
      pageData.showGoHomeIcon = true;
    }
  } else {
    pageData.showGoHomeIcon = true;
  }
});

function onFindListClick(event: any) {
  // 未登录 去登陆
  if (isEmptyObject(userInfo.value)) {
    navigateToPage("/h5/user/login", { pageSource: "/h5/find" }, false, event);
  } else {
    navigateToPage("/h5/find", {}, false, event);
  }
}
</script>

<style scoped lang="scss">
.logo-header {
  width: 100%;
  height: 2.08rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 0.16rem;
  padding-bottom: 0.16rem;
  background-color: #fff;
  z-index: 10;
}

.back-header {
  width: 100%;
  height: 1.12rem;
  padding: 0.16rem 0.08rem;
  background-color: #fff;
  border: 0.02rem solid #e5e7eb;
  z-index: 10;
}
.back-header :deep(.search-bar-input) {
  margin-left: 0.02rem !important;
  margin-right: 0 !important;
}
</style>
