syntax = "proto3";
package chilat.marketing;

option java_package = "com.chilat.rpc.marketing.model";

import "common.proto";
import "chilat/marketing/marketing_common.proto";
import "common/business.proto";

// 获取促销活动列表返回
message PromotionPageListResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated PromotionDetailModel data = 3;
}

// 获取促销活动详情返回
message GetPromotionDetailResp {
  common.Result result = 1;
  PromotionDetailModel data = 2;
}

// 促销活动详情对象
message PromotionDetailModel {
  string id = 10; // 活动ID
  string promotionCode = 20; //活动代码（保留字段）
  string promotionName = 30; //活动名称（中文）
  string displayName = 40; //商城前台显示名称（西语）
  int64 beginTime = 50; // 开始时间
  int64 endTime = 60; // 结束时间
  PromotionUserScope userScope = 70; //活动类型（促销活动用户范围）
  string salesmanId = 80; //业务员ID（系统活动，须将业务员ID置为空）
  string remark = 90; //备注
  common.GoodsSelectorType selectorType = 100; // 商品筛选器类型（默认GOODS_SELECTOR_TYPE_GOODS_TAG；保留字段）
  repeated PromotionGoodsSelector goodsSelectors = 110; //商品筛选器列表
  repeated PromotionExclusiveCustomer exclusiveCustomers = 120; //专属客户列表
  string salesmanName = 200; //业务员名称
  //string mallShareUrl = 210; //将改为点击生成接口
  string createBy = 150; //创建人
  int64 createTime = 160; //创建时间
  string lastShortUrl = 170; //最后生成的短链
  bool hasEditPermit = 180; //是否拥有编辑权限
}

// 获取活动上架商品数返回
message MultiGetOnlineCountResp {
  common.Result result = 1;
  map<string, int32> data = 2;
}

// 生成商城短链链返回
message PromotionMallShortLinkResp {
  common.Result result = 1;
  PromotionMallShortLinkModel data = 2;
}

// 生成商城短链链返回对象
message PromotionMallShortLinkModel {
  string shortUrl = 10; //转换短链接后的URL
  string padc = 20; //促销活动动态代码
}


// 操作日志返回
message PromotionOperationLogQueryResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated PromotionOperationLogModel data = 3;
}

// 操作日志返回对象
message PromotionOperationLogModel {
  string id = 1; //ID
  string operation = 2; //操作类型（新增、修改、删除或更详细的操作说明）
  string content = 3; //操作内容（html格式）
  string operator = 4; //操作人
  int64 operateTime = 5; //操作时间
}