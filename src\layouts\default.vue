<template>
  <div>
    <!-- 悬浮按钮 -->
    <floating-buttons></floating-buttons>
    <!-- 内容 -->
    <slot />
    <!-- 底部信息 -->
    <page-footer v-if="showPageFooter"></page-footer>
    <!-- 操作指南 -->
    <guide-modal ref="guideModalRef"></guide-modal>
    <!-- 全局挂载 注册登录 组件  -->
    <login-register ref="loginRegisterRef"></login-register>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";

const route = useRoute();
const authStore = useAuthStore();
const guideModalRef = ref<any>(null);
const loginRegisterRef = ref<any>(null);
const showPageFooter = computed(() => useAuthStore().getShowPageFooter);
const showCopyButton = computed(() => route.query?.preview !== "true");

// 提供全局的注册登录方法
provide("loginRegister", {
  loginRegisterRef,
  openLogin(params?: any, step?: any, callback?: Function) {
    loginRegisterRef?.value?.onOpenLogin(params, step, callback);
  },
});

// 需要隐藏底部信息
function shouldHideFooter(path: any) {
  const hideFooterPages = [
    "/",
    "/open",
    "/home",
    "/inquiry",
    "/login",
    "/register",
    "/modifyPwd",
    "/order/details",
    "/order/payment",
    "/notas",
    "/notas/success",
    "/activate",
    "/open/notas",
    "/viajar-a-china",
    "/vip",
    "/survey",
    "/selector",
    "/goods/looking",
    "/looking",
    "/h5/comprar",
    "/h5/comprar/goods",
    "/h5/comprar/success",
  ];
  if (hideFooterPages.includes(path)) {
    return true;
  }
  // 使用正则表达式匹配商品列表和详情页
  const isGoodsList = /^\/goods\/list\/(all|\d+)$/.test(path);
  const isGoodsDetail = /^\/goods\/\d+$/.test(path);
  if (isGoodsList || isGoodsDetail) {
    return true;
  }
  return false;
}
if (shouldHideFooter(normalizePath(route.path))) {
  authStore.setShowPageFooter(false);
} else {
  authStore.setShowPageFooter(true);
}
</script>
<style scoped></style>
