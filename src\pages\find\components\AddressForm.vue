<template>
  <div class="flex items-center gap-[4px] mb-[12px]">
    <icon-card name="material-symbols:lock" size="24" color="#333"> </icon-card>
    <span class="text-[16px] leading-[16px]">
      {{ authStore.i18n("cm_addr.secureInfo") }}
    </span>
  </div>
  <n-form
    :model="editForm"
    ref="editFormRef"
    :rules="rules"
    label-width="200px"
    label-placement="top"
    class="mb-[16px]"
  >
    <n-grid :cols="24" :x-gap="12">
      <n-form-item-gi
        :span="8"
        path="countryId"
        :label="authStore.i18n('cm_addr.country')"
      >
        <n-select
          filterable
          disabled
          value-field="id"
          label-field="countryEsName"
          :options="props.countryList"
          v-model:value="editForm.countryId"
          @update:value="onSelectCountry('update')"
          :placeholder="authStore.i18n('cm_addr.selectPh')"
          to="body"
        />
      </n-form-item-gi>
      <!-- 联系人 -->
      <n-form-item-gi
        :span="8"
        path="contactName"
        :label="authStore.i18n('cm_addr.contact')"
      >
        <n-input
          v-trim
          clearable
          v-model:value="editForm.contactName"
          :placeholder="authStore.i18n('cm_addr.inputPh')"
        ></n-input>
      </n-form-item-gi>
      <!-- WhatsApp -->
      <n-form-item-gi :span="8" path="phone" label="WhatsApp">
        <n-input-group>
          <n-input
            v-trim
            readonly
            class="!w-20"
            @keydown.enter.prevent
            v-model:value="editForm.areaCode"
            :placeholder="authStore.i18n('cm_submit.telephonePrefix')"
          />
          <n-input
            v-trim
            clearable
            @keydown.enter.prevent
            v-model:value="editForm.phone"
            :placeholder="authStore.i18n('cm_addr.inputPh')"
          />
        </n-input-group>
      </n-form-item-gi>
    </n-grid>
    <n-grid :cols="24" :x-gap="12">
      <!-- 州 -->
      <n-form-item-gi
        :span="8"
        path="province"
        :label="authStore.i18n('cm_addr.stateOrProvince')"
      >
        <n-select
          tag
          filterable
          value-field="name"
          label-field="name"
          children-field="child"
          :options="pageData.provinceList"
          v-model:value="editForm.province"
          @update:value="onSelectProvince('update')"
          :placeholder="authStore.i18n('cm_addr.selectPh')"
          to="body"
        >
        </n-select>
      </n-form-item-gi>
      <n-form-item-gi
        :span="8"
        path="city"
        :label="authStore.i18n('cm_addr.city')"
      >
        <n-select
          tag
          filterable
          value-field="name"
          label-field="name"
          :options="pageData.cityList"
          v-model:value="editForm.city"
          :placeholder="authStore.i18n('cm_addr.selectPh')"
          to="body"
        >
        </n-select>
      </n-form-item-gi>
      <!-- 邮政编码 -->
      <n-form-item-gi
        :span="8"
        path="postcode"
        :label="authStore.i18n('cm_addr.postcode')"
      >
        <n-input
          v-trim
          clearable
          v-model:value="editForm.postcode"
          :input-props="{ type: 'number' }"
          :placeholder="authStore.i18n('cm_addr.inputPh')"
        >
          <template #suffix>
            <n-popover to="body" trigger="hover" placement="top">
              <template #trigger>
                <icon-card
                  name="mingcute:question-line"
                  size="20"
                  color="#333"
                  class="cursor-pointer hover:text-[#e50113]"
                />
              </template>
              <div class="max-w-[240px] text-[14px]">
                {{ authStore.i18n("cm_addr.provideExactPostalCode") }}
              </div>
            </n-popover>
          </template></n-input
        >
      </n-form-item-gi>
    </n-grid>

    <n-form-item :label="authStore.i18n('cm_addr.address')" path="address">
      <n-input
        v-trim
        clearable
        style="width: 100%"
        v-model:value="editForm.address"
        :placeholder="authStore.i18n('cm_addr.inputPh')"
      ></n-input>
    </n-form-item>
    <n-checkbox v-model:checked="editForm.isDefault">
      <span class="ml-[6px]">
        {{ authStore.i18n("cm_addr.defaultFlag") }}
      </span>
    </n-checkbox>
  </n-form>
  <div class="flex justify-end space-x-3 mt-[20px]" v-if="type === 'modal'">
    <n-button round @click="onCloseAddAddrForm">{{
      authStore.i18n("cm_addr.cancelBtn")
    }}</n-button>
    <n-button round type="primary" @click="onSaveAndUseAddress('modal')">{{
      authStore.i18n("cm_addr.confirmBtn")
    }}</n-button>
  </div>
  <div v-else>
    <n-button round type="primary" @click="onSaveAndUseAddress('inline')">{{
      authStore.i18n("cm_addr.saveAndUseAddress")
    }}</n-button>
  </div>
</template>

<script setup lang="ts">
import type { FormInst, FormRules, FormItemRule } from "naive-ui";
import { useMessage } from "naive-ui";

const props = defineProps({
  type: {
    type: String,
    default: "modal",
  },
  countryList: {
    type: Array,
    default: () => [],
  },
  countryRegexes: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["onCloseAddAddr", "onUpdateListUserAddress"]);

// Refs
const editFormRef = ref();
const message = useMessage();
const authStore = useAuthStore();
const editForm = reactive<any>({});
const pageData = reactive<any>({
  provinceList: <any>[],
  cityList: <any>[],
});

// Validation rules
const rules: FormRules = {
  contactName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.contactPh"),
  },
  phone: {
    required: true,
    trigger: "blur",
    message: (() => {
      const phoneCountMessage = props.countryRegexes?.phoneCount
        ? `${authStore.i18n("cm_submit.whatsappTips")} ${
            props.countryRegexes?.phoneCount
          } ${authStore.i18n("cm_submit.whatsapp")}`
        : authStore.i18n("cm_submit.whatsappRequired");
      return `${phoneCountMessage}`;
    })(),
    validator(rule: FormItemRule, value: any) {
      const lengths =
        props.countryRegexes.phoneCount &&
        props.countryRegexes.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.countryPh"),
  },
  postcode: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.postcodePh"),
  },
  province: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.provincePh"),
  },
  city: {
    required: true,
    trigger: "change",
    message: authStore.i18n("cm_addr.cityPh"),
  },
  address: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_addr.addressPh"),
  },
};

watch(
  () => props.countryRegexes,
  (val) => {
    if (val?.phoneCount) {
      (rules["phone"] as any).message = `${authStore.i18n(
        "cm_submit.whatsappTips"
      )} ${val?.phoneCount} ${authStore.i18n("cm_submit.whatsapp")}`;
    }
  },
  { deep: true }
);

function onInitAddrFormData(address?: any) {
  // 清空表单数据
  Object.keys(editForm).forEach((key) => {
    delete editForm[key];
  });

  // 确保国家ID是秘鲁
  editForm.countryId = props.countryRegexes.id;
  editForm.areaCode = props.countryRegexes.areaCode;

  // 清空省市数据
  pageData.provinceList = [];
  pageData.cityList = [];

  if (address) {
    Object.assign(editForm, address);
  }

  if (editForm.countryId) {
    onSelectCountry();
    if (pageData.provinceList && editForm.province) {
      onSelectProvince();
    }
  }
}

function onCloseAddAddrForm() {
  emit("onCloseAddAddr");
}

function onSelectProvince(type?: any) {
  const matchProvince = pageData.provinceList.find(
    (item: any) => item.name === editForm.province
  );
  pageData.cityList = matchProvince?.children;
  if (type === "update") {
    editForm.city = "";
  }
}

async function onSelectCountry(type?: any) {
  const res: any = await useListRegionByCountry({ id: editForm.countryId });
  if (res?.result?.code === 200) {
    onHandleRegion(res?.data, type);
  } else {
    onHandleRegion([], type);
    message.error(res.result?.message);
  }
}

function onHandleRegion(data: any, type?: any) {
  pageData.provinceList = data;
  pageData.cityList = [];
  if (type === "update") {
    editForm.province = "";
    editForm.city = "";
  }
}

async function onSaveAndUseAddress(type: any) {
  await editFormRef.value?.validate();
  const matchProvince = pageData.provinceList.find(
    (item: any) => item.name === editForm.province
  );
  const matchCity = matchProvince?.children?.find(
    (item: any) => item.name === editForm.city
  );

  const provinceCode = matchProvince?.code;
  const cityCode = matchCity?.code;
  let params = {
    ...editForm,
    cityCode,
    provinceCode,
  };
  const res: any = await useSaveUserAddress(params);
  if (res?.result?.code === 200) {
    emit("onUpdateListUserAddress", type);
    message.success(authStore.i18n("cm_addr.addSuccess"));
  } else {
    message.error(res.result?.message);
  }
}

defineExpose({
  onCloseAddAddrForm,
  onInitAddrFormData,
});
</script>
