import { ref } from "vue";

// 定义站点信息类型
export interface SiteInfo {
  id: string;
  code: string;
  name: string;
  logo: string;
}

// 声明window接口扩展
declare global {
  interface Window {
    siteData: {
      siteInfo: SiteInfo;
      siteList: SiteInfo[];
    };
  }
}

// 创建响应式数据
const siteData = ref<{
  siteInfo: SiteInfo;
  siteList: SiteInfo[];
}>({
  siteInfo: {
    id: "",
    code: "",
    name: "",
    logo: "",
  },
  siteList: [],
});

// 初始化全局站点数据
export function initSiteData() {
  // 确保在浏览器环境中运行
  if (typeof window === "undefined") return;

  if (!window.siteData) {
    window.siteData = siteData.value;
  }
}

// 初始化站点数据
export function initSiteInfo(siteId: any, siteList: any) {
  // 确保在浏览器环境中运行
  if (typeof window === "undefined") return;

  initSiteData();

  // 保存站点列表
  siteData.value.siteList = siteList || [];
  window.siteData.siteList = siteList || [];

  // 如果提供了站点ID，则设置当前站点
  if (siteId && siteList && siteList.length > 0) {
    const currentSite = siteList.find(
      (site: SiteInfo) => site.id === siteId || site.id.toString() === siteId
    );

    if (currentSite) {
      setSiteInfo({
        id: currentSite.id,
        code: currentSite.code,
        name: currentSite.name,
        logo: currentSite.logo,
      });
    }
  }
}

// 设置站点信息
export function setSiteInfo(siteInfo: SiteInfo) {
  // 确保在浏览器环境中运行
  if (typeof window === "undefined") return;

  initSiteData();
  siteData.value.siteInfo = siteInfo;
  window.siteData.siteInfo = siteInfo;
}

// 获取站点信息
export function getSiteInfo(): SiteInfo {
  initSiteData();
  // 确保window.siteData存在
  if (typeof window !== "undefined" && window.siteData) {
    return siteData.value.siteInfo;
  }
  // 返回默认值
  return {
    id: "",
    code: "",
    name: "",
    logo: "",
  };
}

// 获取站点列表
export function getSiteList(): SiteInfo[] {
  initSiteData();
  // 确保window.siteData存在
  if (typeof window !== "undefined" && window.siteData) {
    return siteData.value.siteList;
  }
  // 返回默认空数组
  return [];
}

// 更新当前站点
export async function updateCurrentSite(siteId: any) {
  const currentSite = siteData.value.siteList.find(
    (site: SiteInfo) => site.id === siteId || site.id.toString() === siteId
  );

  if (currentSite) {
    setSiteInfo({
      id: currentSite.id,
      code: currentSite.code,
      name: currentSite.name,
      logo: currentSite.logo,
    });
  }
}
