syntax = "proto3";

package chilat.inquiry;

import "chilat/inquiry/inquiry_common.proto";
import "common.proto";


option java_package = "com.chilat.rpc.inquiry.param";

message InquiryInfoParam {
    int64 createStartTime = 1; // 创建开始时间
    int64 createEndTime = 2; // 创建结束时间
    int64 inquiryStartTime = 3; // 询价开始时间
    int64 inquiryEndTime = 4; // 询价结束时间
    int64 inquiryFinshStartTime = 5; // 询价完成开始时间
    int64 inquiryFinshEndTime = 6; // 询价完成结束时间
    string submitName = 7; // 客户名称，表示发起询价的客户
    string storeName = 8; // 店铺名称，表示具体参与询价的店铺名称
    string sourceGoodsId = 9; // 1688商品ID，表示在1688平台上的商品ID
    string skuNo = 10; // SKU商品货号
    string currentInquiryPersonId = 11; // 现询价人，ID
    common.PageParam page = 12;
    InquiryStatus inquiryStatus = 13;//询价单状态，通过点击 开始询价按钮/完成询价按钮变更状态（未询价：NO_INQUIRY  询价中：PROGRESS_INQUIRY 已询价：HAS_INQUIRY）

}


message InquiryDetailParam {
    string inquiryBatchNo = 1; // 询价批次号
}