import _ from "lodash";
import { h } from "vue";
import { createDiscreteApi, NCollapse, NCollapseItem, NButton } from "naive-ui";

// WhatsApp点击追踪状态管理
interface WhatsAppClickState {
  clickCount: number; // 有效点击计数（间隔1秒以上才+1）
  lastClickTime: number; // 上次点击时间
  pageLoadTime: number; // 页面加载时间
}

// 全局状态存储
let whatsAppClickState: WhatsAppClickState = {
  clickCount: 0,
  lastClickTime: 0,
  pageLoadTime: Date.now(),
};

// 防重复弹窗标记
let isHelpDialogShowing = false;
let globalWhatsAppTimeoutId: number | null = null;

// 重置点击状态（页面刷新时调用）
export function resetWhatsAppClickState() {
  whatsAppClickState = {
    clickCount: 0,
    lastClickTime: 0,
    pageLoadTime: Date.now(),
  };

  if (globalWhatsAppTimeoutId !== null) {
    window.clearTimeout(globalWhatsAppTimeoutId);
    globalWhatsAppTimeoutId = null;
  }
}

function generateApiPath(routePath: string): string {
  if (
    /^\/tienda\/[^/]+$/.test(routePath) ||
    /^\/h5\/tienda\/[^/]+$/.test(routePath)
  ) {
    return "tienda-detail";
  }

  if (routePath === "/") {
    return "/";
  }

  if (/^(\/h5\/?)$/.test(routePath)) {
    return "h5";
  }

  let normalizedPath = routePath.replace(/^\//, "");
  if (normalizedPath.startsWith("h5/")) {
    normalizedPath = normalizedPath.substring(3);
  }

  return normalizedPath;
}

function createDefaultWhatsAppConfig() {
  const phone = "8613385799309";
  const text = "quiero saber sobre importar desde China, ¿puedes ayudarme?";
  return {
    jumpUrl: `whatsapp://send/?phone=${phone}&text=${encodeURIComponent(
      text
    )}&type=phone_number&app_absent=0`,
    webUrl: `https://web.whatsapp.com/send/?phone=${phone}&text=${encodeURIComponent(
      text
    )}&type=phone_number&app_absent=1`,
    whatsapp: phone,
  };
}

export async function loadWhatsAppConfig() {
  let whatsAppConfig = null;
  try {
    const route = useRoute();
    const apiPath = generateApiPath(route.path);
    const { useGetPageWhatsAppConfig } = await import("@/composables/http");

    const res: any = await useGetPageWhatsAppConfig({
      pageCode: apiPath,
      visitSite: "VISIT_SITE_SELECT_SHOP",
      useDefaultConfig: true,
    });

    if (res?.result?.code === 200 && res?.data) {
      whatsAppConfig = {
        jumpUrl: res.data.jumpUrl,
        webUrl: res.data.webUrl,
        whatsapp: res.data.whatsapp,
      };
    } else {
      whatsAppConfig = createDefaultWhatsAppConfig();
    }
  } catch (error) {
    whatsAppConfig = createDefaultWhatsAppConfig();
  }
  return whatsAppConfig;
}

export async function showWhatsAppHelpDialog(whatsAppConfig: any) {
  if (isHelpDialogShowing) return; // 已有弹窗在显示，避免重复创建
  isHelpDialogShowing = true;
  const email = "<EMAIL>";
  const { dialog } = createDiscreteApi(["dialog", "message"]);

  const d = dialog.create({
    title: "¿Tiene dificultades para contactarnos por WhatsApp?",
    style: "width:640px;padding:20px 24px",
    content: () =>
      h("div", { style: "color:#333;line-height:1.6;font-size:14px" }, [
        h(
          "div",
          { style: "margin-bottom:12px;" },
          "Siga estas indicaciones según su caso:"
        ),
        h(
          NCollapse,
          {
            defaultExpandedNames: [],
            accordion: false,
            style: `
            --n-title-font-size:14px;
            --n-font-size:14px;
            --n-title-font-weight:500;
            --n-divider-color:#f0f0f0;
          `,
            onItemHeaderClick: (info: any) => {
              if (!info?.expanded) return;
              const name = String(info?.name);
              const map: Record<string, [string, string]> = {
                installed: [
                  "pop_whatsapp_installed",
                  "已安装 WhatsApp，但无法打开应用",
                ],
                have_account: [
                  "pop_whatsapp_have_account",
                  "有 WhatsApp 账号，但未在设备上安装应用",
                ],
                no_account: ["pop_whatsapp_no_account", "没有 WhatsApp 账号"],
              };
              const evt = map[name];
              if (evt) (window as any)?.MyStat?.addPageEvent?.(evt[0], evt[1]);
            },
          },
          {
            default: () => [
              h(
                NCollapseItem,
                {
                  name: "installed",
                },
                {
                  header: () =>
                    h("span", null, [
                      "① WhatsApp está instalado pero no puede abrirlo",
                      h(
                        "span",
                        {
                          style:
                            "margin-left:4px;color:#2162a1;text-decoration:underline;white-space:nowrap",
                        },
                        "Ver detalles"
                      ),
                    ]),
                  default: () =>
                    h(
                      "div",
                      { style: "margin-left:20px" },
                      `Abra la aplicación, busque y agregue nuestro número ${whatsAppConfig.whatsapp} para comunicarse con nosotros.`
                    ),
                }
              ),
              h(
                NCollapseItem,
                {
                  name: "have_account",
                },
                {
                  header: () =>
                    h("span", null, [
                      "② Tiene cuenta de WhatsApp pero no la aplicación en este dispositivo",
                      h(
                        "span",
                        {
                          style:
                            "margin-left:4px;color:#2162a1;text-decoration:underline;white-space:nowrap",
                        },
                        "Ver detalles"
                      ),
                    ]),
                  default: () =>
                    h("div", { style: "margin-left:20px" }, [
                      "Puede usar ",
                      h(
                        "a",
                        {
                          href: whatsAppConfig?.webUrl || "#",
                          target: "_blank",
                          rel: "noopener noreferrer",
                          style: "color:#23D366;cursor:pointer",
                          onClick: () =>
                            (window as any)?.MyStat?.addPageEvent?.(
                              "pop_whatsapp_click_web",
                              "点击WhatsApp Web版链接"
                            ),
                        },
                        ["WhatsApp Web", " ↗"]
                      ),
                      " para escribirnos,",
                      h("br"),
                      `o instalar la aplicación y luego agregar el número ${whatsAppConfig.whatsapp}.`,
                    ]),
                }
              ),
              h(
                NCollapseItem,
                {
                  name: "no_account",
                },
                {
                  header: () =>
                    h("span", null, [
                      h(
                        "span",
                        { style: "white-space:nowrap" },
                        "③ No tiene cuenta de WhatsApp"
                      ),
                      h(
                        "span",
                        {
                          style:
                            "margin-left:4px;color:#2162a1;text-decoration:underline;white-space:nowrap",
                        },
                        "Ver detalles"
                      ),
                    ]),
                  default: () =>
                    h("div", { style: "margin-left:20px" }, [
                      "Envíenos un correo electrónico a ",
                      h(
                        "a",
                        {
                          href: `mailto:${email}`,
                          target: "_blank",
                          rel: "noopener noreferrer",
                          style: "color:#e50113;cursor:pointer",
                          onClick: () =>
                            (window as any)?.MyStat?.addPageEvent?.(
                              "pop_whatsapp_click_email",
                              "在WhatsApp弹窗中点击官网Email"
                            ),
                        },
                        [email, " ↗"]
                      ),
                      " y le responderemos lo antes posible.",
                    ]),
                }
              ),
            ],
          }
        ),
        h(
          "div",
          { style: { textAlign: "center", marginTop: "20px" } },
          h(
            NButton,
            {
              color: "#E50113",
              style: {
                height: "42px",
                padding: "0 12px",
                fontSize: "16px",
                borderRadius: "50px",
                display: "inline-block",
              },
              onClick: () => {
                (window as any)?.MyStat?.addPageEvent?.(
                  "pop_whatsapp_already_jump",
                  "点击“我已成功打开WhatsApp”按钮"
                ),
                  d.destroy();
                isHelpDialogShowing = false;
              },
            },
            "He abierto el WhatsApp exitosamente"
          )
        ),
      ]),

    showIcon: false,
    autoFocus: false,
    maskClosable: false,
    closeOnEsc: false,
    onAfterEnter: () =>
      (window as any)?.MyStat?.addPageEvent?.(
        "pop_whatsapp_open",
        "跳转WhatsApp异常弹窗打开"
      ),
    onClose: () => {
      (window as any)?.MyStat?.addPageEvent?.(
        "pop_whatsapp_close",
        "跳转WhatsApp异常弹窗关闭"
      );
      isHelpDialogShowing = false;
    },
  });
}

// 智能尝试打开 WhatsApp：若 1 秒内没有 window.blur（且时间 <=1250ms），触发兜底模态
export function smartOpenWhatsApp(whatsAppConfig: any) {
  if (globalWhatsAppTimeoutId !== null) {
    window.clearTimeout(globalWhatsAppTimeoutId);
    globalWhatsAppTimeoutId = null;
  }

  const start = Date.now();
  globalWhatsAppTimeoutId = window.setTimeout(() => {
    if (Date.now() - start <= 1250) {
      showWhatsAppHelpDialog(whatsAppConfig);
    }
    globalWhatsAppTimeoutId = null;
  }, 1000);

  const clear = () => {
    if (globalWhatsAppTimeoutId !== null) {
      window.clearTimeout(globalWhatsAppTimeoutId);
      globalWhatsAppTimeoutId = null;
    }
  };

  window.addEventListener("blur", clear, { once: true });
  try {
    window.location.href = whatsAppConfig.jumpUrl;
  } catch {
    clear();
    showWhatsAppHelpDialog(whatsAppConfig);
  }
}

export async function handleWhatsAppClick() {
  const whatsAppConfig = await loadWhatsAppConfig();
  const jumpUrl = whatsAppConfig.jumpUrl as string;
  const currentTime = Date.now();

  const isNewClick =
    whatsAppClickState.clickCount === 0 ||
    currentTime - whatsAppClickState.lastClickTime >= 1000;

  if (isNewClick) {
    whatsAppClickState.clickCount++;
    // 仅在有效点击时更新 lastClickTime
    whatsAppClickState.lastClickTime = currentTime;
  }

  let eventName = "";
  let eventDescription = "";

  switch (whatsAppClickState.clickCount) {
    case 1:
      eventName = "click_whatsapp";
      eventDescription = `点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
    case 2:
      eventName = "click_whatsapp_second";
      eventDescription = `二次点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
    case 3:
      eventName = "click_whatsapp_third";
      eventDescription = `三次点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
    default:
      eventName = "click_whatsapp_more";
      eventDescription = `第${whatsAppClickState.clickCount}次点击Whatsapp图标；Whatsapp链接：${jumpUrl}`;
      break;
  }

  (window as any)?.MyStat?.addPageEvent?.(eventName, eventDescription);
  smartOpenWhatsApp(whatsAppConfig);
}

export const onWhatsAppClick = _.throttle(handleWhatsAppClick, 500, {
  trailing: false,
});
