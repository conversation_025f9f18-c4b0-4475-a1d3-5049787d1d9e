syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis.param";

import "common.proto";
import "common/business.proto";

message GoodsTagListPageQueryParam {
  common.PageParam page = 1;
  GoodsTagListQueryParam param = 2;
}

message GoodsTagListQueryParam {
  int64 startTime = 1; // 开始创建时间
  int64 endTime = 2; // 结束创建时间
  string tagName = 3; //标签名称
  string salesManId = 4; // 业务员id
  common.GoodsTagTypeEnum goodsTagType = 5; // 标签类型
}

message GoodsTagSaveParam {
  string tagId = 1; // 标签ID 新建时不传,编辑需要传, 复制时是来源标签ID
  string tagName = 2; // 标签名称
  string salesManId = 3; //  业务员id
  common.GoodsTagTypeEnum goodsTagType = 4; // 标签类型
  bool isCopy  = 5; // 是否复制
  string productType = 6; //产品类型
}

message DeleteTagParam {
  string tagId = 1;
  bool  isConformDelete = 2; // 是否已确认删除， 为true时为最终确认删除
}