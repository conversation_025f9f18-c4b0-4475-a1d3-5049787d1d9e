/* eslint-disable */
import * as _m0 from "protobufjs/minimal";

export const protobufPackage = "chilat.basis";

export interface ListRegionByPostcodeParam {
  countryId: string;
  postcode: string;
}

export interface QueryCountryParam {
  /** 是否包含“中国”，默认不包含 */
  containChina: boolean;
}

function createBaseListRegionByPostcodeParam(): ListRegionByPostcodeParam {
  return { countryId: "", postcode: "" };
}

export const ListRegionByPostcodeParam = {
  encode(message: ListRegionByPostcodeParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.countryId !== "") {
      writer.uint32(10).string(message.countryId);
    }
    if (message.postcode !== "") {
      writer.uint32(18).string(message.postcode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ListRegionByPostcodeParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListRegionByPostcodeParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.countryId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.postcode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ListRegionByPostcodeParam {
    return {
      countryId: isSet(object.countryId) ? globalThis.String(object.countryId) : "",
      postcode: isSet(object.postcode) ? globalThis.String(object.postcode) : "",
    };
  },

  toJSON(message: ListRegionByPostcodeParam): unknown {
    const obj: any = {};
    if (message.countryId !== "") {
      obj.countryId = message.countryId;
    }
    if (message.postcode !== "") {
      obj.postcode = message.postcode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ListRegionByPostcodeParam>, I>>(base?: I): ListRegionByPostcodeParam {
    return ListRegionByPostcodeParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRegionByPostcodeParam>, I>>(object: I): ListRegionByPostcodeParam {
    const message = createBaseListRegionByPostcodeParam();
    message.countryId = object.countryId ?? "";
    message.postcode = object.postcode ?? "";
    return message;
  },
};

function createBaseQueryCountryParam(): QueryCountryParam {
  return { containChina: false };
}

export const QueryCountryParam = {
  encode(message: QueryCountryParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.containChina !== false) {
      writer.uint32(80).bool(message.containChina);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): QueryCountryParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseQueryCountryParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 10:
          if (tag !== 80) {
            break;
          }

          message.containChina = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): QueryCountryParam {
    return { containChina: isSet(object.containChina) ? globalThis.Boolean(object.containChina) : false };
  },

  toJSON(message: QueryCountryParam): unknown {
    const obj: any = {};
    if (message.containChina !== false) {
      obj.containChina = message.containChina;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<QueryCountryParam>, I>>(base?: I): QueryCountryParam {
    return QueryCountryParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryCountryParam>, I>>(object: I): QueryCountryParam {
    const message = createBaseQueryCountryParam();
    message.containChina = object.containChina ?? false;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
