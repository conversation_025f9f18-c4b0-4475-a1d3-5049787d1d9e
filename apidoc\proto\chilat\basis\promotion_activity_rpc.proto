syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "chilat/basis/model/marketing_activity_model.proto";
import "chilat/basis/param/marketing_activity_param.proto";
import "common.proto";

service PromotionActivityRpc {
  // 根据“促销活动代码”查询促销活动商品索引页信息
  rpc getPromotionGoodsIndex (MidGetPromotionGoodsIndexParam) returns (MidPromotionGoodsIndexResp);

  // 获取商城活动短链（活动专属链接）
  rpc getMallShortUrl (common.IdParam) returns (MidPromotionMallShortLinkResp);

  // 获取商品标签短链（商品标签专属链接）
  rpc getGoodsTagShortUrl (common.IdParam) returns (MidPromotionMallShortLinkResp);

  // 将padc列表转换为商城短链
  rpc convertMallShortUrl (common.CodesParam) returns (common.MapResult);
}
