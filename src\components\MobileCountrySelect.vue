<template>
  <n-drawer
    height="11.8rem"
    placement="bottom"
    v-model:show="showDrawer"
    :close-on-esc="props.mode !== 'modal' ? true : false"
    :mask-closable="props.mode !== 'modal' ? true : false"
  >
    <div class="pt-[0.28rem] py-[0.32rem]">
      <div class="flex items-center justify-center">
        <img
          loading="lazy"
          src="@/assets/icons/deliveryAddress.svg"
          alt="country-select"
          class="w-[0.3rem] mr-[0.16rem]"
          referrerpolicy="no-referrer"
        />
        <div class="text-[0.36rem] leading-[0.36rem]">
          {{ authStore.i18n("cm_common.selectYourLocation") }}
        </div>
      </div>
      <div
        class="text-[#7F7F7F] mt-[0.48rem] text-center text-[0.28rem] leading-[0.28rem]"
      >
        {{ authStore.i18n("cm_common.locationDesc") }}
      </div>
    </div>
    <div class="px-[0.16rem] h-[8.2rem] overflow-y-auto country-wrapper">
      <div
        v-for="(item, index) in siteList"
        class="flex py-[0.28rem] pl-[0.16rem] border-b-1 border-[#F2F2F2] transition-all duration-300 ease-in-out"
        :class="{
          'bg-[#E6E6E6] selected-country': selectedCountry.id === item.id,
          'border-t-1': index === 0,
        }"
        @click="onSelectCountry(item)"
      >
        <img
          loading="lazy"
          class="w-[0.46rem] h-[0.32rem] mr-[0.16rem]"
          :src="item.logo"
          :alt="item.name"
          referrerpolicy="no-referrer"
        />
        <div class="text-[0.32rem] leading-[0.32rem]">{{ item.name }}</div>
      </div>
    </div>
    <div
      class="w-full px-[0.32rem] flex text-[0.32rem] leading-[0.32rem] pt-[0.28rem] pb-[0.12rem]"
    >
      <div>{{ authStore.i18n("cm_common.selectedCountry") }}</div>
      <div class="ml-[0.12rem] text-[#e50113] font-medium">
        {{ selectedCountry?.name }}
      </div>
    </div>
    <div class="p-[0.16rem]">
      <n-button type="primary" size="large" block round @click="onSaveCountry">
        <span class="text-[0.32rem] leading-[0.32rem]">
          {{ authStore.i18n("cm_common.confirmLocation") }}
        </span>
      </n-button>
    </div>
  </n-drawer>
  <div
    class="cursor-pointer flex items-center flex-wrap"
    v-if="mode !== 'modal'"
    @click="showDrawer = true"
  >
    <div class="text-[0.28rem] leading-[0.32rem] mr-[0.16rem] country-delivery">
      {{ authStore.i18n("cm_common.deliveryTo") }}
    </div>
    <div
      class="flex items-center text-[0.30rem] leading-[0.30rem] font-medium country-code"
      v-if="siteInfo.logo"
    >
      <img
        loading="lazy"
        alt="logo"
        class="w-[0.4rem] h-[0.28rem] mr-[0.08rem]"
        :src="siteInfo.logo"
        referrerpolicy="no-referrer"
      />
      <div>
        {{ siteInfo.code }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { h, ref, computed, watch, onMounted, nextTick } from "vue";
import { useMessage } from "naive-ui";
import { getSiteInfo, getSiteList, updateCurrentSite } from "@/utils/siteUtils";

const props = defineProps({
  mode: {
    type: String,
    default: "popover",
  },
  show: {
    type: Boolean,
    default: false,
  },
  spm: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:show", "save"]);

const message = useMessage();
const authStore = useAuthStore();

// 控制抽屉显示
const showDrawer = ref(false);

// 监听props.show变化
watch(
  () => props.show,
  (newValue) => {
    showDrawer.value = newValue;
    if (newValue && props.spm === "select_site_from_pop") {
      const remark = "在弹窗中打开站点选择框";
      window?.MyStat?.addPageEvent(props.spm, remark);
    }
  },
  { immediate: true }
);

// 监听内部showDrawer变化
const scrollToSelectedCountry = () => {
  nextTick(() => {
    const selectedElement = document.querySelector(".selected-country");
    if (selectedElement) {
      selectedElement.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  });
};

watch(showDrawer, (newValue) => {
  if (newValue) {
    selectedCountry.value = siteInfo.value || "";
    scrollToSelectedCountry(); // 定位到当前选中的国家
    let remark;
    if (props.spm === "select_site_from_nav") {
      remark = "在导航栏打开站点选择框";
    } else if (props.spm === "select_site_from_pop") {
      remark = "在弹窗中打开站点选择框";
    } else if (props.spm === "select_site_from_goods") {
      remark = "在商品信息中打开站点选择框";
    } else if (props.spm === "select_site_from_cart") {
      remark = "在购物车中打开站点选择框";
    }
    window?.MyStat?.addPageEvent(props.spm, remark);
  } else {
    window?.MyStat?.addPageEvent("select_site_close_dialog", `关闭站点选择框`);
  }
  emit("update:show", newValue);
});

// 站点选择相关数据
const selectedCountry = ref<any>({});
const siteInfo = computed(() => getSiteInfo());
const siteList = computed(() => getSiteList());

// 初始化组件
onMounted(() => {
  if (siteInfo.value.id) {
    selectedCountry.value = siteInfo.value;
  }
});

// 选择国家时更新选中值
const onSelectCountry = (item: any) => {
  selectedCountry.value = item;
  window?.MyStat?.addPageEvent(
    "select_site_click_one",
    `点击选择了站点：${item.code}`
  );
};

// 保存选择的国家
async function onSaveCountry() {
  if (!selectedCountry.value) return;
  const res: any = await useSetCurrentSite({
    id: selectedCountry.value.id,
  });
  if (res.result.code === 200) {
    updateCurrentSite(selectedCountry.value.id);
    emit("save", selectedCountry.value);
    window?.MyStat?.addPageEvent(
      "select_site_changed",
      `当前站点已改变：${selectedCountry.value.code}`,
      true
    );
    showDrawer.value = false; //关闭抽屉
  }
}
</script>

<style scoped>
:deep(.n-drawer-content) {
  border-radius: 0.32rem 0.32rem 0 0;
}
</style>
