import { defineStore } from "pinia";
import { useCategoryTree, useGetCartByTab } from "~/composables/http";
import { storageCategoryTree } from "@/utils/constant";

export const useAuthStore = defineStore("use-auth", {
  state: () => ({
    locale: "es",
    userInfo: <any>{},
    categoryTree: <any>{},
    customI18n: <any>{},

    // 主题配置
    pageTheme: {
      name: "",
      icon: "",
      logo: "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/04/6552d857-0770-4091-a929-c66961de1daf.png",
      homeLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/04/5ede27ed-7753-48ad-8f90-481b6abb175b.png",
      searchLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/qa/2024/04/12/e7df3074-946c-4412-86e1-4586646a660c.png",
      cateLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/qa/2024/03/05/fbfa47a9-2976-46a3-8209-73e9d5e7dc1c.jpg",
      bgLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/qa/2024/04/11/7d5dbddf-9d5e-4f07-9e6b-3bb882c9757f.jpg",
      guideLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/07/c1158497-05a4-4666-b8e2-a10055d72221.png",
      mobileGuideLogo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/06/07/397dc481-987f-48d9-9995-7ce8075feb13.png",
      guidePoster:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/21/022e4a42-a22a-4440-8add-3dc98d0964ea.png",
      guideVideo:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/21/fcb3a224-2e5b-4373-b7af-b29df6abe721.mp4",
      Guide:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/22/b1798be2-736d-420d-a4f2-2d63ae7ff377.png",
      mobileGuide:
        "https://ldnsso.oss-us-east-1.aliyuncs.com/xpormayor/prod/2024/05/22/ba160b19-ce52-4859-b230-2bb60cb705df.png",
      loading: {
        color: "#E50113",
        height: 3,
        duration: 2000,
        throttle: 200,
      },
      naiveBar: {
        color: "#E50113",
      },
      header: {
        icon: "ep:arrow-left-bold",
        display: false,
      },
    },

    // 购物车数据（包含分类和统计）
    cartByTab: <any>null,

    // 其他业务数据
    inquiryInfo: <any>[], //询盘列表
    ShowedGuide: false, //是否展示过向导
    showAnchor: false, // 控制悬浮按钮展示
    showCarousel: false, // 控制顶部侵权走马灯提示
    showPageFooter: false, // 控制PC底部消息展示
    fromInviteCode: "", //获取分享链接里的邀请码 用于注册回填
  }),
  getters: {
    // 获取总购物车统计信息
    cartStat: (state) => state.cartByTab?.stat || {},

    // 获取总商品数量
    cartTotalCount: (state) => state.cartByTab?.stat?.goodsCount || 0,

    // 获取所有商品列表（合并）
    allCartGoods: (state) => {
      const onlineGoods = state.cartByTab?.onlineOrderCartTab?.goodsList || [];
      const lookingGoods =
        state.cartByTab?.goodsLookingCartTab?.goodsList || [];
      return [...onlineGoods, ...lookingGoods];
    },

    // 检查是否有购物车数据
    hasCartData: (state) =>
      !!state.cartByTab &&
      (state.cartByTab.onlineOrderCartTab?.goodsList?.length > 0 ||
        state.cartByTab.goodsLookingCartTab?.goodsList?.length > 0),

    getPageTheme(state: any) {
      return state.pageTheme;
    },
    getShowAnchor(state: any) {
      return state.showAnchor;
    },
    getShowCarousel(state: any) {
      return state.showCarousel;
    },
    getShowPageFooter(state: any) {
      return state.showPageFooter;
    },
    getUserInfo(state: any) {
      return state.userInfo;
    },
    getFromInviteCode(state: any) {
      return state.fromInviteCode;
    },
  },
  actions: {
    async nuxtServerInit() {
      //TODO 是否先去缓存里看下是否有缓存
      const p1 = useSetLanguage({
        language: "es",
        prefix: "cm_",
      });
      const [language]: any = await Promise.all([p1]);
      this.customI18n = language?.data?.trans;
    },
    async setLanguage() {
      if (Object.keys(this.customI18n || {}).length > 0) {
        return;
      }
      const result: any = await useSetLanguage({
        language: "es",
        prefix: "cm_",
      });
      if (result) {
        this.customI18n = result?.data?.trans;
      }
    },
    i18n(key: string) {
      return this.customI18n[key] || key;
    },
    setShowAnchor(flag: boolean) {
      this.showAnchor = flag;
    },
    setShowCarousel(flag: boolean) {
      this.showCarousel = flag;
    },
    setShowPageFooter(flag: boolean) {
      this.showPageFooter = flag;
    },
    async getCategoryTree() {
      if (this.categoryTree) {
        return this.categoryTree;
      }
      useCategoryTree({}).then((res: any) => {
        if (res?.result?.code === 200) {
          this.categoryTree = useLocalStorage(storageCategoryTree, res?.data);
        }
        return res?.data;
      });
    },
    // 获取购物车数据
    async getCartList(data?: any) {
      if (isEmptyObject(this.userInfo)) {
        this.cartByTab = null;
        this.inquiryInfo = null;
        return;
      }

      // 如果传入了数据，直接设置
      if (data) {
        this.cartByTab = data;
        return;
      }

      // 调用接口获取购物车数据
      try {
        const res: any = await useGetCartByTab({});
        if (res?.result?.code === 200) {
          this.cartByTab = res?.data;
        }
      } catch (error) {
        console.error("获取购物车数据失败:", error);
        this.cartByTab = null;
      }
    },
    setInquiryInfo(data: any) {
      this.inquiryInfo = data;
    },
    getInquiryInfo() {
      return this.inquiryInfo;
    },
    // 设置页面头部信息
    setPageHeaderIcon(icon: string) {
      this.pageTheme.header.icon = icon || "fluent:arrow-reset-24-filled";
    },
    setShowedGuideStatus(data: boolean) {
      this.ShowedGuide = data;
    },
    getShowedGuideStatus() {
      return this.ShowedGuide;
    },
    setUserInfo(data: any) {
      this.userInfo = data;
      if (isEmptyObject(this.userInfo)) {
        this.cartByTab = null;
        this.inquiryInfo = null;
      }
    },
    setFromInviteCode() {
      const urlParams = new URLSearchParams(window.location.search);
      let fromInviteCode = urlParams.get("utm_source");
      if (fromInviteCode && fromInviteCode.startsWith("invite_code_")) {
        const inviteCode = fromInviteCode.split("_").pop();
        this.fromInviteCode = inviteCode || "";
      } else {
        this.fromInviteCode = "";
      }
    },
  },
  persist: process.client && {
    storage: localStorage,
  },
});
