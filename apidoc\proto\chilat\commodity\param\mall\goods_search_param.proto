syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.param";

import "common.proto";

// 商品分页搜索
message GoodsSearchPageQueryParam {
  common.PageParam page = 1;
  GoodsSearchQueryParam query = 2;
}

// 商品搜索
message GoodsSearchQueryParam {
  string imageId = 10; // 图片ID
  string categoryId = 20; //类目ID（必填）
  string keyword = 30; //关键字
  double minPrice = 40; //最低价限制
  double maxPrice = 50; //最高价限制
}