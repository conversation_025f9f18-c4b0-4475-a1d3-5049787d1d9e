<template>
  <div class="mobile-container">
    <div class="w-full relative">
      <div class="w-full pt-[1.98rem]">
        <img loading="lazy"
          alt="chilat"
          class="w-[2.6rem] absolute left-[0.2rem] top-[0.76rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/18/ef9d46aa-1b6c-45c9-841c-e41089c3ee6d.png"
        />
        <div
          class="text-[0.42rem] leading-[0.52rem] font-semibold text-center text-[#fff] relative z-10 px-[1rem]"
        >
          {{ authStore.i18n("cm_nota.oneOnOneService") }}
        </div>
        <img loading="lazy"
          alt="chilat"
          class="w-[5.2rem] absolute right-0 top-[0.8rem]"
          src="https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/11/15/8edbcaa4-7b80-434a-9fdd-384940a4a00e.png"
        />
      </div>
      <div class="mt-[0.48rem] px-[0.16rem] relative z-10">
        <n-form
          :rules="userRules"
          :model="userForm"
          ref="userFormRef"
          label-align="left"
          label-placement="top"
        >
          <div
            class="w-full bg-white rounded-[0.08rem] px-[0.2rem] pt-[0.2rem] pb-[0.4rem]"
          >
            <n-form-item
              path="contactName"
              class="top-form-item input-form-item"
              :label="authStore.i18n('cm_nota.username')"
            >
              <n-input
                v-trim
                clearable
                maxlength="100"
                class="custom-input"
                @keydown.enter.prevent
                v-model:value="userForm.contactName"
                :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                @blur="
                  onBlurEvent(
                    userForm.contactName,
                    authStore.i18n('cm_nota.username')
                  )
                "
              />
            </n-form-item>
            <n-form-item
              path="countryId"
              class="top-form-item input-form-item"
              :label="authStore.i18n('cm_nota.country')"
            >
              <n-select
                filterable
                value-field="id"
                label-field="countryEsName"
                class="custom-input"
                :options="pageData.countryList"
                v-model:value="userForm.countryId"
                @update:value="
                  (value, option) =>
                    onSelectCountry(
                      value,
                      option,
                      authStore.i18n('cm_nota.country')
                    )
                "
                :placeholder="authStore.i18n('cm_nota.selectPlaceholder')"
              />
            </n-form-item>
            <n-form-item
              path="whatsapp"
              class="others-form-item input-form-item"
              :label="authStore.i18n('cm_nota.whatsapp')"
            >
              <div class="!w-[0.8rem] ml-[0.06rem]">
                <span v-if="pageData?.countryRegexps?.areaCode">{{
                  pageData.countryRegexps.areaCode
                }}</span>
                <span class="text-[#A6A6A6]" v-else>+000</span>
              </div>
              <n-divider vertical class="h-full" />
              <n-input
                v-trim
                clearable
                maxlength="64"
                class="custom-input"
                @keydown.enter.prevent
                v-model:value="userForm.whatsapp"
                :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                @blur="
                  onBlurEvent(
                    userForm.whatsapp,
                    authStore.i18n('cm_nota.whatsapp')
                  )
                "
              />
            </n-form-item>
            <n-form-item
              path="email"
              class="top-form-item input-form-item"
              :label="authStore.i18n('cm_nota.email')"
            >
              <n-input
                v-trim
                clearable
                maxlength="64"
                class="custom-input"
                @keydown.enter.prevent
                v-model:value="userForm.email"
                :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                @blur="
                  onBlurEvent(userForm.email, authStore.i18n('cm_nota.email'))
                "
              />
            </n-form-item>
          </div>
          <div
            class="w-full bg-white rounded-[0.08rem] px-[0.2rem] pb-[0.4rem] mt-[0.16rem]"
          >
            <n-form-item
              path="userType"
              :label="authStore.i18n('cm_nota.userType')"
              class="top-form-item"
            >
              <n-radio-group
                class="mb-[0.08rem]"
                v-model:value="userForm.userType"
                :on-update:value="
                  (value) =>
                    onSelectEvent(
                      value,
                      'userType',
                      authStore.i18n('cm_nota.userType')
                    )
                "
              >
                <n-space vertical>
                  <n-radio
                    v-for="item in userTypeList"
                    :value="item.value"
                    :key="item.value"
                  >
                    <div class="flex">
                      <div>{{ item.label }}</div>
                    </div>
                  </n-radio>
                </n-space>
                <n-form-item
                  label=""
                  path="userTypeRemark"
                  class="inner-form-item"
                  v-if="userForm.userType === 'POTENTIAL_USER_TYPE_OTHER'"
                >
                  <n-input
                    round
                    v-trim
                    clearable
                    maxlength="200"
                    @keydown.enter.prevent
                    v-model:value="userForm.userTypeRemark"
                    :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                  />
                </n-form-item>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              path="withImportExperience"
              class="top-form-item input-form-item"
              :label="authStore.i18n('cm_nota.withImportExperience')"
            >
              <n-radio-group
                v-model:value="userForm.withImportExperience"
                :on-update:value="
                  (value) =>
                    onSelectEvent(
                      value,
                      'withImportExperience',
                      authStore.i18n('cm_nota.withImportExperience')
                    )
                "
              >
                <n-space>
                  <n-radio
                    v-for="item in withImportExperienceList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</n-radio
                  >
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item
              path="serviceType"
              class="top-form-item"
              :label="authStore.i18n('cm_nota.serviceType')"
            >
              <n-radio-group
                class="mt-[0.08rem]"
                v-model:value="userForm.serviceType"
                :on-update:value="
                  (value) =>
                    onSelectEvent(
                      value,
                      'serviceType',
                      authStore.i18n('cm_nota.serviceType')
                    )
                "
              >
                <n-space
                  vertical
                  :style="{ gap: '0.18rem 0' }"
                  class="mb-[0.1rem]"
                >
                  <n-radio
                    v-for="item in serviceTypeList"
                    :value="item.value"
                    :key="item.value"
                  >
                    {{ item.label }}
                    <ul>
                      <li
                        v-for="desc in item.labelDesc"
                        :key="desc"
                        v-html="highlightUSPrice(desc)"
                      ></li>
                    </ul>
                  </n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
          </div>
          <div
            class="w-full bg-white rounded-[0.08rem] px-[0.2rem] pb-[0.16rem] mt-[0.16rem]"
          >
            <n-form-item
              path="buyerRemark"
              :label="authStore.i18n('cm_nota.buyerRemark')"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              class="pt-[0.36rem] mt-[0.16rem] rounded-[0.08rem]"
            >
              <n-input
                v-trim
                clearable
                type="textarea"
                :maxlength="60000"
                class="rounded-[0.16rem]"
                @keydown.enter.prevent
                v-model:value="userForm.buyerRemark"
                :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                @blur="
                  onBlurEvent(
                    userForm.buyerRemark,
                    authStore.i18n('cm_nota.buyerRemark')
                  )
                "
              />
            </n-form-item>
          </div>
          <div
            class="w-full bg-white rounded-[0.08rem] px-[0.2rem] pb-[0.16rem] mt-[0.16rem]"
          >
            <n-form-item
              path="captchaCode"
              :label="authStore.i18n('cm_nota.captchaCode')"
              :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
              class="pt-[0.36rem] mt-[0.16rem] rounded-[0.08rem]"
            >
              <n-image
                class="mr-[0.16rem]"
                :preview-disabled="true"
                :src="pageData.captchaImage"
                @click="onRefreshCaptcha"
              ></n-image>
              <n-input
                v-trim
                clearable
                :maxlength="100"
                class="rounded-[0.16rem]"
                @keydown.enter.prevent
                v-model:value="userForm.captchaCode"
                :placeholder="authStore.i18n('cm_nota.inputPlaceholder')"
                @blur="
                  onBlurEvent(
                    userForm.captchaCode,
                    authStore.i18n('cm_nota.captchaCode'),
                    'captchaCode'
                  )
                "
              />
            </n-form-item>
          </div>
        </n-form>
      </div>
      <div
        class="bg-white pt-[0.16rem] pb-[0.6rem] mt-[0.16rem] rounded-[0.08rem] px-[0.16rem]"
      >
        <n-button
          color="#E50113"
          @click="onSubmit"
          :loading="pageData.submitLoading"
          data-spm-box="potential_user_note_submit"
          class="w-full py-[0.4rem] rounded-[0.16rem] text-[0.32rem] leading-[0.32rem] font-semibold"
        >
          <div
            class="flex items-center justify-center"
            v-html="authStore.i18n('cm_nota.submit')"
          ></div>
        </n-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userForm = reactive<any>({
  userType: null, // 用户类型
  userTypeRemark: null, // 用户类型备注（用户类型为“其它”，用户输入类型说明）
  withImportExperience: null, // 是否有进口经验（true:有，false:无）
  serviceType: null, // 服务类型
  contactName: null, // 联系人
  countryId: null, // 国家
  whatsapp: null, // whatsapp
  email: null, // 邮箱
  buyerRemark: null, // 留言
  captchaCode: null,
});
const pageData = reactive<any>({
  countryList: <any>[],
  countryRegexps: <any>{},
  submitLoading: false,
  captchaImage: null,
});

const userTypeList = [
  {
    value: "POTENTIAL_USER_TYPE_WHOLESALE",
    label: authStore.i18n("cm_nota.wholesale"),
  },
  {
    value: "POTENTIAL_USER_TYPE_RETAIL",
    label: authStore.i18n("cm_nota.retail"),
  },
  {
    value: "POTENTIAL_USER_TYPE_ONLINE_SHOP",
    label: authStore.i18n("cm_nota.personalUse"),
  },
  {
    value: "POTENTIAL_USER_TYPE_OTHER",
    label: authStore.i18n("cm_nota.other"),
  },
];

const withImportExperienceList = [
  {
    value: "true",
    label: authStore.i18n("cm_nota.yesExperience"),
  },
  {
    value: "false",
    label: authStore.i18n("cm_nota.noExperience"),
  },
];

const serviceTypeList = [
  {
    value: "POTENTIAL_SERVICE_TYPE_ONLINE_BUY",
    label: authStore.i18n("cm_nota.onlineSelection"),
    labelDesc: [
      authStore.i18n("cm_nota.onlineSelectionDesc1"),
      authStore.i18n("cm_nota.onlineSelectionDesc2"),
    ],
  },
  {
    value: "POTENTIAL_SERVICE_TYPE_VISIT_CHINA",
    label: authStore.i18n("cm_nota.visitChina"),
    labelDesc: [
      authStore.i18n("cm_nota.visitChinaDesc1"),
      authStore.i18n("cm_nota.visitChinaDesc2"),
    ],
  },
  {
    value: "POTENTIAL_SERVICE_TYPE_CUSTOMIZE_PRODUCT",
    label: authStore.i18n("cm_nota.vipCustomSelection"),
    labelDesc: [
      authStore.i18n("cm_nota.vipCustomSelectionDesc1"),
      authStore.i18n("cm_nota.vipCustomSelectionDesc2"),
    ],
  },
];

const userFormRef = ref<FormInst | null>(null);
const userRules: FormRules = {
  userType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  userTypeRemark: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
  withImportExperience: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  serviceType: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  contactName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.selectPlaceholder"),
  },
  whatsapp: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
    validator(rule: FormItemRule, value: any) {
      const lengths =
        pageData.countryRegexps.phoneCount &&
        pageData.countryRegexps.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths?.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            return true;
          }
        }
      } else {
        if (value) {
          return true;
        }
      }
      return false;
    },
  },
  email: {
    required: true,
    trigger: "blur",
    validator(rule: FormItemRule, value: any, callback: Function) {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      // 未填写邮箱
      if (!value) {
        return callback(new Error(authStore.i18n("cm_nota.inputPlaceholder")));
      }
      // 格式不正确
      if (!pattern.test(value)) {
        return callback(new Error(authStore.i18n("cm_common.emailTips")));
      }
      return callback();
    },
  },
  captchaCode: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_nota.inputPlaceholder"),
  },
};

await onGetCountry();
async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    if (config.public.defaultCountryCode) {
      res?.data.map((country: any) => {
        if (country.countryCodeTwo === config.public.defaultCountryCode) {
          userForm.countryId = country.id;
          pageData.countryRegexps = country;
          if (pageData.countryRegexps.phoneCount) {
            userRules["whatsapp"].message = `${authStore.i18n(
              "cm_submit.whatsappTips"
            )} ${pageData.countryRegexps.phoneCount} ${authStore.i18n(
              "cm_submit.whatsapp"
            )}`;
          }
        }
      });
    }
  }
}

onGetCaptchaImage();
async function onGetCaptchaImage() {
  const res: any = await useGetCaptchaImage({ captchaPageSource: "notas" });
  if (res?.result?.code === 200) {
    pageData.captchaImage = res?.data.imageData;
  }
}

function onRefreshCaptcha() {
  onGetCaptchaImage();
  userForm.captchaCode = "";
}

// 价格高亮  匹配 “US$: xxxx” 红色标红
function highlightUSPrice(desc: string) {
  const regex = /(US\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g;
  return desc.replace(regex, '<span style="color: #e50113;">$1</span>');
}

function onSelectCountry(value: any, country: any, label: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${label} 选择：${country?.countryName} （原值：${
      pageData.countryRegexps.countryName || "无"
    }）`
  );
  pageData.countryRegexps = country;
  // 如果有长度校验 则校验长度
  if (pageData.countryRegexps.phoneCount) {
    userRules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappTips"
    )} ${pageData.countryRegexps.phoneCount} ${authStore.i18n(
      "cm_submit.whatsapp"
    )}`;
  } else {
    // 没有长度校验 校验必填
    userRules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappRequired"
    )}`;
  }
}

async function onSubmit(event: any) {
  try {
    // 校验表单
    const isValid = await userFormRef.value?.validate();
    if (isValid) {
      pageData.submitLoading = true;
      try {
        loadReCAPTCHA("submit", async function (success: any, token: any) {
          let paramsObj = {
            ...userForm,
            userSource:
              route?.query?.utm_source === "tiktok_notas"
                ? "chilat直播表单-tiktok"
                : route?.query?.utm_source === "facebook_notas"
                ? "chilat直播表单-facebook"
                : route?.query?.utm_source || "直播",
            reCaptchaLoadSuccess: success,
            reCaptchaToken: token,
            captchaPageSource: "notas",
          };
          const res: any = await useSaveUserInfo(paramsObj);
          pageData.submitLoading = false;
          const areaCode = pageData.countryRegexps.areaCode;
          if (res?.result?.code === 200) {
            window?.MyStat?.addPageEvent(
              "potential_user_submit_success",
              `保存潜客信息成功，顺序号：${res?.data?.seqNo}`
            );

            navigateTo({
              path: "/h5/notas/success",
              query: {
                serviceType: userForm.serviceType,
                contactName: userForm.contactName,
                whatsapp: areaCode + userForm.whatsapp,
                spm: window.MyStat.getPageSPM(event),
              },
            });
          } else {
            if (res?.result?.code === 952401) {
              onRefreshCaptcha();
              showToast(authStore.i18n("cm_nota.captchaCodeError"));
            } else {
              showToast(res?.result?.message);
            }
            window?.MyStat?.addPageEvent(
              "potential_user_submit_error",
              `潜客信息表单错误：${res?.result?.message}`
            );
          }
        });
      } catch (error) {
        pageData.submitLoading = false;
      }
    }
  } catch (error) {
    const remark = `${error?.[0]?.[0]?.message} [${error?.[0]?.[0]?.field}]`;
    window?.MyStat?.addPageEvent(
      "potential_user_submit_error",
      `潜客信息表单错误：${remark}`
    );
  }
}

// 下拉选择埋点事件
function onSelectEvent(value: string, attr: any, label: any) {
  userForm[attr] = value;
  let list = <any>[];
  if (attr === "userType") {
    list = userTypeList;
  }
  if (attr === "withImportExperience") {
    list = withImportExperienceList;
  }
  if (attr === "serviceType") {
    list = serviceTypeList;
  }
  const match = list.find((item: any) => item.value === value);
  if (!value) return;
  window?.MyStat?.addPageEvent(
    "potential_user_select",
    `${label} 选择：${match?.label}`
  );
}

// 输入框埋点事件
async function onBlurEvent(value: string, label: any, form?: any) {
  window?.MyStat?.addPageEvent(
    "potential_user_input",
    `${label} 输入：${value}`
  );
  if (form === "captchaCode") {
    const res: any = await useCheckCaptchaImage({
      captchaPageSource: "notas",
      captchaCode: value,
    });
    if (res?.result?.code === 952401) {
      onRefreshCaptcha();
      showToast(authStore.i18n("cm_nota.captchaCodeError"));
    }
  }
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: auto;
  min-height: 100vh;
  background: linear-gradient(180deg, #db1121 0%, #f87e7e 100%), #f2f2f2;
  background-size: 100% 4.52rem, 100% 100%;
  background-repeat: no-repeat;
  color: #333;
}
ul {
  margin-top: 0.2rem;
  list-style-type: disc;
  padding-left: 0.4rem;
  color: #7f7f7f;
  font-size: 0.26rem;
  line-height: 0.36rem;
  background-color: #f2f2f2;
  padding: 0.2rem 0.32rem 0.12rem 0.52rem;
  border-radius: 0.16rem;
  li {
    margin-bottom: 0.08rem;
    font-size: 0.24rem;
    line-height: 0.36rem;
  }
}
:deep(.n-form-item-label__text) {
  font-weight: 500;
  font-size: 0.28rem;
  line-height: 0.36rem;
}

.rounded-top {
  border-top-left-radius: 0.08rem;
  border-top-right-radius: 0.08rem;
}
.rounded-bottom {
  border-bottom-left-radius: 0.08rem;
  border-bottom-right-radius: 0.08rem;
}
:deep(.n-radio__label) {
  color: #333;
}

.top-form-item {
  padding-top: 0.4rem;
  padding-bottom: 0.16rem;
  margin-bottom: 0.1rem;
  position: relative;
  border-bottom: 0.02rem solid #e6e6e6;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -0.6rem;
    left: 0;
    color: #e50113;
  }
}

.others-form-item {
  padding-top: 0.4rem;
  padding-bottom: 0.16rem;
  margin-bottom: 0.1rem;
  position: relative;
  border-bottom: 0.02rem solid #e6e6e6;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    top: 0.12rem;
    left: 0;
    color: #e50113;
    .n-form-item-feedback__line {
      line-height: 1;
    }
  }
}

.inner-form-item {
  width: 5.4rem;
  margin-left: 0.16rem;
  position: absolute;
  bottom: 0rem;
  left: 1.2rem;
  :deep(.n-form-item-feedback-wrapper) {
    position: absolute;
    bottom: -0.6rem;
    left: -1.36rem;
    color: #e50113;
  }
}

.custom-input {
  border: none !important;
  --n-border: none !important;
  --n-border-warning: none;
  --n-border-focus-warning: none;
  --n-border-hover-warning: none;
  --n-border: none;
  --n-border-disabled: none;
  --n-border-hover: none;
  --n-border-focus: none;
  --n-box-shadow-focus: none;
  --n-box-shadow-focus-warning: none;
  --n-border-error: none;
  --n-border-focus-error: none;
  --n-border-hover-error: none;
  --n-box-shadow-focus-error: none;
  --n-border-active-error: none;
  --n-box-shadow-active: none;
  --n-border-active: none;
  :deep(.n-base-selection) {
    border: none !important;
    --n-border: none !important;
    --n-border-warning: none;
    --n-border-focus-warning: none;
    --n-border-hover-warning: none;
    --n-border: none;
    --n-border-disabled: none;
    --n-border-hover: none;
    --n-border-focus: none;
    --n-box-shadow-focus: none;
    --n-box-shadow-focus-warning: none;
    --n-border-error: none;
    --n-border-focus-error: none;
    --n-border-hover-error: none;
    --n-box-shadow-focus-error: none;
    --n-border-active-error: none;
    --n-box-shadow-active: none;
    --n-border-active: none;
  }
  :deep(.n-input-wrapper) {
    padding-left: 0;
    padding-right: 0;
  }
  :deep(.n-base-selection-input) {
    padding-left: 0;
    padding-right: 0;
  }
  :deep(.n-base-selection .n-base-selection-overlay) {
    padding-left: 0;
    padding-right: 0;
  }
  :deep(.n-input-wrapper) {
    height: 0.44rem;
    line-height: 0.44rem;
  }
  :deep(.n-form-item-blank) {
    height: 0.44rem;
    line-height: 0.44rem;
  }
  :deep(.n-input__input-el) {
    height: 0.44rem;
    line-height: 0.44rem;
  }
}
.input-form-item {
  :deep(.n-form-item-blank) {
    height: 0.44rem;
    line-height: 0.44rem;
    min-height: 0.44rem;
  }
}
</style>
