syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "common.proto";
import "chilat/commodity/commodity_common.proto";

message GoodsDictionaryPageResp {
  common.Result result = 1;
  common.Page page = 2;
  repeated GoodsDictionaryModel data = 3;
}

message GoodsDictionaryModel {
  string id = 1;
  string itemName = 2; //包装名称
  string itemAlias = 7; //包装别名
  string itemValue = 3; //包装数量
  string itemRemark = 4; //包装备注
  int32 idx = 5; //顺序
  string itemNameCn = 6; //中文名
}