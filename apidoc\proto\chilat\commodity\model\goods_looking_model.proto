syntax = "proto3";
package chilat.commodity;

option java_package = "com.chilat.rpc.commodity.model";

import "chilat/commodity/commodity_common.proto";
import "chilat/commodity/model/goods_sku_model.proto";
import "chilat/inquiry/inquiry_common.proto";
import "common.proto";

message GoodsLookingPageResp {
  common.Result result = 1;
  GoodsLookingStatsModel data = 2;
}

// 询盘统计信息
message GoodsLookingStatsModel {
  int32 assignCount = 10; // 待分配数量
  int32 waitCount = 20; // 待下发数量
  int32 findingCount = 30; // 已下发数量
  int32 connectedCount = 40; // 已建联数量
  int32 prepareOrderCount = 50; // 询运费中（待开单）数量
  int32 createdOrderCount = 60; // 已开单数量
  int32 cancelCount = 70; // 已取消数量
  common.Page page = 5; // 分页
  repeated GoodsLookingModel data = 6; // 数据
  int32 inquiryProgressCount = 7; // 询价中询盘单数量
  int32 inquiryFinishCount = 8; // 询价完成待开单询盘单数量
  int32 inquiryOrderCount = 9; // 已开单询盘单数量
}

// 询盘信息
message GoodsLookingModel {
  string id = 1;
  string goodsLookingNo = 2; // 询盘号
  int64 submitTime = 3; // 询盘时间
  string goodsFinder = 4; // 找货人
  GoodsLookingState state = 5; // 询盘状态
  string stateDesc = 6; // 询盘状态描述
  int32 buyQuantity = 7; // 询盘数量
  double buyAmount = 8; // 询盘总价
  string whatsapp = 9; // whatsapp
  string email = 10; // 邮箱
  string goodsSubmitter = 11; // 询盘人
  string countryName = 12; // 国家名称
  repeated GoodsLookingRemarkModel remarks = 13; // 备注
  repeated GoodsLookingSkuModel skus = 14; // 询盘商品
  int32 historyInquiryCount = 15; //历史询盘数
  int32 todayInquiryCount = 16; //今日询盘数
  string token = 17; //token
  string customerRemark = 18; // 客户备注
  string originGoodsFinders = 19; //归属业务员
  string address = 20; //地址
  string postcode = 21; //邮编
  string createBy = 22; //创建人
  inquiry.InquiryXpState inquiryState = 23; //询价状态(未询价：NO_XP_INQUIRY 询价中：PROGRESS_XP_INQUIRY 已询价：HAS_XP_INQUIRY)
  bool goodLookingOrderStatus = 24; //询盘开单状态（true 已开单）
  double totalEstimateFreight = 181; //预估运费总计（全部SKU可预估运费时，才返回）
  double partEstimateFreight = 182; //预估运费部分（只要有一个SKU可预估运费时，则返回）
  int32 freightGoodsQty = 183; //参与预估运费的商品数量
  int32 siteId = 210; //站点ID（即配送国家ID）
  string siteCode = 220; //站点代码（两位配送国家代码；大写英文字母）
  string siteName = 230; //站点名称（即配送国家名称）
  string siteLogo = 240; //站点LOGO图片（国家标识图片URL，46x32像素）
  string fromInviteUsername = 250; //询盘单创建者的邀请人
}

// 询盘SKU
message GoodsLookingSkuModel {
  string lookingGoodsLineId = 24; // 询盘商品行ID
  string skuId = 1; // SKU ID
  string skuNo = 2; // SKU号
  string skuImage = 3; // SKU图片
  repeated GoodsLookingSkuSpecModel specsInfo = 4; // 规格
  int32 buyQuantity = 5; // 询盘数量
  double salePrice = 6; // 单价
  double currentSalePrice = 26; // 当前单价（商品最新销售价；按阶梯价取值；若SKU已失效，则此字段不返回）
  double buyAmount = 7; // 总价
  string goodsName = 8; // 商品名称
  string goodsNo = 9; // 商品编号
  string supplyLink = 10; // 货源链接
  string goodsImage = 11; // 商品图片
  int32 packageQuantity = 12; // 包装数量
  double weight = 13; // 重量
  double length = 14; // 长
  double width = 15; // 宽
  double height = 16; // 高
  double supplyPrice = 17; // 采购价
  string categoryName = 18; //分类名称
  string sourceShopId = 19; //源店铺ID
  repeated common.IdNameModel goodsTags = 23; // 商品标签
  double dimensionCm = 25; //体积
  double shippingCostPerBox = 40; //供应商运费（单箱运费，单位RMB，表示每个包装箱的运输费用）
  double estimateFreight = 181; //预估运费
  string skuKey = 86; //SKU列表中的sku唯一键（由“skuId+padc”组合而成）
  string padc = 88; //促销活动动态代码
  string paName = 89; //促销活动名称（活动显示名称）
}

// SKU规格信息
message GoodsLookingSkuSpecModel {
  string specId = 1; // 规格ID
  string specName = 2; // 规格名称
  string itemId = 3; // 规格值ID
  string itemName = 4; // 规格值
  string imageUrl = 5; // 图片
  string color = 6; // 颜色
}

// 询盘备注
message GoodsLookingRemarkModel {
  string remark = 1; // 备注
  string coperator = 2; // 操作人
  int64 cdate = 3; // 操作时间
}

message GoodsLookingDetailResp {
  common.Result result = 1;
  GoodsLookingModel data = 2;
}