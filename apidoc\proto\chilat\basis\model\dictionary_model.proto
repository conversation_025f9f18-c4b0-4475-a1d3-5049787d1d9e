syntax = "proto3";
package chilat.basis;

option java_package = "com.chilat.rpc.basis";

import "common.proto";

message JobStatListResp {
  common.Result result = 1;
  repeated JobStatListModel data = 2;
}

message JobStatListModel {
  string remark = 10;
}

//获取站点列表返回
message GetSiteListResp {
  common.Result result = 1;
  repeated MidSiteModel data = 2;
}

//站点信息
message MidSiteModel {
  int32 id = 10; //站点ID（即配送国家ID）
  string siteCode = 20; //站点代码（两位配送国家代码；大写英文字母）
  string siteName = 30; //站点名称（即配送国家名称）
  string siteNameZh = 31; //站点中文名称（即配送国家中文名称）
  string siteNameEn = 32; //站点英文名称（即配送国家英文名称）
  string siteLogo = 40; //站点LOGO图片（国家标识图片URL，46x32像素）
}

